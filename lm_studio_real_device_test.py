#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LM Studio 双角色测试器 - 真实设备版本
基于 Planner + Actor 架构，集成真实设备工具
"""

import json
import time
import random
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from dataclasses import dataclass, asdict
from langchain_openai import ChatOpenAI

# 导入真实工具模块 (来自concurrent_agent.py)
from tools.check_devices_tools import get_available_device
from tools.wait_tools import wait_for_seconds
from tools.screenshot_tools import get_screenshot
from tools.tap_tools import tap
from tools.slide_tools import slide
from tools.api_ocr_tools import show_page_ocr_result, locate_element_from_ocr
from tools.app_operate_tools import restart_app, background_switch
from tools.smart_input_tools import smart_input_text
from tools.llm_base_tools import start_test, end_test, record_summary, record_issue
from tools._device_status_manage_tools import (
    update_device_status, 
    create_device_status, 
    get_device_status,
)
from tools.check_page_detail_tools import check_ui_bugs, locate_element_from_layout

# 全局logger变量
logger = None

def setup_logging():
    """初始化日志配置"""
    global logger
    
    # 生成当前时间的日志文件名
    log_filename = f'dual_role_real_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - [%(name)s] - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ],
        force=True  # 强制重新配置
    )
    
    logger = logging.getLogger("DualRoleTest")
    logger.info(f"📝 日志系统初始化完成，日志文件: {log_filename}")
    return log_filename


@dataclass
class StepResult:
    """步骤执行结果"""
    step_id: int
    status: str  # success, failed, retry, skipped
    result_data: Dict[str, Any]
    execution_time: float
    retry_count: int
    error_message: Optional[str] = None


@dataclass
class DAGState:
    """DAG状态管理"""
    completed_steps: List[int]
    failed_steps: List[int]
    current_step: int
    variables: Dict[str, Any]  # 存储步骤间传递的变量
    step_results: Dict[int, StepResult]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "completed_steps": self.completed_steps,
            "failed_steps": self.failed_steps,
            "current_step": self.current_step,
            "variables": self.variables,
            "step_results": {k: asdict(v) for k, v in self.step_results.items()}
        }


def real_find_available_device(platform: str) -> Dict[str, Any]:
    """查找可用设备 - 真实实现"""
    try:
        time.sleep(0.5)
        udid = get_available_device(platform)
        
        if udid:
            device_info = get_device_status(udid)
            device_name = device_info.get("device_name", "Unknown") if device_info else "Unknown"
            
            logger.info(f"找到可用{platform}设备: {udid} ({device_name})")
            return {
                "success": True,
                "device": {
                    "udid": udid,
                    "platform": platform,
                    "status": "available",
                    "model": device_name
                },
                "udid": udid,  # 提供给变量传递
                "message": f"成功找到{platform}设备"
            }
        else:
            return {
                "success": False,
                "error": f"没有找到可用的{platform}设备"
            }
    except Exception as e:
        return {
            "success": False,
            "error": f"查找设备异常: {str(e)}"
        }


def real_start_device_test(udid: str) -> Dict[str, Any]:
    """启动设备测试会话 - 真实实现"""
    try:
        time.sleep(0.5)
        
        # 标记设备为测试中
        current_status = get_device_status(udid)
        if current_status:
            update_device_status(udid, {
                "status": "testing",
                "start_time": time.time()
            })
        
        result = start_test(udid)
        
        # 解析结果
        if isinstance(result, str):
            try:
                result_data = json.loads(result)
                if result_data.get("status") == "success":
                    logger.info(f"启动设备 {udid} 的测试会话")
                    return {
                        "success": True,
                        "session_id": f"session_{udid}_{int(time.time())}",
                        "message": "成功启动测试会话"
                    }
                else:
                    return {
                        "success": False,
                        "error": result_data.get("message", "启动测试失败")
                    }
            except:
                return {
                    "success": True,
                    "session_id": f"session_{udid}_{int(time.time())}",
                    "message": "成功启动测试会话"
                }
        else:
            return {
                "success": True,
                "session_id": f"session_{udid}_{int(time.time())}",
                "message": "成功启动测试会话"
            }
                
    except Exception as e:
        return {
            "success": False,
            "error": f"启动测试异常: {str(e)}"
        }


def real_find_element_on_page(udid: str, element: str, scene_desc: str = "元素查找") -> Dict[str, Any]:
    """在页面上查找元素 - 真实实现"""
    try:
        time.sleep(0.8)
        
        find_result = locate_element_from_layout(udid, element, scene_desc)
        
        if find_result["status"] == "success":
            text_result = find_result["text_result"]
            
            if not text_result.startswith("未找到"):
                logger.info(f"在设备 {udid} 找到元素: {element}")
                return {
                    "success": True,
                    "text_result": text_result,
                    "image_url": find_result.get("image_url", ""),
                    "message": f"成功找到元素: {element}"
                }
            else:
                return {
                    "success": False,
                    "error": f"未找到元素: {element}"
                }
        else:
            return {
                "success": False,
                "error": find_result.get("text_result", "查找元素失败")
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"查找元素异常: {str(e)}"
        }


def real_tap_device(udid: str, x: int, y: int) -> Dict[str, Any]:
    """点击设备屏幕 - 真实实现"""
    try:
        time.sleep(0.3)
        
        success = tap(udid, x, y)
        
        if success:
            logger.info(f"在设备 {udid} 的坐标 ({x}, {y}) 执行点击")
            return {
                "success": True,
                "message": f"成功点击坐标 ({x}, {y})"
            }
        else:
            return {
                "success": False,
                "error": f"点击坐标 ({x}, {y}) 失败"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"点击操作异常: {str(e)}"
        }


def real_wait_seconds(seconds: int) -> Dict[str, Any]:
    """等待指定秒数 - 真实实现"""
    try:
        logger.info(f"等待 {seconds} 秒...")
        result = wait_for_seconds(seconds)
        
        return {
            "success": True,
            "message": f"等待 {seconds} 秒完成"
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"等待操作异常: {str(e)}"
        }


def real_ocr_text_only(udid: str) -> Dict[str, Any]:
    """OCR识别页面文本 - 真实实现"""
    try:
        time.sleep(1.0)
        
        ocr_result = show_page_ocr_result(udid)
        text_result = ocr_result.get("final_text", "")
        image_url = ocr_result.get("image_url", "")
        
        logger.info(f"OCR识别结果: {text_result[:100]}...")
        
        return {
            "success": True,
            "text": text_result,
            "image_url": image_url,
            "message": "OCR识别完成"
        }
        
    except Exception as e:
        return {
            "success": False,
            "text": "",
            "error": f"OCR识别异常: {str(e)}"
        }


def real_ocr_text_validation(udid: str, target_text: str) -> Dict[str, Any]:
    """OCR文本校验 - 真实实现"""
    try:
        time.sleep(1.0)
        
        validate_result = locate_element_from_ocr(udid, target_text)
        
        logger.info(f"OCR文本校验完成: 目标'{target_text}'")
        
        # 直接返回原始结果，添加success标记
        result = validate_result.copy()
        result["success"] = True
        result["message"] = "OCR校验完成"
        return result
        
    except Exception as e:
        return {
            "success": False,
            "found": False,
            "error": f"OCR校验异常: {str(e)}"
        }


def real_input_text_smart(udid: str, text: str) -> Dict[str, Any]:
    """智能输入文本 - 真实实现"""
    try:
        time.sleep(0.6)
        
        result = smart_input_text(udid, text)
        
        # 解析结果
        if isinstance(result, str):
            try:
                result_data = json.loads(result)
                if result_data.get("status") == "success":
                    logger.info(f"在设备 {udid} 输入文本: {text}")
                    return {
                        "success": True,
                        "message": f"成功输入文本: {text}"
                    }
                else:
                    return {
                        "success": False,
                        "error": result_data.get("message", "输入失败")
                    }
            except:
                logger.info(f"在设备 {udid} 输入文本: {text}")
                return {
                    "success": True,
                    "message": f"成功输入文本: {text}"
                }
        else:
            return {
                "success": True,
                "message": f"成功输入文本: {text}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"输入文本异常: {str(e)}"
        }


def real_check_page_display(udid: str, scene_desc: str = "页面检查") -> Dict[str, Any]:
    """检查页面展示 - 真实实现"""
    try:
        time.sleep(1.0)
        
        check_result = check_ui_bugs(udid, scene_desc)
        
        if check_result["status"] == "success":
            logger.info(f"页面检查完成")
            
            # 直接返回原始结果，添加success标记
            result = check_result.copy()
            result["success"] = True
            result["message"] = "页面检查完成"
            return result
        else:
            return {
                "success": False,
                "error": check_result.get("text_result", "页面检查失败")
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"页面检查异常: {str(e)}"
        }


def real_end_device_test(udid: str) -> Dict[str, Any]:
    """结束设备测试会话 - 真实实现"""
    try:
        time.sleep(0.5)
        
        # 恢复设备状态
        current_status = get_device_status(udid)
        if current_status:
            start_time = current_status.get("start_time", 0)
            test_duration = time.time() - start_time if start_time > 0 else 0
            
            update_device_status(udid, {
                "status": "ready",
                "test_duration": test_duration,
                "start_time": 0.0
            })
        
        result = end_test(udid)
        
        # 解析结果
        if isinstance(result, str):
            try:
                result_data = json.loads(result)
                if result_data.get("status") == "success":
                    logger.info(f"结束设备 {udid} 的测试会话")
                    return {
                        "success": True,
                        "message": "成功结束测试会话"
                    }
                else:
                    return {
                        "success": False,
                        "error": result_data.get("message", "结束测试失败")
                    }
            except:
                logger.info(f"结束设备 {udid} 的测试会话")
                return {
                    "success": True,
                    "message": "成功结束测试会话"
                }
        else:
            return {
                "success": True,
                "message": "成功结束测试会话"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"结束测试异常: {str(e)}"
        }


# 真实工具注册表 - 映射human_plan.json中的action到真实工具函数
REAL_TOOL_REGISTRY = {
    "find_device": real_find_available_device,
    "start_test": real_start_device_test,
    "find_element": real_find_element_on_page,
    "tap": real_tap_device,
    "wait": real_wait_seconds,
    "ocr_text_only": real_ocr_text_only,
    "ocr_validate_text": real_ocr_text_validation,
    "input_text": real_input_text_smart,
    "check_page": real_check_page_display,
    "end_test": real_end_device_test
}


class TestPlanner:
    """测试策略规划器 - 负责DAG管理和决策"""
    
    def __init__(self, llm: ChatOpenAI):
        self.llm = llm
        
    def get_next_action(self, plan: Dict[str, Any], dag_state: DAGState) -> Dict[str, Any]:
        """决定下一步执行策略"""
        
        logger.info("🧠 [Planner] 开始分析当前测试状态...")
        
        # 构建当前状态摘要
        remaining_steps = [s for s in plan["steps"] if s["step_id"] not in dag_state.completed_steps]
        recent_results = list(dag_state.step_results.values())[-3:] if dag_state.step_results else []
        
        logger.info(f"🧠 [Planner] 当前进度: 已完成 {len(dag_state.completed_steps)} 步骤, 失败 {len(dag_state.failed_steps)} 步骤, 剩余 {len(remaining_steps)} 步骤")
        
        state_snippet = {
            "total_steps": len(plan["steps"]),
            "completed_count": len(dag_state.completed_steps),
            "failed_count": len(dag_state.failed_steps),
            "current_step_id": dag_state.current_step,
            "remaining_steps": len(remaining_steps),
            "recent_results": [{"step_id": r.step_id, "status": r.status} for r in recent_results]
        }
        
        # 检查依赖关系，找出可执行的下一步
        executable_steps = []
        for step in remaining_steps:
            step_id = step["step_id"]
            
            # 跳过已经彻底失败的步骤（重试次数已达上限）
            if step_id in dag_state.failed_steps:
                continue
                
            # 简单的顺序依赖：step_id-1 必须完成，或者可以跳过失败的前置步骤
            deps_satisfied = True
            if step_id > 1:
                prev_step_id = step_id - 1
                # 前置步骤完成 或 前置步骤已失败（可跳过）
                deps_satisfied = (prev_step_id in dag_state.completed_steps or 
                                prev_step_id in dag_state.failed_steps)
            
            if deps_satisfied:
                executable_steps.append(step_id)
        
        logger.info(f"🧠 [Planner] 可执行步骤: {executable_steps}")
        if dag_state.failed_steps:
            logger.info(f"🧠 [Planner] 已失败步骤: {dag_state.failed_steps}")
        
        # 构建Planner prompt
        planner_prompt = f"""你是一名测试策略师，只能输出 JSON 格式的决策。

全局目标：{plan["summary"]}
总步骤数：{plan["total_steps"]}
已完成节点：{dag_state.completed_steps}
失败节点：{dag_state.failed_steps}
当前进度：{len(dag_state.completed_steps)}/{len(plan["steps"])}

当前状态：
{json.dumps(state_snippet, ensure_ascii=False, indent=2)}

可执行步骤：{executable_steps}

决策规则：
1. 如果有可执行步骤，选择其中step_id最小的执行
2. 已经彻底失败的步骤(在failed_steps中)不再重试，直接跳过
3. 如果所有关键步骤完成或失败，可以选择complete
4. 优先执行能够推进测试流程的步骤

请输出JSON格式的决策：
{{"next": <step_id>, "action": "<action_type>", "reason": "<决策理由>"}}"""

        try:
            response = self.llm.invoke([{"role": "user", "content": planner_prompt}])
            decision_text = response.content.strip()
            
            # 尝试解析JSON
            if decision_text.startswith("```json"):
                decision_text = decision_text.split("```json")[1].split("```")[0].strip()
            elif decision_text.startswith("```"):
                decision_text = decision_text.split("```")[1].split("```")[0].strip()
            
            decision = json.loads(decision_text)
            logger.info(f"🧠 Planner决策: {decision}")
            return decision
            
        except Exception as e:
            logger.error(f"Planner决策解析失败: {e}")
            # 默认策略：执行下一个可行步骤
            if executable_steps:
                return {
                    "next": min(executable_steps),
                    "action": "continue",
                    "reason": "默认策略：执行下一个可行步骤"
                }
            else:
                # 检查是否还有未尝试的步骤
                total_processed = len(dag_state.completed_steps) + len(dag_state.failed_steps)
                if total_processed >= len(plan["steps"]):
                    return {
                        "next": -1,
                        "action": "complete", 
                        "reason": "所有步骤都已处理（成功或失败），任务完成"
                    }
                else:
                    return {
                        "next": -1,
                        "action": "complete", 
                        "reason": "没有可执行步骤，可能存在依赖问题"
                    }


class TestActor:
    """测试执行器 - 负责单步工具调用"""
    
    def __init__(self, llm: ChatOpenAI):
        self.llm = llm
        
    def execute_step(self, step: Dict[str, Any], variables: Dict[str, Any]) -> StepResult:
        """执行单个测试步骤 - 增强版：LLM自主决策参数"""
        start_time = time.time()
        step_id = step["step_id"]
        
        try:
            logger.info(f"🤖 [Actor] 准备执行步骤 {step_id}: {step['description']}")
            
            # 检查是否有坐标类动态参数需要LLM解析
            original_params = step["parameters"]
            has_coordinate_params = any(isinstance(v, str) and v.startswith("{") and v.endswith("}") and
                                      ("_x" in v or "_y" in v)
                                      for v in original_params.values())
            
            if has_coordinate_params:
                # 使用LLM智能解析坐标参数
                resolved_params = self._llm_resolve_parameters(step, original_params, variables)
                logger.info(f"🤖 [Actor] LLM参数解析: {original_params} → {resolved_params}")
            else:
                # 使用增强的简单参数解析（处理device_udid等）
                resolved_params = self._resolve_parameters(original_params, variables)
                if original_params != resolved_params:
                    logger.info(f"🤖 [Actor] 参数解析: {original_params} → {resolved_params}")
                else:
                    logger.info(f"🤖 [Actor] 执行参数: {resolved_params}")
            
            logger.info(f"🤖 [Actor] 调用工具: {step['action']}")
            
            # 调用真实工具
            tool_name = step["action"]
            if tool_name in REAL_TOOL_REGISTRY:
                tool_func = REAL_TOOL_REGISTRY[tool_name]
                result = tool_func(**resolved_params)
                
                execution_time = time.time() - start_time
                logger.info(f"🤖 [Actor] 工具执行完成，耗时: {execution_time:.2f}秒")
                
                # 判断执行状态
                if isinstance(result, dict):
                    if result.get("success", False):
                        status = "success"
                        error_message = None
                        logger.info(f"🤖 [Actor] ✅ 工具执行成功: {result.get('message', '无消息')}")
                    else:
                        status = "failed"
                        error_message = result.get("error", "未知错误")
                        logger.warning(f"🤖 [Actor] ❌ 工具执行失败: {error_message}")
                else:
                    status = "success"
                    error_message = None
                    logger.info(f"🤖 [Actor] ✅ 工具执行成功: {str(result)[:100]}")
                
                return StepResult(
                    step_id=step_id,
                    status=status,
                    result_data=result if isinstance(result, dict) else {"result": result},
                    execution_time=execution_time,
                    retry_count=step.get("retry_count", 0),
                    error_message=error_message
                )
            else:
                return StepResult(
                    step_id=step_id,
                    status="failed",
                    result_data={"error": f"工具 {tool_name} 未找到"},
                    execution_time=time.time() - start_time,
                    retry_count=step.get("retry_count", 0),
                    error_message=f"工具 {tool_name} 未找到"
                )
                
        except Exception as e:
            logger.error(f"Actor执行步骤 {step_id} 异常: {e}")
            return StepResult(
                step_id=step_id,
                status="failed",
                result_data={"error": str(e)},
                execution_time=time.time() - start_time,
                retry_count=step.get("retry_count", 0),
                error_message=str(e)
            )
    
    def _resolve_parameters(self, params: Dict[str, Any], variables: Dict[str, Any]) -> Dict[str, Any]:
        """解析参数中的变量引用 - 增强版"""
        resolved = {}
        
        for key, value in params.items():
            if isinstance(value, str) and value.startswith("{") and value.endswith("}"):
                # 解析变量引用，如 {device_udid} -> 实际设备ID
                var_name = value[1:-1]  # 去掉 {}
                
                if var_name == "device_udid":
                    # 查找设备UDID - 从step1或任何包含udid的步骤
                    found_udid = None
                    for step_name, step_var in variables.items():
                        if isinstance(step_var, dict):
                            # 先检查直接的udid字段
                            if "udid" in step_var:
                                found_udid = step_var["udid"]
                                break
                            # 再检查raw_result中的udid
                            if "raw_result" in step_var and isinstance(step_var["raw_result"], dict):
                                if "udid" in step_var["raw_result"]:
                                    found_udid = step_var["raw_result"]["udid"]
                                    break
                    
                    if found_udid and not found_udid.startswith("{"):
                        resolved[key] = found_udid
                    else:
                        resolved[key] = value  # 保持原值
                        
                elif var_name.endswith("_x") or var_name.endswith("_y"):
                    # 坐标参数保持原格式，让LLM解析
                    resolved[key] = value  # 保持原值
                else:
                    # 查找其他变量
                    found = False
                    for step_var in variables.values():
                        if isinstance(step_var, dict) and var_name in step_var:
                            resolved[key] = step_var[var_name]
                            found = True
                            break
                    if not found:
                        resolved[key] = value  # 保持原值
            else:
                resolved[key] = value
                
        return resolved
    
    def _llm_resolve_parameters(self, step: Dict[str, Any], params: Dict[str, Any], variables: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM智能解析动态参数"""
        try:
            # 构建上下文信息
            recent_results = []
            for step_name, step_data in variables.items():
                if isinstance(step_data, dict):
                    recent_results.append(f"{step_name}: {step_data}")
            
            context = "\n".join(recent_results[-3:])  # 最近3步的结果
            
            llm_prompt = f"""你是一个智能测试执行器。根据之前步骤的执行结果，智能解析当前步骤所需的参数。

当前要执行的步骤：
- 步骤ID: {step['step_id']}
- 动作: {step['action']}
- 描述: {step['description']}
- 原始参数: {json.dumps(params, ensure_ascii=False)}

之前步骤的执行结果：
{context}

请仔细分析上面的执行历史，找到需要的信息来解析参数：

解析规则：
1. 如果看到参数值是 "{{device_udid}}"，需要从step1的结果中找到实际的设备ID
2. 如果看到参数值是 "{{element_x}}" 或 "{{element_y}}"，需要从最近的find_element步骤的text_result中解析坐标
3. 如果text_result中有 "center_pos=(85,57)" 这样的信息，那么x=85, y=57
4. 如果找不到坐标信息，使用默认值 x=200, y=150
5. 其他参数保持不变

示例：
- 如果参数是 {{"udid": "{{device_udid}}"}}，且step1结果有 "udid": "00008110-001104592252801E"，则返回 {{"udid": "00008110-001104592252801E"}}
- 如果参数是 {{"x": "{{element_x}}"}}，且找到 center_pos=(85,57)，则返回 {{"x": 85}}

请返回解析后的参数JSON（只要JSON，不要任何其他文字）："""

            response = self.llm.invoke([{"role": "user", "content": llm_prompt}])
            result_text = response.content.strip()
            
            # 解析JSON响应
            if result_text.startswith("```json"):
                result_text = result_text.split("```json")[1].split("```")[0].strip()
            elif result_text.startswith("```"):
                result_text = result_text.split("```")[1].split("```")[0].strip()
            
            resolved_params = json.loads(result_text)
            
            # 验证解析结果，确保关键参数存在
            for key, value in params.items():
                if key not in resolved_params:
                    resolved_params[key] = value
            
            return resolved_params
            
        except Exception as e:
            logger.warning(f"🤖 [Actor] LLM参数解析失败: {e}，使用简单解析")
            # 降级到简单解析
            return self._resolve_parameters(params, variables)
    
    def evaluate_step_result(self, step: Dict[str, Any], result: StepResult, variables: Dict[str, Any]) -> Dict[str, str]:
        """评估步骤执行结果，决定下一步策略"""
        try:
            # 构建评估上下文
            recent_results = []
            for step_name, step_data in variables.items():
                if isinstance(step_data, dict) and "raw_result" in step_data:
                    recent_results.append(f"{step_name}: {step_data['raw_result']}")
            
            context = "\n".join(recent_results[-2:])  # 最近2步的结果
            
            evaluation_prompt = f"""你是一个智能测试评估器。根据刚执行完的步骤结果，简单判断是否应该继续按原计划执行。

刚执行的步骤：
- 步骤ID: {step['step_id']}
- 动作: {step['action']}
- 描述: {step['description']}
- 执行状态: {result.status}
- 执行结果: {result.result_data}

之前的执行历史：
{context}

请简单判断：
1. 如果步骤执行成功且结果符合预期，建议继续
2. 如果步骤失败但可能是临时问题，建议重试
3. 如果步骤失败且可能影响后续步骤，建议调整计划
4. 如果发现异常情况需要人工介入，建议暂停

只返回JSON格式的建议，不要添加其他说明：
{{"action": "continue/retry/adjust/pause", "reason": "简短理由"}}"""

            response = self.llm.invoke([{"role": "user", "content": evaluation_prompt}])
            result_text = response.content.strip()
            
            # 解析JSON响应
            if result_text.startswith("```json"):
                result_text = result_text.split("```json")[1].split("```")[0].strip()
            elif result_text.startswith("```"):
                result_text = result_text.split("```")[1].split("```")[0].strip()
            
            evaluation = json.loads(result_text)
            
            logger.info(f"🤖 [Actor] 步骤评估: {evaluation}")
            return evaluation
            
        except Exception as e:
            logger.warning(f"🤖 [Actor] 步骤评估失败: {e}")
            # 默认建议：成功就继续，失败就重试
            if result.status == "success":
                return {"action": "continue", "reason": "执行成功"}
            else:
                return {"action": "retry", "reason": "执行失败，建议重试"}


class DualRoleOrchestrator:
    """双角色协调器 - 协调Planner和Actor"""
    
    def __init__(self, base_url: str = "https://056ce1ef90e9.ngrok-free.app/v1", model_name: str = "local-model"):
        self.llm = ChatOpenAI(
            base_url=base_url,
            api_key="lm-studio",
            model=model_name,
            temperature=0.1,
            timeout=60
        )
        
        self.planner = TestPlanner(self.llm)
        self.actor = TestActor(self.llm)
        
        # 加载测试计划
        self.plan = self._load_plan()
        
        # 初始化DAG状态
        self.dag_state = DAGState(
            completed_steps=[],
            failed_steps=[],
            current_step=1,
            variables={"global": {"platform": self.plan.get("platform", "ios")}},
            step_results={}
        )
        
        # 状态持久化文件
        self.state_file = f"dag_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
    def _load_plan(self) -> Dict[str, Any]:
        """加载测试计划"""
        try:
            with open("human_plan.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载测试计划失败: {e}")
            raise
    
    def _save_state(self):
        """保存DAG状态"""
        try:
            with open(self.state_file, "w", encoding="utf-8") as f:
                json.dump(self.dag_state.to_dict(), f, ensure_ascii=False, indent=2)
            logger.debug(f"💾 [协调器] DAG状态已保存到: {self.state_file}")
        except Exception as e:
            logger.warning(f"💾 [协调器] 保存状态失败: {e}")
    
    def _extract_step_outputs(self, step: Dict[str, Any], result: StepResult):
        """从步骤结果中提取输出变量 - 传递完整原始结果"""
        step_id = step["step_id"]
        
        # 保存完整的原始结果数据，无论成功失败
        step_vars = {
            "status": result.status,
            "raw_result": result.result_data,  # 完整的原始结果
            "execution_time": result.execution_time,
            "error_message": result.error_message
        }
        
        # 如果执行成功，额外提取一些常用字段便于快速访问
        if result.status == "success" and isinstance(result.result_data, dict):
            for key in ["udid", "text_result", "found", "is_normal"]:
                if key in result.result_data:
                    step_vars[key] = result.result_data[key]
        
        # 保存步骤变量
        self.dag_state.variables[f"step{step_id}"] = step_vars
        logger.info(f"📊 步骤 {step_id} 输出变量: {step_vars}")
    
    def run_test(self) -> Dict[str, Any]:
        """运行完整测试"""
        logger.info("🚀 启动双角色真实设备测试执行器")
        start_time = time.time()
        
        max_iterations = 50
        iteration = 0
        
        try:
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"\n━━━━━━━━━━━━━ 第 {iteration} 轮执行 ━━━━━━━━━━━━━")
                logger.info(f"🔄 [协调器] 当前执行轮次: {iteration}/{max_iterations}")
                
                # Planner决策下一步
                logger.info(f"🧠 [协调器] 请求 Planner 进行决策...")
                decision = self.planner.get_next_action(self.plan, self.dag_state)
                logger.info(f"🧠 [协调器] Planner决策结果: {decision}")
                
                if decision["action"] == "complete":
                    logger.info("🎉 Planner决定任务完成")
                    break
                
                if decision["action"] == "wait":
                    wait_seconds = decision.get("seconds", 3)
                    logger.info(f"⏱️ Planner要求等待 {wait_seconds} 秒")
                    time.sleep(wait_seconds)
                    continue
                
                # 获取要执行的步骤
                next_step_id = decision["next"]
                if next_step_id <= 0:
                    logger.warning("⚠️ 没有可执行的步骤")
                    break
                    
                step = next((s for s in self.plan["steps"] if s["step_id"] == next_step_id), None)
                if not step:
                    logger.error(f"❌ 步骤 {next_step_id} 不存在")
                    break
                
                # Actor执行步骤
                logger.info(f"🎯 [协调器] 开始执行步骤 {next_step_id}: {step['description']}")
                logger.info(f"🤖 [协调器] 请求 Actor 执行步骤...")
                result = self.actor.execute_step(step, self.dag_state.variables)
                
                # 记录结果
                logger.info(f"📊 [协调器] 步骤 {next_step_id} 执行结果: {result.status}")
                self.dag_state.step_results[next_step_id] = result
                self.dag_state.current_step = next_step_id
                
                # 先提取步骤输出变量（无论成功失败都提取）
                self._extract_step_outputs(step, result)
                
                # 使用Actor的智能评估来决定下一步
                evaluation = self.actor.evaluate_step_result(step, result, self.dag_state.variables)
                logger.info(f"🧠 [协调器] Actor评估建议: {evaluation}")
                
                if result.status == "success":
                    self.dag_state.completed_steps.append(next_step_id)
                    logger.info(f"✅ 步骤 {next_step_id} 执行成功")
                else:
                    # 根据Actor的评估建议决定重试策略
                    if evaluation.get("action") == "retry":
                        # 更新重试次数
                        step["retry_count"] = step.get("retry_count", 0) + 1
                        
                        if step["retry_count"] >= 3:
                            # 达到最大重试次数，标记为失败
                            if next_step_id not in self.dag_state.failed_steps:
                                self.dag_state.failed_steps.append(next_step_id)
                                logger.error(f"❌ 步骤 {next_step_id} 失败且达到最大重试次数")
                                logger.warning(f"⚠️ 将跳过失败步骤 {next_step_id}，继续执行后续步骤")
                        else:
                            logger.warning(f"⚠️ 步骤 {next_step_id} 失败，Actor建议重试 ({step['retry_count']}/3): {evaluation.get('reason', '无理由')}")
                    elif evaluation.get("action") == "continue":
                        # Actor建议虽然失败但可以继续
                        logger.warning(f"⚠️ 步骤 {next_step_id} 失败，但Actor建议继续: {evaluation.get('reason', '无理由')}")
                    else:
                        # 其他情况，标记为失败
                        if next_step_id not in self.dag_state.failed_steps:
                            self.dag_state.failed_steps.append(next_step_id)
                            logger.error(f"❌ 步骤 {next_step_id} 失败，Actor建议: {evaluation.get('action', 'unknown')}")
                
                # 保存状态
                self._save_state()
                
                # 检查完成条件
                if len(self.dag_state.completed_steps) >= len(self.plan["steps"]):
                    logger.info("🎊 所有步骤已完成")
                    break
            
            execution_time = time.time() - start_time
            
            # 生成总结报告
            return self._generate_summary_report(execution_time, iteration)
            
        except Exception as e:
            logger.error(f"💥 测试执行异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time
            }
    
    def _generate_summary_report(self, execution_time: float, iterations: int) -> Dict[str, Any]:
        """生成测试总结报告"""
        total_steps = len(self.plan["steps"])
        completed_count = len(self.dag_state.completed_steps)
        failed_count = len(self.dag_state.failed_steps)
        success_rate = (completed_count / total_steps) * 100 if total_steps > 0 else 0
        
        return {
            "success": completed_count >= total_steps * 0.8,  # 80%成功率即认为成功
            "execution_time": execution_time,
            "total_iterations": iterations,
            "statistics": {
                "total_steps": total_steps,
                "completed_steps": completed_count,
                "failed_steps": failed_count,
                "success_rate": round(success_rate, 2)
            },
            "completed_steps": self.dag_state.completed_steps,
            "failed_steps": self.dag_state.failed_steps,
            "final_variables": self.dag_state.variables
        }
    
    def print_summary(self, result: Dict[str, Any]):
        """打印测试总结"""
        print("\n" + "="*80)
        print("🎭 LM Studio 双角色真实设备测试总结 (Planner + Actor)")
        print("="*80)
        
        stats = result["statistics"]
        
        if result["success"]:
            print(f"✅ 测试成功完成")
            print(f"⏱️  总执行时间: {result['execution_time']:.2f} 秒")
            print(f"🔄 总执行轮数: {result['total_iterations']} 轮")
            print(f"📊 成功率: {stats['success_rate']}% ({stats['completed_steps']}/{stats['total_steps']})")
        else:
            print(f"❌ 测试未完全成功")
            print(f"⏱️  执行时间: {result['execution_time']:.2f} 秒")
            print(f"📊 完成度: {stats['success_rate']}% ({stats['completed_steps']}/{stats['total_steps']})")
            print(f"💥 失败步骤: {result['failed_steps']}")
        
        print(f"\n📈 执行统计:")
        print(f"  ✅ 成功步骤: {stats['completed_steps']} 个")
        print(f"  ❌ 失败步骤: {stats['failed_steps']} 个")
        print(f"  📋 总步骤数: {stats['total_steps']} 个")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    # 初始化日志系统
    log_filename = setup_logging()
    
    logger.info("🎭 启动 LM Studio 双角色真实设备测试器...")
    logger.info("📡 连接地址: http://localhost:1234")
    logger.info("🎯 测试架构: Planner (策略) + Actor (执行)")
    logger.info("📱 真实设备集成: 是")
    logger.info("-" * 60)
    
    try:
        # 创建双角色协调器
        logger.info("🏗️ 正在初始化双角色协调器...")
        orchestrator = DualRoleOrchestrator()
        
        # 执行测试
        logger.info("🚀 开始执行测试...")
        result = orchestrator.run_test()
        
        # 打印结果
        logger.info("📊 测试执行完成，生成总结报告...")
        orchestrator.print_summary(result)
        
        logger.info(f"📝 详细日志已保存到: {log_filename}")
        
    except Exception as e:
        logger.error(f"💥 程序执行异常: {str(e)}")
        import traceback
        logger.error(f"🔍 异常详情:\n{traceback.format_exc()}")
        raise


if __name__ == "__main__":
    main()