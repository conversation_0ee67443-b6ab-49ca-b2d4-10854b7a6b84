#!/usr/bin/env python3
"""
SimplifyAgent 文件工具函数

提供文件清理、验证等通用工具函数。

作者: SimplifyAgent Development Team
创建时间: 2024-09-05
"""

import os
import shutil
import time
from pathlib import Path
from typing import List, Tuple, Dict, Any
from datetime import datetime


def get_files_by_type(watch_path: str, watch_type: str) -> List[Dict[str, Any]]:
    """根据监控类型获取文件列表"""
    files_info = []
    
    try:
        path = Path(watch_path)
        if not path.exists():
            return files_info
        
        if watch_type == 'json_plan':
            # JSON计划文件 - 严格匹配 plan_mlx_* 模式
            for file_path in path.glob('plan_mlx_*.json'):
                if file_path.is_file():
                    stat = file_path.stat()
                    files_info.append({
                        'path': file_path,
                        'name': file_path.name,
                        'mtime': stat.st_mtime,
                        'size': stat.st_size
                    })
        
        elif watch_type == 'agent_execute':
            # 执行日志文件夹
            for folder_path in path.iterdir():
                if folder_path.is_dir() and folder_path.name.startswith('round_'):
                    stat = folder_path.stat()
                    folder_size = get_folder_size(str(folder_path))
                    files_info.append({
                        'path': folder_path,
                        'name': folder_path.name,
                        'mtime': stat.st_mtime,
                        'size': folder_size
                    })
        
        elif watch_type == 'judge_report':
            # 评价报告文件 - 严格匹配 round_*_report.log 模式
            for file_path in path.glob('round_*_report.log'):
                if file_path.is_file():
                    stat = file_path.stat()
                    files_info.append({
                        'path': file_path,
                        'name': file_path.name,
                        'mtime': stat.st_mtime,
                        'size': stat.st_size
                    })
        
        # 按修改时间排序（旧的在前）
        files_info.sort(key=lambda x: x['mtime'])
        
    except Exception as e:
        print(f"获取文件列表失败: {watch_path}, {e}")
    
    return files_info


def cleanup_old_files(watch_path: str, watch_type: str, max_files: int = 20) -> Tuple[int, float]:
    """清理旧文件，保持文件数量在限制内"""
    files_info = get_files_by_type(watch_path, watch_type)
    
    files_to_delete = len(files_info) - max_files
    if files_to_delete <= 0:
        return 0, 0.0
    
    deleted_count = 0
    total_size = 0
    
    # 删除最旧的文件
    for file_info in files_info[:files_to_delete]:
        try:
            file_path = file_info['path']
            file_size = file_info['size']
            
            # 安全删除
            if watch_type == 'agent_execute':
                if safe_delete_folder(str(file_path)):
                    deleted_count += 1
                    total_size += file_size
            else:
                if safe_delete_file(str(file_path)):
                    deleted_count += 1
                    total_size += file_size
                    
        except Exception as e:
            print(f"删除文件失败: {file_info['path']}, 错误: {e}")
    
    return deleted_count, total_size / (1024 * 1024)  # 转换为MB


def safe_delete_file(file_path: str) -> bool:
    """安全删除文件"""
    try:
        # 检查文件是否被占用
        if is_file_in_use(file_path):
            return False
        
        # 执行删除
        os.remove(file_path)
        return True
        
    except FileNotFoundError:
        # 文件已经不存在
        return True
    except (PermissionError, OSError):
        return False


def safe_delete_folder(folder_path: str) -> bool:
    """安全删除文件夹"""
    try:
        # 检查文件夹中的文件是否被占用
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_full_path = os.path.join(root, file)
                if is_file_in_use(file_full_path):
                    return False
        
        # 执行删除
        shutil.rmtree(folder_path)
        return True
        
    except FileNotFoundError:
        # 文件夹已经不存在
        return True
    except (PermissionError, OSError):
        return False


def is_file_in_use(file_path: str) -> bool:
    """检查文件是否被占用"""
    try:
        # 尝试以独占方式打开文件
        with open(file_path, 'r+b'):
            pass
        return False
    except (IOError, OSError):
        return True


def get_folder_size(folder_path: str) -> int:
    """获取文件夹大小（字节）"""
    total_size = 0
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                except (OSError, IOError):
                    pass
    except Exception:
        pass
    return total_size


def validate_file_name_pattern(file_path: str, watch_type: str) -> Tuple[bool, str]:
    """验证文件名是否符合预期模式"""
    import os
    file_name = os.path.basename(file_path)
    
    if watch_type == 'json_plan':
        # JSON计划文件必须匹配 plan_mlx_YYYYMMDD_HHMMSS.json 模式
        if not (file_name.startswith('plan_mlx_') and file_name.endswith('.json')):
            return False, f"JSON计划文件名不符合 plan_mlx_* 模式: {file_name}"
        
        # 可以进一步验证时间戳格式
        name_part = file_name[9:-5]  # 去掉 plan_mlx_ 和 .json
        if len(name_part) != 15:  # YYYYMMDD_HHMMSS (8+1+6 = 15)
            return False, f"JSON计划文件时间戳长度不正确: {file_name}"
        
        # 检查格式：YYYYMMDD_HHMMSS
        date_part, time_part = name_part.split('_') if '_' in name_part else ('', '')
        if len(date_part) != 8 or not date_part.isdigit() or len(time_part) != 6 or not time_part.isdigit():
            return False, f"JSON计划文件时间戳格式不正确: {file_name}"
    
    elif watch_type == 'judge_report':
        # 评价报告文件必须匹配 round_XXXXXX_YYYYMMDD_HHMMSS_report.log 模式
        if not (file_name.startswith('round_') and file_name.endswith('_report.log')):
            return False, f"评价报告文件名不符合 round_*_report.log 模式: {file_name}"
        
        # 验证格式更严格
        parts = file_name[6:-11].split('_')  # 去掉 round_ 和 _report.log
        if len(parts) != 3:  # round_XXXXXX_YYYYMMDD_HHMMSS 应该有3部分
            return False, f"评价报告文件格式不正确: {file_name}"
    
    elif watch_type == 'agent_execute':
        # 执行日志文件夹必须匹配 round_XXXXXX_YYYYMMDD_HHMMSS 模式
        if not file_name.startswith('round_'):
            return False, f"执行日志文件夹不符合 round_* 模式: {file_name}"
        
        # 验证格式
        parts = file_name[6:].split('_')  # 去掉 round_
        if len(parts) != 3:  # round_XXXXXX_YYYYMMDD_HHMMSS 应该有3部分
            return False, f"执行日志文件夹格式不正确: {file_name}"
    
    return True, "文件名格式正确"


def validate_json_file(file_path: str) -> Tuple[bool, str]:
    """验证JSON文件格式"""
    try:
        import json
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        return True, "JSON格式正确"
    except json.JSONDecodeError as e:
        return False, f"JSON格式错误: {e}"
    except Exception as e:
        return False, f"文件读取失败: {e}"


def validate_log_file(file_path: str) -> Tuple[bool, str]:
    """验证日志文件完整性"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():
                return False, "日志文件为空"
            return True, "日志文件正常"
    except Exception as e:
        return False, f"日志文件读取失败: {e}"


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f}KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f}MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.1f}GB"


if __name__ == '__main__':
    # 文件工具函数测试
    test_path = "/tmp/test_files"
    
    # 测试文件列表获取
    files = get_files_by_type(test_path, 'json_plan')
    print(f"找到 {len(files)} 个JSON文件")
    
    # 测试清理功能
    deleted, size_freed = cleanup_old_files(test_path, 'json_plan', 5)
    print(f"清理结果: 删除 {deleted} 个文件，释放 {size_freed:.2f}MB")