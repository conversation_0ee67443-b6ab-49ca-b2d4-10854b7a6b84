"""
SimplifyAgent 通用服务日志记录系统

提供结构化日志记录、多级别输出、文件滚动归档等功能，
专门为各类服务设计的高性能日志处理模块（支持 log_server、api_server 等）。

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
更新时间: 2024-09-05 (重构为通用版本)
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, Union
import json
import threading
from config import get_config


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, service_name: str = "service", include_context: bool = True):
        super().__init__()
        self.service_name = service_name
        self.include_context = include_context
    
    def format(self, record: logging.LogRecord) -> str:
        # 创建基础日志数据
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'service': self.service_name,
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加上下文信息
        if self.include_context and hasattr(record, 'context'):
            log_data['context'] = record.context
        
        # 添加自定义字段
        custom_fields = ['file_path', 'operation', 'duration', 'success', 'error_code',
                        'request_id', 'user_id', 'endpoint', 'method', 'status_code']
        for field in custom_fields:
            if hasattr(record, field):
                log_data[field] = getattr(record, field)
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class ConsoleFormatter(logging.Formatter):
    """控制台友好的日志格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def __init__(self, service_name: str = "service", use_colors: bool = True):
        super().__init__()
        self.service_name = service_name
        self.use_colors = use_colors and hasattr(sys.stdout, 'isatty') and sys.stdout.isatty()
    
    def format(self, record: logging.LogRecord) -> str:
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        if self.use_colors:
            color = self.COLORS.get(record.levelname, '')
            reset = self.COLORS['RESET']
            level_colored = f"{color}{record.levelname:<8}{reset}"
            service_colored = f"\033[94m[{self.service_name}]\033[0m"  # 蓝色服务名
        else:
            level_colored = f"{record.levelname:<8}"
            service_colored = f"[{self.service_name}]"
        
        # 基础格式
        message = f"[{timestamp}] {level_colored} {service_colored} {record.name} - {record.getMessage()}"
        
        # 添加上下文信息
        if hasattr(record, 'context') and record.context:
            context_str = json.dumps(record.context, ensure_ascii=False, separators=(',', ':'))
            message += f" | Context: {context_str}"
        
        # 添加文件和操作信息
        if hasattr(record, 'file_path'):
            message += f" | File: {Path(record.file_path).name}"
        
        if hasattr(record, 'operation'):
            message += f" | Op: {record.operation}"
        
        if hasattr(record, 'duration') and record.duration is not None:
            message += f" | Duration: {record.duration:.3f}s"
        
        # API相关信息
        if hasattr(record, 'endpoint'):
            message += f" | {record.method} {record.endpoint}"
        
        if hasattr(record, 'status_code'):
            message += f" | Status: {record.status_code}"
        
        return message


class ServerLogger:
    """通用服务日志记录器"""
    
    def __init__(self, service_name: str = "service", logger_name: Optional[str] = None, 
                 config_path: Optional[str] = None):
        """
        初始化服务日志记录器
        
        Args:
            service_name: 服务名称 (如 "log_server", "api_server", "web_server")
            logger_name: 日志记录器名称，默认使用服务名称
            config_path: 配置文件路径，如果不提供则使用默认配置
        """
        self.service_name = service_name
        self.logger_name = logger_name or service_name
        
        # 加载配置
        try:
            if config_path:
                self.config = get_config(config_path)
            else:
                self.config = get_config()
        except Exception:
            self.config = None
        
        self.logger = logging.getLogger(self.logger_name)
        self._setup_logger()
        
        # 性能统计
        self._performance_metrics = {}
        self._metrics_lock = threading.Lock()
    
    def _setup_logger(self):
        """设置日志记录器"""
        # 获取日志配置
        if self.config:
            log_config = self.config.get_logging_config()
        else:
            # 默认配置
            log_config = {
                'level': 'INFO',
                'file_path': f'log/{self.service_name}/{self.service_name}.log',
                'max_bytes': 10 * 1024 * 1024,  # 10MB
                'backup_count': 5,
                'console_output': True
            }
        
        # 设置日志级别
        level = getattr(logging, log_config['level'].upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 文件处理器（结构化日志）
        self._setup_file_handler(log_config)
        
        # 控制台处理器（友好格式）
        if log_config.get('console_output', True):
            self._setup_console_handler(log_config)
        
        # 防止重复日志
        self.logger.propagate = False
    
    def _setup_file_handler(self, log_config: Dict[str, Any]):
        """设置文件处理器"""
        log_file = log_config['file_path']
        
        # 确保日志目录存在
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 创建旋转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=log_config.get('max_bytes', 10*1024*1024),
            backupCount=log_config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        file_handler.setFormatter(
            StructuredFormatter(service_name=self.service_name, include_context=True)
        )
        self.logger.addHandler(file_handler)
    
    def _setup_console_handler(self, log_config: Dict[str, Any]):
        """设置控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(
            ConsoleFormatter(service_name=self.service_name, use_colors=True)
        )
        self.logger.addHandler(console_handler)
    
    def _log_with_context(self, level: int, message: str, context: Optional[Dict[str, Any]] = None, **kwargs):
        """带上下文信息的日志记录"""
        extra = {}
        
        if context:
            extra['context'] = context
        
        # 添加自定义字段
        for key, value in kwargs.items():
            extra[key] = value
        
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, context: Optional[Dict[str, Any]] = None, **kwargs):
        """调试日志"""
        self._log_with_context(logging.DEBUG, message, context, **kwargs)
    
    def info(self, message: str, context: Optional[Dict[str, Any]] = None, **kwargs):
        """信息日志"""
        self._log_with_context(logging.INFO, message, context, **kwargs)
    
    def warning(self, message: str, context: Optional[Dict[str, Any]] = None, **kwargs):
        """警告日志"""
        self._log_with_context(logging.WARNING, message, context, **kwargs)
    
    def error(self, message: str, context: Optional[Dict[str, Any]] = None, **kwargs):
        """错误日志"""
        self._log_with_context(logging.ERROR, message, context, **kwargs)
    
    def critical(self, message: str, context: Optional[Dict[str, Any]] = None, **kwargs):
        """严重错误日志"""
        self._log_with_context(logging.CRITICAL, message, context, **kwargs)
    
    # === 业务专用日志方法 ===
    
    def log_file_event(self, event_type: str, file_path: str, success: bool = True, 
                      duration: Optional[float] = None, error_msg: Optional[str] = None):
        """记录文件操作事件 (适用于 log_server)"""
        context = {
            'event_type': event_type,
            'file_name': Path(file_path).name,
            'success': success
        }
        
        if error_msg:
            context['error'] = error_msg
        
        message = f"File {event_type}: {Path(file_path).name}"
        if not success:
            message += f" - FAILED: {error_msg}"
        
        level = logging.INFO if success else logging.ERROR
        self._log_with_context(
            level, message, context,
            file_path=file_path, operation=event_type, 
            duration=duration, success=success
        )
    
    def log_database_operation(self, operation: str, table: str, success: bool = True,
                             affected_rows: Optional[int] = None, duration: Optional[float] = None,
                             error_msg: Optional[str] = None):
        """记录数据库操作"""
        context = {
            'operation': operation,
            'table': table,
            'success': success
        }
        
        if affected_rows is not None:
            context['affected_rows'] = affected_rows
        
        if error_msg:
            context['error'] = error_msg
        
        message = f"DB {operation} on {table}"
        if affected_rows is not None:
            message += f" ({affected_rows} rows)"
        if not success:
            message += f" - FAILED: {error_msg}"
        
        level = logging.INFO if success else logging.ERROR
        self._log_with_context(
            level, message, context,
            operation=operation, duration=duration, success=success
        )
    
    def log_api_request(self, method: str, endpoint: str, status_code: int,
                       duration: Optional[float] = None, user_id: Optional[str] = None,
                       request_id: Optional[str] = None, error_msg: Optional[str] = None):
        """记录API请求 (适用于 api_server)"""
        context = {
            'method': method,
            'endpoint': endpoint,
            'status_code': status_code,
            'success': status_code < 400
        }
        
        if user_id:
            context['user_id'] = user_id
        if request_id:
            context['request_id'] = request_id
        if error_msg:
            context['error'] = error_msg
        
        message = f"{method} {endpoint} -> {status_code}"
        if error_msg:
            message += f" - {error_msg}"
        
        level = logging.INFO if status_code < 400 else logging.ERROR
        self._log_with_context(
            level, message, context,
            method=method, endpoint=endpoint, status_code=status_code,
            duration=duration, request_id=request_id, user_id=user_id
        )
    
    def log_performance_metric(self, metric_name: str, value: Union[int, float], 
                             unit: str = "", context: Optional[Dict[str, Any]] = None):
        """记录性能指标"""
        with self._metrics_lock:
            if metric_name not in self._performance_metrics:
                self._performance_metrics[metric_name] = []
            
            self._performance_metrics[metric_name].append({
                'timestamp': datetime.now().isoformat(),
                'value': value,
                'unit': unit
            })
        
        log_context = {'metric': metric_name, 'value': value, 'unit': unit}
        if context:
            log_context.update(context)
        
        self.info(f"Metric {metric_name}: {value} {unit}", log_context)
    
    def log_system_status(self, component: str, status: str, details: Optional[Dict[str, Any]] = None):
        """记录系统状态"""
        context = {
            'component': component,
            'status': status,
            'service': self.service_name
        }
        
        if details:
            context.update(details)
        
        message = f"System status - {component}: {status}"
        
        if status.lower() in ['error', 'failed', 'unhealthy']:
            level = logging.ERROR
        elif status.lower() in ['warning', 'degraded']:
            level = logging.WARNING
        else:
            level = logging.INFO
        
        self._log_with_context(level, message, context)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        with self._metrics_lock:
            return dict(self._performance_metrics)
    
    def clear_performance_metrics(self):
        """清除性能指标"""
        with self._metrics_lock:
            self._performance_metrics.clear()
    
    def create_child_logger(self, child_name: str) -> 'ServerLogger':
        """创建子日志记录器"""
        full_name = f"{self.logger_name}.{child_name}"
        child_logger = ServerLogger(
            service_name=self.service_name,
            logger_name=full_name
        )
        return child_logger


class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, logger: ServerLogger, operation: str, 
                 context: Optional[Dict[str, Any]] = None):
        self.logger = logger
        self.operation = operation
        self.context = context or {}
        self.start_time = None
        self.duration = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.debug(f"Starting {self.operation}", self.context)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        self.duration = (end_time - self.start_time).total_seconds()
        
        success = exc_type is None
        if success:
            self.logger.debug(
                f"Completed {self.operation}", 
                self.context,
                operation=self.operation,
                duration=self.duration,
                success=True
            )
        else:
            error_msg = str(exc_val) if exc_val else "Unknown error"
            self.logger.error(
                f"Failed {self.operation}: {error_msg}",
                self.context,
                operation=self.operation,
                duration=self.duration,
                success=False
            )
        
        # 记录性能指标
        self.logger.log_performance_metric(
            f"{self.operation}_duration", 
            self.duration, 
            "seconds"
        )


# === 全局实例管理 ===

_logger_instances: Dict[str, ServerLogger] = {}
_logger_lock = threading.Lock()


def get_logger(service_name: str = "service", logger_name: Optional[str] = None) -> ServerLogger:
    """
    获取服务日志记录器实例（单例模式）
    
    Args:
        service_name: 服务名称 (如 "log_server", "api_server")
        logger_name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    key = f"{service_name}.{logger_name}" if logger_name else service_name
    
    with _logger_lock:
        if key not in _logger_instances:
            _logger_instances[key] = ServerLogger(service_name, logger_name)
        return _logger_instances[key]


def reset_loggers():
    """重置所有日志记录器（主要用于测试）"""
    with _logger_lock:
        _logger_instances.clear()


if __name__ == '__main__':
    # 通用日志系统测试
    print("=== SimplifyAgent 通用日志系统测试 ===")
    
    # 测试 log_server 日志
    log_server_logger = get_logger('log_server')
    log_server_logger.info("日志服务器启动", {'version': '1.0.0', 'mode': 'test'})
    log_server_logger.log_file_event('parsed', '/test/path/plan.json', success=True, duration=0.156)
    log_server_logger.log_database_operation('insert', 'test_plans', success=True, affected_rows=1, duration=0.023)
    
    # 测试 api_server 日志
    api_server_logger = get_logger('api_server')
    api_server_logger.info("API服务器启动", {'port': 8000})
    api_server_logger.log_api_request('GET', '/api/plans', 200, duration=0.045, user_id='user123')
    api_server_logger.log_api_request('POST', '/api/plans', 400, error_msg='Invalid request')
    
    # 测试性能计时器
    with PerformanceTimer(log_server_logger, "test_operation", {'test': True}):
        import time
        time.sleep(0.1)
    
    print("通用日志测试完成")
    print(f"性能指标: {log_server_logger.get_performance_metrics()}")