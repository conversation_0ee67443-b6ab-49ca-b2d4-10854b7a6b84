#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接管理模块
提供 SQLite 数据库连接的统一管理，包括连接池、事务管理和自动重连机制
"""

import sqlite3
import threading
import os
import sys
import time
from typing import Optional, Any, Dict, List, Tuple
from contextlib import contextmanager
from dataclasses import dataclass
import logging

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from tools._concurrent_log_manager import get_current_task_log_manager


@dataclass
class DatabaseConfig:
    """数据库配置类"""
    database_path: str = 'simplify_agent/data/test_database.db'
    connection_timeout: int = 30
    max_connections: int = 10
    enable_foreign_keys: bool = True
    journal_mode: str = 'WAL'
    synchronous: str = 'NORMAL'
    busy_timeout: int = 30000  # 毫秒
    
    def __post_init__(self):
        """确保数据库目录存在"""
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(self.database_path):
            # 获取当前文件所在的目录，然后向上找到项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))  # database目录
            project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录
            self.database_path = os.path.join(project_root, self.database_path)
        
        db_dir = os.path.dirname(self.database_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)


class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self._connections: List[sqlite3.Connection] = []
        self._used_connections: List[sqlite3.Connection] = []
        self._lock = threading.Lock()
        self._created_connections = 0
        
    def _create_connection(self) -> sqlite3.Connection:
        """创建新的数据库连接"""
        try:
            # 确保数据库文件路径存在
            db_dir = os.path.dirname(self.config.database_path)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
                
            conn = sqlite3.connect(
                self.config.database_path,
                timeout=self.config.connection_timeout,
                check_same_thread=False
            )
            
            # 配置数据库连接
            if self.config.enable_foreign_keys:
                conn.execute("PRAGMA foreign_keys = ON")
                
            conn.execute(f"PRAGMA journal_mode = {self.config.journal_mode}")
            conn.execute(f"PRAGMA synchronous = {self.config.synchronous}")
            conn.execute(f"PRAGMA busy_timeout = {self.config.busy_timeout}")
            
            # 使用Row工厂，支持字典式访问
            conn.row_factory = sqlite3.Row
            
            get_current_task_log_manager().info_tools(f"创建数据库连接: {self.config.database_path}", "ConnectionPool")
            return conn
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"创建数据库连接失败: {e}", "ConnectionPool")
            raise
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        with self._lock:
            # 尝试从池中获取可用连接
            if self._connections:
                conn = self._connections.pop()
                self._used_connections.append(conn)
                return conn
                
            # 如果没有可用连接且未达到最大连接数，创建新连接
            if self._created_connections < self.config.max_connections:
                conn = self._create_connection()
                self._created_connections += 1
                self._used_connections.append(conn)
                return conn
                
            # 连接池已满，抛出异常
            raise Exception(f"连接池已满，最大连接数: {self.config.max_connections}")
    
    def return_connection(self, conn: sqlite3.Connection) -> None:
        """归还连接到池中"""
        with self._lock:
            if conn in self._used_connections:
                self._used_connections.remove(conn)
                
                # 检查连接是否仍然有效
                try:
                    conn.execute("SELECT 1")
                    self._connections.append(conn)
                except:
                    # 连接已失效，创建新连接替代
                    try:
                        conn.close()
                    except:
                        pass
                    self._created_connections -= 1
    
    def close_all_connections(self) -> None:
        """关闭所有连接"""
        with self._lock:
            all_connections = self._connections + self._used_connections
            for conn in all_connections:
                try:
                    conn.close()
                except:
                    pass
            
            self._connections.clear()
            self._used_connections.clear()
            self._created_connections = 0
            
            get_current_task_log_manager().info_tools("已关闭所有数据库连接", "ConnectionPool")


class DatabaseManager:
    """数据库管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config: Optional[DatabaseConfig] = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        if hasattr(self, '_initialized'):
            return
            
        self.config = config or DatabaseConfig()
        self.pool = ConnectionPool(self.config)
        self._initialized = True
        
        get_current_task_log_manager().info_tools(f"数据库管理器初始化完成: {self.config.database_path}", "DatabaseManager")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = self.pool.get_connection()
            yield conn
        except Exception as e:
            get_current_task_log_manager().error_tools(f"获取数据库连接失败: {e}", "DatabaseManager")
            raise
        finally:
            if conn:
                self.pool.return_connection(conn)
    
    @contextmanager
    def get_transaction(self):
        """获取数据库事务的上下文管理器"""
        conn = None
        try:
            conn = self.pool.get_connection()
            conn.execute("BEGIN")
            yield conn
            conn.commit()
            get_current_task_log_manager().info_tools("数据库事务提交成功", "DatabaseManager")
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                    get_current_task_log_manager().warning_tools(f"数据库事务回滚: {e}", "DatabaseManager")
                except:
                    pass
            get_current_task_log_manager().error_tools(f"数据库事务失败: {e}", "DatabaseManager")
            raise
        finally:
            if conn:
                self.pool.return_connection(conn)
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """执行查询语句"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                    
                results = cursor.fetchall()
                cursor.close()
                
                get_current_task_log_manager().info_tools(f"查询执行成功，返回 {len(results)} 行", "DatabaseManager")
                return results
                
        except Exception as e:
            get_current_task_log_manager().error_tools(f"查询执行失败: {query[:100]}..., 错误: {e}", "DatabaseManager")
            raise
    
    def execute_update(self, query: str, params: Optional[Tuple] = None) -> int:
        """执行更新语句（INSERT/UPDATE/DELETE）"""
        try:
            with self.get_transaction() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                    
                affected_rows = cursor.rowcount
                last_row_id = cursor.lastrowid
                cursor.close()
                
                get_current_task_log_manager().info_tools(f"更新执行成功，影响 {affected_rows} 行", "DatabaseManager")
                return last_row_id or affected_rows
                
        except Exception as e:
            get_current_task_log_manager().error_tools(f"更新执行失败: {query[:100]}..., 错误: {e}", "DatabaseManager")
            raise
    
    def execute_batch(self, query: str, params_list: List[Tuple]) -> int:
        """批量执行语句"""
        try:
            with self.get_transaction() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                
                affected_rows = cursor.rowcount
                cursor.close()
                
                get_current_task_log_manager().info_tools(f"批量执行成功，影响 {affected_rows} 行", "DatabaseManager")
                return affected_rows
                
        except Exception as e:
            get_current_task_log_manager().error_tools(f"批量执行失败: {query[:100]}..., 错误: {e}", "DatabaseManager")
            raise
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
            results = self.execute_query(query, (table_name,))
            return len(results) > 0
        except Exception as e:
            get_current_task_log_manager().error_tools(f"检查表存在性失败: {table_name}, 错误: {e}", "DatabaseManager")
            return False
    
    def get_table_info(self, table_name: str) -> List[sqlite3.Row]:
        """获取表结构信息"""
        try:
            query = f"PRAGMA table_info({table_name})"
            return self.execute_query(query)
        except Exception as e:
            get_current_task_log_manager().error_tools(f"获取表信息失败: {table_name}, 错误: {e}", "DatabaseManager")
            raise
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            info = {
                'database_path': self.config.database_path,
                'path': self.config.database_path,  # 添加path字段以兼容前端
                'database_size': 0,
                'table_count': 0,
                'tables': [],
                'version': None
            }
            
            # 获取数据库文件大小
            if os.path.exists(self.config.database_path):
                info['database_size'] = os.path.getsize(self.config.database_path)
            
            # 获取SQLite版本
            try:
                version_result = self.execute_query("SELECT sqlite_version()")
                if version_result:
                    info['version'] = version_result[0]['sqlite_version()']
            except Exception as e:
                get_current_task_log_manager().warning_tools(f"获取SQLite版本失败: {e}", "DatabaseManager")
            
            # 获取表信息
            tables = self.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
            info['table_count'] = len(tables)
            info['tables'] = [table['name'] for table in tables]
            
            return info
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"获取数据库信息失败: {e}", "DatabaseManager")
            raise
    
    def health_check(self) -> Dict[str, Any]:
        """数据库健康检查"""
        try:
            start_time = time.time()
            
            # 执行简单查询测试连接
            self.execute_query("SELECT 1")
            
            response_time = time.time() - start_time
            
            # 获取数据库信息
            db_info = self.get_database_info()
            
            health_info = {
                'status': 'healthy',
                'response_time': round(response_time, 4),
                'connection_pool_size': len(self.pool._connections),
                'used_connections': len(self.pool._used_connections),
                'database_info': db_info,
                'timestamp': time.time()
            }
            
            get_current_task_log_manager().info_tools("数据库健康检查通过", "DatabaseManager")
            return health_info
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"数据库健康检查失败: {e}", "DatabaseManager")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }
    
    def close(self):
        """关闭数据库管理器"""
        self.pool.close_all_connections()
        get_current_task_log_manager().info_tools("数据库管理器已关闭", "DatabaseManager")


# 全局数据库管理器实例
_db_manager = None
_db_manager_lock = threading.Lock()


def get_database_manager(config: Optional[DatabaseConfig] = None) -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _db_manager
    
    if _db_manager is None:
        with _db_manager_lock:
            if _db_manager is None:
                _db_manager = DatabaseManager(config)
    
    return _db_manager


def init_database_manager(config: Optional[DatabaseConfig] = None) -> DatabaseManager:
    """初始化数据库管理器"""
    global _db_manager
    
    with _db_manager_lock:
        _db_manager = DatabaseManager(config)
        get_current_task_log_manager().info_tools("数据库管理器重新初始化", "init_database_manager")
        
    return _db_manager


# 便捷函数
def execute_query(query: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
    """执行查询的便捷函数"""
    return get_database_manager().execute_query(query, params)


def execute_update(query: str, params: Optional[Tuple] = None) -> int:
    """执行更新的便捷函数"""
    return get_database_manager().execute_update(query, params)


def execute_batch(query: str, params_list: List[Tuple]) -> int:
    """执行批量操作的便捷函数"""
    return get_database_manager().execute_batch(query, params_list)


def get_connection():
    """获取数据库连接的便捷函数"""
    return get_database_manager().get_connection()


def get_transaction():
    """获取数据库事务的便捷函数"""
    return get_database_manager().get_transaction()


if __name__ == "__main__":
    """测试数据库连接模块"""
    
    print("🧪 测试数据库连接模块...")
    
    try:
        # 创建测试配置
        test_config = DatabaseConfig(
            database_path='simplify_agent/data/test_connection.db',
            max_connections=3
        )
        
        # 初始化数据库管理器
        db_manager = init_database_manager(test_config)
        
        # 健康检查
        print("\n📊 数据库健康检查:")
        health = db_manager.health_check()
        print(f"状态: {health['status']}")
        print(f"响应时间: {health['response_time']}秒")
        
        # 测试连接池
        print("\n🔗 测试连接池:")
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]
            print(f"SQLite 版本: {version}")
            cursor.close()
        
        # 测试事务
        print("\n💼 测试事务:")
        with get_transaction() as conn:
            cursor = conn.cursor()
            cursor.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)")
            cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("测试数据",))
            cursor.close()
            print("事务测试成功")
        
        # 查询测试数据
        results = execute_query("SELECT * FROM test_table")
        print(f"查询结果: {len(results)} 行")
        
        # 清理测试数据
        execute_update("DROP TABLE IF EXISTS test_table")
        print("测试数据清理完成")
        
        print("\n✅ 数据库连接模块测试通过!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        
    finally:
        # 关闭数据库连接
        if _db_manager:
            _db_manager.close()