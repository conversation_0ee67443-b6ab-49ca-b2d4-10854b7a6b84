#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据访问对象 (DAO) 包
提供数据库操作的统一接口和基础实现
"""

from .base_dao import BaseDAO
from .test_plan_dao import TestPlanDAO
from .test_execution_dao import TestExecutionDAO
from .tool_execution_dao import ToolExecutionDAO
from .evaluation_dao import EvaluationDAO, PlanEvaluationDAO, ExecutionEvaluationDAO, ComprehensiveEvaluationDAO

__all__ = [
    'BaseDAO',
    'TestPlanDAO',
    'TestExecutionDAO', 
    'ToolExecutionDAO',
    'EvaluationDAO',
    'PlanEvaluationDAO',
    'ExecutionEvaluationDAO',
    'ComprehensiveEvaluationDAO'
]

__version__ = '1.0.0'
__author__ = 'SimplifyAgent Team'