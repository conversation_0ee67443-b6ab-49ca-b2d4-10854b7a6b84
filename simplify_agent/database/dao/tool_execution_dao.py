#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具执行数据访问对象 (Tool Execution DAO)
提供工具执行详情表的专门数据操作方法
"""

import sys
import os
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
import json

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from simplify_agent.database.dao.base_dao import BaseDAO


class ToolExecutionDAO(BaseDAO):
    """工具执行数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "tool_executions"
    
    @property
    def primary_key(self) -> str:
        return "id"
    
    def create_tool_execution(self, tool_data: Dict[str, Any]) -> int:
        """创建工具执行记录
        
        Args:
            tool_data: 工具执行数据，包含以下字段：
                - execution_id: 测试执行ID（必需）
                - round_number: 轮次号（必需）
                - tool_name: 工具名称（必需）
                - tool_parameters: 工具参数（可选，字典或JSON字符串）
                - execution_time: 执行耗时（可选）
                - tool_status: 工具状态（可选）
                - tool_result: 工具结果（可选，字典或JSON字符串）
                - result_summary: 结果摘要（可选）
                - image_url: 图片URL（可选）
                - local_path: 本地文件路径（可选）
                - error_message: 错误信息（可选）
                
        Returns:
            新创建的工具执行记录ID
        """
        try:
            # 验证必需字段
            required_fields = ['execution_id', 'round_number', 'tool_name']
            for field in required_fields:
                if field not in tool_data or tool_data[field] is None:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 验证数据类型
            if not isinstance(tool_data['round_number'], int) or tool_data['round_number'] < 1:
                raise ValueError("round_number必须为正整数")
            
            # 添加时间戳
            tool_data['created_at'] = datetime.now().isoformat()
            
            tool_execution_id = self.insert(tool_data)
            
            self.logger.info_tools(
                f"成功创建工具执行记录: {tool_data['tool_name']} (轮次 {tool_data['round_number']}), 数据库ID: {tool_execution_id}",
                "ToolExecutionDAO"
            )
            
            return tool_execution_id
            
        except Exception as e:
            self.logger.error_tools(f"创建工具执行记录失败: {e}", "ToolExecutionDAO")
            raise
    
    def get_tools_by_execution_id(self, execution_id: str, order_by_round: bool = True) -> List[Dict[str, Any]]:
        """根据执行ID获取所有工具执行记录
        
        Args:
            execution_id: 执行ID
            order_by_round: 是否按轮次排序
            
        Returns:
            工具执行记录列表
        """
        order_clause = 'round_number ASC, created_at ASC' if order_by_round else 'created_at ASC'
        
        return self.find_all(
            conditions={'execution_id': execution_id},
            order_by=order_clause
        )
    
    def get_tools_by_round(self, execution_id: str, round_number: int) -> List[Dict[str, Any]]:
        """获取特定轮次的所有工具执行记录
        
        Args:
            execution_id: 执行ID
            round_number: 轮次号
            
        Returns:
            该轮次的工具执行记录列表
        """
        return self.find_all(
            conditions={
                'execution_id': execution_id,
                'round_number': round_number
            },
            order_by='created_at ASC'
        )
    
    def get_failed_tool_executions(self, execution_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取失败的工具执行记录
        
        Args:
            execution_id: 执行ID（可选，如果不提供则查询所有）
            
        Returns:
            失败的工具执行记录列表
        """
        conditions = {}
        if execution_id:
            conditions['execution_id'] = execution_id
        
        # 查询有错误信息或状态为失败的记录
        try:
            base_query = """
            SELECT * FROM tool_executions WHERE 
            (error_message IS NOT NULL AND error_message != '') 
            OR tool_status = 'error'
            """
            params = []
            
            if execution_id:
                base_query += " AND execution_id = ?"
                params.append(execution_id)
            
            base_query += " ORDER BY created_at DESC"
            
            results = self.db_manager.execute_query(base_query, tuple(params) if params else None)
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取失败工具执行记录失败: {e}", "ToolExecutionDAO")
            raise
    
    def get_tool_performance_stats(self) -> List[Dict[str, Any]]:
        """获取工具性能统计
        
        Returns:
            工具性能统计列表，每个元素包含工具名称、执行次数、平均耗时等
        """
        try:
            query = """
            SELECT 
                tool_name,
                COUNT(*) as execution_count,
                AVG(execution_time) as avg_execution_time,
                MIN(execution_time) as min_execution_time,
                MAX(execution_time) as max_execution_time,
                SUM(CASE WHEN error_message IS NOT NULL AND error_message != '' THEN 1 ELSE 0 END) as error_count,
                ROUND(100.0 * (COUNT(*) - SUM(CASE WHEN error_message IS NOT NULL AND error_message != '' THEN 1 ELSE 0 END)) / COUNT(*), 2) as success_rate
            FROM tool_executions 
            WHERE execution_time IS NOT NULL
            GROUP BY tool_name
            ORDER BY execution_count DESC
            """
            
            results = self.db_manager.execute_query(query)
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取工具性能统计失败: {e}", "ToolExecutionDAO")
            raise
    
    def get_tool_usage_trends(self, days: int = 7) -> List[Dict[str, Any]]:
        """获取工具使用趋势
        
        Args:
            days: 统计最近天数
            
        Returns:
            工具使用趋势列表
        """
        try:
            query = f"""
            SELECT 
                tool_name,
                DATE(created_at) as usage_date,
                COUNT(*) as usage_count,
                AVG(execution_time) as avg_execution_time
            FROM tool_executions 
            WHERE created_at >= datetime('now', '-{days} days')
            GROUP BY tool_name, DATE(created_at)
            ORDER BY usage_date DESC, usage_count DESC
            """
            
            results = self.db_manager.execute_query(query)
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取工具使用趋势失败: {e}", "ToolExecutionDAO")
            raise
    
    def get_execution_summary(self, execution_id: str) -> Dict[str, Any]:
        """获取执行的工具使用总结
        
        Args:
            execution_id: 执行ID
            
        Returns:
            执行总结字典，包含工具统计、轮次信息等
        """
        try:
            # 基本统计
            basic_query = """
            SELECT 
                COUNT(*) as total_tools,
                COUNT(DISTINCT tool_name) as unique_tools,
                COUNT(DISTINCT round_number) as total_rounds,
                AVG(execution_time) as avg_execution_time,
                SUM(CASE WHEN error_message IS NOT NULL AND error_message != '' THEN 1 ELSE 0 END) as error_count
            FROM tool_executions 
            WHERE execution_id = ?
            """
            
            basic_results = self.db_manager.execute_query(basic_query, (execution_id,))
            basic_stats = dict(basic_results[0]) if basic_results else {}
            
            # 工具使用统计
            tools_query = """
            SELECT 
                tool_name,
                COUNT(*) as usage_count,
                AVG(execution_time) as avg_time
            FROM tool_executions 
            WHERE execution_id = ?
            GROUP BY tool_name
            ORDER BY usage_count DESC
            """
            
            tools_results = self.db_manager.execute_query(tools_query, (execution_id,))
            tools_stats = [dict(row) for row in tools_results]
            
            # 轮次统计
            rounds_query = """
            SELECT 
                round_number,
                COUNT(*) as tools_in_round,
                GROUP_CONCAT(tool_name) as tools_used
            FROM tool_executions 
            WHERE execution_id = ?
            GROUP BY round_number
            ORDER BY round_number
            """
            
            rounds_results = self.db_manager.execute_query(rounds_query, (execution_id,))
            rounds_stats = [dict(row) for row in rounds_results]
            
            return {
                'execution_id': execution_id,
                'basic_statistics': basic_stats,
                'tool_statistics': tools_stats,
                'round_statistics': rounds_stats
            }
            
        except Exception as e:
            self.logger.error_tools(f"获取执行总结失败: {e}", "ToolExecutionDAO")
            raise
    
    def update_tool_result(self, tool_execution_id: int, 
                          tool_result: Any, 
                          result_summary: Optional[str] = None,
                          execution_time: Optional[float] = None,
                          tool_status: Optional[str] = None) -> bool:
        """更新工具执行结果
        
        Args:
            tool_execution_id: 工具执行记录ID
            tool_result: 工具执行结果
            result_summary: 结果摘要
            execution_time: 执行耗时
            tool_status: 工具状态
            
        Returns:
            是否更新成功
        """
        try:
            updates = {}
            
            if tool_result is not None:
                updates['tool_result'] = tool_result
            
            if result_summary is not None:
                updates['result_summary'] = result_summary
            
            if execution_time is not None:
                updates['execution_time'] = execution_time
                
            if tool_status is not None:
                updates['tool_status'] = tool_status
            
            if not updates:
                return False
            
            result = self.update(updates, {'id': tool_execution_id})
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"更新工具执行结果失败: {e}", "ToolExecutionDAO")
            raise
    
    def mark_tool_error(self, tool_execution_id: int, error_message: str) -> bool:
        """标记工具执行错误
        
        Args:
            tool_execution_id: 工具执行记录ID
            error_message: 错误信息
            
        Returns:
            是否标记成功
        """
        try:
            result = self.update(
                {'error_message': error_message, 'tool_status': 'error'},
                {'id': tool_execution_id}
            )
            
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"标记工具执行错误失败: {e}", "ToolExecutionDAO")
            raise
    
    def get_tools_with_images(self, execution_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取包含图片的工具执行记录
        
        Args:
            execution_id: 执行ID（可选）
            
        Returns:
            包含图片的工具执行记录列表
        """
        try:
            query = """
            SELECT * FROM tool_executions 
            WHERE (image_url IS NOT NULL AND image_url != '') 
               OR (local_path IS NOT NULL AND local_path != '')
            """
            params = []
            
            if execution_id:
                query += " AND execution_id = ?"
                params.append(execution_id)
            
            query += " ORDER BY created_at DESC"
            
            results = self.db_manager.execute_query(query, tuple(params) if params else None)
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取包含图片的工具执行记录失败: {e}", "ToolExecutionDAO")
            raise
    
    def batch_create_tool_executions(self, tools_data: List[Dict[str, Any]]) -> List[int]:
        """批量创建工具执行记录
        
        Args:
            tools_data: 工具执行数据列表
            
        Returns:
            创建成功的记录ID列表
        """
        try:
            # 验证所有数据
            for i, tool_data in enumerate(tools_data):
                required_fields = ['execution_id', 'round_number', 'tool_name']
                for field in required_fields:
                    if field not in tool_data or tool_data[field] is None:
                        raise ValueError(f"第{i+1}个工具记录缺少必需字段: {field}")
                
                if not isinstance(tool_data['round_number'], int) or tool_data['round_number'] < 1:
                    raise ValueError(f"第{i+1}个工具记录的round_number必须为正整数")
                
                # 添加时间戳
                tool_data['created_at'] = datetime.now().isoformat()
            
            # 批量插入
            self.insert_batch(tools_data)
            
            self.logger.info_tools(
                f"成功批量创建 {len(tools_data)} 个工具执行记录",
                "ToolExecutionDAO"
            )
            
            # 返回成功创建的数量（简化实现，实际应该返回具体ID列表）
            return list(range(len(tools_data)))
            
        except Exception as e:
            self.logger.error_tools(f"批量创建工具执行记录失败: {e}", "ToolExecutionDAO")
            raise
    
    def delete_tools_by_execution_id(self, execution_id: str) -> int:
        """删除指定执行的所有工具记录
        
        Args:
            execution_id: 执行ID
            
        Returns:
            删除的记录数
        """
        try:
            result = self.delete({'execution_id': execution_id})
            
            if result > 0:
                self.logger.info_tools(f"成功删除执行 {execution_id} 的 {result} 个工具记录", "ToolExecutionDAO")
            
            return result
            
        except Exception as e:
            self.logger.error_tools(f"删除工具执行记录失败: {e}", "ToolExecutionDAO")
            raise


if __name__ == "__main__":
    """测试工具执行DAO功能"""
    
    print("🧪 测试工具执行DAO功能...")
    
    try:
        # 初始化数据库
        from simplify_agent.database.init_database import DatabaseInitializer
        
        initializer = DatabaseInitializer()
        initializer.initialize_database()
        
        # 创建DAO实例
        dao = ToolExecutionDAO()
        
        # 清理测试数据
        dao.delete({})  # 删除所有记录进行干净测试
        
        print("✅ 工具执行DAO创建成功")
        
        # 测试创建工具执行记录
        test_tool_data = {
            'execution_id': 'exec_001_test',
            'round_number': 1,
            'tool_name': 'check_page_detail',
            'tool_parameters': {
                'udid': 'device123',
                'action': 'screenshot'
            },
            'execution_time': 2.5,
            'tool_status': 'success',
            'tool_result': {
                'status': 'success',
                'image_url': 'http://example.com/screenshot.png',
                'text_result': '页面检查完成'
            },
            'result_summary': '成功获取页面截图',
            'image_url': 'http://example.com/screenshot.png',
            'local_path': '/tmp/screenshot_001.png'
        }
        
        tool_id = dao.create_tool_execution(test_tool_data)
        print(f"✅ 成功创建工具执行记录，ID: {tool_id}")
        
        # 测试根据执行ID查询工具
        tools = dao.get_tools_by_execution_id('exec_001_test')
        assert len(tools) > 0, "应该能找到刚创建的工具记录"
        assert tools[0]['tool_name'] == 'check_page_detail', "工具名称应该匹配"
        print("✅ 根据执行ID查询测试通过")
        
        # 测试根据轮次查询
        round_tools = dao.get_tools_by_round('exec_001_test', 1)
        assert len(round_tools) > 0, "应该能找到第1轮的工具记录"
        print("✅ 根据轮次查询测试通过")
        
        # 创建更多测试数据
        more_tools = [
            {
                'execution_id': 'exec_001_test',
                'round_number': 2,
                'tool_name': 'click_element',
                'execution_time': 1.2,
                'tool_status': 'success'
            },
            {
                'execution_id': 'exec_001_test', 
                'round_number': 2,
                'tool_name': 'wait_for_element',
                'execution_time': 3.0,
                'tool_status': 'error',
                'error_message': '元素未找到'
            },
            {
                'execution_id': 'exec_002_test',
                'round_number': 1,
                'tool_name': 'scroll_page',
                'execution_time': 0.8,
                'tool_status': 'success'
            }
        ]
        
        batch_result = dao.batch_create_tool_executions(more_tools)
        print(f"✅ 批量创建测试通过，创建了 {len(batch_result)} 个工具记录")
        
        # 测试失败工具查询
        failed_tools = dao.get_failed_tool_executions('exec_001_test')
        assert len(failed_tools) > 0, "应该能找到失败的工具记录"
        print(f"✅ 失败工具查询测试通过，找到 {len(failed_tools)} 个失败工具")
        
        # 测试工具性能统计
        perf_stats = dao.get_tool_performance_stats()
        assert len(perf_stats) > 0, "应该有工具性能统计"
        print(f"✅ 工具性能统计测试通过，统计了 {len(perf_stats)} 个工具")
        
        # 测试执行总结
        summary = dao.get_execution_summary('exec_001_test')
        assert 'basic_statistics' in summary, "总结应该包含基本统计"
        assert 'tool_statistics' in summary, "总结应该包含工具统计"
        assert 'round_statistics' in summary, "总结应该包含轮次统计"
        print("✅ 执行总结测试通过")
        
        # 测试更新工具结果
        update_success = dao.update_tool_result(
            tool_id,
            {'status': 'updated', 'new_field': 'test'},
            result_summary='更新后的结果摘要',
            execution_time=3.0
        )
        assert update_success, "结果更新应该成功"
        print("✅ 工具结果更新测试通过")
        
        # 测试包含图片的工具查询
        tools_with_images = dao.get_tools_with_images()
        assert len(tools_with_images) > 0, "应该有包含图片的工具记录"
        print(f"✅ 包含图片的工具查询测试通过，找到 {len(tools_with_images)} 个记录")
        
        # 最终统计
        total_count = dao.count()
        print(f"✅ 当前总工具执行记录数: {total_count}")
        
        print("\n✅ 工具执行DAO功能测试全部通过!")
        
    except Exception as e:
        print(f"\n❌ 工具执行DAO功能测试失败: {e}")
        import traceback
        traceback.print_exc()