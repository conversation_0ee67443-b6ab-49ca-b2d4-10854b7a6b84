#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础数据访问对象 (Base DAO)
为所有具体的DAO提供通用的数据库操作方法
"""

import sqlite3
import json
import sys
import os
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any, Tuple, Union
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from tools._concurrent_log_manager import get_current_task_log_manager
from simplify_agent.database.db_connection import get_database_manager, DatabaseManager


class BaseDAO(ABC):
    """基础DAO抽象类，提供通用数据库操作方法"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """初始化DAO
        
        Args:
            db_manager: 数据库管理器实例，如果为None则使用全局实例
        """
        self.db_manager = db_manager or get_database_manager()
        self.logger = get_current_task_log_manager()
        
    @property
    @abstractmethod
    def table_name(self) -> str:
        """返回当前DAO对应的表名"""
        pass
    
    @property
    @abstractmethod
    def primary_key(self) -> str:
        """返回主键字段名"""
        pass
    
    def _dict_to_json(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将字典中的复杂对象转换为JSON字符串
        
        Args:
            data: 原始数据字典
            
        Returns:
            转换后的数据字典
        """
        result = {}
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                result[key] = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result
    
    def _json_to_dict(self, row: sqlite3.Row) -> Dict[str, Any]:
        """将数据库行中的JSON字符串转换回对象
        
        Args:
            row: 数据库行对象
            
        Returns:
            转换后的字典
        """
        result = {}
        for key in row.keys():
            value = row[key]
            if isinstance(value, str):
                # 尝试解析JSON字符串
                try:
                    if value.startswith('{') or value.startswith('['):
                        result[key] = json.loads(value)
                    else:
                        result[key] = value
                except (json.JSONDecodeError, TypeError):
                    result[key] = value
            else:
                result[key] = value
        return result
    
    def _build_where_clause(self, conditions: Dict[str, Any]) -> Tuple[str, Tuple]:
        """构建WHERE子句
        
        Args:
            conditions: 查询条件字典
            
        Returns:
            WHERE子句字符串和参数元组
        """
        if not conditions:
            return "", ()
        
        where_parts = []
        params = []
        
        for key, value in conditions.items():
            if isinstance(value, (list, tuple)):
                placeholders = ','.join(['?' for _ in value])
                where_parts.append(f"{key} IN ({placeholders})")
                params.extend(value)
            elif value is None:
                where_parts.append(f"{key} IS NULL")
            else:
                where_parts.append(f"{key} = ?")
                params.append(value)
        
        where_clause = " AND ".join(where_parts)
        return f"WHERE {where_clause}", tuple(params)
    
    def _build_insert_query(self, data: Dict[str, Any]) -> Tuple[str, Tuple]:
        """构建INSERT查询
        
        Args:
            data: 要插入的数据
            
        Returns:
            INSERT查询字符串和参数元组
        """
        data = self._dict_to_json(data)
        columns = list(data.keys())
        placeholders = ','.join(['?' for _ in columns])
        
        query = f"INSERT INTO {self.table_name} ({','.join(columns)}) VALUES ({placeholders})"
        params = tuple(data.values())
        
        return query, params
    
    def _build_update_query(self, data: Dict[str, Any], conditions: Dict[str, Any]) -> Tuple[str, Tuple]:
        """构建UPDATE查询
        
        Args:
            data: 要更新的数据
            conditions: 更新条件
            
        Returns:
            UPDATE查询字符串和参数元组
        """
        data = self._dict_to_json(data)
        
        # 检查表是否有updated_at字段，如果有则添加
        try:
            table_info = self.get_table_info()
            column_names = [col['name'] for col in table_info]
            if 'updated_at' in column_names and 'updated_at' not in data:
                data['updated_at'] = datetime.now().isoformat()
        except:
            # 如果无法获取表信息，跳过updated_at字段
            pass
        
        set_parts = [f"{key} = ?" for key in data.keys()]
        set_clause = ", ".join(set_parts)
        
        where_clause, where_params = self._build_where_clause(conditions)
        
        query = f"UPDATE {self.table_name} SET {set_clause} {where_clause}"
        params = tuple(data.values()) + where_params
        
        return query, params
    
    def insert(self, data: Dict[str, Any]) -> int:
        """插入单条记录
        
        Args:
            data: 要插入的数据
            
        Returns:
            插入记录的ID
        """
        try:
            query, params = self._build_insert_query(data)
            result = self.db_manager.execute_update(query, params)
            
            self.logger.info_tools(
                f"成功插入 {self.table_name} 记录，ID: {result}", 
                f"{self.__class__.__name__}"
            )
            return result
            
        except Exception as e:
            self.logger.error_tools(
                f"插入 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def insert_batch(self, data_list: List[Dict[str, Any]]) -> int:
        """批量插入记录
        
        Args:
            data_list: 要插入的数据列表
            
        Returns:
            受影响的行数
        """
        if not data_list:
            return 0
        
        try:
            # 使用第一条记录的结构作为模板
            first_data = self._dict_to_json(data_list[0])
            columns = list(first_data.keys())
            placeholders = ','.join(['?' for _ in columns])
            
            query = f"INSERT INTO {self.table_name} ({','.join(columns)}) VALUES ({placeholders})"
            
            # 准备所有记录的参数
            params_list = []
            for data in data_list:
                converted_data = self._dict_to_json(data)
                # 确保所有记录都有相同的字段
                params = tuple(converted_data.get(col) for col in columns)
                params_list.append(params)
            
            result = self.db_manager.execute_batch(query, params_list)
            
            self.logger.info_tools(
                f"成功批量插入 {len(data_list)} 条 {self.table_name} 记录", 
                f"{self.__class__.__name__}"
            )
            return result
            
        except Exception as e:
            self.logger.error_tools(
                f"批量插入 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def update(self, data: Dict[str, Any], conditions: Dict[str, Any]) -> int:
        """更新记录
        
        Args:
            data: 要更新的数据
            conditions: 更新条件
            
        Returns:
            受影响的行数
        """
        try:
            query, params = self._build_update_query(data, conditions)
            result = self.db_manager.execute_update(query, params)
            
            self.logger.info_tools(
                f"成功更新 {result} 条 {self.table_name} 记录", 
                f"{self.__class__.__name__}"
            )
            return result
            
        except Exception as e:
            self.logger.error_tools(
                f"更新 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def delete(self, conditions: Dict[str, Any]) -> int:
        """删除记录
        
        Args:
            conditions: 删除条件
            
        Returns:
            受影响的行数
        """
        try:
            where_clause, params = self._build_where_clause(conditions)
            query = f"DELETE FROM {self.table_name} {where_clause}"
            
            result = self.db_manager.execute_update(query, params)
            
            self.logger.info_tools(
                f"成功删除 {result} 条 {self.table_name} 记录", 
                f"{self.__class__.__name__}"
            )
            return result
            
        except Exception as e:
            self.logger.error_tools(
                f"删除 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def find_by_id(self, record_id: Union[int, str]) -> Optional[Dict[str, Any]]:
        """根据ID查找单条记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            记录字典或None
        """
        try:
            query = f"SELECT * FROM {self.table_name} WHERE {self.primary_key} = ?"
            results = self.db_manager.execute_query(query, (record_id,))
            
            if results:
                return self._json_to_dict(results[0])
            return None
            
        except Exception as e:
            self.logger.error_tools(
                f"根据ID查找 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def find_all(self, conditions: Optional[Dict[str, Any]] = None, 
                 order_by: Optional[str] = None, 
                 limit: Optional[int] = None,
                 offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """查找多条记录
        
        Args:
            conditions: 查询条件
            order_by: 排序字段
            limit: 限制条数
            offset: 偏移量
            
        Returns:
            记录列表
        """
        try:
            query = f"SELECT * FROM {self.table_name}"
            params = ()
            
            # 添加WHERE子句
            if conditions:
                where_clause, params = self._build_where_clause(conditions)
                query += f" {where_clause}"
            
            # 添加ORDER BY子句
            if order_by:
                query += f" ORDER BY {order_by}"
            
            # 添加LIMIT和OFFSET子句
            if limit is not None:
                query += f" LIMIT {limit}"
                if offset is not None:
                    query += f" OFFSET {offset}"
            
            results = self.db_manager.execute_query(query, params)
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(
                f"查找 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def count(self, conditions: Optional[Dict[str, Any]] = None) -> int:
        """统计记录数量
        
        Args:
            conditions: 查询条件
            
        Returns:
            记录数量
        """
        try:
            query = f"SELECT COUNT(*) as count FROM {self.table_name}"
            params = ()
            
            if conditions:
                where_clause, params = self._build_where_clause(conditions)
                query += f" {where_clause}"
            
            results = self.db_manager.execute_query(query, params)
            return results[0]['count'] if results else 0
            
        except Exception as e:
            self.logger.error_tools(
                f"统计 {self.table_name} 记录失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise
    
    def exists(self, conditions: Dict[str, Any]) -> bool:
        """检查记录是否存在
        
        Args:
            conditions: 查询条件
            
        Returns:
            是否存在
        """
        return self.count(conditions) > 0
    
    def find_one(self, conditions: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """查找单条记录
        
        Args:
            conditions: 查询条件
            
        Returns:
            记录字典或None
        """
        results = self.find_all(conditions, limit=1)
        return results[0] if results else None
    
    def get_table_info(self) -> List[Dict[str, Any]]:
        """获取表结构信息
        
        Returns:
            表结构信息列表
        """
        try:
            info = self.db_manager.get_table_info(self.table_name)
            return [dict(row) for row in info]
            
        except Exception as e:
            self.logger.error_tools(
                f"获取 {self.table_name} 表信息失败: {e}", 
                f"{self.__class__.__name__}"
            )
            raise


if __name__ == "__main__":
    """测试基础DAO功能"""
    
    print("🧪 测试基础DAO功能...")
    
    # 创建一个测试DAO类
    class TestDAO(BaseDAO):
        @property
        def table_name(self) -> str:
            return "test_plans"
        
        @property
        def primary_key(self) -> str:
            return "id"
    
    try:
        # 初始化数据库（确保表存在）
        from simplify_agent.database.init_database import DatabaseInitializer
        
        initializer = DatabaseInitializer()
        initializer.initialize_database()
        
        # 创建测试DAO实例
        dao = TestDAO()
        
        print("✅ 基础DAO类创建成功")
        print(f"表名: {dao.table_name}")
        print(f"主键: {dao.primary_key}")
        
        # 测试查询功能
        count = dao.count()
        print(f"当前 {dao.table_name} 表记录数: {count}")
        
        # 测试表信息获取
        table_info = dao.get_table_info()
        print(f"表结构字段数: {len(table_info)}")
        
        print("\n✅ 基础DAO功能测试通过!")
        
    except Exception as e:
        print(f"\n❌ 基础DAO功能测试失败: {e}")
        import traceback
        traceback.print_exc()