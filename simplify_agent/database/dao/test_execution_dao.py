#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试执行数据访问对象 (Test Execution DAO)
提供测试执行表的专门数据操作方法
"""

import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from simplify_agent.database.dao.base_dao import BaseDAO


class TestExecutionDAO(BaseDAO):
    """测试执行数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "test_executions"
    
    @property
    def primary_key(self) -> str:
        return "id"
    
    def create_execution(self, execution_data: Dict[str, Any]) -> int:
        """创建测试执行记录
        
        Args:
            execution_data: 测试执行数据，包含以下字段：
                - execution_id: 执行ID（必需）
                - plan_id: 计划ID（可选，关联测试计划）
                - original_request: 原始用户请求（必需）
                - execution_status: 执行状态（可选，默认为'running'）
                - total_rounds: 总轮数（可选，默认为0）
                - total_duration: 总耗时（可选，默认为0）
                - start_time: 开始时间（可选，默认为当前时间）
                - end_time: 结束时间（可选）
                
        Returns:
            新创建的测试执行记录ID
        """
        try:
            # 验证必需字段
            required_fields = ['execution_id', 'original_request']
            for field in required_fields:
                if field not in execution_data or execution_data[field] is None:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 检查execution_id是否已存在
            if self.exists({'execution_id': execution_data['execution_id']}):
                raise ValueError(f"执行ID {execution_data['execution_id']} 已存在")
            
            # 设置默认值
            if 'execution_status' not in execution_data:
                execution_data['execution_status'] = 'running'
            if 'total_rounds' not in execution_data:
                execution_data['total_rounds'] = 0
            if 'total_duration' not in execution_data:
                execution_data['total_duration'] = 0.0
            if 'start_time' not in execution_data:
                execution_data['start_time'] = datetime.now().isoformat()
            
            # 验证执行状态
            valid_statuses = ['running', 'success', 'failed', 'timeout', 'cancelled']
            if execution_data['execution_status'] not in valid_statuses:
                raise ValueError(f"无效的执行状态: {execution_data['execution_status']}")
            
            # 添加时间戳
            execution_data['created_at'] = datetime.now().isoformat()
            
            execution_id = self.insert(execution_data)
            
            self.logger.info_tools(
                f"成功创建测试执行记录: {execution_data['execution_id']}, 数据库ID: {execution_id}",
                "TestExecutionDAO"
            )
            
            return execution_id
            
        except Exception as e:
            self.logger.error_tools(f"创建测试执行记录失败: {e}", "TestExecutionDAO")
            raise
    
    def get_execution_by_execution_id(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """根据执行ID获取测试执行记录
        
        Args:
            execution_id: 执行ID
            
        Returns:
            测试执行记录字典或None
        """
        return self.find_one({'execution_id': execution_id})
    
    def get_executions_by_plan_id(self, plan_id: str) -> List[Dict[str, Any]]:
        """根据计划ID获取所有执行记录
        
        Args:
            plan_id: 计划ID
            
        Returns:
            执行记录列表
        """
        return self.find_all(
            conditions={'plan_id': plan_id},
            order_by='created_at DESC'
        )
    
    def get_executions_by_status(self, status: str) -> List[Dict[str, Any]]:
        """根据执行状态获取执行记录列表
        
        Args:
            status: 执行状态
            
        Returns:
            执行记录列表
        """
        return self.find_all(
            conditions={'execution_status': status},
            order_by='created_at DESC'
        )
    
    def update_execution_status(self, execution_id: str, status: str, 
                               end_time: Optional[str] = None,
                               total_duration: Optional[float] = None) -> bool:
        """更新执行状态
        
        Args:
            execution_id: 执行ID
            status: 新的执行状态
            end_time: 结束时间（可选）
            total_duration: 总耗时（可选）
            
        Returns:
            是否更新成功
        """
        try:
            valid_statuses = ['running', 'success', 'failed', 'timeout', 'cancelled']
            if status not in valid_statuses:
                raise ValueError(f"无效的执行状态: {status}")
            
            updates = {'execution_status': status}
            
            if end_time:
                updates['end_time'] = end_time
            elif status in ['success', 'failed', 'timeout', 'cancelled']:
                # 如果执行结束但没有指定结束时间，使用当前时间
                updates['end_time'] = datetime.now().isoformat()
            
            if total_duration is not None:
                updates['total_duration'] = total_duration
            
            result = self.update(updates, {'execution_id': execution_id})
            
            if result > 0:
                self.logger.info_tools(
                    f"成功更新执行状态: {execution_id} -> {status}",
                    "TestExecutionDAO"
                )
            
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"更新执行状态失败: {e}", "TestExecutionDAO")
            raise
    
    def update_execution_rounds(self, execution_id: str, total_rounds: int) -> bool:
        """更新执行轮数
        
        Args:
            execution_id: 执行ID
            total_rounds: 总轮数
            
        Returns:
            是否更新成功
        """
        try:
            if total_rounds < 0:
                raise ValueError("执行轮数不能为负数")
            
            result = self.update(
                {'total_rounds': total_rounds},
                {'execution_id': execution_id}
            )
            
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"更新执行轮数失败: {e}", "TestExecutionDAO")
            raise
    
    def get_running_executions(self) -> List[Dict[str, Any]]:
        """获取所有正在运行的执行记录
        
        Returns:
            正在运行的执行记录列表
        """
        return self.get_executions_by_status('running')
    
    def get_completed_executions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取已完成的执行记录
        
        Args:
            limit: 返回数量限制
            
        Returns:
            已完成的执行记录列表
        """
        return self.find_all(
            conditions={'execution_status': ['success', 'failed', 'timeout', 'cancelled']},
            order_by='end_time DESC',
            limit=limit
        )
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息
        
        Returns:
            统计信息字典，包含各状态的执行数量、平均耗时等
        """
        try:
            # 状态统计
            status_query = """
            SELECT 
                execution_status,
                COUNT(*) as count,
                AVG(total_duration) as avg_duration,
                AVG(total_rounds) as avg_rounds
            FROM test_executions 
            GROUP BY execution_status
            """
            
            status_stats = self.db_manager.execute_query(status_query)
            
            # 总体统计
            overall_query = """
            SELECT 
                COUNT(*) as total_executions,
                AVG(total_duration) as avg_duration,
                MAX(total_duration) as max_duration,
                MIN(total_duration) as min_duration,
                AVG(total_rounds) as avg_rounds,
                MAX(total_rounds) as max_rounds
            FROM test_executions
            """
            
            overall_stats = self.db_manager.execute_query(overall_query)
            
            # 最近7天统计
            recent_query = """
            SELECT 
                COUNT(*) as recent_executions,
                SUM(CASE WHEN execution_status = 'success' THEN 1 ELSE 0 END) as recent_success
            FROM test_executions 
            WHERE created_at >= datetime('now', '-7 days')
            """
            
            recent_stats = self.db_manager.execute_query(recent_query)
            
            return {
                'status_distribution': [dict(row) for row in status_stats],
                'overall_statistics': dict(overall_stats[0]) if overall_stats else {},
                'recent_statistics': dict(recent_stats[0]) if recent_stats else {}
            }
            
        except Exception as e:
            self.logger.error_tools(f"获取执行统计失败: {e}", "TestExecutionDAO")
            raise
    
    def get_execution_duration_analysis(self) -> List[Dict[str, Any]]:
        """获取执行耗时分析
        
        Returns:
            耗时分析数据列表
        """
        try:
            query = """
            SELECT 
                CASE 
                    WHEN total_duration < 60 THEN '0-1分钟'
                    WHEN total_duration < 300 THEN '1-5分钟'
                    WHEN total_duration < 600 THEN '5-10分钟'
                    WHEN total_duration < 1800 THEN '10-30分钟'
                    ELSE '30分钟以上'
                END as duration_range,
                COUNT(*) as execution_count,
                AVG(total_rounds) as avg_rounds
            FROM test_executions 
            WHERE total_duration > 0
            GROUP BY duration_range
            ORDER BY 
                CASE duration_range
                    WHEN '0-1分钟' THEN 1
                    WHEN '1-5分钟' THEN 2
                    WHEN '5-10分钟' THEN 3
                    WHEN '10-30分钟' THEN 4
                    ELSE 5
                END
            """
            
            results = self.db_manager.execute_query(query)
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取执行耗时分析失败: {e}", "TestExecutionDAO")
            raise
    
    def get_executions_with_plan_info(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取包含计划信息的执行记录
        
        Args:
            limit: 返回数量限制
            
        Returns:
            包含计划信息的执行记录列表
        """
        try:
            query = """
            SELECT 
                e.*,
                p.platform,
                p.total_steps as plan_steps,
                p.plan_summary
            FROM test_executions e
            LEFT JOIN test_plans p ON e.plan_id = p.plan_id
            ORDER BY e.created_at DESC
            LIMIT ?
            """
            
            results = self.db_manager.execute_query(query, (limit,))
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取包含计划信息的执行记录失败: {e}", "TestExecutionDAO")
            raise
    
    def delete_execution(self, execution_id: str) -> bool:
        """删除测试执行记录
        
        Args:
            execution_id: 执行ID
            
        Returns:
            是否删除成功
        """
        try:
            result = self.delete({'execution_id': execution_id})
            
            if result > 0:
                self.logger.info_tools(f"成功删除测试执行记录: {execution_id}", "TestExecutionDAO")
            
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"删除测试执行记录失败: {e}", "TestExecutionDAO")
            raise
    
    def search_executions_by_request(self, request_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """根据原始请求内容搜索执行记录
        
        Args:
            request_text: 搜索文本
            limit: 返回结果限制
            
        Returns:
            匹配的执行记录列表
        """
        try:
            query = """
            SELECT * FROM test_executions 
            WHERE original_request LIKE ? 
            ORDER BY created_at DESC 
            LIMIT ?
            """
            
            search_pattern = f"%{request_text}%"
            results = self.db_manager.execute_query(query, (search_pattern, limit))
            
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"搜索执行记录失败: {e}", "TestExecutionDAO")
            raise


if __name__ == "__main__":
    """测试测试执行DAO功能"""
    
    print("🧪 测试测试执行DAO功能...")
    
    try:
        # 初始化数据库
        from simplify_agent.database.init_database import DatabaseInitializer
        
        initializer = DatabaseInitializer()
        initializer.initialize_database()
        
        # 创建DAO实例
        dao = TestExecutionDAO()
        
        # 清理测试数据
        dao.delete({})  # 删除所有记录进行干净测试
        
        print("✅ 测试执行DAO创建成功")
        
        # 测试创建测试执行记录
        test_execution_data = {
            'execution_id': 'exec_001_20250903',
            'plan_id': 'test_plan_001',
            'original_request': '自动化测试登录功能',
            'execution_status': 'running',
            'total_rounds': 0,
            'total_duration': 0.0
        }
        
        exec_id = dao.create_execution(test_execution_data)
        print(f"✅ 成功创建测试执行记录，ID: {exec_id}")
        
        # 测试根据execution_id查询
        found_execution = dao.get_execution_by_execution_id('exec_001_20250903')
        assert found_execution is not None, "应该能找到刚创建的执行记录"
        assert found_execution['execution_status'] == 'running', "执行状态应该为running"
        print("✅ 根据execution_id查询测试通过")
        
        # 测试更新执行状态
        update_success = dao.update_execution_status(
            'exec_001_20250903', 
            'success',
            total_duration=120.5
        )
        assert update_success, "状态更新应该成功"
        
        updated_execution = dao.get_execution_by_execution_id('exec_001_20250903')
        assert updated_execution['execution_status'] == 'success', "状态应该已更新为success"
        assert updated_execution['total_duration'] == 120.5, "耗时应该已更新"
        print("✅ 状态更新测试通过")
        
        # 测试更新轮数
        rounds_update_success = dao.update_execution_rounds('exec_001_20250903', 5)
        assert rounds_update_success, "轮数更新应该成功"
        
        updated_execution = dao.get_execution_by_execution_id('exec_001_20250903')
        assert updated_execution['total_rounds'] == 5, "轮数应该已更新为5"
        print("✅ 轮数更新测试通过")
        
        # 创建更多测试数据用于统计测试
        more_executions = [
            {
                'execution_id': f'exec_{i:03d}_test',
                'original_request': f'测试任务 {i}',
                'execution_status': 'success' if i % 2 == 0 else 'failed',
                'total_rounds': i,
                'total_duration': i * 30.0
            }
            for i in range(2, 6)
        ]
        
        for exec_data in more_executions:
            dao.create_execution(exec_data)
        
        print("✅ 创建额外测试数据成功")
        
        # 测试状态查询
        success_executions = dao.get_executions_by_status('success')
        assert len(success_executions) > 0, "应该有成功的执行记录"
        print(f"✅ 状态查询测试通过，找到 {len(success_executions)} 个成功执行")
        
        # 测试统计功能
        stats = dao.get_execution_statistics()
        assert 'status_distribution' in stats, "统计应该包含状态分布"
        assert 'overall_statistics' in stats, "统计应该包含总体信息"
        print("✅ 执行统计测试通过")
        
        # 测试耗时分析
        duration_analysis = dao.get_execution_duration_analysis()
        print(f"✅ 耗时分析测试通过，分析了 {len(duration_analysis)} 个时间段")
        
        # 测试搜索功能
        search_results = dao.search_executions_by_request('测试')
        assert len(search_results) > 0, "应该能搜索到包含'测试'的执行记录"
        print(f"✅ 搜索功能测试通过，找到 {len(search_results)} 个匹配记录")
        
        # 最终统计
        total_count = dao.count()
        print(f"✅ 当前总执行记录数: {total_count}")
        
        print("\n✅ 测试执行DAO功能测试全部通过!")
        
    except Exception as e:
        print(f"\n❌ 测试执行DAO功能测试失败: {e}")
        import traceback
        traceback.print_exc()