#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试计划数据访问对象 (Test Plan DAO)
提供测试计划表的专门数据操作方法
"""

import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from simplify_agent.database.dao.base_dao import BaseDAO


class TestPlanDAO(BaseDAO):
    """测试计划数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "test_plans"
    
    @property  
    def primary_key(self) -> str:
        return "id"
    
    def create_test_plan(self, plan_data: Dict[str, Any]) -> int:
        """创建测试计划
        
        Args:
            plan_data: 测试计划数据，包含以下字段：
                - plan_id: 计划ID
                - original_request: 原始用户请求
                - platform: 测试平台
                - total_steps: 总步骤数
                - plan_summary: 计划摘要（可选）
                - structured_plan: 结构化计划（字典或JSON字符串）
                - generation_metadata: 生成元数据（可选）
                - agent_instructions: Agent指令（可选）
                
        Returns:
            新创建的测试计划记录ID
        """
        try:
            # 验证必需字段
            required_fields = ['plan_id', 'original_request', 'platform', 'total_steps', 'structured_plan']
            for field in required_fields:
                if field not in plan_data or plan_data[field] is None:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 检查plan_id是否已存在
            if self.exists({'plan_id': plan_data['plan_id']}):
                raise ValueError(f"计划ID {plan_data['plan_id']} 已存在")
            
            # 添加时间戳
            plan_data['created_at'] = datetime.now().isoformat()
            plan_data['updated_at'] = datetime.now().isoformat()
            
            plan_id = self.insert(plan_data)
            
            self.logger.info_tools(
                f"成功创建测试计划: {plan_data['plan_id']}, 数据库ID: {plan_id}",
                "TestPlanDAO"
            )
            
            return plan_id
            
        except Exception as e:
            self.logger.error_tools(f"创建测试计划失败: {e}", "TestPlanDAO")
            raise
    
    def get_plan_by_plan_id(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """根据计划ID获取测试计划
        
        Args:
            plan_id: 计划ID
            
        Returns:
            测试计划字典或None
        """
        return self.find_one({'plan_id': plan_id})
    
    def get_plans_by_platform(self, platform: str) -> List[Dict[str, Any]]:
        """根据平台获取测试计划列表
        
        Args:
            platform: 测试平台
            
        Returns:
            测试计划列表
        """
        return self.find_all(
            conditions={'platform': platform},
            order_by='created_at DESC'
        )
    
    def search_plans_by_request(self, request_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """根据原始请求内容搜索测试计划
        
        Args:
            request_text: 搜索文本
            limit: 返回结果限制
            
        Returns:
            匹配的测试计划列表
        """
        try:
            # 使用LIKE查询进行模糊匹配
            query = """
            SELECT * FROM test_plans 
            WHERE original_request LIKE ? 
            ORDER BY created_at DESC 
            LIMIT ?
            """
            
            search_pattern = f"%{request_text}%"
            results = self.db_manager.execute_query(query, (search_pattern, limit))
            
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"搜索测试计划失败: {e}", "TestPlanDAO")
            raise
    
    def update_plan(self, plan_id: str, updates: Dict[str, Any]) -> bool:
        """更新测试计划
        
        Args:
            plan_id: 计划ID
            updates: 要更新的字段
            
        Returns:
            是否更新成功
        """
        try:
            # 不允许更新plan_id和created_at
            if 'plan_id' in updates:
                del updates['plan_id']
            if 'created_at' in updates:
                del updates['created_at']
            
            result = self.update(updates, {'plan_id': plan_id})
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"更新测试计划失败: {e}", "TestPlanDAO")
            raise
    
    def delete_plan(self, plan_id: str) -> bool:
        """删除测试计划
        
        Args:
            plan_id: 计划ID
            
        Returns:
            是否删除成功
        """
        try:
            result = self.delete({'plan_id': plan_id})
            
            if result > 0:
                self.logger.info_tools(f"成功删除测试计划: {plan_id}", "TestPlanDAO")
            
            return result > 0
            
        except Exception as e:
            self.logger.error_tools(f"删除测试计划失败: {e}", "TestPlanDAO")
            raise
    
    def get_recent_plans(self, days: int = 7, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近的测试计划
        
        Args:
            days: 最近天数
            limit: 返回数量限制
            
        Returns:
            最近的测试计划列表
        """
        try:
            query = """
            SELECT * FROM test_plans 
            WHERE created_at >= datetime('now', '-{} days')
            ORDER BY created_at DESC 
            LIMIT ?
            """.format(days)
            
            results = self.db_manager.execute_query(query, (limit,))
            return [self._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取最近测试计划失败: {e}", "TestPlanDAO")
            raise
    
    def get_platform_statistics(self) -> List[Dict[str, Any]]:
        """获取各平台的测试计划统计
        
        Returns:
            平台统计列表，每个元素包含平台名称和计划数量
        """
        try:
            query = """
            SELECT 
                platform,
                COUNT(*) as plan_count,
                AVG(total_steps) as avg_steps,
                MIN(created_at) as first_created,
                MAX(created_at) as last_created
            FROM test_plans 
            GROUP BY platform
            ORDER BY plan_count DESC
            """
            
            results = self.db_manager.execute_query(query)
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取平台统计失败: {e}", "TestPlanDAO")
            raise
    
    def get_step_distribution(self) -> List[Dict[str, Any]]:
        """获取测试步骤数的分布统计
        
        Returns:
            步骤数分布列表
        """
        try:
            query = """
            SELECT 
                total_steps,
                COUNT(*) as plan_count,
                platform
            FROM test_plans 
            GROUP BY total_steps, platform
            ORDER BY total_steps, plan_count DESC
            """
            
            results = self.db_manager.execute_query(query)
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取步骤分布统计失败: {e}", "TestPlanDAO")
            raise
    
    def batch_create_plans(self, plans_data: List[Dict[str, Any]]) -> List[int]:
        """批量创建测试计划
        
        Args:
            plans_data: 测试计划数据列表
            
        Returns:
            创建成功的记录ID列表
        """
        try:
            # 验证所有计划数据
            for i, plan_data in enumerate(plans_data):
                required_fields = ['plan_id', 'original_request', 'platform', 'total_steps', 'structured_plan']
                for field in required_fields:
                    if field not in plan_data or plan_data[field] is None:
                        raise ValueError(f"第{i+1}个计划缺少必需字段: {field}")
                
                # 检查plan_id是否已存在
                if self.exists({'plan_id': plan_data['plan_id']}):
                    raise ValueError(f"计划ID {plan_data['plan_id']} 已存在")
                
                # 添加时间戳
                plan_data['created_at'] = datetime.now().isoformat()
                plan_data['updated_at'] = datetime.now().isoformat()
            
            # 批量插入
            self.insert_batch(plans_data)
            
            # 获取插入的记录ID
            plan_ids = [plan['plan_id'] for plan in plans_data]
            created_records = []
            for plan_id in plan_ids:
                record = self.get_plan_by_plan_id(plan_id)
                if record:
                    created_records.append(record['id'])
            
            self.logger.info_tools(
                f"成功批量创建 {len(created_records)} 个测试计划",
                "TestPlanDAO"
            )
            
            return created_records
            
        except Exception as e:
            self.logger.error_tools(f"批量创建测试计划失败: {e}", "TestPlanDAO")
            raise


if __name__ == "__main__":
    """测试测试计划DAO功能"""
    
    print("🧪 测试测试计划DAO功能...")
    
    try:
        # 初始化数据库
        from simplify_agent.database.init_database import DatabaseInitializer
        
        initializer = DatabaseInitializer()
        initializer.initialize_database()
        
        # 创建DAO实例
        dao = TestPlanDAO()
        
        # 清理测试数据
        dao.delete({})  # 删除所有记录进行干净测试
        
        print("✅ 测试计划DAO创建成功")
        
        # 测试创建测试计划
        test_plan_data = {
            'plan_id': 'test_plan_001',
            'original_request': '测试自动登录功能',
            'platform': 'ios',
            'total_steps': 5,
            'plan_summary': '自动化登录流程测试',
            'structured_plan': {
                'steps': [
                    {'step': 1, 'action': '打开应用'},
                    {'step': 2, 'action': '点击登录按钮'},
                    {'step': 3, 'action': '输入用户名'},
                    {'step': 4, 'action': '输入密码'},
                    {'step': 5, 'action': '点击确认登录'}
                ]
            },
            'generation_metadata': {'model': 'test_model', 'version': '1.0'},
            'agent_instructions': '请严格按照步骤执行测试'
        }
        
        plan_id = dao.create_test_plan(test_plan_data)
        print(f"✅ 成功创建测试计划，ID: {plan_id}")
        
        # 测试根据plan_id查询
        found_plan = dao.get_plan_by_plan_id('test_plan_001')
        assert found_plan is not None, "应该能找到刚创建的计划"
        assert found_plan['original_request'] == '测试自动登录功能', "原始请求应该匹配"
        print("✅ 根据plan_id查询测试通过")
        
        # 测试搜索功能
        search_results = dao.search_plans_by_request('登录')
        assert len(search_results) > 0, "应该能搜索到包含'登录'的计划"
        print("✅ 搜索功能测试通过")
        
        # 测试平台查询
        platform_plans = dao.get_plans_by_platform('ios')
        assert len(platform_plans) > 0, "应该能找到ios平台的计划"
        print("✅ 平台查询测试通过")
        
        # 测试更新功能
        update_success = dao.update_plan('test_plan_001', {
            'plan_summary': '更新后的自动化登录流程测试'
        })
        assert update_success, "更新应该成功"
        
        updated_plan = dao.get_plan_by_plan_id('test_plan_001')
        assert updated_plan['plan_summary'] == '更新后的自动化登录流程测试', "摘要应该已更新"
        print("✅ 更新功能测试通过")
        
        # 测试统计功能
        platform_stats = dao.get_platform_statistics()
        assert len(platform_stats) > 0, "应该有平台统计数据"
        print(f"✅ 平台统计测试通过，统计了 {len(platform_stats)} 个平台")
        
        # 测试批量创建
        batch_plans = [
            {
                'plan_id': f'batch_plan_00{i}',
                'original_request': f'批量测试任务 {i}',
                'platform': 'android',
                'total_steps': i + 2,
                'structured_plan': {'steps': [f'步骤{j+1}' for j in range(i+2)]}
            }
            for i in range(1, 4)
        ]
        
        batch_ids = dao.batch_create_plans(batch_plans)
        assert len(batch_ids) == 3, "应该创建3个批量计划"
        print(f"✅ 批量创建测试通过，创建了 {len(batch_ids)} 个计划")
        
        # 最终统计
        total_count = dao.count()
        print(f"✅ 当前总计划数: {total_count}")
        
        print("\n✅ 测试计划DAO功能测试全部通过!")
        
    except Exception as e:
        print(f"\n❌ 测试计划DAO功能测试失败: {e}")
        import traceback
        traceback.print_exc()