#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
评价数据访问对象 (Evaluation DAO)
提供所有评价表（计划评价、执行评价、综合评价）的统一数据操作方法
"""

import sys
import os
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from simplify_agent.database.dao.base_dao import BaseDAO


class PlanEvaluationDAO(BaseDAO):
    """计划评价数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "plan_evaluations"
    
    @property
    def primary_key(self) -> str:
        return "id"
    
    def create_plan_evaluation(self, evaluation_data: Dict[str, Any]) -> int:
        """创建计划评价记录
        
        Args:
            evaluation_data: 计划评价数据
            
        Returns:
            新创建的评价记录ID
        """
        try:
            # 验证必需字段
            required_fields = ['execution_id']
            for field in required_fields:
                if field not in evaluation_data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 添加时间戳
            evaluation_data['created_at'] = datetime.now().isoformat()
            
            evaluation_id = self.insert(evaluation_data)
            
            self.logger.info_tools(
                f"成功创建计划评价记录: execution_id={evaluation_data['execution_id']}, ID: {evaluation_id}",
                "PlanEvaluationDAO"
            )
            
            return evaluation_id
            
        except Exception as e:
            self.logger.error_tools(f"创建计划评价记录失败: {e}", "PlanEvaluationDAO")
            raise


class ExecutionEvaluationDAO(BaseDAO):
    """执行评价数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "execution_evaluations"
    
    @property
    def primary_key(self) -> str:
        return "id"
    
    def create_execution_evaluation(self, evaluation_data: Dict[str, Any]) -> int:
        """创建执行评价记录
        
        Args:
            evaluation_data: 执行评价数据
            
        Returns:
            新创建的评价记录ID
        """
        try:
            # 验证必需字段
            required_fields = ['execution_id']
            for field in required_fields:
                if field not in evaluation_data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 添加时间戳
            evaluation_data['created_at'] = datetime.now().isoformat()
            
            evaluation_id = self.insert(evaluation_data)
            
            self.logger.info_tools(
                f"成功创建执行评价记录: execution_id={evaluation_data['execution_id']}, ID: {evaluation_id}",
                "ExecutionEvaluationDAO"
            )
            
            return evaluation_id
            
        except Exception as e:
            self.logger.error_tools(f"创建执行评价记录失败: {e}", "ExecutionEvaluationDAO")
            raise


class ComprehensiveEvaluationDAO(BaseDAO):
    """综合评价数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "comprehensive_evaluations"
    
    @property
    def primary_key(self) -> str:
        return "id"
    
    def create_comprehensive_evaluation(self, evaluation_data: Dict[str, Any]) -> int:
        """创建综合评价记录
        
        Args:
            evaluation_data: 综合评价数据
            
        Returns:
            新创建的评价记录ID
        """
        try:
            # 验证必需字段
            required_fields = ['execution_id']
            for field in required_fields:
                if field not in evaluation_data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 检查execution_id是否已存在（综合评价表有UNIQUE约束）
            if self.exists({'execution_id': evaluation_data['execution_id']}):
                raise ValueError(f"执行ID {evaluation_data['execution_id']} 的综合评价已存在")
            
            # 添加时间戳
            evaluation_data['created_at'] = datetime.now().isoformat()
            if 'analysis_time' not in evaluation_data:
                evaluation_data['analysis_time'] = datetime.now().isoformat()
            
            evaluation_id = self.insert(evaluation_data)
            
            self.logger.info_tools(
                f"成功创建综合评价记录: execution_id={evaluation_data['execution_id']}, ID: {evaluation_id}",
                "ComprehensiveEvaluationDAO"
            )
            
            return evaluation_id
            
        except Exception as e:
            self.logger.error_tools(f"创建综合评价记录失败: {e}", "ComprehensiveEvaluationDAO")
            raise


class EvaluationDAO:
    """统一的评价数据访问对象，管理所有评价相关表"""
    
    def __init__(self, db_manager=None):
        """初始化评价DAO
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.plan_dao = PlanEvaluationDAO(db_manager)
        self.execution_dao = ExecutionEvaluationDAO(db_manager)
        self.comprehensive_dao = ComprehensiveEvaluationDAO(db_manager)
        
        self.logger = self.plan_dao.logger  # 使用相同的日志记录器
    
    # 计划评价相关方法
    def create_plan_evaluation(self, evaluation_data: Dict[str, Any]) -> int:
        """创建计划评价记录"""
        return self.plan_dao.create_plan_evaluation(evaluation_data)
    
    def get_plan_evaluation_by_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """根据执行ID获取计划评价"""
        return self.plan_dao.find_one({'execution_id': execution_id})
    
    # 执行评价相关方法  
    def create_execution_evaluation(self, evaluation_data: Dict[str, Any]) -> int:
        """创建执行评价记录"""
        return self.execution_dao.create_execution_evaluation(evaluation_data)
    
    def get_execution_evaluation_by_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """根据执行ID获取执行评价"""
        return self.execution_dao.find_one({'execution_id': execution_id})
    
    # 综合评价相关方法
    def create_comprehensive_evaluation(self, evaluation_data: Dict[str, Any]) -> int:
        """创建综合评价记录"""
        return self.comprehensive_dao.create_comprehensive_evaluation(evaluation_data)
    
    def get_comprehensive_evaluation_by_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """根据执行ID获取综合评价"""
        return self.comprehensive_dao.find_one({'execution_id': execution_id})
    
    def update_comprehensive_evaluation(self, execution_id: str, updates: Dict[str, Any]) -> bool:
        """更新综合评价记录"""
        try:
            result = self.comprehensive_dao.update(updates, {'execution_id': execution_id})
            return result > 0
        except Exception as e:
            self.logger.error_tools(f"更新综合评价记录失败: {e}", "EvaluationDAO")
            raise
    
    # 综合查询方法
    def get_complete_evaluation(self, execution_id: str) -> Dict[str, Any]:
        """获取完整的评价报告（包含所有三种评价）
        
        Args:
            execution_id: 执行ID
            
        Returns:
            完整的评价报告字典
        """
        try:
            result = {
                'execution_id': execution_id,
                'plan_evaluation': None,
                'execution_evaluation': None,
                'comprehensive_evaluation': None
            }
            
            # 获取计划评价
            plan_eval = self.get_plan_evaluation_by_execution(execution_id)
            if plan_eval:
                result['plan_evaluation'] = plan_eval
            
            # 获取执行评价
            exec_eval = self.get_execution_evaluation_by_execution(execution_id)
            if exec_eval:
                result['execution_evaluation'] = exec_eval
            
            # 获取综合评价
            comp_eval = self.get_comprehensive_evaluation_by_execution(execution_id)
            if comp_eval:
                result['comprehensive_evaluation'] = comp_eval
            
            return result
            
        except Exception as e:
            self.logger.error_tools(f"获取完整评价报告失败: {e}", "EvaluationDAO")
            raise
    
    def get_evaluation_statistics(self) -> Dict[str, Any]:
        """获取评价统计信息
        
        Returns:
            评价统计信息字典
        """
        try:
            # 基本统计
            plan_count = self.plan_dao.count()
            execution_count = self.execution_dao.count()
            comprehensive_count = self.comprehensive_dao.count()
            
            # 评分统计（综合评价表）
            score_stats_query = """
            SELECT 
                AVG(overall_success_score) as avg_success_score,
                AVG(confidence_score) as avg_confidence_score,
                COUNT(CASE WHEN final_success_status = 'success' THEN 1 END) as success_count,
                COUNT(CASE WHEN final_success_status = 'failed' THEN 1 END) as failed_count
            FROM comprehensive_evaluations
            WHERE overall_success_score IS NOT NULL
            """
            
            score_results = self.comprehensive_dao.db_manager.execute_query(score_stats_query)
            score_stats = dict(score_results[0]) if score_results else {}
            
            # 最近评价统计
            recent_query = """
            SELECT COUNT(*) as recent_count
            FROM comprehensive_evaluations 
            WHERE created_at >= datetime('now', '-7 days')
            """
            
            recent_results = self.comprehensive_dao.db_manager.execute_query(recent_query)
            recent_stats = dict(recent_results[0]) if recent_results else {}
            
            return {
                'basic_counts': {
                    'plan_evaluations': plan_count,
                    'execution_evaluations': execution_count,
                    'comprehensive_evaluations': comprehensive_count
                },
                'score_statistics': score_stats,
                'recent_statistics': recent_stats
            }
            
        except Exception as e:
            self.logger.error_tools(f"获取评价统计失败: {e}", "EvaluationDAO")
            raise
    
    def get_top_performers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取表现最佳的测试执行（根据综合评分）
        
        Args:
            limit: 返回数量限制
            
        Returns:
            表现最佳的测试执行列表
        """
        try:
            query = """
            SELECT 
                ce.*,
                te.original_request,
                te.execution_status,
                te.total_duration
            FROM comprehensive_evaluations ce
            JOIN test_executions te ON ce.execution_id = te.execution_id
            WHERE ce.overall_success_score IS NOT NULL
            ORDER BY ce.overall_success_score DESC, ce.confidence_score DESC
            LIMIT ?
            """
            
            results = self.comprehensive_dao.db_manager.execute_query(query, (limit,))
            return [self.comprehensive_dao._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取表现最佳执行失败: {e}", "EvaluationDAO")
            raise
    
    def get_evaluation_trends(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取评价趋势（最近N天的评价情况）
        
        Args:
            days: 统计天数
            
        Returns:
            评价趋势列表
        """
        try:
            query = f"""
            SELECT 
                DATE(created_at) as evaluation_date,
                COUNT(*) as evaluation_count,
                AVG(overall_success_score) as avg_score,
                COUNT(CASE WHEN final_success_status = 'success' THEN 1 END) as success_count
            FROM comprehensive_evaluations 
            WHERE created_at >= datetime('now', '-{days} days')
            GROUP BY DATE(created_at)
            ORDER BY evaluation_date DESC
            """
            
            results = self.comprehensive_dao.db_manager.execute_query(query)
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"获取评价趋势失败: {e}", "EvaluationDAO")
            raise
    
    def search_evaluations_by_content(self, search_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """根据评价内容搜索评价记录
        
        Args:
            search_text: 搜索文本
            limit: 返回数量限制
            
        Returns:
            匹配的评价记录列表
        """
        try:
            query = """
            SELECT * FROM comprehensive_evaluations 
            WHERE comprehensive_analysis LIKE ? 
               OR root_cause_analysis LIKE ?
               OR improvement_directions LIKE ?
            ORDER BY created_at DESC 
            LIMIT ?
            """
            
            search_pattern = f"%{search_text}%"
            results = self.comprehensive_dao.db_manager.execute_query(
                query, 
                (search_pattern, search_pattern, search_pattern, limit)
            )
            
            return [self.comprehensive_dao._json_to_dict(row) for row in results]
            
        except Exception as e:
            self.logger.error_tools(f"搜索评价记录失败: {e}", "EvaluationDAO")
            raise
    
    def delete_evaluations_by_execution(self, execution_id: str) -> Dict[str, int]:
        """删除指定执行的所有评价记录
        
        Args:
            execution_id: 执行ID
            
        Returns:
            删除统计字典
        """
        try:
            plan_deleted = self.plan_dao.delete({'execution_id': execution_id})
            execution_deleted = self.execution_dao.delete({'execution_id': execution_id})
            comprehensive_deleted = self.comprehensive_dao.delete({'execution_id': execution_id})
            
            total_deleted = plan_deleted + execution_deleted + comprehensive_deleted
            
            if total_deleted > 0:
                self.logger.info_tools(
                    f"成功删除执行 {execution_id} 的所有评价记录，共 {total_deleted} 条",
                    "EvaluationDAO"
                )
            
            return {
                'plan_evaluations_deleted': plan_deleted,
                'execution_evaluations_deleted': execution_deleted,
                'comprehensive_evaluations_deleted': comprehensive_deleted,
                'total_deleted': total_deleted
            }
            
        except Exception as e:
            self.logger.error_tools(f"删除评价记录失败: {e}", "EvaluationDAO")
            raise
    
    def get_evaluation_coverage(self) -> Dict[str, Any]:
        """获取评价覆盖情况统计
        
        Returns:
            评价覆盖情况字典
        """
        try:
            # 查询所有测试执行
            total_executions_query = "SELECT COUNT(*) as total FROM test_executions"
            total_result = self.comprehensive_dao.db_manager.execute_query(total_executions_query)
            total_executions = total_result[0]['total'] if total_result else 0
            
            # 查询有评价的执行数量
            coverage_query = """
            SELECT 
                COUNT(DISTINCT pe.execution_id) as plan_coverage,
                COUNT(DISTINCT ee.execution_id) as execution_coverage,
                COUNT(DISTINCT ce.execution_id) as comprehensive_coverage,
                COUNT(DISTINCT COALESCE(pe.execution_id, ee.execution_id, ce.execution_id)) as any_evaluation_coverage
            FROM test_executions te
            LEFT JOIN plan_evaluations pe ON te.execution_id = pe.execution_id
            LEFT JOIN execution_evaluations ee ON te.execution_id = ee.execution_id
            LEFT JOIN comprehensive_evaluations ce ON te.execution_id = ce.execution_id
            """
            
            coverage_results = self.comprehensive_dao.db_manager.execute_query(coverage_query)
            coverage_stats = dict(coverage_results[0]) if coverage_results else {}
            
            # 计算覆盖率
            if total_executions > 0:
                coverage_stats['plan_coverage_rate'] = round(100.0 * coverage_stats.get('plan_coverage', 0) / total_executions, 2)
                coverage_stats['execution_coverage_rate'] = round(100.0 * coverage_stats.get('execution_coverage', 0) / total_executions, 2)
                coverage_stats['comprehensive_coverage_rate'] = round(100.0 * coverage_stats.get('comprehensive_coverage', 0) / total_executions, 2)
                coverage_stats['any_evaluation_coverage_rate'] = round(100.0 * coverage_stats.get('any_evaluation_coverage', 0) / total_executions, 2)
            
            return {
                'total_executions': total_executions,
                'coverage_statistics': coverage_stats
            }
            
        except Exception as e:
            self.logger.error_tools(f"获取评价覆盖情况失败: {e}", "EvaluationDAO")
            raise


if __name__ == "__main__":
    """测试评价DAO功能"""
    
    print("🧪 测试评价DAO功能...")
    
    try:
        # 初始化数据库
        from simplify_agent.database.init_database import DatabaseInitializer
        
        initializer = DatabaseInitializer()
        initializer.initialize_database()
        
        # 创建DAO实例
        dao = EvaluationDAO()
        
        # 清理测试数据
        dao.plan_dao.delete({})
        dao.execution_dao.delete({})
        dao.comprehensive_dao.delete({})
        
        print("✅ 评价DAO创建成功")
        
        # 测试创建计划评价
        plan_eval_data = {
            'execution_id': 'exec_test_001',
            'plan_id': 'plan_test_001', 
            'plan_quality_score': 0.85,
            'plan_analysis_content': '测试计划整体结构合理，步骤清晰',
            'plan_key_issues': '部分参数定义不够明确',
            'plan_improvement_suggestions': '建议增加更详细的参数说明',
            'step_completeness_score': 0.9,
            'step_logic_score': 0.8,
            'parameter_definition_score': 0.75,
            'redundancy_score': 0.85,
            'plan_evaluation_notes': '综合评价良好'
        }
        
        plan_eval_id = dao.create_plan_evaluation(plan_eval_data)
        print(f"✅ 成功创建计划评价记录，ID: {plan_eval_id}")
        
        # 测试创建执行评价
        exec_eval_data = {
            'execution_id': 'exec_test_001',
            'execution_compliance_score': 0.9,
            'execution_quality_score': 0.85,
            'goal_achievement_score': 0.95,
            'compliance_analysis_content': '执行过程严格遵循计划',
            'quality_analysis_content': '执行质量良好，错误处理得当',
            'path_tracking_analysis': '路径追踪准确',
            'parameter_handling_score': 0.8,
            'error_handling_score': 0.9,
            'step_execution_rate': 0.95,
            'average_step_success_rate': 0.92,
            'execution_key_issues': '个别步骤执行时间较长',
            'execution_improvement_suggestions': '优化步骤执行效率',
            'execution_evaluation_notes': '执行效果优秀'
        }
        
        exec_eval_id = dao.create_execution_evaluation(exec_eval_data)
        print(f"✅ 成功创建执行评价记录，ID: {exec_eval_id}")
        
        # 测试创建综合评价
        comp_eval_data = {
            'execution_id': 'exec_test_001',
            'evaluation_round': 'round_001',
            'analysis_model': 'evaluation_model_v1.0',
            'final_success_status': 'success',
            'overall_success_score': 0.88,
            'confidence_score': 0.92,
            'comprehensive_analysis': '本次测试执行整体表现优秀，达到预期目标',
            'root_cause_analysis': '成功的主要原因是计划制定合理，执行严格',
            'best_practices_suggestions': '建议将此次经验推广到类似测试中',
            'improvement_directions': '可在执行效率方面进一步优化',
            'business_logic_impact': '对业务逻辑验证有积极作用',
            'user_experience_impact': '提升用户体验质量',
            'evaluation_summary': '测试成功完成，质量优秀',
            'next_steps_recommendations': '继续保持当前测试标准'
        }
        
        comp_eval_id = dao.create_comprehensive_evaluation(comp_eval_data)
        print(f"✅ 成功创建综合评价记录，ID: {comp_eval_id}")
        
        # 测试获取完整评价报告
        complete_eval = dao.get_complete_evaluation('exec_test_001')
        assert complete_eval['plan_evaluation'] is not None, "应该有计划评价"
        assert complete_eval['execution_evaluation'] is not None, "应该有执行评价"
        assert complete_eval['comprehensive_evaluation'] is not None, "应该有综合评价"
        print("✅ 获取完整评价报告测试通过")
        
        # 创建更多测试数据
        more_comp_evals = [
            {
                'execution_id': f'exec_test_{i:03d}',
                'final_success_status': 'success' if i % 2 == 0 else 'failed',
                'overall_success_score': 0.8 + i * 0.01,
                'confidence_score': 0.9 - i * 0.005,
                'comprehensive_analysis': f'测试执行 {i} 的综合分析',
                'evaluation_summary': f'测试 {i} 的总结'
            }
            for i in range(2, 6)
        ]
        
        for eval_data in more_comp_evals:
            dao.create_comprehensive_evaluation(eval_data)
        
        print("✅ 创建额外测试数据成功")
        
        # 测试评价统计
        stats = dao.get_evaluation_statistics()
        assert 'basic_counts' in stats, "统计应该包含基本计数"
        assert 'score_statistics' in stats, "统计应该包含评分统计"
        print("✅ 评价统计测试通过")
        
        # 测试表现最佳的执行
        top_performers = dao.get_top_performers(3)
        print(f"✅ 表现最佳执行测试通过，找到 {len(top_performers)} 个")
        
        # 测试搜索功能
        search_results = dao.search_evaluations_by_content('测试')
        assert len(search_results) > 0, "应该能搜索到包含'测试'的评价"
        print(f"✅ 搜索功能测试通过，找到 {len(search_results)} 个匹配记录")
        
        # 测试评价覆盖情况
        coverage = dao.get_evaluation_coverage()
        assert 'total_executions' in coverage, "覆盖情况应该包含总执行数"
        assert 'coverage_statistics' in coverage, "覆盖情况应该包含覆盖统计"
        print("✅ 评价覆盖情况测试通过")
        
        # 测试更新综合评价
        update_success = dao.update_comprehensive_evaluation(
            'exec_test_001',
            {'evaluation_summary': '更新后的测试总结'}
        )
        assert update_success, "综合评价更新应该成功"
        print("✅ 综合评价更新测试通过")
        
        # 最终统计
        final_stats = dao.get_evaluation_statistics()
        basic_counts = final_stats['basic_counts']
        print(f"✅ 最终统计: 计划评价={basic_counts['plan_evaluations']}, 执行评价={basic_counts['execution_evaluations']}, 综合评价={basic_counts['comprehensive_evaluations']}")
        
        print("\n✅ 评价DAO功能测试全部通过!")
        
    except Exception as e:
        print(f"\n❌ 评价DAO功能测试失败: {e}")
        import traceback
        traceback.print_exc()