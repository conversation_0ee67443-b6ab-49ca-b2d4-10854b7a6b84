# SimplifyAgent Web管理系统启动指南

## 快速启动

### 一键启动（推荐）
```bash
cd /Users/<USER>/Desktop/work/langchain_ollama/simplify_agent
python run.py
```

## 系统要求

### 必备环境
- **Python 3.7+** (已安装 Flask 等依赖)
- **Node.js 16+** (包含 npm)

### 端口配置
- **前端端口**: 3000
- **后端端口**: 5003
- **局域网IP**: ************ (en0网口)

## 访问地址

### 本机访问
- 前端: http://************:3000
- 后端API: http://************:5003

### 局域网访问
同网段内的其他设备可直接通过上述地址访问

## 功能模块

### 用户模式 (默认)
- **测试记录**: 查看所有测试执行记录
- **执行详情**: 查看测试计划、执行流程、综合评价
- **统计分析**: 成功率统计、执行趋势、平台分布等图表分析

### 开发者模式
- **登录入口**: `/dev/login` (需要开发者密码)
- **控制台**: 数据库状态监控和概览
- **表管理**: 查看所有数据表信息
- **数据管理**: 对表记录进行增删改查操作
- **SQL执行器**: 执行自定义SQL查询

## 手动启动 (如需要)

### 后端启动
```bash
cd web_backend
export PORT=5003
export FLASK_ENV=production
python -m web_backend.app
```

### 前端启动
```bash
cd web_frontend
npm install  # 首次运行需要
npm run dev
```

## 常见问题

### 端口冲突
如果端口被占用，可以修改 `run.py` 中的端口配置：
- `BACKEND_PORT`: 后端端口
- `FRONTEND_PORT`: 前端端口

### 网络访问问题
1. **防火墙设置**: 确保允许端口 3000 和 5003
2. **IP地址**: 脚本会自动配置使用 ************
3. **网络连通性**: 确保设备在同一网段

### 依赖问题
- **Python依赖**: 确保已安装 Flask 及相关包
- **Node.js依赖**: 自动执行 `npm install` 安装前端依赖

## 停止服务

按 `Ctrl+C` 即可优雅地停止所有服务

## 技术架构

```
┌─────────────────┐    HTTP/REST API    ┌──────────────────┐
│   前端 (React)   │ ◄─────────────────► │  后端 (Flask)     │
│   Port: 3000    │                    │  Port: 5003      │
└─────────────────┘                    └──────────────────┘
        │                                        │
        │                                        │
   ┌─────────┐                            ┌──────────────┐
   │ 用户访问 │                            │ 数据库访问    │
   └─────────┘                            │ (SQLite)     │
                                         └──────────────┘
```

## 项目结构

```
simplify_agent/
├── run.py                 # 一键启动脚本
├── web_backend/           # 后端代码
│   ├── app.py            # Flask应用入口
│   ├── api/              # API路由
│   ├── services/         # 业务服务层
│   └── ...
├── web_frontend/          # 前端代码
│   ├── src/              # 源码
│   ├── package.json      # 依赖配置
│   └── vite.config.ts    # 构建配置
└── database/             # 数据库文件
```