# SimplifyAgent 日志数据自动化处理服务开发计划

## 项目概述
为 SimplifyAgent 构建纯文件监控的日志数据自动化入库服务，通过24小时监控三大核心模块的日志输出文件，实现数据的智能解析、处理和数据库存储。采用完全解耦的架构设计，确保业务模块与数据处理逻辑完全分离。

## 目标
- 建立24小时纯文件监控的数据采集系统
- 实现三个模块日志文件的智能解析和自动入库
- 维护日志文件数量控制和自动清理机制
- 提供完整的数据处理追踪和错误恢复机制
- 确保业务模块零感知的数据处理体验

---

## 系统架构设计

### 核心服务架构（纯文件监控方案）
```
┌─────────────────────────────────────────────────────────────┐
│                    log_server.py                           │
│                  (独立后台服务进程)                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   文件监控模块   │ │   数据解析模块   │ │   数据存储模块   ││
│  │   FileWatcher   │ │   DataParser    │ │  DatabaseSync   ││
│  │  (watchdog监控)  │ │ (智能解析引擎)   │ │ (批量入库处理)   ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   文件清理模块   │ │   错误处理模块   │ │   状态监控模块   ││
│  │   FileCleaner   │ │  ErrorHandler   │ │ StatusMonitor  ││
│  │  (自动维护20个)  │ │  (重试+告警)    │ │  (健康检查)     ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Database Layer                           │
│                 (已有的DAO接口层)                            │
└─────────────────────────────────────────────────────────────┘
                              ▲
                              │ 完全解耦
┌─────────────────┬─────────────────┬─────────────────────────┐
│ 测试计划生成模块  │  测试执行模块    │   测试评价模块           │
│ (无感知数据处理) │ (专注业务逻辑)   │  (独立评价分析)         │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 监控目标路径
1. **测试计划监控**: `simplify_agent/log/json_plan/`
2. **测试执行监控**: `simplify_agent/log/agent_execute_log/`
3. **测试评价监控**: `simplify_agent/log/judge_report/`

---

## 开发计划

### 阶段一：核心框架搭建 [ ] 未完成

#### 1.1 日志服务器主体框架 [ ] 未完成
**文件**: `simplify_agent/log_server.py`
**功能**:
- 主服务器启动和生命周期管理
- 多线程文件监控协调器
- 配置文件加载和管理
- 优雅关闭和资源清理

**要点**:
- 使用 asyncio 或 threading 实现并发监控
- 支持配置文件热重载
- 实现信号处理（SIGTERM, SIGINT）
- 添加服务健康检查接口

#### 1.2 配置管理模块 [ ] 未完成
**文件**: `simplify_agent/config/log_server_config.py`
**功能**:
- 监控路径配置
- 数据库连接配置
- 文件清理策略配置
- 日志级别和输出配置

**配置示例**:
```python
LOG_SERVER_CONFIG = {
    'watch_paths': {
        'json_plan': 'simplify_agent/log/json_plan',
        'agent_execute': 'simplify_agent/log/agent_execute_log',
        'judge_report': 'simplify_agent/log/judge_report'
    },
    'file_limits': {
        'json_plan_max': 20,
        'agent_execute_max': 20,
        'judge_report_max': 20
    },
    'processing': {
        'batch_size': 10,
        'retry_attempts': 3,
        'retry_delay': 5
    }
}
```

#### 1.3 基础日志记录系统 [ ] 未完成
**文件**: `simplify_agent/log_server_logger.py`
**功能**:
- 结构化日志记录
- 多级别日志输出
- 文件滚动和归档
- 性能监控日志

---

### 阶段二：文件监控系统 [ ] 未完成

#### 2.1 文件监控核心模块 [ ] 未完成
**文件**: `simplify_agent/monitors/file_watcher.py`
**功能**:
- 基于 watchdog 的文件监控
- 三个目录的并发监控
- 文件事件过滤和去重
- 监控状态维护和恢复

**监控事件类型**:
- 新文件创建 (created)
- 文件修改完成 (modified)
- 目录变化监控

**要点**:
- 使用 Observer 模式监控文件系统
- 实现文件写入完成检测（避免读取不完整文件）
- 支持监控路径动态添加和移除
- 添加文件锁检测机制

#### 2.2 事件队列管理器 [ ] 未完成
**文件**: `simplify_agent/monitors/event_queue.py`
**功能**:
- 文件事件队列管理
- 事件优先级排序
- 重复事件去重
- 队列持久化（防止服务重启丢失事件）

**事件优先级**:
1. 高优先级: JSON计划文件（实时性要求高）
2. 中优先级: 执行日志文件夹（批量处理）
3. 低优先级: 评价报告（延时处理可接受）

---

### 阶段三：数据解析引擎 [ ] 未完成

#### 3.1 测试计划解析器 [ ] 未完成
**文件**: `simplify_agent/parsers/plan_parser.py`
**功能**:
- JSON计划文件解析
- 数据校验和格式化
- test_plans表数据映射
- 解析错误处理和恢复

**核心方法**:
```python
class PlanParser:
    def parse_plan_file(self, file_path: str) -> Dict[str, Any]
    def validate_plan_data(self, plan_data: Dict) -> bool
    def extract_metadata(self, plan_data: Dict) -> Dict
    def format_for_database(self, plan_data: Dict) -> Dict
```

**数据映射关系**:
| JSON字段 | 数据库字段 | 处理逻辑 |
|---------|-----------|---------|
| `structured_plan.plan_id` | `plan_id` | 直接映射 |
| `original_request` | `original_request` | 直接映射 |
| `structured_plan.platform` | `platform` | 大小写标准化 |
| `structured_plan.total_steps` | `total_steps` | 数值验证 |
| `structured_plan.summary` | `plan_summary` | 文本清理 |
| `structured_plan` | `structured_plan` | JSON序列化 |
| `generation_result` | `generation_metadata` | JSON序列化 |
| `agent_instructions` | `agent_instructions` | JSON序列化 |
| `timestamp` | `created_at` | 时间格式转换 |

#### 3.2 测试执行解析器 [ ] 未完成
**文件**: `simplify_agent/parsers/execution_parser.py`
**功能**:
- 执行日志文件夹解析
- agent.log 和 task_structured.log 联合解析
- test_executions 和 tool_executions 表数据映射
- 执行状态和统计信息提取

**核心方法**:
```python
class ExecutionParser:
    def parse_execution_folder(self, folder_path: str) -> Dict[str, Any]
    def parse_agent_log(self, log_path: str) -> Dict
    def parse_task_structured_log(self, log_path: str) -> List[Dict]
    def extract_tool_executions(self, log_content: str) -> List[Dict]
    def calculate_execution_stats(self, tool_executions: List) -> Dict
```

**解析策略**:
- 从 `agent.log` 提取基本信息（执行ID、计划ID、原始请求）
- 从 `task_structured.log` 解析详细执行流程
- 使用正则表达式提取工具调用信息
- 统计成功/失败轮次和总耗时

#### 3.3 测试评价解析器 [ ] 未完成
**文件**: `simplify_agent/parsers/evaluation_parser.py`
**功能**:
- 评价报告文件解析
- 三个评价表数据分离和映射
- 评分数据提取和验证
- 文本内容结构化处理

**核心方法**:
```python
class EvaluationParser:
    def parse_report_file(self, file_path: str) -> Dict[str, Any]
    def extract_comprehensive_evaluation(self, content: str) -> Dict
    def extract_plan_evaluation(self, content: str) -> Dict
    def extract_execution_evaluation(self, content: str) -> Dict
    def parse_scores_and_metrics(self, content: str) -> Dict
```

**解析重点**:
- 提取各维度评分（成功评分、置信度、质量评分等）
- 分离不同类型的分析内容
- 处理中文文本的结构化存储
- 建立评价数据与执行记录的关联

#### 3.4 通用解析工具 [ ] 未完成
**文件**: `simplify_agent/parsers/parser_utils.py`
**功能**:
- 时间解析和格式化工具
- JSON安全解析工具
- 文本清理和标准化工具
- 数据验证装饰器

---

### 阶段四：数据存储集成 [ ] 未完成

#### 4.1 数据库同步管理器 [ ] 未完成
**文件**: `simplify_agent/storage/database_sync.py`
**功能**:
- 纯文件解析数据到数据库的批量同步
- 事务管理和错误回滚
- 基于时间戳文件名的简单去重机制
- 存储性能监控和统计

**核心方法**:
```python
class DatabaseSyncManager:
    def sync_plan_data(self, plan_data: Dict, file_path: str) -> bool
    def sync_execution_data(self, execution_data: Dict, tool_data: List, folder_path: str) -> bool
    def sync_evaluation_data(self, eval_data: Dict, report_path: str) -> bool
    def batch_sync(self, data_batch: List[Dict]) -> Dict[str, Any]
    def is_file_already_processed(self, file_name: str) -> bool
    def mark_file_as_processed(self, file_name: str) -> None
```

**简化的同步策略**:
- 使用已有的DAO接口进行数据操作，完全复用现有数据库架构
- 基于**时间戳文件名唯一性**进行去重，无需复杂hash计算
- 维护简单的已处理文件名列表，支持服务重启后的增量处理
- 实现批量插入优化，提高处理大量文件时的性能

#### 4.2 简化的处理记录管理 [ ] 未完成
**文件**: `simplify_agent/storage/processing_tracker.py`
**功能**:
- 维护已处理文件名的简单记录
- 基于时间戳文件名的快速去重查询
- 处理状态跟踪和统计
- 支持服务重启后的增量处理

**简化的处理记录表设计**:
```sql
CREATE TABLE processed_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_name VARCHAR(255) UNIQUE NOT NULL,  -- 时间戳文件名，天然唯一
    file_type VARCHAR(50) NOT NULL,          -- plan/execution/evaluation
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_duration_ms INTEGER,          -- 处理耗时（毫秒）
    record_count INTEGER DEFAULT 1,          -- 入库记录数量
    status VARCHAR(20) DEFAULT 'success'     -- success/failed/retry
);

CREATE INDEX idx_file_name ON processed_files(file_name);
CREATE INDEX idx_file_type ON processed_files(file_type);
```

**核心方法**:
```python
class ProcessingTracker:
    def is_file_processed(self, file_name: str) -> bool
    def mark_file_processed(self, file_name: str, file_type: str, duration_ms: int, record_count: int) -> None
    def get_processing_stats(self, hours: int = 24) -> Dict[str, int]
    def cleanup_old_records(self, days: int = 30) -> int
```

#### 4.3 数据验证和清洗 [ ] 未完成
**文件**: `simplify_agent/storage/data_validator.py`
**功能**:
- 数据完整性验证
- 字段格式标准化
- 关联关系检查
- 异常数据标记和处理

**验证规则**:
| 数据类型 | 验证项目 | 处理策略 |
|---------|---------|---------|
| 测试计划 | plan_id唯一性 | 重复则跳过 |
| 测试计划 | platform值有效性 | 标准化为ios/android |
| 执行记录 | execution_id格式 | 轮次格式验证 |
| 工具执行 | tool_name存在性 | 无效工具名标记 |
| 评价数据 | 评分范围 | 0-1之间数值验证 |

---

### 阶段五：文件清理管理 [ ] 未完成

#### 5.1 文件生命周期管理器 [ ] 未完成
**文件**: `simplify_agent/cleanup/file_lifecycle.py`
**功能**:
- 文件年龄计算和排序
- 超量文件自动清理
- 清理策略配置管理
- 清理操作审计日志

**清理策略**:
```python
CLEANUP_POLICIES = {
    'json_plan': {
        'max_files': 20,
        'cleanup_strategy': 'oldest_first',
        'preserve_recent_hours': 24
    },
    'agent_execute_log': {
        'max_files': 20, 
        'cleanup_strategy': 'oldest_first',
        'preserve_recent_hours': 48
    },
    'judge_report': {
        'max_files': 20,
        'cleanup_strategy': 'oldest_first', 
        'preserve_recent_hours': 12
    }
}
```

#### 5.2 安全删除机制 [ ] 未完成
**文件**: `simplify_agent/cleanup/safe_delete.py`
**功能**:
- 删除前数据备份确认
- 分阶段删除（标记 -> 延迟 -> 实际删除）
- 删除操作撤销机制
- 删除失败重试逻辑

**安全机制**:
- 删除前检查数据是否已入库
- 实现软删除（移动到trash目录）
- 重要文件多重确认
- 定期清理trash目录

---

### 阶段六：监控和运维 [ ] 未完成

#### 6.1 服务状态监控 [ ] 未完成
**文件**: `simplify_agent/monitoring/service_monitor.py`
**功能**:
- 服务运行状态检查
- 文件监控性能统计
- 数据处理延迟监控
- 错误率和成功率统计

**监控指标**:
- 文件监控响应时间
- 数据解析成功率
- 数据库写入延迟
- 队列积压情况
- 内存使用状况

#### 6.2 健康检查接口 [ ] 未完成
**文件**: `simplify_agent/monitoring/health_check.py`
**功能**:
- HTTP健康检查端点
- 服务状态JSON报告
- 依赖服务连通性检查
- 自诊断和自修复能力

**健康检查端点**:
```
GET /health
{
    "status": "healthy",
    "timestamp": "2025-09-04T10:00:00Z",
    "components": {
        "file_watcher": "healthy",
        "database": "healthy", 
        "queue": "healthy"
    },
    "metrics": {
        "processed_files_today": 156,
        "processing_queue_size": 3,
        "average_processing_time": 0.45
    }
}
```

#### 6.3 告警和通知系统 [ ] 未完成
**文件**: `simplify_agent/monitoring/alert_system.py`
**功能**:
- 异常情况告警
- 处理失败通知
- 队列积压预警
- 磁盘空间监控

---

### 阶段七：测试和部署 [ ] 未完成

#### 7.1 单元测试套件 [ ] 未完成
**目录**: `simplify_agent/tests/log_server/`
**覆盖范围**:
- 文件监控功能测试
- 数据解析准确性测试
- 数据库同步完整性测试
- 文件清理逻辑测试
- 异常处理和恢复测试

#### 7.2 集成测试 [ ] 未完成
**测试场景**:
- 端到端数据流测试
- 并发文件处理测试
- 服务重启恢复测试
- 大量文件处理性能测试
- 异常场景鲁棒性测试

#### 7.3 部署和运维脚本 [ ] 未完成
**文件**: `simplify_agent/scripts/`
**脚本清单**:
- `start_log_server.sh` - 服务启动脚本
- `stop_log_server.sh` - 优雅停止脚本
- `install_dependencies.sh` - 依赖安装脚本
- `backup_logs.sh` - 日志备份脚本
- `check_service_status.sh` - 服务状态检查

---

## 数据库表关联关系设计

### 核心关联模式
基于对三个模块数据的详细分析，建立以下关联关系：

```
test_plans (测试计划表) 
    ↓ (plan_id)
test_executions (测试执行表)
    ↓ (execution_id)
├── tool_executions (工具执行详情表)
├── execution_failures (执行失败详情表) 
└── comprehensive_evaluations (综合评价表)
    ├── plan_evaluations (计划评价表)
    └── execution_evaluations (执行评价表)
```

### 外键关系说明
- `test_executions.plan_id` → `test_plans.plan_id` (多对一)
- `tool_executions.execution_id` → `test_executions.execution_id` (多对一)  
- `execution_failures.execution_id` → `test_executions.execution_id` (多对一)
- `comprehensive_evaluations.execution_id` → `test_executions.execution_id` (一对一)
- `plan_evaluations.execution_id` → `test_executions.execution_id` (多对一)
- `execution_evaluations.execution_id` → `test_executions.execution_id` (多对一)

### 关联数据流
1. **计划生成** → `test_plans`表 (通过plan_id关联)
2. **执行过程** → `test_executions` + `tool_executions`表 (通过execution_id关联)  
3. **评价分析** → 三个评价表 (通过execution_id关联到执行记录)

### 详细字段映射和数据提取规则

#### 1. 测试计划数据映射 (test_plans表)

**数据源**: `simplify_agent/log/json_plan/plan_mlx_*.json`
**关键字段映射关系**:

| 数据库字段 | JSON源字段 | 数据处理逻辑 | 示例值 |
|-----------|------------|-------------|--------|
| `plan_id` | `structured_plan.plan_id` | 直接映射，作为主键 | `"plan_1234567890abcdef"` |
| `original_request` | `original_request` | 直接映射 | `"重启美团app，等待2秒，校验..."` |
| `platform` | `structured_plan.platform` | 小写标准化 | `"ios"` |
| `total_steps` | `structured_plan.total_steps` | 数值验证 | `30` |
| `plan_summary` | `structured_plan.summary` | 文本清理 | `"测试美团app首页功能及侧边栏交互流程"` |
| `structured_plan` | `structured_plan` | JSON序列化存储 | 完整计划步骤详情 |
| `generation_metadata` | `generation_result` | JSON序列化存储 | 生成过程元数据 |
| `agent_instructions` | `agent_instructions` | JSON序列化存储 | Agent执行指令 |
| `created_at` | `timestamp` | ISO时间戳转换 | `2025-09-04 16:55:40` |

**数据提取函数实现**:
```python
def extract_test_plan_data(json_file_path: str) -> Dict[str, Any]:
    """从JSON计划文件提取test_plans表数据"""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    structured_plan = data.get('structured_plan', {})
    return {
        'plan_id': structured_plan.get('plan_id'),
        'original_request': data.get('original_request'),
        'platform': structured_plan.get('platform', '').lower(),
        'total_steps': int(structured_plan.get('total_steps', 0)),
        'plan_summary': structured_plan.get('summary', '').strip(),
        'structured_plan': json.dumps(structured_plan, ensure_ascii=False),
        'generation_metadata': json.dumps(data.get('generation_result', {}), ensure_ascii=False),
        'agent_instructions': json.dumps(data.get('agent_instructions', {}), ensure_ascii=False),
        'created_at': datetime.fromisoformat(data.get('timestamp').replace('Z', '+00:00')),
        'updated_at': datetime.now()
    }
```

#### 2. 测试执行数据映射 (test_executions + tool_executions表)

**数据源**: `simplify_agent/log/agent_execute_log/round_*/`
**处理策略**: 联合解析 `agent.log` 和 `task_structured.log` 文件

##### 2.1 执行基本信息映射 (test_executions表)

| 数据库字段 | 数据源 | 提取方式 | 示例值 |
|-----------|--------|---------|--------|
| `execution_id` | 文件夹名 | 正则提取 | `"round_000006_20250904_171233"` |
| `plan_id` | `agent.log` | 正则提取计划文件路径 | `"plan_1234567890abcdef"` |
| `original_request` | `agent.log` | 正则提取原始请求 | `"重启美团app，等待2秒..."` |
| `execution_status` | `task_structured.log` | 最终状态分析 | `"超时"/"成功"/"失败"` |
| `total_rounds` | `task_structured.log` | 统计执行轮次 | `30` |
| `total_duration` | `task_structured.log` | 时间统计 | `283.9` (秒) |
| `start_time` | `task_structured.log` | 第一轮时间戳 | `2025-09-04 17:12:33` |
| `end_time` | `task_structured.log` | 最后轮次时间戳 | `2025-09-04 17:17:10` |

**执行信息提取函数**:
```python
def extract_execution_data(folder_path: str) -> Dict[str, Any]:
    """从执行日志文件夹提取test_executions表数据"""
    execution_id = os.path.basename(folder_path)
    agent_log_path = os.path.join(folder_path, 'agent.log')
    task_log_path = os.path.join(folder_path, 'task_structured.log')
    
    # 从agent.log提取基本信息
    plan_id = extract_plan_id_from_agent_log(agent_log_path)
    original_request = extract_original_request_from_agent_log(agent_log_path)
    
    # 从task_structured.log提取执行统计
    execution_stats = analyze_task_structured_log(task_log_path)
    
    return {
        'execution_id': execution_id,
        'plan_id': plan_id,
        'original_request': original_request,
        'execution_status': execution_stats['final_status'],
        'total_rounds': execution_stats['total_rounds'],
        'total_duration': execution_stats['duration_seconds'],
        'start_time': execution_stats['start_time'],
        'end_time': execution_stats['end_time'],
        'created_at': datetime.now()
    }

def analyze_task_structured_log(log_path: str) -> Dict[str, Any]:
    """分析task_structured.log获取执行统计信息"""
    with open(log_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取所有执行轮次
    rounds = re.findall(r'─── 第(\d+)轮执行 ───', content)
    total_rounds = len(rounds)
    
    # 提取最终状态
    if '超时' in content:
        final_status = '超时'
    elif '成功' in content and '失败' not in content:
        final_status = '成功'
    else:
        final_status = '失败'
    
    # 提取总耗时（从最后的统计信息中）
    duration_match = re.search(r'总耗时: ([\d.]+)秒', content)
    duration = float(duration_match.group(1)) if duration_match else 0.0
    
    return {
        'final_status': final_status,
        'total_rounds': total_rounds,
        'duration_seconds': duration,
        'start_time': datetime.now(),  # 需要从日志中精确提取
        'end_time': datetime.now()      # 需要从日志中精确提取
    }
```

##### 2.2 工具执行详情映射 (tool_executions表)

| 数据库字段 | 数据源 | 提取方式 | 示例值 |
|-----------|--------|---------|--------|
| `execution_id` | 父文件夹名 | 直接传递 | `"round_000006_20250904_171233"` |
| `round_number` | `task_structured.log` | 正则提取轮次号 | `12` |
| `tool_name` | `task_structured.log` | 正则提取工具名 | `"find_text_on_page"` |
| `tool_parameters` | `task_structured.log` | JSON参数提取 | `'{"text": "资质与规则"}'` |
| `execution_time` | `task_structured.log` | 正则提取耗时 | `1.23` (秒) |
| `tool_status` | `task_structured.log` | 从执行结果解析 | `"success"/"error"` |
| `tool_result` | `task_structured.log` | JSON结果提取 | 完整工具返回结果 |
| `result_summary` | `task_structured.log` | 摘要文本提取 | `"成功找到文本"` |
| `image_url` | 工具结果JSON | 解析image_url字段 | 截图URL |
| `local_path` | 工具结果JSON | 解析local_path字段 | 本地文件路径 |

**工具执行解析函数**:
```python
def extract_tool_executions(task_log_path: str, execution_id: str) -> List[Dict[str, Any]]:
    """从task_structured.log提取所有工具执行记录"""
    with open(task_log_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    tool_executions = []
    
    # 分割每个执行轮次
    round_sections = re.split(r'─── 第(\d+)轮执行 ───', content)[1:]
    
    for i in range(0, len(round_sections), 2):
        if i + 1 >= len(round_sections):
            break
            
        round_num = int(round_sections[i])
        section = round_sections[i + 1]
        
        # 提取各字段
        tool_name = extract_by_regex(section, r'🔧 执行工具: (\w+)', 'unknown')
        tool_params = extract_by_regex(section, r'📝 工具参数: ({.*?})', '{}')
        exec_time = float(extract_by_regex(section, r'⏱️\s*执行耗时: ([\d.]+)秒', '0'))
        tool_result = extract_by_regex(section, r'📊 执行结果: ({.*?})\s*(?=\n.*?💬|$)', '{}')
        summary = extract_by_regex(section, r'💬 结果摘要: ([^\n]+)', '').strip()
        
        # 解析工具结果获取状态和附加信息
        try:
            result_json = json.loads(tool_result)
            status = result_json.get('status', 'unknown')
            image_url = result_json.get('image_url', '')
            local_path = result_json.get('local_path', '')
        except json.JSONDecodeError:
            status = 'parse_error'
            image_url = ''
            local_path = ''
        
        tool_executions.append({
            'execution_id': execution_id,
            'round_number': round_num,
            'tool_name': tool_name,
            'tool_parameters': tool_params,
            'execution_time': exec_time,
            'tool_status': status,
            'tool_result': tool_result,
            'result_summary': summary,
            'image_url': image_url,
            'local_path': local_path,
            'error_message': result_json.get('message', '') if status == 'error' else '',
            'created_at': datetime.now()
        })
    
    return tool_executions

def extract_by_regex(text: str, pattern: str, default: str = '') -> str:
    """通用正则提取辅助函数"""
    match = re.search(pattern, text, re.DOTALL)
    return match.group(1) if match else default
```

#### 3. 测试评价数据映射 (evaluation表)

**数据源**: `simplify_agent/log/judge_report/*_report.log`
**处理策略**: 分别映射到三个评价表

##### 3.1 综合评价映射 (comprehensive_evaluations表)

| 数据库字段 | 报告源字段 | 提取方式 | 示例值 |
|-----------|-----------|---------|--------|
| `execution_id` | 文件名 | 去除_report.log后缀 | `"round_000006_20250904_171233"` |
| `evaluation_round` | `🔢 轮次编号:` | 正则提取 | `6` |
| `analysis_time` | `⏰ 分析时间:` | 时间解析 | `2025-09-04 18:00:47` |
| `analysis_model` | `🤖 分析模型:` | 文本提取 | `"Qwen3-Coder-30B-A3B-Instruct-4bit-dwq-v2"` |
| `final_success_status` | `**最终成功状态**:` | 文本提取 | `"成功"` |
| `overall_success_score` | `**成功评分**:` | 数值解析 | `0.95` |
| `confidence_score` | `**置信度**:` | 数值解析 | `0.92` |
| `comprehensive_analysis` | `## 3. 综合评价与建议` | 章节内容提取 | 完整分析文本 |
| `evaluation_summary` | `## 总体评价结果` | 章节内容提取 | 评价摘要 |

##### 3.2 计划评价映射 (plan_evaluations表)

| 数据库字段 | 报告源字段 | 提取方式 | 示例值 |
|-----------|-----------|---------|--------|
| `execution_id` | 文件名 | 去除后缀 | `"round_000006_20250904_171233"` |
| `plan_quality_score` | `**测试计划质量评分**:` | 数值解析 | `0.85` |
| `plan_analysis_content` | `## 1. 测试计划质量分析` | 章节内容提取 | 计划质量分析文本 |
| `plan_key_issues` | `然而，存在一些关键问题：` | 列表内容提取 | 问题列表文本 |
| `plan_improvement_suggestions` | `**改进建议**:` | 列表内容提取 | 改进建议文本 |

##### 3.3 执行评价映射 (execution_evaluations表)

| 数据库字段 | 报告源字段 | 提取方式 | 示例值 |
|-----------|-----------|---------|--------|
| `execution_id` | 文件名 | 去除后缀 | `"round_000006_20250904_171233"` |
| `execution_compliance_score` | `**执行符合度评分**:` | 数值解析 | `0.80` |
| `execution_quality_score` | `**执行质量评分**:` | 数值解析 | `0.85` |
| `goal_achievement_score` | `**目标达成度评分**:` | 数值解析 | `0.90` |
| `compliance_analysis_content` | `## 2. 执行符合度分析` | 章节内容提取 | 执行分析文本 |
| `execution_key_issues` | `- **工具调用错误**:` | 列表内容提取 | 执行问题文本 |
| `execution_improvement_suggestions` | 执行部分改进建议 | 列表内容提取 | 执行改进建议 |

**评价数据提取函数**:
```python
def extract_evaluation_data(report_path: str) -> Dict[str, Dict[str, Any]]:
    """从评价报告提取三个评价表的数据"""
    with open(report_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 从文件名提取execution_id
    filename = os.path.basename(report_path)
    execution_id = filename.replace('_report.log', '')
    
    return {
        'comprehensive': extract_comprehensive_evaluation(content, execution_id),
        'plan': extract_plan_evaluation(content, execution_id),
        'execution': extract_execution_evaluation(content, execution_id)
    }

def extract_comprehensive_evaluation(content: str, execution_id: str) -> Dict[str, Any]:
    """提取综合评价数据"""
    return {
        'execution_id': execution_id,
        'evaluation_round': extract_number(content, r'🔢 轮次编号: (\d+)'),
        'analysis_time': parse_datetime(extract_text(content, r'⏰ 分析时间: ([\d-]+ [\d:]+)')),
        'analysis_model': extract_text(content, r'🤖 分析模型: (.+)'),
        'final_success_status': extract_text(content, r'\*\*最终成功状态\*\*: (.+)'),
        'overall_success_score': extract_float(content, r'\*\*成功评分\*\*: ([\d.]+)'),
        'confidence_score': extract_float(content, r'\*\*置信度\*\*: ([\d.]+)'),
        'comprehensive_analysis': extract_section(content, r'## 3\. 综合评价与建议(.+?)(?=\n={40,}|$)'),
        'evaluation_summary': extract_section(content, r'## 总体评价结果(.+?)(?=## |$)'),
        'next_steps_recommendations': extract_section(content, r'\*\*改进方向\*\*:(.+?)(?=\n\n|$)'),
        'created_at': datetime.now()
    }

def extract_plan_evaluation(content: str, execution_id: str) -> Dict[str, Any]:
    """提取计划评价数据"""
    return {
        'execution_id': execution_id,
        'plan_quality_score': extract_float(content, r'\*\*测试计划质量评分\*\*: ([\d.]+)'),
        'plan_analysis_content': extract_section(content, r'## 1\. 测试计划质量分析(.+?)(?=## |$)'),
        'plan_key_issues': extract_section(content, r'然而，存在一些关键问题：(.+?)(?=\*\*改进建议\*\*|$)'),
        'plan_improvement_suggestions': extract_section(content, r'1\. 统一工具参数命名规范(.+?)(?=## |$)'),
        'created_at': datetime.now()
    }

def extract_execution_evaluation(content: str, execution_id: str) -> Dict[str, Any]:
    """提取执行评价数据"""
    return {
        'execution_id': execution_id,
        'execution_compliance_score': extract_float(content, r'\*\*执行符合度评分\*\*: ([\d.]+)'),
        'execution_quality_score': extract_float(content, r'\*\*执行质量评分\*\*: ([\d.]+)'),
        'goal_achievement_score': extract_float(content, r'\*\*目标达成度评分\*\*: ([\d.]+)'),
        'compliance_analysis_content': extract_section(content, r'## 2\. 执行符合度分析(.+?)(?=## |$)'),
        'execution_key_issues': extract_section(content, r'- \*\*工具调用错误\*\*:(.+?)(?=\*\*改进建议\*\*|$)'),
        'execution_improvement_suggestions': extract_section(content, r'1\. 建立工具参数校验机制(.+?)(?=## |$)'),
        'created_at': datetime.now()
    }

# 辅助提取函数
def extract_text(content: str, pattern: str) -> str:
    """提取文本"""
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def extract_number(content: str, pattern: str) -> int:
    """提取整数"""
    match = re.search(pattern, content)
    return int(match.group(1)) if match else 0

def extract_float(content: str, pattern: str) -> float:
    """提取浮点数"""
    match = re.search(pattern, content)
    try:
        return float(match.group(1)) if match else 0.0
    except ValueError:
        return 0.0

def extract_section(content: str, pattern: str) -> str:
    """提取章节内容，清理格式"""
    match = re.search(pattern, content, re.DOTALL)
    if match:
        section = match.group(1).strip()
        # 清理多余空行，保持结构
        return re.sub(r'\n\s*\n\s*\n', '\n\n', section)
    return ""

def parse_datetime(datetime_str: str) -> datetime:
    """解析日期时间字符串"""
    try:
        return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        return datetime.now()
```

---

## 技术实现细节

### 错误处理策略

#### 纯文件监控的错误处理
```python
class FileProcessingError(Exception):
    pass

def safe_file_processing(file_path: str) -> Optional[Dict]:
    """纯文件监控的安全处理，包含完整错误处理和去重逻辑"""
    try:
        # 文件存在性检查
        if not os.path.exists(file_path):
            logger.warning(f"监控到文件事件但文件不存在: {file_path}")
            return None
        
        # 文件完整性检查（避免处理正在写入的文件）
        if not is_file_write_complete(file_path):
            logger.info(f"文件写入未完成，延后处理: {file_path}")
            # 加入延迟队列，1秒后重新检查
            schedule_delayed_processing(file_path, delay=1)
            return None
        
        # 检查是否已处理过此文件（基于时间戳文件名）
        file_name = os.path.basename(file_path)
        if is_file_already_processed(file_name):
            logger.debug(f"文件已处理过，跳过: {file_name}")
            return None
        
        # 解析文件内容
        parsed_data = parse_file_content(file_path)
        
        # 标记文件已处理（仅记录文件名）
        mark_file_as_processed(file_name)
        
        return parsed_data
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败: {file_path}, 错误: {e}")
        # 移动到错误文件夹等待人工检查
        move_to_error_folder(file_path, f"json_parse_error_{int(time.time())}")
        return None
        
    except FileProcessingError as e:
        logger.warning(f"文件处理警告: {file_path}, 原因: {e}")
        # 标记为稍后重试
        mark_for_retry(file_path, str(e))
        return None
        
    except Exception as e:
        logger.error(f"文件处理异常: {file_path}, 错误: {e}")
        # 记录异常但不移动文件，等待下次监控事件重试
        record_processing_failure(file_path, str(e))
        return None

def is_file_write_complete(file_path: str, stable_time: float = 0.5) -> bool:
    """检查文件写入是否完成（文件大小在指定时间内保持稳定）"""
    try:
        initial_size = os.path.getsize(file_path)
        time.sleep(stable_time)
        final_size = os.path.getsize(file_path)
        return initial_size == final_size
    except OSError:
        return False
```

#### 数据库操作错误处理
```python
def safe_database_operation(operation_func, *args, **kwargs) -> bool:
    """安全的数据库操作，包含重试和回滚机制"""
    max_retries = 3
    retry_delay = 5
    
    for attempt in range(max_retries):
        try:
            with database_transaction():
                result = operation_func(*args, **kwargs)
                return True
                
        except DatabaseConnectionError:
            logger.warning(f"数据库连接失败，第{attempt+1}次重试")
            time.sleep(retry_delay)
            
        except IntegrityError as e:
            logger.warning(f"数据完整性错误: {e}")
            # 可能是重复数据，检查并处理
            if handle_duplicate_data(*args, **kwargs):
                return True
            break
            
        except Exception as e:
            logger.error(f"数据库操作异常: {e}")
            if attempt == max_retries - 1:
                # 最后一次尝试失败，记录到失败队列
                add_to_failed_queue(operation_func, args, kwargs, str(e))
            break
    
    return False
```

### 性能优化策略

#### 批量处理优化
- 实现文件批量解析，减少I/O操作
- 使用数据库批量插入，提高写入效率
- 实现内存缓冲，避免频繁数据库连接

#### 并发处理优化
- 使用线程池处理不同类型文件
- 实现异步I/O减少阻塞
- 添加队列限流避免系统过载

#### 资源使用优化
- 实现文件内容分块读取，降低内存占用
- 添加LRU缓存减少重复计算
- 定期清理临时文件和内存缓存

---

## 配置文件

### 纯文件监控配置文件
**文件**: `simplify_agent/config/log_server.json`
```json
{
    "service": {
        "name": "SimplifyAgent-PureFileMonitor",
        "version": "1.0.0",
        "pid_file": "/tmp/log_server.pid",
        "log_level": "INFO",
        "architecture": "pure_file_monitoring"
    },
    "monitoring": {
        "watch_paths": {
            "json_plan": {
                "path": "simplify_agent/log/json_plan",
                "pattern": "plan_mlx_*.json",
                "max_files": 20,
                "recursive": false
            },
            "agent_execute": {
                "path": "simplify_agent/log/agent_execute_log",
                "pattern": "round_*",
                "max_files": 20,
                "recursive": true
            },
            "judge_report": {
                "path": "simplify_agent/log/judge_report", 
                "pattern": "*_report.log",
                "max_files": 20,
                "recursive": false
            }
        },
        "polling_interval": 1.0,
        "batch_size": 10
    },
    "database": {
        "connection_pool_size": 5,
        "transaction_timeout": 30,
        "retry_attempts": 3,
        "retry_delay": 5
    },
    "cleanup": {
        "enable_auto_cleanup": true,
        "cleanup_interval": 3600,
        "preserve_hours": {
            "json_plan": 24,
            "agent_execute": 48, 
            "judge_report": 12
        }
    },
    "health_check": {
        "enable": true,
        "port": 8080,
        "endpoint": "/health"
    }
}
```

---

## 部署和运维

### 服务部署脚本
**文件**: `simplify_agent/scripts/deploy_log_server.sh`
```bash
#!/bin/bash
# SimplifyAgent 日志服务器部署脚本

set -e

echo "🚀 开始部署 SimplifyAgent 日志服务器..."

# 检查Python环境
python3 --version || (echo "❌ Python 3 未安装" && exit 1)

# 安装依赖
echo "📦 安装依赖包..."
pip3 install watchdog schedule requests

# 检查数据库连接
echo "🔍 检查数据库连接..."
python3 -c "
from simplify_agent.database.db_connection import DatabaseConnection
try:
    db = DatabaseConnection()
    db.get_connection()
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"

# 创建必要的目录
mkdir -p simplify_agent/log/{json_plan,agent_execute_log,judge_report}
mkdir -p /tmp/log_server_backup

# 设置权限
chmod +x simplify_agent/scripts/*.sh

# 创建systemd服务（可选）
if command -v systemctl >/dev/null 2>&1; then
    echo "📋 创建 systemd 服务..."
    cat > /etc/systemd/system/simplify-log-server.service << EOF
[Unit]
Description=SimplifyAgent Log Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
ExecStart=$(which python3) -m simplify_agent.log_server
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    echo "✅ systemd 服务已创建，使用以下命令管理："
    echo "   启动: sudo systemctl start simplify-log-server"
    echo "   停止: sudo systemctl stop simplify-log-server" 
    echo "   开机启动: sudo systemctl enable simplify-log-server"
fi

echo "🎉 部署完成！"
echo "使用以下命令启动服务:"
echo "  python3 -m simplify_agent.log_server"
```

### 服务管理脚本
**文件**: `simplify_agent/scripts/manage_log_server.sh`
```bash
#!/bin/bash
# 服务管理脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PID_FILE="/tmp/log_server.pid"

case "$1" in
    start)
        if [ -f "$PID_FILE" ]; then
            echo "⚠️  服务可能已在运行 (PID: $(cat $PID_FILE))"
            exit 1
        fi
        echo "🚀 启动日志服务器..."
        cd "$PROJECT_DIR/.."
        nohup python3 -m simplify_agent.log_server > /tmp/log_server.out 2>&1 &
        echo $! > "$PID_FILE"
        echo "✅ 服务已启动 (PID: $!)"
        ;;
    stop)
        if [ ! -f "$PID_FILE" ]; then
            echo "⚠️  服务未运行"
            exit 1
        fi
        PID=$(cat "$PID_FILE")
        echo "🛑 停止服务 (PID: $PID)..."
        kill -TERM "$PID"
        rm -f "$PID_FILE"
        echo "✅ 服务已停止"
        ;;
    status)
        if [ -f "$PID_FILE" ]; then
            PID=$(cat "$PID_FILE")
            if kill -0 "$PID" 2>/dev/null; then
                echo "✅ 服务运行中 (PID: $PID)"
                # 显示健康检查信息
                curl -s http://localhost:8080/health | python3 -m json.tool || true
            else
                echo "❌ 服务未运行 (但PID文件存在)"
                rm -f "$PID_FILE"
            fi
        else
            echo "❌ 服务未运行"
        fi
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    logs)
        tail -f /tmp/log_server.out
        ;;
    *)
        echo "使用方法: $0 {start|stop|status|restart|logs}"
        exit 1
        ;;
esac
```

---

## 开发约定

### 代码规范
- 使用 Python 3.8+ 类型提示
- 遵循 PEP 8 代码风格
- 使用 dataclass 定义数据结构
- 异步操作使用 asyncio 模式
- 所有公共方法添加详细文档字符串

### 日志规范
- 使用结构化日志格式（JSON）
- 包含必要的上下文信息（文件路径、操作类型、耗时等）
- 区分不同级别的日志（DEBUG、INFO、WARNING、ERROR）
- 敏感信息脱敏处理

### 测试规范
- 每个核心模块都要有对应的单元测试
- 集成测试覆盖端到端数据流
- 使用模拟数据进行测试，避免依赖实际文件
- 性能测试验证处理大量文件的能力

### 错误处理规范
- 所有异常都要有明确的处理策略
- 关键操作要有重试机制
- 失败的操作要有详细的日志记录
- 提供人工介入的接口和工具

---

## 进度跟踪

### 完成标记说明
- [x] 已完成
- [ ] 未完成  
- [~] 进行中
- [!] 需要注意或修改

### 纯文件监控方案的里程碑计划
- **第1周**: 完成阶段一和阶段二 (核心框架 + 文件监控系统)
- **第2周**: 完成阶段三 (三个数据解析引擎)
- **第3周**: 完成阶段四和阶段五 (数据存储集成 + 文件清理管理)
- **第4周**: 完成阶段六和阶段七 (监控运维 + 测试部署)

### 纯文件监控开发计划总结

本开发计划基于纯文件监控方案，详细规划了SimplifyAgent日志数据自动化处理服务的完整实现路径。通过分阶段开发，将构建一个完全解耦、性能优良、运维友好的日志监控和数据处理系统。

**纯文件监控方案的核心特性**：
- ✅ 24小时智能文件监控，零业务入侵
- ✅ 完全解耦的架构，业务模块无感知数据处理  
- ✅ 基于时间戳文件名的简单去重机制，性能优异
- ✅ 智能文件写入完成检测，避免处理不完整文件
- ✅ 自动化文件清理，维持20个文件的存储上限
- ✅ 完善的错误处理和重试机制
- ✅ 健康检查和监控告警

**纯方案的技术优势**：
- 采用事件驱动的文件监控，响应性极高
- 业务模块专注核心逻辑，数据处理完全透明
- 支持历史数据的批量处理和增量同步
- 提供完整的故障恢复和数据一致性保证
- 独立部署和扩展，不影响现有业务流程

---

*最后更新: 2025-09-04*
*当前阶段: 需求分析和架构设计 [已完成]*
*下一阶段: 阶段一 - 核心框架搭建*