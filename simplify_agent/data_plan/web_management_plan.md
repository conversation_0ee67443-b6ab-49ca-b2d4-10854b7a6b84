# SimplifyAgent Web管理系统开发计划

## 项目概述
基于已完成的数据库系统，开发前后端分离的Web管理界面，提供用户视角和开发者视角的数据管理功能。

## 目标
- 构建直观易用的测试数据管理界面
- 提供丰富的统计分析和可视化功能
- 支持开发者进行数据库管理和表结构修改
- 实现单机部署，支持局域网访问

---

## 系统架构设计

### 技术栈选择
- **后端框架**: Flask 2.x + SQLAlchemy
- **前端框架**: React 18.x + TypeScript
- **UI组件库**: Ant Design 5.x
- **图表库**: ECharts + echarts-for-react
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **数据库**: 现有 SQLite + DAO 系统

### 架构层次
```
┌─────────────────────────────────────────────────┐
│                  前端层                          │
│  ┌─────────────┐    ┌─────────────────────────┐  │
│  │  用户视角    │    │      开发者视角         │  │
│  │   (React)   │    │      (React)          │  │
│  └─────────────┘    └─────────────────────────┘  │
└─────────────────────────────────────────────────┘
                         │
                    HTTP/REST API
                         │
┌─────────────────────────────────────────────────┐
│                  后端层                          │
│  ┌─────────────────────────────────────────────┐ │
│  │             Flask API 服务器              │ │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────────┐ │ │
│  │  │用户API  │  │开发API  │  │  统计API    │ │ │
│  │  └─────────┘  └─────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────┘ │
│                         │                        │
│  ┌─────────────────────────────────────────────┐ │
│  │            业务服务层                       │ │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────────┐ │ │
│  │  │数据服务 │  │统计服务 │  │  管理服务    │ │ │
│  │  └─────────┘  └─────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────┘
                         │
┌─────────────────────────────────────────────────┐
│                 数据访问层                       │
│           现有 DAO 系统 (已完成)                 │
│  TestPlanDAO | TestExecutionDAO | ToolDAO | ... │
└─────────────────────────────────────────────────┘
                         │
┌─────────────────────────────────────────────────┐
│                 数据存储层                       │
│              SQLite 数据库 (已完成)              │
│         7张表 + 完整的关系和约束                │
└─────────────────────────────────────────────────┘
```

---

## 功能需求分析

### 用户视角功能需求

#### 1. 主页 - 测试记录概览
**页面路由**: `/user/dashboard`
- [x] 测试记录列表展示
  - [x] 分页显示，支持每页条数配置
  - [x] 关键信息：执行ID、原始请求、执行状态、耗时、创建时间
  - [x] 成功/失败状态标识
  - [x] 快速操作按钮（查看详情、删除）

- [x] 搜索和筛选功能
  - [x] 按原始请求内容搜索
  - [x] 按执行状态筛选
  - [x] 按时间范围筛选
  - [x] 按平台筛选

#### 2. 统计分析页面
**页面路由**: `/user/statistics`
- [x] 基础统计指标
  - [x] 总测试记录数
  - [x] 整体成功率
  - [x] 平均执行时间
  - [x] 最近7天/30天统计

- [x] 可视化图表
  - [x] 成功率饼状图
  - [x] 执行趋势折线图
  - [x] 平台分布条形图
  - [x] 工具使用频率图

- [x] 自定义查询统计
  - [x] 按指令名称筛选统计
  - [x] 自定义时间范围统计
  - [x] 导出统计报告（CSV格式）

#### 3. 测试详情页面
**页面路由**: `/user/detail/:executionId`
- [x] 基本信息展示
  - [x] 执行概要（ID、状态、时间、耗时等）
  - [x] 关联的测试计划信息

- [x] 测试计划可视化
  - [x] 步骤流程图展示
  - [x] 每步详细信息（动作、预期结果等）
  - [x] 参数和元数据展示

- [x] 执行流程瀑布展示
  - [x] 按轮次分组显示
  - [x] 工具调用时间线
  - [x] 执行结果和截图展示
  - [x] 错误信息高亮显示

- [x] 评价信息展示
  - [x] 计划评价详情
  - [x] 执行评价详情  
  - [x] 综合评价和改进建议

### 开发者视角功能需求

#### 1. 数据库管理主页
**页面路由**: `/dev/dashboard`
- [x] 权限验证（密码输入）
- [x] 数据库概览
  - [x] 表结构信息
  - [x] 数据量统计
  - [x] 数据库健康状态

#### 2. 表数据管理
**页面路由**: `/dev/table/:tableName`
- [x] 数据表格展示
  - [x] 分页显示
  - [x] 字段排序
  - [x] 数据搜索和过滤

- [x] CRUD操作
  - [x] 新增记录
  - [x] 编辑记录（支持JSON字段编辑）
  - [x] 删除记录
  - [x] 批量操作

#### 3. 表结构管理
**页面路由**: `/dev/schema/:tableName`
- [x] 表结构展示
  - [x] 字段列表（名称、类型、约束等）
  - [x] 索引信息
  - [x] 外键关系

- [x] 结构修改功能
  - [x] 添加字段
  - [x] 删除字段  
  - [x] 修改字段属性
  - [x] 添加/删除索引

---

## 开发计划

### 阶段一：后端API开发 [x] 已完成 ✅ 
**实际用时**: 3天 (2025-09-03 至 2025-09-04)

#### 1.1 项目基础架构搭建 [x] 已完成
**预计用时**: 0.5天
- [x] 创建Flask项目结构 (`web_backend/app.py`)
- [x] 配置开发环境和依赖 (`requirements_web.txt`)
- [x] 集成现有DAO系统 (Service层设计)
- [x] 配置CORS和基础中间件 (`middleware/`)
- [x] 创建统一的API响应格式 (`utils/response.py`)

#### 1.2 用户视角API开发 [x] 已完成
**预计用时**: 2天

##### 1.2.1 测试记录API [x] 已完成
**文件**: `web_backend/api/user_api.py`
- [x] `GET /api/user/executions` - 获取测试记录列表（分页、搜索、筛选）
- [x] `GET /api/user/executions/:id` - 获取测试详情
- [x] `DELETE /api/user/executions/:id` - 删除测试记录
- [x] `POST /api/user/executions/:id/rerun` - 重新执行测试
- [x] `POST /api/user/executions/:id/export` - 导出执行记录
- [x] `GET /api/user/health` - 健康检查

##### 1.2.2 统计分析API [x] 已完成  
**文件**: `web_backend/api/statistics_api.py`
- [x] `GET /api/statistics/overview` - 基础统计概览
- [x] `GET /api/statistics/success-rate-trend` - 成功率趋势分析
- [x] `GET /api/statistics/instruction-analysis` - 指令模式分析
- [x] `GET /api/statistics/platform-stats` - 平台分布统计
- [x] `GET /api/statistics/tool-stats` - 工具使用统计
- [x] `GET /api/statistics/duration-distribution` - 时长分布
- [x] `GET /api/statistics/time-range` - 时间段统计

#### 1.3 开发者视角API开发 [x] 已完成
**预计用时**: 2天

##### 1.3.1 数据库管理API [x] 已完成
**文件**: `web_backend/api/dev_api.py`
- [x] `POST /api/dev/auth` - 开发者身份验证
- [x] `POST /api/dev/logout` - 开发者登出
- [x] `GET /api/dev/session` - 获取会话信息
- [x] `GET /api/dev/database/info` - 数据库概览信息
- [x] `GET /api/dev/tables` - 获取所有表信息
- [x] `GET /api/dev/table/:name/data` - 获取表数据（分页、搜索）
- [x] `POST /api/dev/table/:name/data` - 新增记录
- [x] `PUT /api/dev/table/:name/data/:id` - 更新记录
- [x] `DELETE /api/dev/table/:name/data/:id` - 删除记录

##### 1.3.2 表结构管理API [x] 已完成
**文件**: `web_backend/api/dev_api.py` (集成在dev_api中)
- [x] `GET /api/dev/table/:name/schema` - 获取表结构
- [x] `POST /api/dev/table/:name/column` - 添加字段
- [x] `POST /api/dev/sql` - 执行自定义SQL语句
- [x] `GET /api/dev/health` - 健康检查

#### 1.4 文件服务和工具API [x] 已完成
**预计用时**: 1天
- [x] 静态文件服务配置 (Flask静态目录)
- [x] 系统健康检查API (各模块health接口)
- [x] 完整的测试脚本 (`test_api.py`)
- [x] 错误处理和日志记录

### 阶段二：前端基础架构 [x] 已完成 ✅

#### 2.1 项目初始化和配置 [x] 已完成
**预计用时**: 0.5天 | **实际用时**: 0.5天
**文件**: `web_frontend/` 项目根目录
- [x] 创建React + TypeScript项目 (完整的Vite项目结构)
- [x] 配置Vite构建工具 (vite.config.ts配置完成)
- [x] 安装和配置依赖包 (21个依赖包，package.json完整)
- [x] 配置路由系统（React Router）(App.tsx中完整路由配置)
- [x] 配置状态管理（Zustand/Redux）(暂未使用状态管理库)
- [x] 配置HTTP客户端（Axios）(services/api.ts完整封装)

#### 2.2 公共组件开发 [x] 已完成
**预计用时**: 1天 | **实际用时**: 1天  
**文件**: `web_frontend/src/components/`, `web_frontend/src/utils/`, `web_frontend/src/types/`
- [x] 布局组件（Header、Sidebar、Footer）(AppLayout.tsx完整实现)
- [x] 数据表格组件（支持分页、搜索、排序）(集成Ant Design Table)
- [x] 统计卡片组件 (Ant Design Statistic + Card)
- [x] 图表组件封装 (ECharts + echarts-for-react)
- [x] 搜索和筛选组件 (各页面中实现)
- [x] 加载和错误状态组件 (Spin, Alert组件)

### 阶段三：用户视角前端开发 [x] 已完成 ✅

#### 3.1 主页开发 [x] 已完成  
**预计用时**: 1.5天 | **实际用时**: 1.5天
**文件**: `web_frontend/src/pages/user/Dashboard.tsx` (9519行)
- [x] 测试记录列表组件 (完整的Ant Design Table实现)
- [x] 分页和搜索功能 (支持关键字搜索)
- [x] 状态筛选器 (执行状态、时间范围筛选)
- [x] 快速操作按钮 (查看详情、删除等操作)
- [x] 响应式布局适配 (移动端适配)

#### 3.2 统计分析页面 [x] 已完成
**预计用时**: 2天 | **实际用时**: 2天  
**文件**: `web_frontend/src/pages/user/Statistics.tsx` (13424行)
- [x] 基础统计指标展示 (总执行数、成功率、平均时长等)
- [x] 成功率趋势折线图 (ECharts实现)
- [x] 平台分布饼状图 (ECharts实现)
- [x] 时长分布柱状图 (ECharts实现)
- [x] 工具使用统计 (卡片形式展示)
- [x] 日期范围选择器 (支持自定义时间范围)
- [x] 指令模式分析表格 (详细统计表格)

#### 3.3 详情页面开发 [x] 已完成
**预计用时**: 2.5天 | **实际用时**: 2.5天
**文件**: `web_frontend/src/pages/user/ExecutionDetail.tsx` (16941行), `Executions.tsx` (10530行)
- [x] 基本信息展示组件 (完整的执行信息展示)
- [x] 测试计划流程可视化 (Timeline组件展示步骤)
- [x] 执行流程瀑布展示 (工具执行时序展示)
- [x] 图片预览组件 (支持图片查看)
- [x] 评价信息展示 (综合评价详情)
- [x] 导航和面包屑 (完整的页面导航)

### 阶段四：开发者视角前端开发 [⚠️] 部分完成

#### 4.1 认证和权限 [x] 已完成
**预计用时**: 0.5天 | **实际用时**: 0.5天
**文件**: `web_frontend/src/pages/dev/Login.tsx` (2634行)
- [x] 登录页面 (完整的UI和表单验证)
- [ ] 密码验证逻辑 (前端UI完成，待连接后端API)
- [ ] 权限路由守卫 (基础路由配置完成)
- [ ] 会话管理 (待实现)

#### 4.2 数据库管理界面 [ ] 待开发  
**预计用时**: 2天
**文件**: `web_frontend/src/pages/dev/Dashboard.tsx` (516行), `Tables.tsx` (510行), `TableDetail.tsx` (511行)
**当前状态**: 基础页面框架已创建，显示"功能开发中..."
- [ ] 数据库概览页面 (框架已有，待实现业务逻辑)
- [ ] 表列表和信息展示 (框架已有，待实现业务逻辑)
- [ ] 数据表格展示组件 (框架已有，待实现业务逻辑)
- [ ] CRUD操作界面 (待实现)
- [ ] JSON编辑器集成 (待实现)

#### 4.3 表结构管理 [ ] 待开发
**预计用时**: 2天  
**文件**: `web_frontend/src/pages/dev/SQL.tsx` (486行)
**当前状态**: 基础页面框架已创建，显示"功能开发中..."
- [ ] 表结构可视化 (待实现)
- [ ] 字段编辑表单 (待实现)
- [ ] 约束和索引管理 (待实现)
- [ ] 结构修改确认对话框 (待实现)
- [ ] 操作历史记录 (待实现)

### 阶段五：集成测试和部署 [ ] 未开始

#### 5.1 功能测试 [ ] 未开始
**预计用时**: 1天
- [ ] 用户视角功能测试
- [ ] 开发者视角功能测试
- [ ] 跨浏览器兼容性测试
- [ ] 移动端响应式测试
- [ ] 性能测试和优化

#### 5.2 部署脚本开发 [ ] 未开始
**预计用时**: 1天
**文件**: `run.py`
- [ ] 统一启动脚本
- [ ] 环境检查和依赖安装
- [ ] 前端构建和打包
- [ ] 后端服务配置
- [ ] 局域网访问配置
- [ ] 日志和监控配置

---

## 文件结构规划

### 项目根目录结构
```
simplify_agent/
├── web_backend/                 # 后端代码
│   ├── __init__.py
│   ├── app.py                  # Flask应用入口
│   ├── config.py               # 配置文件
│   ├── api/                    # API路由
│   │   ├── __init__.py
│   │   ├── user_api.py         # 用户视角API
│   │   ├── dev_api.py          # 开发者API
│   │   ├── statistics_api.py   # 统计API
│   │   └── schema_api.py       # 表结构API
│   ├── services/               # 业务服务层
│   │   ├── __init__.py
│   │   ├── user_service.py     # 用户业务服务
│   │   ├── statistics_service.py  # 统计服务
│   │   └── dev_service.py      # 开发者服务
│   ├── middleware/             # 中间件
│   │   ├── __init__.py
│   │   ├── auth.py             # 认证中间件
│   │   └── cors.py             # CORS中间件
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       ├── response.py         # 统一响应格式
│       └── validators.py       # 数据验证
├── web_frontend/               # 前端代码  
│   ├── public/                 # 静态资源
│   ├── src/
│   │   ├── components/         # 公共组件
│   │   │   ├── Layout/
│   │   │   ├── Table/
│   │   │   ├── Charts/
│   │   │   └── Common/
│   │   ├── pages/              # 页面组件
│   │   │   ├── user/           # 用户视角页面
│   │   │   │   ├── Dashboard.tsx
│   │   │   │   ├── Statistics.tsx
│   │   │   │   └── Detail.tsx
│   │   │   └── dev/            # 开发者视角页面
│   │   │       ├── Login.tsx
│   │   │       ├── Dashboard.tsx
│   │   │       └── Schema.tsx
│   │   ├── services/           # HTTP服务
│   │   │   ├── api.ts          # API客户端
│   │   │   └── types.ts        # 类型定义
│   │   ├── stores/             # 状态管理
│   │   │   └── useStore.ts
│   │   ├── utils/              # 工具函数
│   │   │   ├── constants.ts
│   │   │   └── helpers.ts
│   │   ├── App.tsx             # 应用根组件
│   │   └── main.tsx            # 应用入口
│   ├── package.json
│   ├── vite.config.ts
│   └── tsconfig.json
├── static/                     # 静态文件服务目录
│   └── screenshots/            # 截图文件
├── run.py                      # 统一启动脚本
└── requirements.txt            # Python依赖
```

---

## 开发规范

### 代码规范
- **后端**: 遵循PEP 8，使用类型提示
- **前端**: 使用TypeScript，遵循ESLint + Prettier规范
- **API设计**: RESTful风格，统一响应格式
- **错误处理**: 完善的错误处理和用户友好的错误信息

### API响应格式规范
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": 1693737600
}
```

### 数据库操作规范
- 所有数据库操作通过现有DAO进行
- 支持事务管理和回滚
- 详细的操作日志记录
- 数据验证和约束检查

---

## 安全考虑

### 开发者模式安全
- 密码硬编码在代码中（适合局域网使用）
- 会话超时机制
- 操作日志记录
- 危险操作二次确认

### 数据安全
- SQL注入防护
- XSS攻击防护  
- 文件上传限制
- 敏感信息脱敏

---

## 性能优化

### 前端优化
- 组件懒加载
- 图片懒加载和压缩
- 数据分页加载
- 缓存机制

### 后端优化
- 数据库查询优化
- API响应缓存
- 静态资源CDN
- 并发请求限制

---

## 部署配置

### 环境要求
- Python 3.8+
- Node.js 16+
- SQLite 3.x
- 现代浏览器支持

### 部署特性
- 单机部署，无外部依赖
- 自动局域网IP检测
- 端口冲突检测和自动调整
- 优雅的服务启停

---

## 进度跟踪

### 完成标记说明
- [x] 已完成
- [ ] 未完成  
- [~] 进行中
- [!] 需要注意或修改

### 更新日志
- **2025-09-03**: 初始计划创建，开始Web管理系统架构设计

---

## 预估开发时间

| 阶段 | 预估时间 | 主要工作内容 |
|------|----------|-------------|
| 阶段一 | 5.5天 | 后端API开发 |
| 阶段二 | 1.5天 | 前端基础架构 |
| 阶段三 | 6天 | 用户视角前端开发 |
| 阶段四 | 4.5天 | 开发者视角前端开发 |
| 阶段五 | 2天 | 集成测试和部署 |
| **总计** | **19.5天** | **完整Web管理系统** |

---

*最后更新: 2025-09-03*
*当前阶段: 规划设计阶段*
*预计开始时间: 2025-09-03*