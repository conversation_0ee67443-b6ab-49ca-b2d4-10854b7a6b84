#!/usr/bin/env python3
"""
SimplifyAgent 日志数据自动化处理服务 - 主服务器

24小时运行的纯文件监控服务，自动监控三个核心模块的日志输出，
解析数据并存储到数据库中。支持优雅启停、健康检查和错误恢复。

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
版本: 1.0.0
"""

import asyncio
import signal
import sys
import os
import time
import threading
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import json
import argparse

# 添加项目根路径到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from config import get_config, LogServerConfig
from server_logger import get_logger, PerformanceTimer


class LogServerManager:
    """日志服务器主管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化日志服务器"""
        # 确保使用正确的基础路径（log_server.py 所在的 simplify_agent 目录）
        base_path = os.path.dirname(__file__)
        self.config = get_config(config_file, base_path)
        self.logger = get_logger('log_server', 'main')
        
        # 核心状态
        self.is_running = False
        self.is_shutting_down = False
        self.start_time = None
        
        # 模块实例
        self.file_watchers = {}
        self.data_parser = None
        self.event_queue = None
        self.state_manager = None
        
        # 监控线程
        self.worker_threads = {}
        self.health_check_thread = None
        
        # 性能统计
        self.stats = {
            'processed_files': 0,
            'failed_files': 0,
            'total_processing_time': 0.0,
            'uptime_start': None
        }
        
        self._setup_signal_handlers()
        self._create_pid_file()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            self.logger.info(f"收到信号 {signal_name}，开始优雅关闭...")
            # 设置关闭标志，让主循环自然退出
            self.is_shutting_down = True
            self.is_running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)
    
    def _create_pid_file(self):
        """创建PID文件"""
        pid_file = self.config.get('monitoring.pid_file')
        try:
            with open(pid_file, 'w') as f:
                f.write(str(os.getpid()))
            self.logger.debug(f"PID文件已创建: {pid_file}")
        except Exception as e:
            self.logger.warning(f"创建PID文件失败: {e}")
    
    def _remove_pid_file(self):
        """删除PID文件"""
        pid_file = self.config.get('monitoring.pid_file')
        try:
            if os.path.exists(pid_file):
                os.remove(pid_file)
            self.logger.debug("PID文件已删除")
        except Exception as e:
            self.logger.warning(f"删除PID文件失败: {e}")
    
    async def initialize(self):
        """初始化所有组件"""
        self.logger.info("开始初始化日志服务器组件...")
        
        try:
            # 验证配置
            is_valid, errors = self.config.validate_config()
            if not is_valid:
                for error in errors:
                    self.logger.error(f"配置错误: {error}")
                raise RuntimeError("配置验证失败")
            
            # 确保目录存在
            self.config.ensure_directories()
            
            # 初始化各模块（动态导入以避免循环依赖）
            await self._initialize_data_parser()
            await self._initialize_event_queue()
            await self._initialize_state_manager()  # 在文件监控器之前初始化状态管理器
            await self._initialize_file_watchers()
            
            # 初始化统计信息
            self.stats['uptime_start'] = datetime.now()
            
            self.logger.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.critical(f"组件初始化失败: {e}")
            return False
    
    async def _initialize_data_parser(self):
        """初始化数据解析器"""
        try:
            from parsers.data_parser import DataParser
            self.data_parser = DataParser(self.config)
            self.logger.info("数据解析器初始化完成")
        except ImportError as e:
            self.logger.error(f"数据解析器导入失败: {e}")
            raise
    
    async def _initialize_event_queue(self):
        """初始化事件队列"""
        try:
            from monitors.event_queue import EventQueue
            self.event_queue = EventQueue(
                self.config,
                self.data_parser,
                batch_size=self.config.get('processing.batch_size', 10)
            )
            self.logger.info("事件队列初始化完成")
        except ImportError as e:
            self.logger.error(f"事件队列导入失败: {e}")
            raise
    
    async def _initialize_state_manager(self):
        """初始化简化的文件状态管理器"""
        try:
            from monitors.file_state_manager import SimplifiedFileStateManager
            self.state_manager = SimplifiedFileStateManager(self.config)
            
            # 记录启动时存在的文件（不处理）
            self.state_manager.record_startup_files()
            
            self.logger.info("简化文件状态管理器初始化完成")
        except ImportError as e:
            self.logger.error(f"状态管理器导入失败: {e}")
            raise
    
    async def _initialize_file_watchers(self):
        """初始化文件监控器"""
        try:
            from monitors.file_watcher import FileWatcher
            
            watch_paths = self.config.get_watch_paths()
            for name, path in watch_paths.items():
                watcher = FileWatcher(
                    watch_path=path,
                    event_queue=self.event_queue,
                    watch_type=name,
                    config=self.config,
                    state_manager=self.state_manager  # 传入状态管理器
                )
                self.file_watchers[name] = watcher
                self.logger.info(f"文件监控器 {name} 初始化完成: {path}")
            
        except ImportError as e:
            self.logger.error(f"文件监控器导入失败: {e}")
            raise
    
    
    async def start(self):
        """启动日志服务器"""
        if self.is_running:
            self.logger.warning("日志服务器已经在运行中")
            return
        
        self.logger.info("启动 SimplifyAgent 日志服务器...")
        self.start_time = datetime.now()
        
        try:
            # 初始化组件
            if not await self.initialize():
                raise RuntimeError("组件初始化失败")
            
            # 启动各个组件（注意顺序：先启动事件队列，再启动文件监控器）
            await self._start_event_queue()
            await self._start_file_watchers()
            await self._start_health_monitor()
            
            self.is_running = True
            self.logger.info("日志服务器启动成功")
            
            # 记录系统状态
            self.logger.log_system_status('log_server', 'started', {
                'version': self.config.get('server.version'),
                'watch_paths': len(self.file_watchers),
                'pid': os.getpid()
            })
            
            # 进入主循环
            await self._main_loop()
            
        except Exception as e:
            self.logger.critical(f"日志服务器启动失败: {e}")
            await self.shutdown()
            raise
    
    async def _start_file_watchers(self):
        """启动所有文件监控器"""
        for name, watcher in self.file_watchers.items():
            try:
                await watcher.start()
                self.logger.info(f"文件监控器 {name} 启动成功")
            except Exception as e:
                self.logger.error(f"文件监控器 {name} 启动失败: {e}")
                raise
    
    async def _start_event_queue(self):
        """启动事件队列处理器"""
        try:
            await self.event_queue.start()
            self.logger.info("事件队列处理器启动成功")
        except Exception as e:
            self.logger.error(f"事件队列启动失败: {e}")
            raise
    
    
    async def _start_health_monitor(self):
        """启动健康监控"""
        async def health_monitor():
            interval = self.config.get('server.health_check_interval', 30)
            while self.is_running and not self.is_shutting_down:
                try:
                    await self._perform_health_check()
                    await asyncio.sleep(interval)
                except Exception as e:
                    self.logger.error(f"健康检查失败: {e}")
                    await asyncio.sleep(interval)
        
        self.health_check_thread = asyncio.create_task(health_monitor())
        self.logger.info("健康监控启动成功")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        with PerformanceTimer(self.logger, "health_check"):
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': (datetime.now() - self.stats['uptime_start']).total_seconds(),
                'processed_files': self.stats['processed_files'],
                'failed_files': self.stats['failed_files'],
                'file_watchers': {},
                'event_queue_size': 0,
                'memory_usage_mb': 0
            }
            
            # 检查文件监控器状态
            for name, watcher in self.file_watchers.items():
                health_status['file_watchers'][name] = {
                    'is_running': watcher.is_running,
                    'files_watched': watcher.get_watched_files_count(),
                    'last_event_time': watcher.get_last_event_time()
                }
            
            # 检查事件队列
            if self.event_queue:
                health_status['event_queue_size'] = self.event_queue.get_queue_size()
            
            # 记录健康状态
            self.logger.log_system_status('health_check', 'completed', health_status)
            
            # 保存状态文件
            status_file = self.config.get('monitoring.status_file')
            try:
                with open(status_file, 'w', encoding='utf-8') as f:
                    json.dump(health_status, f, indent=2, ensure_ascii=False)
            except Exception as e:
                self.logger.warning(f"保存状态文件失败: {e}")
    
    async def _main_loop(self):
        """主事件循环"""
        self.logger.info("进入主事件循环")
        
        try:
            while self.is_running and not self.is_shutting_down:
                # 主循环主要负责协调和监控
                await asyncio.sleep(1.0)
                
                # 定期更新统计信息
                if self.event_queue:
                    queue_size = self.event_queue.get_queue_size()
                    if queue_size > 50:  # 队列积压警告
                        self.logger.warning(f"事件队列积压: {queue_size} 个待处理事件")
                
        except asyncio.CancelledError:
            self.logger.info("主循环被取消")
        except Exception as e:
            self.logger.error(f"主循环异常: {e}")
        finally:
            self.logger.info("主循环结束")
            
        # 主循环结束后，启动关闭流程
        await self.shutdown()
    
    async def shutdown(self):
        """优雅关闭服务器"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        self.logger.info("开始优雅关闭日志服务器...")
        
        try:
            # 停止健康监控
            if self.health_check_thread:
                self.health_check_thread.cancel()
                try:
                    await self.health_check_thread
                except asyncio.CancelledError:
                    pass
            
            # 停止文件监控器
            for name, watcher in self.file_watchers.items():
                try:
                    await watcher.stop()
                    self.logger.info(f"文件监控器 {name} 已停止")
                except Exception as e:
                    self.logger.error(f"停止文件监控器 {name} 失败: {e}")
            
            # 停止事件队列（等待处理完当前任务）
            if self.event_queue:
                await self.event_queue.stop()
                self.logger.info("事件队列已停止")
            
            
            # 记录最终统计
            self._log_final_stats()
            
            # 清理资源
            self._remove_pid_file()
            
            self.is_running = False
            self.logger.info("日志服务器已优雅关闭")
            
        except Exception as e:
            self.logger.error(f"关闭过程中发生错误: {e}")
        finally:
            self.is_shutting_down = False
    
    def _log_final_stats(self):
        """记录最终统计信息"""
        uptime = (datetime.now() - self.stats['uptime_start']).total_seconds()
        
        final_stats = {
            'uptime_seconds': uptime,
            'uptime_human': f"{uptime/3600:.1f}小时",
            'processed_files': self.stats['processed_files'],
            'failed_files': self.stats['failed_files'],
            'success_rate': f"{(self.stats['processed_files'] / max(self.stats['processed_files'] + self.stats['failed_files'], 1) * 100):.1f}%",
            'avg_processing_time': f"{self.stats['total_processing_time'] / max(self.stats['processed_files'], 1):.3f}秒"
        }
        
        self.logger.log_system_status('shutdown', 'completed', final_stats)
    
    def update_stats(self, processed: bool, processing_time: float = 0):
        """更新统计信息"""
        if processed:
            self.stats['processed_files'] += 1
            self.stats['total_processing_time'] += processing_time
        else:
            self.stats['failed_files'] += 1
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        return {
            'is_running': self.is_running,
            'is_shutting_down': self.is_shutting_down,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'uptime_seconds': (datetime.now() - self.stats['uptime_start']).total_seconds() if self.stats['uptime_start'] else 0,
            'stats': self.stats.copy(),
            'config_version': self.config.get('server.version'),
            'pid': os.getpid()
        }


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SimplifyAgent 日志数据自动化处理服务')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--daemon', '-d', action='store_true', help='后台运行')
    parser.add_argument('--status', '-s', action='store_true', help='查看服务状态')
    parser.add_argument('--stop', action='store_true', help='停止服务')
    
    args = parser.parse_args()
    
    # 处理状态查询
    if args.status:
        print_status()
        return
    
    # 处理停止服务
    if args.stop:
        stop_service()
        return
    
    # 启动服务器
    server = LogServerManager(config_file=args.config)
    
    try:
        if args.daemon:
            # 后台运行模式（简化实现）
            server.logger.info("以后台模式启动服务器")
        
        await server.start()
        
    except KeyboardInterrupt:
        server.logger.info("收到键盘中断信号（Ctrl+C）")
    except Exception as e:
        server.logger.critical(f"服务器运行异常: {e}")
    finally:
        # 确保服务器正确关闭（shutdown方法内部有重复执行保护）
        await server.shutdown()


def print_status():
    """打印服务状态"""
    try:
        config = get_config()
        status_file = config.get('monitoring.status_file')
        
        if not os.path.exists(status_file):
            print("❌ 服务器未运行")
            return
        
        with open(status_file, 'r', encoding='utf-8') as f:
            status = json.load(f)
        
        print("=== SimplifyAgent 日志服务器状态 ===")
        print(f"📊 运行时间: {status.get('uptime_seconds', 0)/3600:.1f}小时")
        print(f"📁 处理文件: {status.get('processed_files', 0)}")
        print(f"❌ 失败文件: {status.get('failed_files', 0)}")
        print(f"📋 队列大小: {status.get('event_queue_size', 0)}")
        print(f"🔍 监控路径: {len(status.get('file_watchers', {}))}")
        
        # 显示各监控器状态
        for name, watcher_status in status.get('file_watchers', {}).items():
            status_icon = "✅" if watcher_status.get('is_running') else "❌"
            print(f"  {status_icon} {name}: {watcher_status.get('files_watched', 0)} 个文件")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")


def stop_service():
    """停止服务"""
    try:
        config = get_config()
        pid_file = config.get('monitoring.pid_file')
        
        if not os.path.exists(pid_file):
            print("❌ 服务器未运行")
            return
        
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        os.kill(pid, signal.SIGTERM)
        print(f"✅ 发送停止信号到进程 {pid}")
        
        # 等待服务停止
        for _ in range(30):  # 最多等待30秒
            if not os.path.exists(pid_file):
                print("✅ 服务器已停止")
                return
            time.sleep(1)
        
        print("⚠️ 服务器可能未完全停止")
        
    except Exception as e:
        print(f"❌ 停止服务失败: {e}")


if __name__ == '__main__':
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())