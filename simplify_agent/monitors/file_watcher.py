"""
SimplifyAgent 文件监控模块

基于 watchdog 实现的高性能文件系统监控，支持多路径并发监控、
文件完整性检查、事件过滤和智能去重等功能。

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
"""

import os
import time
import threading
from pathlib import Path
from typing import Optional, Dict, Any, Callable, Tuple
from datetime import datetime, timedelta
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

from server_logger import get_logger, PerformanceTimer

# 导入文件工具函数
import sys
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))
from file_utils import cleanup_old_files, validate_json_file, validate_log_file, validate_file_name_pattern


class FileWatcherHandler(FileSystemEventHandler):
    """文件监控事件处理器"""
    
    def __init__(self, parent: 'FileWatcher'):
        super().__init__()
        self.parent = parent
        self.logger = parent.logger.create_child_logger('handler')
        
        # 事件过滤配置
        self.ignored_patterns = [
            '.DS_Store', '.tmp', '.swp', '~', '.lock',
            '__pycache__', '.pyc', '.git'
        ]
    
    def _should_ignore_file(self, file_path: str) -> bool:
        """检查文件是否应该被忽略（增强文件名模式检查）"""
        file_name = os.path.basename(file_path)
        
        # 检查忽略模式
        for pattern in self.ignored_patterns:
            if pattern in file_name:
                return True
        
        # 根据监控类型检查文件名模式
        watch_type = self.parent.watch_type
        
        if watch_type == 'json_plan':
            # JSON计划文件必须匹配 plan_mlx_* 模式
            if not (file_name.startswith('plan_mlx_') and file_name.endswith('.json')):
                return True
        
        elif watch_type == 'judge_report':
            # 评价报告文件必须匹配 round_*_report.log 模式
            if not (file_name.startswith('round_') and file_name.endswith('_report.log')):
                return True
        
        else:
            # 其他类型使用原有的扩展名检查
            allowed_extensions = ['.json', '.log']
            if not any(file_path.endswith(ext) for ext in allowed_extensions):
                return True
        
        return False
    
    def on_created(self, event: FileSystemEvent):
        """文件创建事件"""
        file_path = event.src_path
        
        # 对于agent_execute类型，我们需要监控目录创建
        if self.parent.watch_type == 'agent_execute':
            if not event.is_directory:
                return  # 只关注目录创建
            
            # 检查是否为执行日志文件夹
            dir_name = os.path.basename(file_path)
            if not dir_name.startswith('round_'):
                return
                
            self.logger.debug(f"检测到执行文件夹创建: {file_path}")
            self.parent._handle_file_event('created', file_path)
        else:
            # 其他类型监控文件创建
            if event.is_directory:
                return
            
            if self._should_ignore_file(file_path):
                return
            
            self.logger.debug(f"检测到文件创建: {file_path}")
            self.parent._handle_file_event('created', file_path)
    
    def on_modified(self, event: FileSystemEvent):
        """文件修改事件"""
        file_path = event.src_path
        
        # 对于agent_execute类型，我们需要监控目录修改
        if self.parent.watch_type == 'agent_execute':
            if not event.is_directory:
                return  # 只关注目录修改
            
            # 检查是否为执行日志文件夹
            dir_name = os.path.basename(file_path)
            if not dir_name.startswith('round_'):
                return
            
            # 避免重复的修改事件
            if not self.parent._should_process_modify_event(file_path):
                return
                
            self.logger.debug(f"检测到执行文件夹修改: {file_path}")
            self.parent._handle_file_event('modified', file_path)
        else:
            # 其他类型监控文件修改
            if event.is_directory:
                return
            
            if self._should_ignore_file(file_path):
                return
            
            # 避免重复的修改事件
            if not self.parent._should_process_modify_event(file_path):
                return
            
            self.logger.debug(f"检测到文件修改: {file_path}")
            self.parent._handle_file_event('modified', file_path)
    
    def on_moved(self, event: FileSystemEvent):
        """文件移动事件"""
        if event.is_directory:
            return
        
        # 处理文件重命名/移动
        self.logger.debug(f"检测到文件移动: {event.src_path} -> {event.dest_path}")


class FileWatcher:
    """文件监控器"""
    
    def __init__(self, watch_path: str, event_queue: 'EventQueue', 
                 watch_type: str, config: 'LogServerConfig', 
                 state_manager: 'FileStateManager' = None):
        """
        初始化文件监控器
        
        Args:
            watch_path: 监控路径
            event_queue: 事件队列
            watch_type: 监控类型 (json_plan, agent_execute, judge_report)
            config: 配置对象
            state_manager: 文件状态管理器
        """
        self.watch_path = Path(watch_path)
        self.event_queue = event_queue
        self.watch_type = watch_type
        self.config = config
        self.state_manager = state_manager
        
        # 创建日志记录器
        self.logger = get_logger('log_server', f'file_watcher.{watch_type}')
        
        # 状态管理
        self.is_running = False
        self.observer = None
        self.handler = None
        
        # 事件去重和防重复处理
        self.processed_files = set()  # 已处理文件集合
        self.last_modify_times = {}   # 文件最后修改时间缓存
        self.event_lock = threading.Lock()
        
        # 延迟处理队列（处理正在写入的文件）
        self.delayed_queue = {}
        self.delay_processor_thread = None
        self.stop_delay_processor = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_events': 0,
            'processed_events': 0,
            'ignored_events': 0,
            'last_event_time': None,
            'watched_files_count': 0
        }
        
        self.logger.info(f"文件监控器初始化: {watch_path} (类型: {watch_type})")
    
    async def start(self):
        """启动文件监控"""
        if self.is_running:
            self.logger.warning("监控器已经在运行")
            return
        
        try:
            # 确保监控路径存在
            self.watch_path.mkdir(parents=True, exist_ok=True)
            
            # 创建事件处理器
            self.handler = FileWatcherHandler(self)
            
            # 创建观察者
            self.observer = Observer()
            self.observer.schedule(
                self.handler, 
                str(self.watch_path), 
                recursive=(self.watch_type == 'agent_execute')  # 执行日志需要递归监控
            )
            
            # 启动观察者
            self.observer.start()
            
            # 启动延迟处理器
            self._start_delay_processor()
            
            # 扫描现有文件
            await self._scan_existing_files()
            
            self.is_running = True
            self.logger.info(f"文件监控器启动成功: {self.watch_path}")
            
        except Exception as e:
            self.logger.error(f"启动文件监控失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止文件监控"""
        if not self.is_running:
            return
        
        self.logger.info("停止文件监控器...")
        
        try:
            # 停止观察者
            if self.observer:
                self.observer.stop()
                self.observer.join(timeout=5.0)
                self.observer = None
            
            # 停止延迟处理器
            self._stop_delay_processor()
            
            self.is_running = False
            self.logger.info("文件监控器已停止")
            
        except Exception as e:
            self.logger.error(f"停止文件监控失败: {e}")
    
    def _start_delay_processor(self):
        """启动延迟处理线程"""
        self.stop_delay_processor.clear()
        
        def delay_processor():
            while not self.stop_delay_processor.wait(timeout=0.5):
                current_time = time.time()
                files_to_process = []
                
                with self.event_lock:
                    for file_path, (delay_time, event_type) in list(self.delayed_queue.items()):
                        if current_time >= delay_time:
                            files_to_process.append((file_path, event_type))
                            del self.delayed_queue[file_path]
                
                # 处理延迟队列中的文件
                for file_path, event_type in files_to_process:
                    if self._is_file_write_complete(file_path):
                        self._process_file_immediately(file_path, event_type)
                    else:
                        # 文件仍在写入，继续延迟（根据类型设置不同的重试间隔）
                        retry_delay = 5.0 if self.watch_type == 'agent_execute' else 1.0
                        self._schedule_delayed_processing(file_path, event_type, delay=retry_delay)
        
        self.delay_processor_thread = threading.Thread(target=delay_processor, daemon=True)
        self.delay_processor_thread.start()
    
    def _stop_delay_processor(self):
        """停止延迟处理线程"""
        if self.delay_processor_thread:
            self.stop_delay_processor.set()
            self.delay_processor_thread.join(timeout=2.0)
            self.delay_processor_thread = None
    
    async def _scan_existing_files(self):
        """扫描现有文件（简化版：仅记录到状态管理器，不处理）"""
        self.logger.info("扫描现有文件（仅记录，不处理）...")
        
        try:
            existing_files = []
            
            if self.watch_type == 'agent_execute':
                # 扫描执行日志文件夹
                for item in self.watch_path.iterdir():
                    if item.is_dir() and item.name.startswith('round_'):
                        existing_files.append(str(item))
            else:
                # 扫描计划和报告文件
                for file_path in self.watch_path.glob('*'):
                    if file_path.is_file() and not file_path.name.startswith('.'):
                        existing_files.append(str(file_path))
            
            self.stats['watched_files_count'] = len(existing_files)
            self.logger.info(f"发现 {len(existing_files)} 个现有文件，已记录但不处理")
            
            # 现有文件只记录到状态管理器，不发送到事件队列
            # 状态管理器已在启动时记录过了，这里无需额外操作
            
        except Exception as e:
            self.logger.error(f"扫描现有文件失败: {e}")
    
    def _should_process_existing_file(self, file_path: str) -> bool:
        """判断现有文件是否需要处理"""
        # 如果有状态管理器，使用状态管理器判断
        if self.state_manager:
            return self.state_manager.is_new_file(file_path, self.watch_type)
        
        # 否则使用原有逻辑
        file_name = os.path.basename(file_path)
        return file_name not in self.processed_files
    
    def _handle_file_event(self, event_type: str, file_path: str):
        """处理文件事件"""
        with self.event_lock:
            self.stats['total_events'] += 1
            self.stats['last_event_time'] = datetime.now()
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.logger.debug(f"文件不存在，忽略事件: {file_path}")
                self.stats['ignored_events'] += 1
                return
            
            # 检查文件是否已处理过（使用状态管理器）
            file_name = os.path.basename(file_path)
            if self.state_manager and not self.state_manager.is_new_file(file_path, self.watch_type):
                self.logger.debug(f"文件已处理过，跳过: {file_name}")
                self.stats['ignored_events'] += 1
                return
            elif not self.state_manager and file_name in self.processed_files:
                self.logger.debug(f"文件已处理过，跳过: {file_name}")
                self.stats['ignored_events'] += 1
                return
            
            # 检查文件是否写入完成
            if not self._is_file_write_complete(file_path):
                self.logger.debug(f"文件写入未完成，延迟处理: {file_path}")
                self._schedule_delayed_processing(file_path, event_type)
                return
            
            # 立即处理文件
            self._process_file_immediately(file_path, event_type)
    
    def _is_file_write_complete(self, file_path: str) -> bool:
        """检查文件写入是否完成"""
        try:
            # 对于agent_execute类型的特殊处理
            if self.watch_type == 'agent_execute':
                return self._is_execution_log_complete(file_path)
            
            # 其他类型使用原有逻辑
            return self._is_regular_file_complete(file_path)
                
        except (OSError, IOError) as e:
            self.logger.debug(f"检查文件写入状态失败: {file_path}, {e}")
            return False
    
    def _is_execution_log_complete(self, file_path: str) -> bool:
        """检查执行日志是否完成（针对agent_execute类型）"""
        try:
            # 如果是文件夹内的单个文件，需要检查整个执行文件夹的状态
            if os.path.isfile(file_path):
                execution_folder = os.path.dirname(file_path)
            else:
                execution_folder = file_path
            
            task_log_path = os.path.join(execution_folder, 'task_structured.log')
            
            # 检查task_structured.log是否存在
            if not os.path.exists(task_log_path):
                self.logger.debug(f"task_structured.log不存在，继续等待: {task_log_path}")
                return False
            
            # 优先级1: 检查内容结束标志
            if self._check_task_completion_marker(task_log_path):
                self.logger.debug(f"检测到任务完成标志: {task_log_path}")
                return True
            
            # 优先级2: 检查文件修改时间（90秒兜底机制）
            if self._check_file_idle_timeout(task_log_path, timeout_seconds=90):
                self.logger.debug(f"文件90秒无修改，判定为完成: {task_log_path}")
                return True
            
            # 文件仍在写入或等待中
            return False
            
        except Exception as e:
            self.logger.debug(f"检查执行日志完成状态失败: {file_path}, {e}")
            return False
    
    def _check_task_completion_marker(self, task_log_path: str) -> bool:
        """检查任务完成标志"""
        try:
            with open(task_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查任务完成标志模式
            import re
            completion_pattern = r'任务完成\s+\[TASK-'
            
            if re.search(completion_pattern, content):
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"检查任务完成标志失败: {task_log_path}, {e}")
            return False
    
    def _check_file_idle_timeout(self, file_path: str, timeout_seconds: int = 90) -> bool:
        """检查文件是否在指定时间内无修改（兜底机制）"""
        try:
            current_time = time.time()
            file_mtime = os.path.getmtime(file_path)
            
            # 如果文件修改时间距现在超过指定秒数，认为已完成
            if current_time - file_mtime >= timeout_seconds:
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"检查文件空闲超时失败: {file_path}, {e}")
            return False
    
    def _is_regular_file_complete(self, file_path: str) -> bool:
        """检查常规文件写入是否完成（原有逻辑）"""
        try:
            # 方法1: 检查文件大小稳定性
            initial_size = os.path.getsize(file_path)
            time.sleep(0.1)
            final_size = os.path.getsize(file_path)
            
            if initial_size != final_size:
                return False
            
            # 方法2: 尝试以独占模式打开文件
            try:
                with open(file_path, 'r+b') as f:
                    pass
                return True
            except (IOError, OSError):
                return False
                
        except (OSError, IOError) as e:
            self.logger.debug(f"检查常规文件写入状态失败: {file_path}, {e}")
            return False
    
    def _schedule_delayed_processing(self, file_path: str, event_type: str, delay: float = None):
        """安排延迟处理"""
        # 根据监控类型设置不同的延迟策略
        if delay is None:
            if self.watch_type == 'agent_execute':
                delay = 10.0  # 执行日志初始延迟10秒，给足时间让文件创建完成
            else:
                delay = 2.0   # 其他类型保持原有2秒延迟
        
        delay_time = time.time() + delay
        with self.event_lock:
            self.delayed_queue[file_path] = (delay_time, event_type)
        
        self.logger.debug(f"安排延迟处理: {file_path} (延迟 {delay}s, 类型: {self.watch_type})")
    
    def _process_file_immediately(self, file_path: str, event_type: str):
        """立即处理文件并执行即时清理"""
        processing_success = False
        
        try:
            with PerformanceTimer(self.logger, f"process_file_{event_type}"):
                # 1. 文件格式验证（兜底机制）
                validation_result = self._validate_file_format(file_path)
                if not validation_result[0]:
                    self.logger.warning(f"文件格式验证失败: {file_path}, {validation_result[1]}")
                    # 格式错误也继续处理，但记录警告
                
                # 2. 创建文件事件
                file_event = {
                    'type': event_type,
                    'path': file_path,
                    'watch_type': self.watch_type,
                    'timestamp': datetime.now(),
                    'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                    'mtime': datetime.fromtimestamp(os.path.getmtime(file_path)) if os.path.exists(file_path) else datetime.now(),
                    'validation_result': validation_result
                }
                
                # 3. 添加到事件队列
                if self.event_queue:
                    self.event_queue.add_event(file_event)
                
                # 4. 标记文件已处理
                file_name = os.path.basename(file_path)
                self.processed_files.add(file_name)
                
                processing_success = True
                self.stats['processed_events'] += 1
                
                self.logger.log_file_event(
                    event_type, file_path, 
                    success=True,
                    duration=0  # 由PerformanceTimer记录实际耗时
                )
                
        except Exception as e:
            self.logger.log_file_event(
                event_type, file_path,
                success=False,
                error_msg=str(e)
            )
            self.logger.error(f"处理文件失败: {file_path}, 错误: {e}")
        
        finally:
            # 5. 使用状态管理器记录处理结果（主要用于日志）
            if self.state_manager:
                self.state_manager.mark_file_processed(file_path, self.watch_type, success=processing_success)
            
            # 6. 即时清理检查（无论处理成功与否都执行）
            self._check_and_cleanup_immediately()
    
    def _should_process_modify_event(self, file_path: str) -> bool:
        """检查是否应该处理修改事件（避免重复处理）"""
        try:
            current_mtime = os.path.getmtime(file_path)
            last_mtime = self.last_modify_times.get(file_path, 0)
            
            # 如果修改时间差小于1秒，认为是重复事件
            if current_mtime - last_mtime < 1.0:
                return False
            
            self.last_modify_times[file_path] = current_mtime
            return True
            
        except OSError:
            return False
    
    def get_watched_files_count(self) -> int:
        """获取监控文件数量"""
        return self.stats['watched_files_count']
    
    def get_last_event_time(self) -> Optional[str]:
        """获取最后事件时间"""
        last_time = self.stats['last_event_time']
        return last_time.isoformat() if last_time else None
    
    def _validate_file_format(self, file_path: str) -> Tuple[bool, str]:
        """验证文件格式（包含文件名模式和内容格式）"""
        try:
            # 首先验证文件名模式
            name_valid, name_msg = validate_file_name_pattern(file_path, self.watch_type)
            if not name_valid:
                return False, name_msg
            
            # 然后验证文件内容格式
            if self.watch_type == 'json_plan':
                content_valid, content_msg = validate_json_file(file_path)
                if not content_valid:
                    return False, f"文件名正确但内容无效: {content_msg}"
                return True, "JSON计划文件验证通过"
                
            elif self.watch_type == 'judge_report':
                content_valid, content_msg = validate_log_file(file_path)
                if not content_valid:
                    return False, f"文件名正确但内容无效: {content_msg}"
                return True, "评价报告文件验证通过"
                
            elif self.watch_type == 'agent_execute':
                # 对于执行文件夹，检查是否存在关键文件
                task_log_path = os.path.join(file_path, 'task_structured.log')
                if os.path.exists(task_log_path):
                    content_valid, content_msg = validate_log_file(task_log_path)
                    if not content_valid:
                        return False, f"文件夹名正确但task_structured.log无效: {content_msg}"
                    return True, "执行日志文件夹验证通过"
                else:
                    return False, "文件夹名正确但缺少task_structured.log文件"
            else:
                return True, "未知类型，跳过验证"
                
        except Exception as e:
            return False, f"验证过程异常: {e}"
    
    def _check_and_cleanup_immediately(self):
        """即时检查并清理文件"""
        try:
            # 每种类型最多保留20个文件
            max_files = 20
            
            deleted_count, space_freed = cleanup_old_files(
                str(self.watch_path), 
                self.watch_type, 
                max_files
            )
            
            if deleted_count > 0:
                self.logger.info(
                    f"即时清理完成 ({self.watch_type}): "
                    f"删除 {deleted_count} 个文件，释放 {space_freed:.2f}MB"
                )
                
                # 更新统计信息
                if 'files_cleaned' not in self.stats:
                    self.stats['files_cleaned'] = 0
                    self.stats['space_freed_mb'] = 0.0
                
                self.stats['files_cleaned'] += deleted_count
                self.stats['space_freed_mb'] += space_freed
            
        except Exception as e:
            self.logger.error(f"即时清理失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'watch_path': str(self.watch_path),
            'watch_type': self.watch_type,
            'is_running': self.is_running,
            'stats': self.stats.copy(),
            'processed_files_count': len(self.processed_files),
            'delayed_queue_size': len(self.delayed_queue)
        }
    
    def clear_processed_files(self):
        """清除已处理文件记录（用于重新处理）"""
        with self.event_lock:
            self.processed_files.clear()
            self.last_modify_times.clear()
        
        self.logger.info("已清除处理记录，将重新处理文件")


# 工具函数
def create_file_watcher(watch_path: str, event_queue: 'EventQueue', 
                       watch_type: str, config: 'LogServerConfig',
                       state_manager: 'FileStateManager' = None) -> FileWatcher:
    """创建文件监控器的工厂函数"""
    return FileWatcher(watch_path, event_queue, watch_type, config, state_manager)


if __name__ == '__main__':
    # 文件监控器测试
    import asyncio
    from config import get_config
    from monitors.event_queue import EventQueue
    
    async def test_file_watcher():
        config = get_config()
        
        # 创建测试事件队列（简化版）
        class TestEventQueue:
            def __init__(self):
                self.events = []
            
            def add_event(self, event):
                self.events.append(event)
                print(f"收到事件: {event['type']} - {event['path']}")
        
        event_queue = TestEventQueue()
        
        # 创建文件监控器
        watcher = FileWatcher(
            watch_path="/tmp/test_watch",
            event_queue=event_queue,
            watch_type="test",
            config=config
        )
        
        try:
            await watcher.start()
            print("文件监控器测试启动，请在 /tmp/test_watch 目录下创建文件...")
            
            # 运行10秒
            await asyncio.sleep(10)
            
        finally:
            await watcher.stop()
            print("文件监控器测试完成")
            print(f"处理统计: {watcher.get_stats()}")
    
    asyncio.run(test_file_watcher())