"""
SimplifyAgent 事件队列管理器

提供高效的文件事件队列处理、批量处理、错误重试和性能优化等功能。
支持优先级处理、流量控制和智能调度。

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
"""

import asyncio
import queue
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from collections import defaultdict
from dataclasses import dataclass, field

from server_logger import get_logger, PerformanceTimer


@dataclass
class FileEvent:
    """文件事件数据类"""
    type: str  # 事件类型: created, modified, existing
    path: str  # 文件路径
    watch_type: str  # 监控类型: json_plan, agent_execute, judge_report
    timestamp: datetime  # 事件时间
    size: int  # 文件大小
    mtime: datetime  # 文件修改时间
    priority: int = 5  # 优先级 (1-10, 1最高)
    retry_count: int = 0  # 重试次数
    last_error: Optional[str] = None  # 上次错误信息
    processing_context: Dict[str, Any] = field(default_factory=dict)  # 处理上下文
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'type': self.type,
            'path': self.path,
            'watch_type': self.watch_type,
            'timestamp': self.timestamp.isoformat(),
            'size': self.size,
            'mtime': self.mtime.isoformat(),
            'priority': self.priority,
            'retry_count': self.retry_count,
            'last_error': self.last_error,
            'processing_context': self.processing_context
        }


class EventQueue:
    """事件队列管理器"""
    
    def __init__(self, config: 'LogServerConfig', data_parser: 'DataParser', 
                 batch_size: int = 10, max_queue_size: int = 1000):
        """
        初始化事件队列管理器
        
        Args:
            config: 配置对象
            data_parser: 数据解析器
            batch_size: 批处理大小
            max_queue_size: 最大队列大小
        """
        self.config = config
        self.data_parser = data_parser
        self.batch_size = batch_size
        self.max_queue_size = max_queue_size
        
        self.logger = get_logger('log_server', 'event_queue')
        
        # 优先级队列（使用priority queue）
        self.event_queue = queue.PriorityQueue(maxsize=max_queue_size)
        
        # 错误重试队列
        self.retry_queue = queue.Queue()
        
        # 运行状态
        self.is_running = False
        self.processor_threads = []
        self.retry_thread = None
        self.metrics_thread = None
        
        # 统计信息
        self.stats = {
            'total_events': 0,
            'processed_events': 0,
            'failed_events': 0,
            'retry_events': 0,
            'queue_size': 0,
            'processing_time_total': 0.0,
            'last_processed_time': None,
            'throughput_per_minute': 0.0
        }
        
        # 性能监控
        self.performance_tracker = {
            'minute_counters': defaultdict(int),  # 每分钟处理计数
            'error_rates': defaultdict(int),      # 错误率统计
            'processing_times': [],               # 处理时间历史
            'queue_size_history': []              # 队列大小历史
        }
        
        # 流量控制
        self.rate_limiter = {
            'max_events_per_second': 50,
            'last_process_time': 0,
            'token_bucket': 50  # 令牌桶算法
        }
        
        self.logger.info(f"事件队列初始化完成 (batch_size={batch_size}, max_queue_size={max_queue_size})")
    
    async def start(self):
        """启动事件队列处理器"""
        if self.is_running:
            self.logger.warning("事件队列已在运行")
            return
        
        try:
            # 启动处理线程池
            num_workers = self.config.get('server.max_workers', 3)
            for i in range(num_workers):
                worker_thread = threading.Thread(
                    target=self._event_processor_worker,
                    name=f"EventProcessor-{i}",
                    daemon=True
                )
                worker_thread.start()
                self.processor_threads.append(worker_thread)
            
            # 启动重试处理线程
            self.retry_thread = threading.Thread(
                target=self._retry_processor_worker,
                name="RetryProcessor",
                daemon=True
            )
            self.retry_thread.start()
            
            # 启动性能监控线程
            self.metrics_thread = threading.Thread(
                target=self._metrics_collector_worker,
                name="MetricsCollector",
                daemon=True
            )
            self.metrics_thread.start()
            
            self.is_running = True
            self.logger.info(f"事件队列启动成功 ({num_workers} 工作线程)")
            
        except Exception as e:
            self.logger.error(f"事件队列启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止事件队列处理器"""
        if not self.is_running:
            return
        
        self.logger.info("停止事件队列处理器...")
        self.is_running = False
        
        # 等待所有工作线程完成
        for thread in self.processor_threads:
            if thread.is_alive():
                thread.join(timeout=5.0)
        
        if self.retry_thread and self.retry_thread.is_alive():
            self.retry_thread.join(timeout=2.0)
        
        if self.metrics_thread and self.metrics_thread.is_alive():
            self.metrics_thread.join(timeout=2.0)
        
        # 记录最终统计
        self._log_final_statistics()
        
        self.logger.info("事件队列已停止")
    
    def add_event(self, event_data: Dict[str, Any]):
        """添加事件到队列"""
        if not self.is_running:
            self.logger.warning("事件队列未运行，忽略事件")
            return
        
        try:
            # 创建文件事件对象
            file_event = FileEvent(
                type=event_data['type'],
                path=event_data['path'],
                watch_type=event_data['watch_type'],
                timestamp=event_data['timestamp'],
                size=event_data['size'],
                mtime=event_data['mtime']
            )
            
            # 设置优先级
            file_event.priority = self._calculate_priority(file_event)
            
            # 检查队列是否满
            if self.event_queue.qsize() >= self.max_queue_size:
                self.logger.warning(f"事件队列已满 ({self.max_queue_size})，丢弃事件: {file_event.path}")
                self.stats['failed_events'] += 1
                return
            
            # 添加到优先级队列 (priority, timestamp, event)
            queue_item = (file_event.priority, time.time(), file_event)
            self.event_queue.put_nowait(queue_item)
            
            self.stats['total_events'] += 1
            self.stats['queue_size'] = self.event_queue.qsize()
            
            self.logger.debug(f"事件已加入队列: {file_event.watch_type}/{file_event.type} - {file_event.path}")
            
        except queue.Full:
            self.logger.error(f"事件队列已满，无法添加事件: {event_data['path']}")
            self.stats['failed_events'] += 1
        except Exception as e:
            self.logger.error(f"添加事件到队列失败: {e}")
            self.stats['failed_events'] += 1
    
    def _calculate_priority(self, event: FileEvent) -> int:
        """计算事件优先级 (1-10, 1最高)"""
        # 基础优先级
        priority = 5
        
        # 根据监控类型调整优先级
        type_priorities = {
            'judge_report': 1,    # 评价报告优先级最高
            'json_plan': 2,       # 计划文件其次
            'agent_execute': 3    # 执行日志优先级稍低
        }
        priority = type_priorities.get(event.watch_type, priority)
        
        # 根据事件类型调整
        if event.type == 'existing':
            priority += 2  # 现有文件优先级较低
        
        # 根据文件大小调整（小文件优先处理）
        if event.size < 1024:  # 1KB以下
            priority -= 1
        elif event.size > 1024 * 1024:  # 1MB以上
            priority += 1
        
        # 确保优先级在有效范围内
        return max(1, min(10, priority))
    
    def _event_processor_worker(self):
        """事件处理工作线程"""
        thread_name = threading.current_thread().name
        logger = self.logger.create_child_logger(thread_name.lower())
        
        logger.info(f"事件处理工作线程启动: {thread_name}")
        
        batch_events = []
        last_batch_time = time.time()
        
        while self.is_running:
            try:
                # 应用流量控制
                if not self._check_rate_limit():
                    time.sleep(0.1)
                    continue
                
                # 获取事件（带超时）
                try:
                    priority, queue_time, event = self.event_queue.get(timeout=1.0)
                    batch_events.append(event)
                    
                    # 更新队列大小统计
                    self.stats['queue_size'] = self.event_queue.qsize()
                    
                except queue.Empty:
                    # 超时，处理当前批次
                    if batch_events and time.time() - last_batch_time > 5.0:
                        self._process_event_batch(batch_events, logger)
                        batch_events.clear()
                        last_batch_time = time.time()
                    continue
                
                # 批处理逻辑
                should_process_batch = (
                    len(batch_events) >= self.batch_size or
                    time.time() - last_batch_time > 3.0  # 3秒超时
                )
                
                if should_process_batch:
                    self._process_event_batch(batch_events, logger)
                    batch_events.clear()
                    last_batch_time = time.time()
                
            except Exception as e:
                logger.error(f"事件处理线程异常: {e}")
                time.sleep(1.0)
        
        # 处理剩余批次
        if batch_events:
            self._process_event_batch(batch_events, logger)
        
        logger.info(f"事件处理工作线程停止: {thread_name}")
    
    def _process_event_batch(self, events: List[FileEvent], logger):
        """处理事件批次"""
        if not events:
            return
        
        batch_id = f"batch_{int(time.time())}"
        logger.info(f"处理事件批次 {batch_id}: {len(events)} 个事件")
        
        with PerformanceTimer(logger, f"process_batch_{len(events)}"):
            success_count = 0
            error_count = 0
            
            for event in events:
                try:
                    success = self._process_single_event(event, logger)
                    if success:
                        success_count += 1
                        self.stats['processed_events'] += 1
                    else:
                        error_count += 1
                        self._handle_processing_error(event, "处理失败")
                    
                except Exception as e:
                    error_count += 1
                    self._handle_processing_error(event, str(e))
                    logger.error(f"处理事件异常: {event.path}, 错误: {e}")
            
            # 更新统计
            self.stats['last_processed_time'] = datetime.now()
            
            # 记录批次结果
            logger.info(f"批次 {batch_id} 处理完成: 成功 {success_count}, 失败 {error_count}")
    
    def _process_single_event(self, event: FileEvent, logger) -> bool:
        """处理单个事件"""
        try:
            # 记录处理开始
            logger.debug(f"处理事件: {event.watch_type}/{event.type} - {event.path}")
            
            # 调用数据解析器处理
            result = self.data_parser.process_file(event.path, event.watch_type)
            
            if result and result.get('success', False):
                logger.debug(f"事件处理成功: {event.path}")
                return True
            else:
                error_msg = result.get('error', '未知错误') if result else '解析器无响应'
                logger.warning(f"事件处理失败: {event.path}, 错误: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"处理事件异常: {event.path}, 错误: {e}")
            return False
    
    def _handle_processing_error(self, event: FileEvent, error_msg: str):
        """处理错误事件"""
        event.retry_count += 1
        event.last_error = error_msg
        
        max_retries = self.config.get('processing.retry_attempts', 3)
        if event.retry_count <= max_retries:
            # 添加到重试队列
            retry_delay = min(60, 5 * event.retry_count)  # 指数退避，最大60秒
            retry_time = time.time() + retry_delay
            
            self.retry_queue.put((retry_time, event))
            self.stats['retry_events'] += 1
            
            self.logger.debug(f"事件加入重试队列: {event.path} (第 {event.retry_count} 次重试)")
        else:
            # 重试次数耗尽，记录失败
            self.stats['failed_events'] += 1
            self.logger.error(f"事件重试次数耗尽: {event.path}, 最后错误: {error_msg}")
    
    def _retry_processor_worker(self):
        """重试处理工作线程"""
        logger = self.logger.create_child_logger('retry_processor')
        logger.info("重试处理线程启动")
        
        while self.is_running:
            try:
                # 获取重试事件
                try:
                    retry_time, event = self.retry_queue.get(timeout=5.0)
                except queue.Empty:
                    continue
                
                # 检查是否到了重试时间
                current_time = time.time()
                if current_time < retry_time:
                    # 时间未到，重新加入队列
                    self.retry_queue.put((retry_time, event))
                    time.sleep(1.0)
                    continue
                
                # 执行重试
                logger.info(f"重试处理事件: {event.path} (第 {event.retry_count} 次)")
                
                success = self._process_single_event(event, logger)
                if success:
                    self.stats['processed_events'] += 1
                    logger.info(f"重试成功: {event.path}")
                else:
                    self._handle_processing_error(event, "重试失败")
                
            except Exception as e:
                logger.error(f"重试处理线程异常: {e}")
                time.sleep(1.0)
        
        logger.info("重试处理线程停止")
    
    def _metrics_collector_worker(self):
        """性能指标收集线程"""
        logger = self.logger.create_child_logger('metrics')
        logger.info("性能监控线程启动")
        
        last_processed_count = 0
        
        while self.is_running:
            try:
                time.sleep(60)  # 每分钟收集一次指标
                
                current_time = int(time.time() // 60)  # 分钟级时间戳
                current_processed = self.stats['processed_events']
                
                # 计算每分钟吞吐量
                throughput = current_processed - last_processed_count
                self.performance_tracker['minute_counters'][current_time] = throughput
                self.stats['throughput_per_minute'] = throughput
                
                # 记录队列大小历史
                queue_size = self.event_queue.qsize()
                self.performance_tracker['queue_size_history'].append((current_time, queue_size))
                
                # 清理旧数据（保留24小时）
                cutoff_time = current_time - 24 * 60
                self.performance_tracker['minute_counters'] = {
                    k: v for k, v in self.performance_tracker['minute_counters'].items() 
                    if k > cutoff_time
                }
                self.performance_tracker['queue_size_history'] = [
                    (t, s) for t, s in self.performance_tracker['queue_size_history'] 
                    if t > cutoff_time
                ]
                
                # 记录性能指标
                logger.log_performance_metric('events_per_minute', throughput, 'count')
                logger.log_performance_metric('queue_size', queue_size, 'count')
                logger.log_performance_metric('retry_queue_size', self.retry_queue.qsize(), 'count')
                
                last_processed_count = current_processed
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
        
        logger.info("性能监控线程停止")
    
    def _check_rate_limit(self) -> bool:
        """检查流量限制（令牌桶算法）"""
        current_time = time.time()
        elapsed = current_time - self.rate_limiter['last_process_time']
        
        # 补充令牌
        tokens_to_add = int(elapsed * self.rate_limiter['max_events_per_second'])
        self.rate_limiter['token_bucket'] = min(
            self.rate_limiter['max_events_per_second'],
            self.rate_limiter['token_bucket'] + tokens_to_add
        )
        
        if self.rate_limiter['token_bucket'] > 0:
            self.rate_limiter['token_bucket'] -= 1
            self.rate_limiter['last_process_time'] = current_time
            return True
        
        return False
    
    def _log_final_statistics(self):
        """记录最终统计信息"""
        self.logger.info("=== 事件队列最终统计 ===")
        self.logger.info(f"总事件数: {self.stats['total_events']}")
        self.logger.info(f"处理成功: {self.stats['processed_events']}")
        self.logger.info(f"处理失败: {self.stats['failed_events']}")
        self.logger.info(f"重试事件: {self.stats['retry_events']}")
        
        if self.stats['processed_events'] > 0:
            avg_time = self.stats['processing_time_total'] / self.stats['processed_events']
            success_rate = (self.stats['processed_events'] / self.stats['total_events']) * 100
            self.logger.info(f"平均处理时间: {avg_time:.3f}秒")
            self.logger.info(f"成功率: {success_rate:.1f}%")
    
    def get_queue_size(self) -> int:
        """获取当前队列大小"""
        return self.event_queue.qsize()
    
    def get_retry_queue_size(self) -> int:
        """获取重试队列大小"""
        return self.retry_queue.qsize()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'stats': self.stats.copy(),
            'performance': {
                'throughput_history': dict(self.performance_tracker['minute_counters']),
                'queue_size_history': self.performance_tracker['queue_size_history'][-60:],  # 最近1小时
                'current_queue_size': self.get_queue_size(),
                'current_retry_queue_size': self.get_retry_queue_size()
            },
            'config': {
                'batch_size': self.batch_size,
                'max_queue_size': self.max_queue_size,
                'max_events_per_second': self.rate_limiter['max_events_per_second']
            }
        }
    
    def clear_stats(self):
        """清除统计信息"""
        self.stats = {key: 0 if isinstance(value, (int, float)) else None 
                     for key, value in self.stats.items()}
        self.performance_tracker = {
            'minute_counters': defaultdict(int),
            'error_rates': defaultdict(int),
            'processing_times': [],
            'queue_size_history': []
        }
        self.logger.info("统计信息已清除")


if __name__ == '__main__':
    # 事件队列测试
    import asyncio
    from datetime import datetime
    
    class MockDataParser:
        def process_file(self, file_path, watch_type):
            # 模拟处理
            import random
            time.sleep(random.uniform(0.1, 0.5))
            return {'success': random.choice([True, True, True, False])}  # 75%成功率
    
    class MockConfig:
        def get(self, key, default=None):
            config_map = {
                'server.max_workers': 2,
                'processing.retry_attempts': 3
            }
            return config_map.get(key, default)
    
    async def test_event_queue():
        config = MockConfig()
        data_parser = MockDataParser()
        
        event_queue = EventQueue(config, data_parser, batch_size=5)
        
        try:
            await event_queue.start()
            
            # 添加测试事件
            for i in range(20):
                event_data = {
                    'type': 'created',
                    'path': f'/test/file_{i}.json',
                    'watch_type': 'json_plan',
                    'timestamp': datetime.now(),
                    'size': 1024 + i * 100,
                    'mtime': datetime.now()
                }
                event_queue.add_event(event_data)
            
            print("已添加20个测试事件")
            
            # 等待处理
            await asyncio.sleep(10)
            
            # 打印统计
            stats = event_queue.get_stats()
            print(f"处理统计: {stats['stats']}")
            
        finally:
            await event_queue.stop()
    
    asyncio.run(test_event_queue())