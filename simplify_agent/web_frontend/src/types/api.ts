// API相关类型定义

// 统一API响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
  timestamp: number;
}

// 分页响应格式
export interface PaginationInfo {
  current_page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

// 错误响应格式
export interface ErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string[]>;
  code: number;
  timestamp: number;
}

// 请求参数类型
export interface PaginationParams {
  page?: number;
  page_size?: number;
}

export interface SearchParams extends PaginationParams {
  search?: string;
}

export interface FilterParams extends SearchParams {
  status?: string;
  [key: string]: any;
}