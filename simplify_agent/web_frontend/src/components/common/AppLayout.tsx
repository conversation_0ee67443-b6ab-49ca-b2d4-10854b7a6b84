// 应用主布局组件
import React, { useState } from 'react';
import { Layout, Menu, Button, Typography, Space } from 'antd';
import {
  DashboardOutlined,
  BarChartOutlined,
  DatabaseOutlined,
  UserOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { ROUTES, PAGE_TITLES } from '@/constants';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
  mode: 'user' | 'dev';
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, mode }) => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // 用户模式菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: ROUTES.USER_DASHBOARD,
      icon: <DashboardOutlined />,
      label: '控制台',
      onClick: () => navigate(ROUTES.USER_DASHBOARD),
    },
    {
      key: ROUTES.USER_PLANS,
      icon: <DatabaseOutlined />,
      label: '测试计划',
      onClick: () => navigate(ROUTES.USER_PLANS),
    },
    {
      key: ROUTES.USER_EXECUTIONS,
      icon: <UserOutlined />,
      label: '测试记录',
      onClick: () => navigate(ROUTES.USER_EXECUTIONS),
    },
    {
      key: ROUTES.USER_STATISTICS,
      icon: <BarChartOutlined />,
      label: '统计分析',
      onClick: () => navigate(ROUTES.USER_STATISTICS),
    },
  ];

  // 开发者模式菜单项
  const devMenuItems: MenuProps['items'] = [
    {
      key: ROUTES.DEV_DASHBOARD,
      icon: <DashboardOutlined />,
      label: '开发者控制台',
      onClick: () => navigate(ROUTES.DEV_DASHBOARD),
    },
    {
      key: ROUTES.DEV_TABLES,
      icon: <DatabaseOutlined />,
      label: '数据库表',
      onClick: () => navigate(ROUTES.DEV_TABLES),
    },
    {
      key: ROUTES.DEV_SQL,
      icon: <SettingOutlined />,
      label: 'SQL执行器',
      onClick: () => navigate(ROUTES.DEV_SQL),
    },
  ];

  const menuItems = mode === 'user' ? userMenuItems : devMenuItems;
  
  // 获取当前页面标题
  const getCurrentTitle = (): string => {
    const pathname = location.pathname;
    
    if (pathname.includes('/user/executions/') && pathname !== ROUTES.USER_EXECUTIONS) {
      return PAGE_TITLES.USER_EXECUTION_DETAIL;
    }
    
    if (pathname.includes('/dev/tables/') && pathname !== ROUTES.DEV_TABLES) {
      return PAGE_TITLES.DEV_TABLE_DETAIL;
    }
    
    const titleMap: Record<string, string> = {
      [ROUTES.USER_DASHBOARD]: PAGE_TITLES.USER_DASHBOARD,
      [ROUTES.USER_EXECUTIONS]: PAGE_TITLES.USER_EXECUTIONS,
      [ROUTES.USER_STATISTICS]: PAGE_TITLES.USER_STATISTICS,
      [ROUTES.DEV_DASHBOARD]: PAGE_TITLES.DEV_DASHBOARD,
      [ROUTES.DEV_TABLES]: PAGE_TITLES.DEV_TABLES,
      [ROUTES.DEV_SQL]: PAGE_TITLES.DEV_SQL,
    };
    
    return titleMap[pathname] || '测试管理系统';
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={220}
        theme="dark"
        style={{
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100,
        }}
      >
        <div
          style={{
            height: 32,
            margin: 16,
            background: 'rgba(255, 255, 255, 0.3)',
            borderRadius: 6,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          {collapsed ? '测试' : '测试管理系统'}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout style={{ marginLeft: collapsed ? 80 : 220, transition: 'margin-left 0.2s' }}>
        <Header
          style={{
            padding: '0 24px',
            background: '#fff',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 40, height: 40 }}
            />
            <Title level={4} style={{ margin: 0 }}>
              {getCurrentTitle()}
            </Title>
          </Space>
          
          <Space>
            {mode === 'user' && (
              <Button
                type="link"
                onClick={() => navigate(ROUTES.DEV_LOGIN)}
              >
                开发者模式
              </Button>
            )}
            {mode === 'dev' && (
              <Button
                type="link"
                onClick={() => {
                  // TODO: 实现登出逻辑
                  navigate(ROUTES.USER_DASHBOARD);
                }}
              >
                退出开发者模式
              </Button>
            )}
          </Space>
        </Header>
        
        <Content
          style={{
            margin: 24,
            padding: 24,
            background: '#fff',
            borderRadius: 8,
            overflow: 'auto',
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout;