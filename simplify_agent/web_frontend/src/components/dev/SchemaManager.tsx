// 表结构管理组件
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  message,
  Popconfirm,
  Tag,
  Typography,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { devApi } from '@/services/devApi';
import type { ColumnsType } from 'antd/es/table';

const { Text } = Typography;
const { Option } = Select;

interface SchemaColumn {
  name: string;
  type: string;
  not_null: boolean;
  default_value: any;
  primary_key: boolean;
}

interface SchemaManagerProps {
  tableName: string;
  visible: boolean;
  onClose: () => void;
}

const SchemaManager: React.FC<SchemaManagerProps> = ({ tableName, visible, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState<SchemaColumn[]>([]);
  const [indexes, setIndexes] = useState<any[]>([]);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingColumn, setEditingColumn] = useState<SchemaColumn | null>(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();

  // 常用的SQLite数据类型
  const dataTypes = [
    'INTEGER',
    'TEXT',
    'REAL',
    'BLOB',
    'TIMESTAMP',
    'VARCHAR(50)',
    'VARCHAR(100)',
    'VARCHAR(255)',
    'BOOLEAN',
    'DATE',
    'DATETIME'
  ];

  // 加载表结构
  const loadSchema = async () => {
    if (!tableName) return;
    
    try {
      setLoading(true);
      const schema = await devApi.getTableSchema(tableName);
      
      // 转换数据格式
      const formattedColumns = schema.columns.map((col: any) => ({
        name: col.name,
        type: col.type,
        not_null: Boolean(col.notnull),
        default_value: col.dflt_value,
        primary_key: Boolean(col.pk)
      }));
      
      setColumns(formattedColumns);
      setIndexes(schema.indexes || []);
    } catch (error) {
      console.error('Load schema failed:', error);
      message.error('加载表结构失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      loadSchema();
    }
  }, [visible, tableName]);

  // 添加字段
  const handleAddColumn = async (values: any) => {
    try {
      await devApi.addColumn(tableName, {
        column_name: values.columnName,
        column_type: values.columnType,
        not_null: values.notNull || false,
        default_value: values.defaultValue
      });
      
      message.success('字段添加成功');
      setAddModalVisible(false);
      form.resetFields();
      loadSchema(); // 重新加载表结构
    } catch (error) {
      console.error('Add column failed:', error);
      message.error('添加字段失败');
    }
  };

  // 删除字段
  const handleDropColumn = async (columnName: string) => {
    try {
      await devApi.dropColumn(tableName, columnName);
      message.success('字段删除成功');
      loadSchema(); // 重新加载表结构
    } catch (error) {
      console.error('Drop column failed:', error);
      message.error(`删除字段失败: ${error}`);
    }
  };

  // 编辑字段
  const handleEditColumn = (column: SchemaColumn) => {
    setEditingColumn(column);
    editForm.setFieldsValue({
      newColumnName: column.name,
      newColumnType: column.type,
      notNull: column.not_null,
      defaultValue: column.default_value
    });
    setEditModalVisible(true);
  };

  // 提交编辑字段
  const handleSubmitEdit = async (values: any) => {
    if (!editingColumn) return;
    
    try {
      await devApi.editColumn(tableName, editingColumn.name, {
        new_column_name: values.newColumnName,
        new_column_type: values.newColumnType,
        not_null: values.notNull || false,
        default_value: values.defaultValue
      });
      
      message.success('字段编辑成功');
      setEditModalVisible(false);
      setEditingColumn(null);
      editForm.resetFields();
      loadSchema(); // 重新加载表结构
    } catch (error) {
      console.error('Edit column failed:', error);
      message.error(`编辑字段失败: ${error}`);
    }
  };

  // 表格列定义
  const tableColumns: ColumnsType<SchemaColumn> = [
    {
      title: '字段名',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          {record.primary_key && <KeyOutlined style={{ color: '#faad14' }} />}
          <Text strong={record.primary_key}>{name}</Text>
        </Space>
      )
    },
    {
      title: '数据类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: '约束',
      key: 'constraints',
      render: (_, record) => (
        <Space>
          {record.primary_key && <Tag color="gold">主键</Tag>}
          {record.not_null && <Tag color="red">非空</Tag>}
          {record.default_value && <Tag color="green">默认值</Tag>}
        </Space>
      )
    },
    {
      title: '默认值',
      dataIndex: 'default_value',
      key: 'default_value',
      render: (value: any) => value || '-'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditColumn(record)}
            disabled={record.primary_key}
          >
            编辑
          </Button>
          {!record.primary_key && (
            <Popconfirm
              title={`确定要删除字段 "${record.name}" 吗？`}
              description="删除字段将会重建表结构，此操作不可撤销！"
              onConfirm={() => handleDropColumn(record.name)}
              okText="确定删除"
              cancelText="取消"
            >
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  return (
    <Modal
      title={`表结构管理 - ${tableName}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <div>
        {/* 统计信息 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                  {columns.length}
                </div>
                <div style={{ color: '#666' }}>字段总数</div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
                  {columns.filter(col => col.primary_key).length}
                </div>
                <div style={{ color: '#666' }}>主键字段</div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#faad14' }}>
                  {indexes.length}
                </div>
                <div style={{ color: '#666' }}>索引数量</div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 字段管理 */}
        <Card
          title="字段管理"
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setAddModalVisible(true)}
            >
              添加字段
            </Button>
          }
        >
          <Table
            loading={loading}
            columns={tableColumns}
            dataSource={columns}
            rowKey="name"
            pagination={false}
            size="small"
          />
        </Card>

        {/* 索引信息 */}
        {indexes.length > 0 && (
          <Card title="索引信息" style={{ marginTop: 16 }}>
            <Table
              dataSource={indexes}
              columns={[
                { title: '索引名', dataIndex: 'name', key: 'name' },
                { title: '类型', dataIndex: 'origin', key: 'origin' },
                { title: '唯一', dataIndex: 'unique', key: 'unique', render: (val: boolean) => val ? '是' : '否' }
              ]}
              pagination={false}
              size="small"
            />
          </Card>
        )}
      </div>

      {/* 添加字段模态框 */}
      <Modal
        title="添加字段"
        open={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddColumn}
        >
          <Form.Item
            label="字段名"
            name="columnName"
            rules={[
              { required: true, message: '请输入字段名' },
              { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名只能包含字母、数字和下划线，且不能以数字开头' }
            ]}
          >
            <Input placeholder="例如: user_name" />
          </Form.Item>

          <Form.Item
            label="数据类型"
            name="columnType"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择数据类型">
              {dataTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="约束"
            name="notNull"
            valuePropName="checked"
          >
            <Space>
              <input type="checkbox" />
              <span>非空约束</span>
            </Space>
          </Form.Item>

          <Form.Item
            label="默认值"
            name="defaultValue"
          >
            <Input placeholder="可选，例如: 'default_value' 或 0" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑字段模态框 */}
      <Modal
        title={`编辑字段 - ${editingColumn?.name || ''}`}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingColumn(null);
          editForm.resetFields();
        }}
        onOk={() => editForm.submit()}
        destroyOnClose
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleSubmitEdit}
        >
          <Form.Item
            label="字段名"
            name="newColumnName"
            rules={[
              { required: true, message: '请输入字段名' },
              { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名只能包含字母、数字和下划线，且不能以数字开头' }
            ]}
          >
            <Input placeholder="例如: user_name" />
          </Form.Item>

          <Form.Item
            label="数据类型"
            name="newColumnType"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择数据类型">
              {dataTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="约束"
            name="notNull"
            valuePropName="checked"
          >
            <Space>
              <input type="checkbox" />
              <span>非空约束</span>
            </Space>
          </Form.Item>

          <Form.Item
            label="默认值"
            name="defaultValue"
          >
            <Input placeholder="可选，例如: 'default_value' 或 0" />
          </Form.Item>

          {editingColumn && (
            <div style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '12px', 
              borderRadius: '6px',
              marginTop: '16px'
            }}>
              <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
                <strong>当前字段信息:</strong>
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                名称: {editingColumn.name} | 
                类型: {editingColumn.type} | 
                约束: {editingColumn.not_null ? '非空' : '可空'} |
                默认值: {editingColumn.default_value || '无'}
              </div>
              <div style={{ fontSize: '11px', color: '#999', marginTop: '4px' }}>
                ⚠️ 编辑字段将会重建表结构，建议先备份重要数据
              </div>
              <div style={{ fontSize: '11px', color: '#52c41a', marginTop: '2px' }}>
                ✅ 现有数据会自动迁移到新结构（事务保护）
              </div>
            </div>
          )}
        </Form>
      </Modal>
    </Modal>
  );
};

export default SchemaManager;
