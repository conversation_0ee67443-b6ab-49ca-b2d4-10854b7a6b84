// 开发者认证守卫组件
import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import { devApi } from '@/services/devApi';
import { ROUTES } from '@/constants';

interface DevAuthGuardProps {
  children: React.ReactNode;
}

const DevAuthGuard: React.FC<DevAuthGuardProps> = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const sessionInfo = await devApi.getSessionInfo();
      setAuthenticated(sessionInfo.authenticated);
    } catch (error) {
      console.error('Auth check failed:', error);
      setAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="检查认证状态..." />
      </div>
    );
  }

  if (!authenticated) {
    return <Navigate to={ROUTES.DEV_LOGIN} replace />;
  }

  return <>{children}</>;
};

export default DevAuthGuard;
