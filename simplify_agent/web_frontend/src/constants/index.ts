// 应用常量配置

// 路由路径
export const ROUTES = {
  // 用户视角
  USER_DASHBOARD: '/user/dashboard',
  USER_PLANS: '/user/plans',
  USER_PLAN_DETAIL: '/user/plans/:id',
  USER_EXECUTIONS: '/user/executions',
  USER_EXECUTION_DETAIL: '/user/executions/:id',
  USER_STATISTICS: '/user/statistics',
  
  // 开发者视角
  DEV_LOGIN: '/dev/login',
  DEV_DASHBOARD: '/dev/dashboard',
  DEV_TABLES: '/dev/tables',
  DEV_TABLE_DETAIL: '/dev/tables/:tableName',
  DEV_SQL: '/dev/sql',
  
  // 根路径
  ROOT: '/',
} as const;

// 页面标题
export const PAGE_TITLES = {
  USER_DASHBOARD: '用户中心',
  USER_EXECUTIONS: '测试记录',
  USER_EXECUTION_DETAIL: '执行详情',
  USER_STATISTICS: '统计分析',
  
  DEV_LOGIN: '开发者登录',
  DEV_DASHBOARD: '开发者中心',
  DEV_TABLES: '数据库表',
  DEV_TABLE_DETAIL: '表数据管理',
  DEV_SQL: 'SQL执行器',
} as const;

// 执行状态配置
export const EXECUTION_STATUS = {
  SUCCESS: 'success',
  FAILURE: 'failure',
  PENDING: 'pending',
  RUNNING: 'running',
} as const;

export const EXECUTION_STATUS_LABELS = {
  [EXECUTION_STATUS.SUCCESS]: '成功',
  [EXECUTION_STATUS.FAILURE]: '失败',
  [EXECUTION_STATUS.PENDING]: '等待中',
  [EXECUTION_STATUS.RUNNING]: '执行中',
} as const;

export const EXECUTION_STATUS_COLORS = {
  [EXECUTION_STATUS.SUCCESS]: '#52c41a',
  [EXECUTION_STATUS.FAILURE]: '#f5222d',
  [EXECUTION_STATUS.PENDING]: '#faad14',
  [EXECUTION_STATUS.RUNNING]: '#1890ff',
} as const;

// 工具状态配置
export const TOOL_STATUS = {
  SUCCESS: 'success',
  FAILURE: 'failure',
  TIMEOUT: 'timeout',
} as const;

export const TOOL_STATUS_LABELS = {
  [TOOL_STATUS.SUCCESS]: '成功',
  [TOOL_STATUS.FAILURE]: '失败',
  [TOOL_STATUS.TIMEOUT]: '超时',
} as const;

export const TOOL_STATUS_COLORS = {
  [TOOL_STATUS.SUCCESS]: '#52c41a',
  [TOOL_STATUS.FAILURE]: '#f5222d',
  [TOOL_STATUS.TIMEOUT]: '#faad14',
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true,
  SHOW_TOTAL: (total: number, range: [number, number]) =>
    `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
} as const;

// 日期格式
export const DATE_FORMATS = {
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
} as const;

// 图表配置
export const CHART_COLORS = [
  '#1890ff',
  '#52c41a',
  '#faad14',
  '#f5222d',
  '#722ed1',
  '#fa8c16',
  '#a0d911',
  '#13c2c2',
  '#eb2f96',
  '#722ed1',
] as const;

// 本地存储键名
export const STORAGE_KEYS = {
  DEV_AUTH_TOKEN: 'dev_auth_token',
  USER_PREFERENCES: 'user_preferences',
  TABLE_VIEW_CONFIG: 'table_view_config',
} as const;

// 错误信息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络错误，请稍后重试',
  AUTH_REQUIRED: '请先登录',
  PERMISSION_DENIED: '权限不足',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误',
  VALIDATION_ERROR: '输入参数有误',
} as const;