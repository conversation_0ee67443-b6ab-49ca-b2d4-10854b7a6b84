// 开发者视角API服务
import { request } from './api';
import type {
  DatabaseTable,
  TableRecord
} from '@/types/models';
import type {
  PaginatedResponse,
  FilterParams
} from '@/types/api';

export const devApi = {
  // 认证相关
  authenticate: (password: string): Promise<{
    authenticated: boolean;
    expires_in: number;
  }> => {
    return request.post('/dev/auth', { password });
  },
  
  logout: (): Promise<void> => {
    return request.post('/dev/logout');
  },
  
  getSessionInfo: (): Promise<{
    authenticated: boolean;
    login_time?: string;
    session_id: boolean;
  }> => {
    return request.get('/dev/session');
  },
  
  // 数据库管理
  getDatabaseInfo: (): Promise<{
    database_info: any;
    health_status: any;
    table_counts: Record<string, number>;
  }> => {
    return request.get('/dev/database/info');
  },
  
  getTableList: (): Promise<{
    tables: DatabaseTable[];
    table_count: number;
  }> => {
    return request.get('/dev/tables');
  },
  
  getTableData: (
    tableName: string,
    params: FilterParams & { order_by?: string }
  ): Promise<PaginatedResponse<TableRecord>> => {
    return request.get(`/dev/table/${tableName}/data`, params);
  },
  
  getTableSchema: (tableName: string): Promise<{
    table_name: string;
    columns: any[];
    indexes: any[];
    foreign_keys: any[];
  }> => {
    return request.get(`/dev/table/${tableName}/schema`);
  },
  
  // 记录操作
  createRecord: (tableName: string, data: TableRecord): Promise<{
    record_id: number;
    table_name: string;
  }> => {
    return request.post(`/dev/table/${tableName}/data`, { data });
  },
  
  updateRecord: (
    tableName: string,
    recordId: number,
    data: Partial<TableRecord>
  ): Promise<{
    record_id: number;
    table_name: string;
  }> => {
    return request.put(`/dev/table/${tableName}/data/${recordId}`, { data });
  },
  
  deleteRecord: (tableName: string, recordId: number): Promise<void> => {
    return request.delete(`/dev/table/${tableName}/data/${recordId}`);
  },
  
  // 表结构操作
  addColumn: (
    tableName: string,
    columnData: {
      column_name: string;
      column_type: string;
      not_null?: boolean;
      default_value?: any;
    }
  ): Promise<{
    table_name: string;
    column_name: string;
    column_type: string;
  }> => {
    return request.post(`/dev/table/${tableName}/column`, columnData);
  },
  
  dropColumn: (
    tableName: string,
    columnName: string
  ): Promise<{
    table_name: string;
    column_name: string;
  }> => {
    return request.delete(`/dev/table/${tableName}/column/${columnName}`);
  },
  
  editColumn: (
    tableName: string,
    columnName: string,
    columnData: {
      new_column_name: string;
      new_column_type: string;
      not_null?: boolean;
      default_value?: any;
    }
  ): Promise<{
    table_name: string;
    old_column_name: string;
    new_column_name: string;
    new_column_type: string;
  }> => {
    return request.put(`/dev/table/${tableName}/column/${columnName}`, columnData);
  },
  
  // SQL执行
  executeSQL: (
    sql: string,
    params?: any[]
  ): Promise<{
    success: boolean;
    data?: any[];
    row_count?: number;
    affected_rows?: number;
    message?: string;
  }> => {
    return request.post('/dev/sql', { sql, params });
  },
  
  // 健康检查
  healthCheck: (): Promise<{ status: string; service: string; version: string }> => {
    return request.get('/dev/health');
  },
};