// API服务基础配置
import axios from 'axios';
import type { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { message } from 'antd';
import type { ApiResponse, ErrorResponse } from '@/types/api';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 30000,
  withCredentials: true, // 支持cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 可以在这里添加认证token
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;
    
    // 检查业务状态码
    if (!data.success) {
      const errorData = data as ErrorResponse;
      message.error(errorData.message || '请求失败');
      return Promise.reject(new Error(errorData.message));
    }
    
    return response;
  },
  (error) => {
    let errorMessage = '网络错误，请稍后重试';
    
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          errorMessage = data?.message || '请求参数错误';
          break;
        case 401:
          errorMessage = '未授权，请先登录';
          break;
        case 403:
          errorMessage = '权限不足';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = data?.message || `请求失败 (${status})`;
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请稍后重试';
    }
    
    message.error(errorMessage);
    return Promise.reject(error);
  }
);

// 泛型请求方法
export const request = {
  get: <T = any>(url: string, params?: any): Promise<T> => {
    return api.get(url, { params }).then(res => res.data.data);
  },
  
  post: <T = any>(url: string, data?: any): Promise<T> => {
    return api.post(url, data).then(res => res.data.data);
  },
  
  put: <T = any>(url: string, data?: any): Promise<T> => {
    return api.put(url, data).then(res => res.data.data);
  },
  
  delete: <T = any>(url: string): Promise<T> => {
    return api.delete(url).then(res => res.data.data);
  },
  
  // 上传文件
  upload: <T = any>(url: string, formData: FormData): Promise<T> => {
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(res => res.data.data);
  },
};

export default api;