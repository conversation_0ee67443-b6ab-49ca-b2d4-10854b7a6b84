// 统计分析API服务
import { request } from './api';
import type {
  StatisticsOverview,
  SuccessRateTrend,
  InstructionAnalysis
} from '@/types/models';
import type { FilterParams } from '@/types/api';

export const statisticsApi = {
  // 获取概览统计
  getOverviewStatistics: (): Promise<StatisticsOverview> => {
    return request.get('/statistics/overview');
  },
  
  // 获取成功率趋势分析
  getSuccessRateTrend: (params: {
    days?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<SuccessRateTrend[]> => {
    return request.get('/statistics/success-rate-trend', params);
  },
  
  // 获取指令模式分析
  getInstructionAnalysis: (params: FilterParams): Promise<{
    items: InstructionAnalysis[];
    total_patterns: number;
  }> => {
    return request.get('/statistics/instruction-analysis', params);
  },
  
  // 获取执行时长分布
  getDurationDistribution: (): Promise<Array<{
    range: string;
    count: number;
    percentage: number;
  }>> => {
    return request.get('/statistics/duration-distribution');
  },
  
  // 获取平台使用统计
  getPlatformStatistics: (): Promise<Array<{
    platform: string;
    count: number;
    success_rate: number;
  }>> => {
    return request.get('/statistics/platform-stats');
  },
  
  // 获取工具使用统计
  getToolStatistics: (): Promise<Array<{
    tool_name: string;
    usage_count: number;
    success_rate: number;
    avg_execution_time: number;
  }>> => {
    return request.get('/statistics/tool-stats');
  },
  
  // 获取时间段统计
  getTimeRangeStatistics: (params: {
    start_date: string;
    end_date: string;
  }): Promise<{
    total_executions: number;
    success_rate: number;
    average_duration: number;
    daily_breakdown: Array<{
      date: string;
      executions: number;
      success_rate: number;
    }>;
  }> => {
    return request.get('/statistics/time-range', params);
  },
  
  // 健康检查
  healthCheck: (): Promise<{ status: string; service: string; version: string }> => {
    return request.get('/statistics/health');
  },
};