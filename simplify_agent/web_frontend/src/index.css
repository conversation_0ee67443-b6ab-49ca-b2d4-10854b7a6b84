/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  background-color: #ffffff;
}

body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

/* 链接样式 */
a {
  color: #1890ff;
  text-decoration: none;
}

a:hover {
  color: #40a9ff;
  text-decoration: none;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 4px;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02);
}

/* 表格样式优化 */
.ant-table {
  border-radius: 6px;
}

/* 布局样式 */
.app-layout {
  min-height: 100vh;
  background-color: #f0f2f5;
}

/* 页面内容区域 */
.page-content {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px);
}

/* 页面标题 */
.page-title {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

/* 统计卡片 */
.statistic-card {
  text-align: center;
  padding: 16px;
}

.statistic-card .ant-statistic-title {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  margin-bottom: 4px;
}

.statistic-card .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px;
  }
  
  .page-title {
    font-size: 20px;
    margin-bottom: 16px;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 48px 24px;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 48px 24px;
  color: rgba(0, 0, 0, 0.45);
}