// SQL执行器页面
import React, { useState, useRef } from 'react';
import AppLayout from '@/components/common/AppLayout';
import {
  Typography,
  Card,
  Button,
  Space,
  Input,
  Table,
  Alert,
  Tabs,
  Row,
  Col,
  Tag,
  message,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  ClearOutlined,
  HistoryOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  CodeOutlined,
  TableOutlined
} from '@ant-design/icons';
import { devApi } from '@/services/devApi';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface QueryResult {
  success: boolean;
  data?: any[];
  row_count?: number;
  affected_rows?: number;
  message?: string;
  error?: string;
  execution_time?: number;
}

interface QueryHistory {
  id: number;
  sql: string;
  timestamp: string;
  success: boolean;
  result?: QueryResult;
}

const DevSQL: React.FC = () => {
  const [sql, setSql] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<QueryResult[]>([]);
  const [history, setHistory] = useState<QueryHistory[]>([]);
  const [activeTab, setActiveTab] = useState('editor');
  const textAreaRef = useRef<any>(null);

  // 执行SQL
  const executeSQL = async () => {
    if (!sql.trim()) {
      message.warning('请输入SQL语句');
      return;
    }

    try {
      setLoading(true);
      const startTime = Date.now();
      
      const result = await devApi.executeSQL(sql.trim());
      const executionTime = Date.now() - startTime;
      
      const queryResult: QueryResult = {
        ...result,
        execution_time: executionTime
      };
      
      setResults([queryResult, ...results]);
      
      // 添加到历史记录
      const historyItem: QueryHistory = {
        id: Date.now(),
        sql: sql.trim(),
        timestamp: new Date().toLocaleString(),
        success: result.success,
        result: queryResult
      };
      setHistory([historyItem, ...history.slice(0, 49)]); // 保持最近50条记录
      
      if (result.success) {
        message.success(`SQL执行成功 (${executionTime}ms)`);
        setActiveTab('results');
      } else {
        message.error('SQL执行失败');
      }
      
    } catch (error: any) {
      console.error('Execute SQL failed:', error);
      const errorResult: QueryResult = {
        success: false,
        error: error.response?.data?.message || error.message,
        execution_time: Date.now() - Date.now()
      };
      setResults([errorResult, ...results]);
      message.error('SQL执行出错：' + errorResult.error);
    } finally {
      setLoading(false);
    }
  };

  // 清空编辑器
  const clearEditor = () => {
    setSql('');
    textAreaRef.current?.focus();
  };

  // 插入示例SQL
  const insertSample = (sampleSQL: string) => {
    setSql(sampleSQL);
    textAreaRef.current?.focus();
  };

  // 从历史记录加载SQL
  const loadFromHistory = (historyItem: QueryHistory) => {
    setSql(historyItem.sql);
    setActiveTab('editor');
    textAreaRef.current?.focus();
  };

  // 导出结果
  const exportResults = (result: QueryResult) => {
    if (!result.data || result.data.length === 0) {
      message.warning('没有可导出的数据');
      return;
    }
    
    try {
      const csv = [
        Object.keys(result.data[0]).join(','),
        ...result.data.map(row => 
          Object.values(row).map(val => 
            typeof val === 'string' ? `"${val}"` : val
          ).join(',')
        )
      ].join('\n');
      
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `query_result_${Date.now()}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('结果已导出');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 示例SQL语句
  const sampleQueries = [
    {
      name: '查看所有表',
      sql: "SELECT name FROM sqlite_master WHERE type='table';"
    },
    {
      name: '查看表结构',
      sql: "PRAGMA table_info(test_executions);"
    },
    {
      name: '统计执行记录',
      sql: "SELECT execution_status, COUNT(*) as count FROM test_executions GROUP BY execution_status;"
    },
    {
      name: '最近的执行记录',
      sql: "SELECT execution_id, original_request, execution_status, created_at FROM test_executions ORDER BY created_at DESC LIMIT 10;"
    }
  ];

  // 生成结果表格列
  const generateResultColumns = (data: any[]) => {
    if (!data || data.length === 0) return [];
    
    return Object.keys(data[0]).map(key => ({
      title: key,
      dataIndex: key,
      key,
      ellipsis: true,
      render: (value: any) => {
        if (value === null || value === undefined) {
          return <Text type="secondary">NULL</Text>;
        }
        if (typeof value === 'object') {
          return <Text code>{JSON.stringify(value)}</Text>;
        }
        return String(value);
      }
    }));
  };

  return (
    <AppLayout mode="dev">
      <div>
        {/* 页面标题 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={3}>
            <CodeOutlined /> SQL执行器
          </Title>
          <Paragraph type="secondary">
            在此执行自定义SQL查询，支持SELECT、INSERT、UPDATE、DELETE等操作
          </Paragraph>
        </div>

        {/* 主要内容区域 */}
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            {
              key: 'editor',
              label: (
                <span>
                  <CodeOutlined />
                  SQL编辑器
                </span>
              ),
              children: (
                <div>
                  {/* SQL编辑区域 */}
                  <Card title="SQL语句" style={{ marginBottom: 16 }}>
                    <div style={{ marginBottom: 16 }}>
                      <Space wrap>
                        <Button 
                          type="primary" 
                          icon={<PlayCircleOutlined />}
                          onClick={executeSQL}
                          loading={loading}
                        >
                          执行SQL
                        </Button>
                        <Button 
                          icon={<ClearOutlined />}
                          onClick={clearEditor}
                        >
                          清空
                        </Button>
                        <Divider type="vertical" />
                        <Text type="secondary">Ctrl+Enter 快速执行</Text>
                      </Space>
                    </div>
                    <TextArea
                      ref={textAreaRef}
                      value={sql}
                      onChange={(e) => setSql(e.target.value)}
                      placeholder="请输入SQL语句..."
                      autoSize={{ minRows: 8, maxRows: 16 }}
                      onKeyDown={(e) => {
                        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                          executeSQL();
                        }
                      }}
                      style={{ 
                        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                        fontSize: '14px'
                      }}
                    />
                  </Card>

                  {/* 示例查询 */}
                  <Card title="示例查询" size="small">
                    <Row gutter={[8, 8]}>
                      {sampleQueries.map((query, index) => (
                        <Col key={index} xs={24} sm={12} md={6}>
                          <Button
                            size="small"
                            block
                            onClick={() => insertSample(query.sql)}
                            style={{ textAlign: 'left', height: 'auto', padding: '8px 12px' }}
                          >
                            <div>
                              <Text strong>{query.name}</Text>
                              <br />
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {query.sql.substring(0, 50)}...
                              </Text>
                            </div>
                          </Button>
                        </Col>
                      ))}
                    </Row>
                  </Card>
                </div>
              )
            },
            {
              key: 'results',
              label: (
                <span>
                  <TableOutlined />
                  执行结果 {results.length > 0 && <Tag color="blue">{results.length}</Tag>}
                </span>
              ),
              children: (
                <div>
                  {results.length === 0 ? (
                    <Card>
                      <div style={{ textAlign: 'center', padding: '40px 0' }}>
                        <InfoCircleOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                        <div style={{ marginTop: '16px', color: '#666' }}>
                          暂无执行结果，请先执行SQL语句
                        </div>
                      </div>
                    </Card>
                  ) : (
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {results.map((result, index) => (
                        <Card 
                          key={index}
                          size="small"
                          title={
                            <Space>
                              <Tag color={result.success ? 'green' : 'red'}>
                                {result.success ? '成功' : '失败'}
                              </Tag>
                              <Text type="secondary">执行时间: {result.execution_time}ms</Text>
                            </Space>
                          }
                          extra={
                            result.success && result.data && result.data.length > 0 && (
                              <Button 
                                size="small" 
                                icon={<DownloadOutlined />}
                                onClick={() => exportResults(result)}
                              >
                                导出CSV
                              </Button>
                            )
                          }
                        >
                          {result.success ? (
                            <div>
                              {result.data && result.data.length > 0 ? (
                                <div>
                                  <div style={{ marginBottom: 8 }}>
                                    <Text>返回 <strong>{result.row_count || result.data.length}</strong> 行结果</Text>
                                  </div>
                                  <Table
                                    columns={generateResultColumns(result.data)}
                                    dataSource={result.data}
                                    size="small"
                                    pagination={{ pageSize: 10 }}
                                    scroll={{ x: true }}
                                    rowKey={(_, idx) => idx || 0}
                                  />
                                </div>
                              ) : result.affected_rows !== undefined ? (
                                <Alert 
                                  message={`操作成功，影响了 ${result.affected_rows} 行记录`}
                                  type="success" 
                                  showIcon
                                />
                              ) : (
                                <Alert 
                                  message={result.message || '执行成功'}
                                  type="success" 
                                  showIcon
                                />
                              )}
                            </div>
                          ) : (
                            <Alert 
                              message="SQL执行失败" 
                              description={result.error || result.message}
                              type="error" 
                              showIcon
                            />
                          )}
                        </Card>
                      ))}
                    </Space>
                  )}
                </div>
              )
            },
            {
              key: 'history',
              label: (
                <span>
                  <HistoryOutlined />
                  历史记录 {history.length > 0 && <Tag color="blue">{history.length}</Tag>}
                </span>
              ),
              children: (
                <div>
                  {history.length === 0 ? (
                    <Card>
                      <div style={{ textAlign: 'center', padding: '40px 0' }}>
                        <HistoryOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                        <div style={{ marginTop: '16px', color: '#666' }}>
                          暂无执行历史
                        </div>
                      </div>
                    </Card>
                  ) : (
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {history.map((item) => (
                        <Card 
                          key={item.id} 
                          size="small"
                          title={
                            <Space>
                              <Tag color={item.success ? 'green' : 'red'}>
                                {item.success ? '成功' : '失败'}
                              </Tag>
                              <Text type="secondary">{item.timestamp}</Text>
                            </Space>
                          }
                          extra={
                            <Space>
                              <Button 
                                size="small"
                                onClick={() => loadFromHistory(item)}
                              >
                                重新执行
                              </Button>
                            </Space>
                          }
                        >
                          <div style={{ 
                            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                            fontSize: '13px',
                            background: '#f5f5f5',
                            padding: '8px 12px',
                            borderRadius: '4px',
                            marginBottom: '8px'
                          }}>
                            {item.sql}
                          </div>
                          
                          {item.result && (
                            <div>
                              {item.result.success ? (
                                <Text type="secondary">
                                  {item.result.data ? 
                                    `返回 ${item.result.row_count || item.result.data.length} 行` :
                                    item.result.affected_rows !== undefined ?
                                    `影响 ${item.result.affected_rows} 行` :
                                    '执行成功'
                                  }
                                  {item.result.execution_time && ` (${item.result.execution_time}ms)`}
                                </Text>
                              ) : (
                                <Text type="danger">
                                  错误: {item.result.error || item.result.message}
                                </Text>
                              )}
                            </div>
                          )}
                        </Card>
                      ))}
                    </Space>
                  )}
                </div>
              )
            }
          ]}
        />
      </div>
    </AppLayout>
  );
};

export default DevSQL;