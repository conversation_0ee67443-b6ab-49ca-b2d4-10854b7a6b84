// 开发者登录页面
import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { LockOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constants';
import { devApi } from '@/services/devApi';

const { Title, Paragraph } = Typography;

const DevLogin: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: { password: string }) => {
    try {
      setLoading(true);
      
      const result = await devApi.authenticate(values.password);
      
      if (result.authenticated) {
        message.success('登录成功');
        navigate(ROUTES.DEV_DASHBOARD);
      }
    } catch (error: any) {
      console.error('Login failed:', error);
      message.error(error.response?.data?.message || '登录失败，请检查密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card 
        style={{ 
          width: 400,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3} style={{ marginBottom: 8 }}>开发者模式</Title>
          <Paragraph type="secondary">
            请输入开发者密码以访问数据库管理功能
          </Paragraph>
        </div>

        <Form
          form={form}
          name="dev-login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入开发者密码' },
              { min: 4, message: '密码至少4位' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请输入开发者密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 16 }}>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              block
              style={{ height: 44 }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button 
            type="link" 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(ROUTES.USER_DASHBOARD)}
          >
            返回用户模式
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default DevLogin;