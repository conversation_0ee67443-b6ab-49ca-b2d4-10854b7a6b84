// 开发者控制台页面
import React, { useEffect, useState } from 'react';
import AppLayout from '@/components/common/AppLayout';
import {
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Space,
  Alert,
  Spin,
  Tag
} from 'antd';
import {
  DatabaseOutlined,
  TableOutlined,
  FileTextOutlined,
  ToolOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { devApi } from '@/services/devApi';
import { ROUTES } from '@/constants';
import type { DatabaseTable } from '@/types/models';
import type { ColumnsType } from 'antd/es/table';

const { Title, Paragraph } = Typography;

interface DatabaseInfo {
  database_info: any;
  health_status: any;
  table_counts: Record<string, number>;
}

const DevDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [databaseInfo, setDatabaseInfo] = useState<DatabaseInfo | null>(null);
  const [tables, setTables] = useState<DatabaseTable[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 加载数据库信息
  const loadDatabaseInfo = async () => {
    try {
      const info = await devApi.getDatabaseInfo();
      setDatabaseInfo(info);
    } catch (error) {
      console.error('Load database info failed:', error);
      setError('加载数据库信息失败');
    }
  };

  // 加载表信息
  const loadTables = async () => {
    try {
      const result = await devApi.getTableList();
      setTables(result.tables);
    } catch (error) {
      console.error('Load tables failed:', error);
      setError('加载表信息失败');
    }
  };

  // 加载所有数据
  const loadAllData = async () => {
    try {
      setLoading(true);
      setError(null);
      await Promise.all([loadDatabaseInfo(), loadTables()]);
    } catch (err) {
      setError('加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllData();
  }, []);

  // 表格列定义
  const tableColumns: ColumnsType<DatabaseTable> = [
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      render: (name: string) => (
        <Button 
          type="link" 
          onClick={() => navigate(ROUTES.DEV_TABLE_DETAIL.replace(':tableName', name))}
        >
          {name}
        </Button>
      )
    },
    {
      title: '字段数',
      dataIndex: 'column_count',
      key: 'column_count',
      width: 100,
      align: 'right'
    },
    {
      title: '记录数',
      dataIndex: 'record_count',
      key: 'record_count',
      width: 120,
      align: 'right',
      render: (count: number) => count.toLocaleString()
    }
  ];

  if (error) {
    return (
      <AppLayout mode="dev">
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadAllData}>
              重新加载
            </Button>
          }
        />
      </AppLayout>
    );
  }

  return (
    <AppLayout mode="dev">
      <div>
        {/* 页面标题 */}
        <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3}>开发者控制台</Title>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadAllData}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary"
              icon={<ToolOutlined />}
              onClick={() => navigate(ROUTES.DEV_SQL)}
            >
              SQL执行器
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          {/* 系统状态概览 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="数据库状态"
                  value={databaseInfo?.health_status?.status === 'healthy' ? '正常' : '异常'}
                  prefix={
                    databaseInfo?.health_status?.status === 'healthy' ? 
                    <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                    <ExclamationCircleOutlined style={{ color: '#f5222d' }} />
                  }
                  valueStyle={{ 
                    color: databaseInfo?.health_status?.status === 'healthy' ? '#52c41a' : '#f5222d'
                  }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="数据表总数"
                  value={tables.length}
                  prefix={<TableOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="总记录数"
                  value={tables.reduce((sum, table) => sum + table.record_count, 0)}
                  prefix={<FileTextOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="数据库大小"
                  value={databaseInfo?.database_info?.size || 'N/A'}
                  prefix={<DatabaseOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 数据库详细信息 */}
          {databaseInfo && (
            <Card title="数据库信息" style={{ marginBottom: 24 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Paragraph>
                    <strong>数据库路径:</strong> {databaseInfo.database_info?.path || 'N/A'}
                  </Paragraph>
                  <Paragraph>
                    <strong>SQLite版本:</strong> {databaseInfo.database_info?.version || 'N/A'}
                  </Paragraph>
                </Col>
                <Col span={12}>
                  <Paragraph>
                    <strong>连接状态:</strong>{' '}
                    <Tag color={databaseInfo.health_status?.status === 'healthy' ? 'green' : 'red'}>
                      {databaseInfo.health_status?.status === 'healthy' ? '已连接' : '连接异常'}
                    </Tag>
                  </Paragraph>
                  <Paragraph>
                    <strong>最后检查时间:</strong> {new Date().toLocaleString()}
                  </Paragraph>
                </Col>
              </Row>
            </Card>
          )}

          {/* 数据表概览 */}
          <Card 
            title="数据表概览" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate(ROUTES.DEV_TABLES)}
              >
                查看全部表
              </Button>
            }
          >
            <Table
              columns={tableColumns}
              dataSource={tables}
              rowKey="table_name"
              pagination={{ pageSize: 10 }}
              size="middle"
            />
          </Card>
        </Spin>
      </div>
    </AppLayout>
  );
};

export default DevDashboard;