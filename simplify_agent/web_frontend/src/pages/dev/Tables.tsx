// 数据库表管理页面
import React, { useEffect, useState } from 'react';
import AppLayout from '@/components/common/AppLayout';
import SchemaManager from '@/components/dev/SchemaManager';
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Alert,
  Spin,
  Tag,
  Tooltip,
  Row,
  Col
} from 'antd';
import {
  TableOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { devApi } from '@/services/devApi';
import { ROUTES } from '@/constants';
import type { DatabaseTable } from '@/types/models';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Search } = Input;

const DevTables: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [tables, setTables] = useState<DatabaseTable[]>([]);
  const [filteredTables, setFilteredTables] = useState<DatabaseTable[]>([]);
  const [searchText, setSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('table_name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [error, setError] = useState<string | null>(null);
  const [schemaModalVisible, setSchemaModalVisible] = useState(false);
  const [selectedTable, setSelectedTable] = useState<string>('');

  // 加载表数据
  const loadTables = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await devApi.getTableList();
      setTables(result.tables);
      setFilteredTables(result.tables);
    } catch (error) {
      console.error('Load tables failed:', error);
      setError('加载表信息失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTables();
  }, []);

  // 搜索和过滤
  useEffect(() => {
    let filtered = tables.filter(table =>
      table.table_name.toLowerCase().includes(searchText.toLowerCase())
    );

    // 排序
    filtered = filtered.sort((a, b) => {
      const aValue = a[sortField as keyof DatabaseTable];
      const bValue = b[sortField as keyof DatabaseTable];
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredTables(filtered);
  }, [tables, searchText, sortField, sortOrder]);

  // 表格列定义
  const columns: ColumnsType<DatabaseTable> = [
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      sorter: true,
      render: (name: string) => (
        <Space>
          <TableOutlined />
          <strong>{name}</strong>
        </Space>
      )
    },
    {
      title: '字段信息',
      key: 'columns',
      render: (_, record) => (
        <div>
          <div>字段数: <strong>{record.column_count}</strong></div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
            主键: {record.columns.filter(col => col.primary_key).map(col => col.name).join(', ') || '无'}
          </div>
        </div>
      )
    },
    {
      title: '数据统计',
      key: 'stats',
      align: 'center',
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
            {record.record_count.toLocaleString()}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            条记录
          </div>
        </div>
      )
    },
    {
      title: '字段类型分布',
      key: 'column_types',
      render: (_, record) => {
        const typeCount: Record<string, number> = {};
        record.columns.forEach(col => {
          const baseType = col.type.split('(')[0].toUpperCase();
          typeCount[baseType] = (typeCount[baseType] || 0) + 1;
        });
        
        return (
          <Space wrap>
            {Object.entries(typeCount).map(([type, count]) => (
              <Tag key={type} color="blue">
                {type}: {count}
              </Tag>
            ))}
          </Space>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看表详情">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(ROUTES.DEV_TABLE_DETAIL.replace(':tableName', record.table_name))}
            >
              详情
            </Button>
          </Tooltip>
          <Tooltip title="表结构管理">
            <Button
              size="small"
              icon={<SettingOutlined />}
              onClick={() => {
                setSelectedTable(record.table_name);
                setSchemaModalVisible(true);
              }}
            >
              结构
            </Button>
          </Tooltip>
        </Space>
      )
    }
  ];

  if (error) {
    return (
      <AppLayout mode="dev">
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadTables}>
              重新加载
            </Button>
          }
        />
      </AppLayout>
    );
  }

  return (
    <AppLayout mode="dev">
      <div>
        {/* 页面标题 */}
        <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3}>数据库表管理</Title>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadTables}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        {/* 统计信息 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                  {tables.length}
                </div>
                <div style={{ color: '#666' }}>数据表总数</div>
              </div>
            </Card>
          </Col>
          <Col xs={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                  {tables.reduce((sum, table) => sum + table.record_count, 0).toLocaleString()}
                </div>
                <div style={{ color: '#666' }}>记录总数</div>
              </div>
            </Card>
          </Col>
          <Col xs={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                  {tables.reduce((sum, table) => sum + table.column_count, 0)}
                </div>
                <div style={{ color: '#666' }}>字段总数</div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 搜索和过滤 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索表名"
                allowClear
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Select
                value={sortField}
                onChange={setSortField}
                style={{ width: '100%' }}
              >
                <Select.Option value="table_name">表名</Select.Option>
                <Select.Option value="column_count">字段数</Select.Option>
                <Select.Option value="record_count">记录数</Select.Option>
              </Select>
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Select
                value={sortOrder}
                onChange={setSortOrder}
                style={{ width: '100%' }}
              >
                <Select.Option value="asc">升序</Select.Option>
                <Select.Option value="desc">降序</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={24} md={8}>
              <div style={{ textAlign: 'right' }}>
                显示 {filteredTables.length} / {tables.length} 个表
              </div>
            </Col>
          </Row>
        </Card>

        {/* 表格 */}
        <Card>
          <Spin spinning={loading}>
            <Table
              columns={columns}
              dataSource={filteredTables}
              rowKey="table_name"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
              }}
              size="middle"
            />
          </Spin>
        </Card>

        {/* 表结构管理模态框 */}
        <SchemaManager
          tableName={selectedTable}
          visible={schemaModalVisible}
          onClose={() => {
            setSchemaModalVisible(false);
            setSelectedTable('');
            // 重新加载表数据以更新统计信息
            loadTables();
          }}
        />
      </div>
    </AppLayout>
  );
};

export default DevTables;