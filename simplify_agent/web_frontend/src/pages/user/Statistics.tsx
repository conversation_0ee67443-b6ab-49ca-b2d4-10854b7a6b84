// 统计分析页面
import React, { useEffect, useState } from 'react';
import AppLayout from '@/components/common/AppLayout';
import {
  Card,
  Row,
  Col,
  Typography,
  Statistic,
  DatePicker,
  Space,
  Spin,
  Alert,
  Table,
  Progress,
  Empty
} from 'antd';
import {
  Bar<PERSON>hartOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';
import { statisticsApi } from '@/services/statisticsApi';
import { formatDuration, formatSuccessRate } from '@/utils';
import { CHART_COLORS } from '@/constants';
import type { 
  StatisticsOverview, 
  SuccessRateTrend, 
  InstructionAnalysis 
} from '@/types/models';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const UserStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overview, setOverview] = useState<StatisticsOverview | null>(null);
  const [trendData, setTrendData] = useState<SuccessRateTrend[]>([]);
  const [instructionData, setInstructionData] = useState<InstructionAnalysis[]>([]);
  const [platformData, setPlatformData] = useState<any[]>([]);
  const [toolData, setToolData] = useState<any[]>([]);
  const [durationData, setDurationData] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  // 日期范围
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);

  // 加载概览数据
  const loadOverviewData = async () => {
    try {
      const data = await statisticsApi.getOverviewStatistics();
      setOverview(data);
    } catch (error) {
      console.error('Load overview failed:', error);
    }
  };

  // 加载趋势数据
  const loadTrendData = async () => {
    try {
      const data = await statisticsApi.getSuccessRateTrend({
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD')
      });
      setTrendData(data);
    } catch (error) {
      console.error('Load trend data failed:', error);
    }
  };

  // 加载指令分析数据
  const loadInstructionData = async () => {
    try {
      const data = await statisticsApi.getInstructionAnalysis({
        page: 1,
        page_size: 10
      });
      setInstructionData(data.items);
    } catch (error) {
      console.error('Load instruction data failed:', error);
    }
  };

  // 加载平台统计
  const loadPlatformData = async () => {
    try {
      const data = await statisticsApi.getPlatformStatistics();
      setPlatformData(data);
    } catch (error) {
      console.error('Load platform data failed:', error);
    }
  };

  // 加载工具统计
  const loadToolData = async () => {
    try {
      const data = await statisticsApi.getToolStatistics();
      setToolData(data);
    } catch (error) {
      console.error('Load tool data failed:', error);
    }
  };

  // 加载时长分布
  const loadDurationData = async () => {
    try {
      const data = await statisticsApi.getDurationDistribution();
      setDurationData(data);
    } catch (error) {
      console.error('Load duration data failed:', error);
    }
  };

  // 加载所有数据
  const loadAllData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await Promise.all([
        loadOverviewData(),
        loadTrendData(),
        loadInstructionData(),
        loadPlatformData(),
        loadToolData(),
        loadDurationData()
      ]);
    } catch (err) {
      setError('加载统计数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllData();
  }, []);

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates) {
      setDateRange([dates[0], dates[1]]);
      loadTrendData();
    }
  };

  // 成功率趋势图配置
  const getTrendChartOption = () => {
    return {
      title: {
        text: '成功率趋势',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const item = params[0];
          return `
            ${item.axisValue}<br/>
            执行总数: ${item.data.total || 0}<br/>
            成功数: ${item.data.success || 0}<br/>
            成功率: ${item.value.toFixed(1)}%
          `;
        }
      },
      xAxis: {
        type: 'category',
        data: trendData.map(item => dayjs(item.date).format('MM-DD'))
      },
      yAxis: {
        type: 'value',
        name: '成功率 (%)',
        min: 0,
        max: 100
      },
      series: [{
        data: trendData.map(item => ({
          value: item.success_rate * 100,
          total: item.total_count,
          success: item.success_count
        })),
        type: 'line',
        smooth: true,
        itemStyle: { color: CHART_COLORS[0] },
        areaStyle: { opacity: 0.3 }
      }],
      grid: { left: 60, right: 30, top: 60, bottom: 30 }
    };
  };

  // 平台分布饼图配置
  const getPlatformChartOption = () => {
    return {
      title: {
        text: '平台分布',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '平台统计',
        type: 'pie',
        radius: '60%',
        data: platformData.map((item, index) => ({
          value: item.count,
          name: item.platform,
          itemStyle: { color: CHART_COLORS[index % CHART_COLORS.length] }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };
  };

  // 时长分布柱图配置
  const getDurationChartOption = () => {
    return {
      title: {
        text: '执行时长分布',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const item = params[0];
          return `
            ${item.axisValue}<br/>
            数量: ${item.value}<br/>
            占比: ${item.data.percentage || 0}%
          `;
        }
      },
      xAxis: {
        type: 'category',
        data: durationData.map(item => item.range)
      },
      yAxis: {
        type: 'value',
        name: '数量'
      },
      series: [{
        data: durationData.map(item => ({
          value: item.count,
          percentage: item.percentage
        })),
        type: 'bar',
        itemStyle: { color: CHART_COLORS[1] }
      }],
      grid: { left: 60, right: 30, top: 60, bottom: 30 }
    };
  };

  // 指令分析表格列
  const instructionColumns: ColumnsType<InstructionAnalysis> = [
    {
      title: '指令模式',
      dataIndex: 'instruction_pattern',
      key: 'instruction_pattern',
      ellipsis: true
    },
    {
      title: '执行次数',
      dataIndex: 'total_count',
      key: 'total_count',
      width: 100,
      align: 'right'
    },
    {
      title: '成功次数',
      dataIndex: 'success_count',
      key: 'success_count',
      width: 100,
      align: 'right'
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      width: 120,
      render: (rate: number) => (
        <Progress 
          percent={Math.round(rate * 100)} 
          size="small" 
          format={percent => `${percent}%`}
        />
      )
    },
    {
      title: '平均时长',
      dataIndex: 'average_duration',
      key: 'average_duration',
      width: 120,
      render: (duration: number) => formatDuration(duration)
    }
  ];

  if (error) {
    return (
      <AppLayout mode="user">
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <button onClick={loadAllData}>重新加载</button>
            </Space>
          }
        />
      </AppLayout>
    );
  }

  return (
    <AppLayout mode="user">
      <div>
        <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3}>统计分析</Title>
          <Space>
            <span>时间范围：</span>
            <RangePicker 
              value={dateRange}
              onChange={handleDateRangeChange}
              format="YYYY-MM-DD"
            />
          </Space>
        </div>

        <Spin spinning={loading}>
          {/* 概览统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="总执行数"
                  value={overview?.total_executions || 0}
                  prefix={<PlayCircleOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="成功率"
                  value={overview?.success_rate || 0}
                  suffix="%"
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                  precision={1}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="平均时长"
                  value={overview?.average_duration ? formatDuration(overview.average_duration) : '-'}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="本周执行"
                  value={overview?.this_week_executions || 0}
                  prefix={<TrophyOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} lg={12}>
              <Card>
                {trendData.length > 0 ? (
                  <ReactECharts 
                    option={getTrendChartOption()} 
                    style={{ height: '300px' }}
                  />
                ) : (
                  <Empty description="暂无趋势数据" />
                )}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card>
                {platformData.length > 0 ? (
                  <ReactECharts 
                    option={getPlatformChartOption()} 
                    style={{ height: '300px' }}
                  />
                ) : (
                  <Empty description="暂无平台统计数据" />
                )}
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} lg={12}>
              <Card>
                {durationData.length > 0 ? (
                  <ReactECharts 
                    option={getDurationChartOption()} 
                    style={{ height: '300px' }}
                  />
                ) : (
                  <Empty description="暂无时长分布数据" />
                )}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="指令模式分析" extra={<BarChartOutlined />}>
                {instructionData.length > 0 ? (
                  <Table
                    columns={instructionColumns}
                    dataSource={instructionData}
                    rowKey="instruction_pattern"
                    pagination={false}
                    size="small"
                    scroll={{ y: 250 }}
                  />
                ) : (
                  <Empty description="暂无指令分析数据" />
                )}
              </Card>
            </Col>
          </Row>

          {/* 工具统计 */}
          {toolData.length > 0 && (
            <Card title="工具使用统计" style={{ marginBottom: 24 }}>
              <Row gutter={[16, 16]}>
                {toolData.slice(0, 6).map((tool, index) => (
                  <Col xs={12} sm={8} md={6} lg={4} key={index}>
                    <Card size="small">
                      <Statistic
                        title={tool.tool_name}
                        value={tool.usage_count}
                        suffix={`次 (${formatSuccessRate(tool.success_rate)})`}
                        valueStyle={{ fontSize: '14px' }}
                      />
                      <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                        平均耗时: {formatDuration(tool.avg_execution_time)}
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          )}
        </Spin>
      </div>
    </AppLayout>
  );
};

export default UserStatistics;