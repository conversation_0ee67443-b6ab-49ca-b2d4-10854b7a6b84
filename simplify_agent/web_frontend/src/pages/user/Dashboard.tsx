// 用户控制台页面
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AppLayout from '@/components/common/AppLayout';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  List, 
  Button, 
  Tag, 
  Spin,
  Alert,
  Space,
  Avatar,
  Divider,
  Tooltip
} from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  RightOutlined,
  CalendarOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { statisticsApi } from '@/services/statisticsApi';
import { userApi } from '@/services/userApi';
import { ROUTES, EXECUTION_STATUS_COLORS } from '@/constants';
import { formatDuration, getTimeAgo } from '@/utils';
import type { StatisticsOverview, TestExecution, TestPlan } from '@/types/models';

const { Title, Paragraph, Text } = Typography;

const UserDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [recentLoading, setRecentLoading] = useState(false);
  const [statistics, setStatistics] = useState<StatisticsOverview | null>(null);
  const [recentExecutions, setRecentExecutions] = useState<TestExecution[]>([]);
  const [recentPlans, setRecentPlans] = useState<TestPlan[]>([]);
  const [plansLoading, setPlansLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载概览统计数据
  const loadStatistics = async () => {
    try {
      setStatisticsLoading(true);
      const data = await statisticsApi.getOverviewStatistics();
      setStatistics(data);
    } catch (err) {
      console.error('加载统计数据失败:', err);
    } finally {
      setStatisticsLoading(false);
    }
  };

  // 加载最近执行记录
  const loadRecentExecutions = async () => {
    try {
      setRecentLoading(true);
      const data = await userApi.getExecutions({
        page: 1,
        page_size: 5
      });
      setRecentExecutions(data.items);
    } catch (err) {
      console.error('加载最近执行记录失败:', err);
    } finally {
      setRecentLoading(false);
    }
  };

  // 加载最近测试计划
  const loadRecentPlans = async () => {
    try {
      setPlansLoading(true);
      const data = await userApi.getPlans({
        page: 1,
        page_size: 5
      });
      setRecentPlans(data.items);
    } catch (err) {
      console.error('加载最近测试计划失败:', err);
    } finally {
      setPlansLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    const loadData = async () => {
      try {
        setError(null);
        await Promise.all([
          loadStatistics(),
          loadRecentExecutions(),
          loadRecentPlans()
        ]);
      } catch (err) {
        setError('加载数据失败，请稍后重试');
      }
    };

    loadData();
  }, []);

  const handleExecutionClick = (executionId: string) => {
    navigate(ROUTES.USER_EXECUTION_DETAIL.replace(':id', executionId));
  };

  if (error) {
    return (
      <AppLayout mode="user">
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => window.location.reload()}>
              重新加载
            </Button>
          }
        />
      </AppLayout>
    );
  }

  return (
    <AppLayout mode="user">
      <div>
        <Title level={2}>欢迎使用测试管理系统</Title>
        <Paragraph>
          在这里您可以查看测试执行记录、分析统计数据，以及管理您的测试流程。
        </Paragraph>
        
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总执行数"
                value={statistics?.total_executions || 0}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
                loading={statisticsLoading}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="成功率"
                value={statistics?.success_rate || 0}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
                precision={1}
                loading={statisticsLoading}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="平均时长"
                value={statistics?.average_duration ? formatDuration(statistics.average_duration) : '-'}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
                loading={statisticsLoading}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="今日执行"
                value={statistics?.today_executions || 0}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#722ed1' }}
                loading={statisticsLoading}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          {/* 最近测试计划 */}
          <Col xs={24} lg={12}>
            <Card 
              title="最近测试计划"
              extra={
                <Button 
                  type="link" 
                  icon={<RightOutlined />}
                  onClick={() => navigate(ROUTES.USER_PLANS)}
                >
                  查看全部
                </Button>
              }
            >
              <Spin spinning={plansLoading}>
                <List
                  dataSource={recentPlans}
                  renderItem={(item) => (
                    <List.Item
                      actions={[
                        <Button
                          key="view"
                          type="link"
                          size="small"
                          onClick={() => navigate(ROUTES.USER_PLAN_DETAIL.replace(':id', item.plan_id))}
                        >
                          查看详情
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            style={{
                              backgroundColor: item.platform === 'ios' ? '#1890ff' : '#52c41a',
                            }}
                            icon={<FileTextOutlined />}
                          />
                        }
                        title={
                          <Space>
                            <Tooltip title={item.original_request}>
                              <Text strong>
                                {item.original_request.length > 50 
                                  ? `${item.original_request.substring(0, 50)}...` 
                                  : item.original_request
                                }
                              </Text>
                            </Tooltip>
                            <Tag color={item.platform === 'ios' ? 'blue' : 'green'}>
                              {item.platform.toUpperCase()}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space split={<Divider type="vertical" />}>
                            <Text type="secondary">{item.total_steps} 步骤</Text>
                            <Text type="secondary">{item.execution_count} 次执行</Text>
                            <Text type="secondary">{getTimeAgo(item.created_at)}</Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                  locale={{ emptyText: '暂无测试计划' }}
                />
              </Spin>
            </Card>
          </Col>

          {/* 最近执行记录 */}
          <Col xs={24} lg={12}>
            <Card 
              title="最近执行记录"
              extra={
                <Button 
                  type="link" 
                  icon={<RightOutlined />}
                  onClick={() => navigate(ROUTES.USER_EXECUTIONS)}
                >
                  查看全部
                </Button>
              }
            >
              <Spin spinning={recentLoading}>
                <List
                  dataSource={recentExecutions}
                  renderItem={(item) => (
                    <List.Item
                      actions={[
                        <Button
                          key="view"
                          type="link"
                          size="small"
                          onClick={() => handleExecutionClick(item.execution_id)}
                        >
                          查看详情
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            style={{
                              backgroundColor: EXECUTION_STATUS_COLORS[item.execution_status],
                            }}
                            icon={
                              item.execution_status === 'success' ? (
                                <CheckCircleOutlined />
                              ) : (
                                <ExclamationCircleOutlined />
                              )
                            }
                          />
                        }
                        title={
                          <Space>
                            <Text strong>{item.original_request.substring(0, 50)}...</Text>
                            <Tag
                              color={item.execution_status === 'success' ? 'green' : 'red'}
                            >
                              {item.execution_status === 'success' ? '成功' : '失败'}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space>
                            <Text type="secondary">
                              耗时: {formatDuration(item.total_duration)}
                            </Text>
                            <Text type="secondary">•</Text>
                            <Text type="secondary">
                              {getTimeAgo(item.created_at)}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                  locale={{ emptyText: '暂无执行记录' }}
                />
              </Spin>
            </Card>
          </Col>

          {/* 快速导航 */}
          <Col xs={24}>
            <Card title="快速导航">
              <List
                size="small"
                dataSource={[
                  {
                    title: '测试计划',
                    description: '查看和管理所有测试计划',
                    path: ROUTES.USER_PLANS,
                    icon: <FileTextOutlined style={{ color: '#722ed1' }} />
                  },
                  {
                    title: '测试记录',
                    description: '查看所有测试执行的详细记录',
                    path: ROUTES.USER_EXECUTIONS,
                    icon: <PlayCircleOutlined style={{ color: '#1890ff' }} />
                  },
                  {
                    title: '统计分析',
                    description: '查看测试数据的图表分析',
                    path: ROUTES.USER_STATISTICS,
                    icon: <ClockCircleOutlined style={{ color: '#52c41a' }} />
                  }
                ]}
                renderItem={(item) => (
                  <List.Item
                    actions={[
                      <Button
                        key="go"
                        type="link"
                        icon={<RightOutlined />}
                        onClick={() => navigate(item.path)}
                      >
                        进入
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={item.icon}
                      title={item.title}
                      description={item.description}
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </div>
    </AppLayout>
  );
};

export default UserDashboard;