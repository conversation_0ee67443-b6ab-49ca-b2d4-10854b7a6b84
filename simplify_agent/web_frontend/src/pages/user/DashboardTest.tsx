// 用户控制台页面 - 测试版本
import React from 'react';
import AppLayout from '@/components/common/AppLayout';
import { Card, Typography, Button } from 'antd';

const { Title, Paragraph } = Typography;

const UserDashboardTest: React.FC = () => {
  return (
    <AppLayout mode="user">
      <div>
        <Title level={2}>SimplifyAgent 测试管理系统</Title>
        <Paragraph>
          欢迎使用测试管理系统！这里是用户控制台页面。
        </Paragraph>
        
        <Card title="系统信息" style={{ marginTop: 16 }}>
          <p>✅ 前端页面渲染正常</p>
          <p>✅ React组件加载成功</p>
          <p>✅ Ant Design UI库工作正常</p>
          <p>✅ 路由系统运行正常</p>
        </Card>

        <Card title="快速测试" style={{ marginTop: 16 }}>
          <Button type="primary" onClick={() => alert('按钮点击测试成功！')}>
            测试按钮
          </Button>
        </Card>
      </div>
    </AppLayout>
  );
};

export default UserDashboardTest;