// 测试执行记录列表页面
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import AppLayout from '@/components/common/AppLayout';
import {
  Table,
  Card,
  Tag,
  Button,
  Input,
  Select,
  Space,
  Typography,
  Popconfirm,
  message,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { userApi } from '@/services/userApi';
import { 
  ROUTES, 
  EXECUTION_STATUS_LABELS, 
  EXECUTION_STATUS_COLORS
} from '@/constants';
import { formatTime, formatDuration, getTimeAgo, handleTableSort } from '@/utils';
import type { TestExecution } from '@/types/models';
import type { PaginatedResponse } from '@/types/api';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const UserExecutions: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TestExecution[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
  });

  // 筛选条件
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || 'all',
  });

  // 加载数据
  const loadData = async (params = {}) => {
    try {
      setLoading(true);
      
      const requestParams = {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: filters.search || undefined,
        status: filters.status === 'all' ? undefined : filters.status,
        ...params,
      };

      const response: PaginatedResponse<TestExecution> = await userApi.getExecutions(requestParams);
      
      setData(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.pagination.total_items,
        current: response.pagination.current_page,
        pageSize: response.pagination.page_size,
      }));

    } catch (error) {
      message.error('加载数据失败');
      console.error('Load executions failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadData();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    const newFilters = { ...filters, search: value };
    setFilters(newFilters);
    
    // 更新URL参数
    if (value) {
      searchParams.set('search', value);
    } else {
      searchParams.delete('search');
    }
    setSearchParams(searchParams);
    
    loadData({ 
      page: 1, 
      search: value || undefined 
    });
  };

  // 状态筛选处理
  const handleStatusChange = (status: string) => {
    const newFilters = { ...filters, status };
    setFilters(newFilters);
    
    // 更新URL参数
    if (status && status !== 'all') {
      searchParams.set('status', status);
    } else {
      searchParams.delete('status');
    }
    setSearchParams(searchParams);
    
    loadData({ 
      page: 1, 
      status: status === 'all' ? undefined : status 
    });
  };

  // 分页处理
  const handleTableChange = (paginationConfig: any, _: any, sorter: any) => {
    const newPagination = {
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    };
    
    setPagination(newPagination);
    
    loadData({
      page: paginationConfig.current,
      page_size: paginationConfig.pageSize,
      order_by: handleTableSort(sorter.field, sorter.order),
    });
  };

  // 查看详情
  const handleView = (record: TestExecution) => {
    navigate(ROUTES.USER_EXECUTION_DETAIL.replace(':id', record.execution_id));
  };

  // 删除执行记录
  const handleDelete = async (record: TestExecution) => {
    try {
      await userApi.deleteExecution(record.execution_id);
      message.success('删除成功');
      loadData(); // 重新加载数据
    } catch (error) {
      message.error('删除失败');
      console.error('Delete execution failed:', error);
    }
  };

  // 重新执行
  const handleRerun = async (record: TestExecution) => {
    try {
      const result = await userApi.rerunExecution(record.execution_id);
      message.success('重新执行已启动');
      
      // 跳转到新的执行记录详情页
      navigate(ROUTES.USER_EXECUTION_DETAIL.replace(':id', result.new_execution_id));
    } catch (error) {
      message.error('重新执行失败');
      console.error('Rerun execution failed:', error);
    }
  };

  // 导出执行记录
  const handleExport = async (record: TestExecution) => {
    try {
      const result = await userApi.exportExecution(record.execution_id);
      // 打开下载链接
      window.open(result.download_url, '_blank');
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
      console.error('Export execution failed:', error);
    }
  };

  // 表格列定义
  const columns: ColumnsType<TestExecution> = [
    {
      title: '请求描述',
      dataIndex: 'original_request',
      key: 'original_request',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string, record: TestExecution) => (
        <Tooltip title={text}>
          <Button
            type="link"
            onClick={() => handleView(record)}
            style={{ textAlign: 'left', padding: 0, height: 'auto' }}
          >
            {text}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '执行状态',
      dataIndex: 'execution_status',
      key: 'execution_status',
      width: 100,
      filters: [
        { text: '成功', value: 'success' },
        { text: '失败', value: 'failure' },
        { text: '等待中', value: 'pending' },
        { text: '执行中', value: 'running' },
      ],
      render: (status: string) => (
        <Tag color={EXECUTION_STATUS_COLORS[status as keyof typeof EXECUTION_STATUS_COLORS]}>
          {EXECUTION_STATUS_LABELS[status as keyof typeof EXECUTION_STATUS_LABELS]}
        </Tag>
      ),
    },
    {
      title: '执行时长',
      dataIndex: 'total_duration',
      key: 'total_duration',
      width: 100,
      sorter: true,
      render: (duration: number) => formatDuration(duration),
    },
    {
      title: '轮数',
      dataIndex: 'total_rounds',
      key: 'total_rounds',
      width: 80,
      sorter: true,
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 150,
      sorter: true,
      render: (time: string) => (
        <Tooltip title={formatTime(time)}>
          {getTimeAgo(time)}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: TestExecution) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleView(record)}
            />
          </Tooltip>
          
          <Tooltip title="重新执行">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => handleRerun(record)}
            />
          </Tooltip>
          
          <Tooltip title="导出数据">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              size="small"
              onClick={() => handleExport(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这条执行记录吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Tooltip title="删除记录">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <AppLayout mode="user">
      <div>
        <Title level={3}>测试执行记录</Title>
        
        {/* 筛选栏 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Search
                placeholder="搜索执行记录"
                allowClear
                enterButton={<SearchOutlined />}
                value={filters.search}
                onChange={e => setFilters({...filters, search: e.target.value})}
                onSearch={handleSearch}
              />
            </Col>
            
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                style={{ width: '100%' }}
                placeholder="筛选状态"
                value={filters.status}
                onChange={handleStatusChange}
              >
                <Option value="all">全部状态</Option>
                <Option value="success">成功</Option>
                <Option value="failure">失败</Option>
                <Option value="pending">等待中</Option>
                <Option value="running">执行中</Option>
              </Select>
            </Col>
            
            <Col xs={24} sm={12} md={8} lg={6}>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadData()}
                loading={loading}
              >
                刷新
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 数据表格 */}
        <Card>
          <Table
            columns={columns}
            dataSource={data}
            rowKey="execution_id"
            loading={loading}
            pagination={pagination}
            onChange={handleTableChange}
            scroll={{ x: 1000 }}
            size="middle"
          />
        </Card>
      </div>
    </AppLayout>
  );
};

export default UserExecutions;