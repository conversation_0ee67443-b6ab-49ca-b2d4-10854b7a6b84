// 测试计划列表页面
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import AppLayout from '@/components/common/AppLayout';
import {
  Table,
  Card,
  Tag,
  Button,
  Input,
  Select,
  Space,
  Typography,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FileTextOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { userApi } from '@/services/userApi';
import { ROUTES } from '@/constants';
import { formatTime, getTimeAgo, handleTableSort } from '@/utils';
import type { TestPlan } from '@/types/models';
import type { PaginatedResponse } from '@/types/api';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const UserPlans: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TestPlan[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
  });

  // 筛选条件
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    platform: searchParams.get('platform') || 'all',
  });

  // 加载数据
  const loadData = async (params = {}) => {
    try {
      setLoading(true);
      
      const requestParams = {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: filters.search || undefined,
        platform: filters.platform === 'all' ? undefined : filters.platform,
        ...params,
      };

      const response: PaginatedResponse<TestPlan> = await userApi.getPlans(requestParams);
      
      setData(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.pagination.total_items,
        current: response.pagination.current_page,
        pageSize: response.pagination.page_size,
      }));

    } catch (error) {
      console.error('Load plans failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadData();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setFilters({...filters, search: value});
    setPagination({...pagination, current: 1});
    loadData({ page: 1, search: value || undefined });
    
    // 更新URL参数
    if (value) {
      searchParams.set('search', value);
    } else {
      searchParams.delete('search');
    }
    setSearchParams(searchParams);
  };

  // 平台筛选处理
  const handlePlatformChange = (value: string) => {
    setFilters({...filters, platform: value});
    setPagination({...pagination, current: 1});
    loadData({ 
      page: 1, 
      platform: value === 'all' ? undefined : value 
    });
    
    // 更新URL参数
    if (value !== 'all') {
      searchParams.set('platform', value);
    } else {
      searchParams.delete('platform');
    }
    setSearchParams(searchParams);
  };

  // 表格变化处理（分页、排序等）
  const handleTableChange = (paginationConfig: any, filtersConfig: any, sorter: any) => {
    const newPagination = {
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    };
    
    setPagination(newPagination);
    
    loadData({
      page: paginationConfig.current,
      page_size: paginationConfig.pageSize,
    });
  };

  // 查看计划详情
  const handleViewDetail = (planId: string) => {
    navigate(ROUTES.USER_PLAN_DETAIL.replace(':id', planId));
  };

  // 表格列定义
  const columns: ColumnsType<TestPlan> = [
    {
      title: '计划ID',
      dataIndex: 'plan_id',
      key: 'plan_id',
      width: 180,
      render: (planId: string) => (
        <Tooltip title={planId}>
          <Button 
            type="link" 
            size="small"
            onClick={() => handleViewDetail(planId)}
          >
            {planId.length > 12 ? `${planId.slice(0, 12)}...` : planId}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '测试请求',
      dataIndex: 'original_request',
      key: 'original_request',
      ellipsis: true,
      render: (request: string) => (
        <Tooltip title={request}>
          <span>{request}</span>
        </Tooltip>
      ),
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 80,
      render: (platform: string) => (
        <Tag color={platform === 'ios' ? 'blue' : 'green'}>
          {platform.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '步骤数',
      dataIndex: 'total_steps',
      key: 'total_steps',
      width: 80,
      align: 'center',
      sorter: true,
    },
    {
      title: '执行次数',
      dataIndex: 'execution_count',
      key: 'execution_count',
      width: 90,
      align: 'center',
      render: (count: number = 0) => (
        <Space>
          <PlayCircleOutlined style={{ color: count > 0 ? '#52c41a' : '#d9d9d9' }} />
          <span>{count}</span>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      sorter: true,
      render: (time: string) => (
        <Tooltip title={formatTime(time)}>
          <span>{getTimeAgo(time)}</span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record: TestPlan) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record.plan_id)}
            >
              详情
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <AppLayout mode="user">
      <div>
        <Title level={3}>测试计划</Title>
        
        {/* 筛选栏 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Search
                placeholder="搜索测试计划"
                allowClear
                enterButton={<SearchOutlined />}
                value={filters.search}
                onChange={e => setFilters({...filters, search: e.target.value})}
                onSearch={handleSearch}
              />
            </Col>
            
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                style={{ width: '100%' }}
                placeholder="筛选平台"
                value={filters.platform}
                onChange={handlePlatformChange}
              >
                <Option value="all">全部平台</Option>
                <Option value="ios">iOS</Option>
                <Option value="android">Android</Option>
              </Select>
            </Col>
            
            <Col xs={24} sm={12} md={8} lg={6}>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadData()}
                loading={loading}
              >
                刷新
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 数据表格 */}
        <Card>
          <Table
            columns={columns}
            dataSource={data}
            rowKey="plan_id"
            loading={loading}
            pagination={pagination}
            onChange={handleTableChange}
            scroll={{ x: 1000 }}
            size="middle"
          />
        </Card>
      </div>
    </AppLayout>
  );
};

export default UserPlans;
