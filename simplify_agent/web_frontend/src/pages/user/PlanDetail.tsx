// 测试计划详情页面
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AppLayout from '@/components/common/AppLayout';
import {
  Card,
  Descriptions,
  Tag,
  Button,
  Typography,
  Space,
  Alert,
  Spin,
  Table,
  Row,
  Col,
  Tooltip,
  Modal,
  Badge
} from 'antd';
import {
  ArrowLeftOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  EyeOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  CodeOutlined,
  FullscreenOutlined,
  DownOutlined,
  UpOutlined
} from '@ant-design/icons';
import { userApi } from '@/services/userApi';
import { ROUTES, EXECUTION_STATUS_COLORS, EXECUTION_STATUS_LABELS } from '@/constants';
import { formatTime, formatDuration } from '@/utils';
import type { TestExecution } from '@/types/models';
import type { ColumnsType } from 'antd/es/table';

const { Title, Paragraph, Text } = Typography;

const UserPlanDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [planDetail, setPlanDetail] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [stepsModalVisible, setStepsModalVisible] = useState(false);
  const [stepsExpanded, setStepsExpanded] = useState(false);

  // 加载计划详情
  const loadPlanDetail = async () => {
    if (!id) {
      setError('计划ID不能为空');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const detail = await userApi.getPlanDetail(id);
      setPlanDetail(detail);
    } catch (err: any) {
      console.error('Load plan detail failed:', err);
      setError(err.message || '加载计划详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlanDetail();
  }, [id]);

  // 查看执行详情
  const handleViewExecution = (executionId: string) => {
    navigate(ROUTES.USER_EXECUTION_DETAIL.replace(':id', executionId));
  };

  // 执行记录表格列定义
  const executionColumns: ColumnsType<TestExecution> = [
    {
      title: '执行ID',
      dataIndex: 'execution_id',
      key: 'execution_id',
      width: 180,
      render: (executionId: string) => (
        <Tooltip title={executionId}>
          <Button 
            type="link" 
            size="small"
            onClick={() => handleViewExecution(executionId)}
          >
            {executionId.length > 12 ? `${executionId.slice(0, 12)}...` : executionId}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'execution_status',
      key: 'execution_status',
      width: 100,
      render: (status: string) => (
        <Tag color={EXECUTION_STATUS_COLORS[status as keyof typeof EXECUTION_STATUS_COLORS] || '#999'}>
          {EXECUTION_STATUS_LABELS[status as keyof typeof EXECUTION_STATUS_LABELS] || status}
        </Tag>
      ),
    },
    {
      title: '执行时长',
      dataIndex: 'total_duration',
      key: 'total_duration',
      width: 120,
      render: (duration: number) => formatDuration(duration),
    },
    {
      title: '轮次',
      dataIndex: 'total_rounds',
      key: 'total_rounds',
      width: 80,
      align: 'center',
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 180,
      render: (time: string) => time ? formatTime(time) : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      fixed: 'right',
      render: (_, record: TestExecution) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewExecution(record.execution_id)}
        >
          查看
        </Button>
      ),
    },
  ];

  if (loading) {
    return (
      <AppLayout mode="user">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout mode="user">
        <div>
          <Alert
            message="加载失败"
            description={error}
            type="error"
            showIcon
            action={
              <Space>
                <Button size="small" onClick={loadPlanDetail}>
                  重试
                </Button>
                <Button size="small" onClick={() => navigate(ROUTES.USER_PLANS)}>
                  返回列表
                </Button>
              </Space>
            }
          />
        </div>
      </AppLayout>
    );
  }

  if (!planDetail) {
    return (
      <AppLayout mode="user">
        <div>
          <Alert
            message="计划不存在"
            description="未找到指定的测试计划"
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={() => navigate(ROUTES.USER_PLANS)}>
                返回列表
              </Button>
            }
          />
        </div>
      </AppLayout>
    );
  }

  const plan = planDetail;
  const executions = planDetail?.executions || [];

  return (
    <AppLayout mode="user">
      <div>
        {/* 页头 */}
        <div style={{ marginBottom: 24 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(ROUTES.USER_PLANS)}
            style={{ marginBottom: 16 }}
          >
            返回计划列表
          </Button>
          
          <Title level={3}>
            <Space>
              <FileTextOutlined />
              测试计划详情
            </Space>
          </Title>
        </div>

        {/* 基本信息 */}
        <Card title="基本信息" style={{ marginBottom: 16 }}>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="计划ID" span={2}>
              <Text code>{plan.plan_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="测试请求" span={2}>
              {plan.original_request}
            </Descriptions.Item>
            <Descriptions.Item label="平台">
              <Tag color={plan.platform === 'ios' ? 'blue' : 'green'}>
                {plan.platform.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="总步骤数">
              {plan.total_steps}
            </Descriptions.Item>
            <Descriptions.Item label="计划摘要" span={2}>
              {plan.plan_summary || '无'}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {formatTime(plan.created_at)}
            </Descriptions.Item>
            {plan.updated_at && (
              <Descriptions.Item label="更新时间">
                {formatTime(plan.updated_at)}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>


        {/* Agent指令 */}
        {plan.agent_instructions && (
          <Card title="Agent执行指令" style={{ marginBottom: 16 }}>
            <Paragraph>
              <pre style={{ whiteSpace: 'pre-wrap', backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
                {typeof plan.agent_instructions === 'string' 
                  ? plan.agent_instructions 
                  : plan.agent_instructions.simple_instruction || plan.agent_instructions.detailed_instruction || JSON.stringify(plan.agent_instructions, null, 2)
                }
              </pre>
            </Paragraph>
          </Card>
        )}


        {/* 详细测试步骤 */}
        {plan.structured_plan && plan.structured_plan.steps && (
          <Card 
            title="详细测试步骤" 
            style={{ marginBottom: 16 }}
            extra={
              <Space>
                <Button 
                  type="text" 
                  icon={stepsExpanded ? <UpOutlined /> : <DownOutlined />}
                  onClick={() => setStepsExpanded(!stepsExpanded)}
                >
                  {stepsExpanded ? '收起' : '展开'}
                </Button>
                <Button 
                  type="primary" 
                  icon={<FullscreenOutlined />}
                  onClick={() => setStepsModalVisible(true)}
                >
                  瀑布流
                </Button>
              </Space>
            }
          >
            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">
                共 {plan.structured_plan.steps.length} 个步骤
                {!stepsExpanded && `，显示前 ${Math.min(3, plan.structured_plan.steps.length)} 个步骤`}
              </Text>
            </div>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {(stepsExpanded ? plan.structured_plan.steps : plan.structured_plan.steps.slice(0, 3))
                .map((step: any, index: number) => (
                <Card 
                  key={index}
                  size="small" 
                  style={{ 
                    transition: 'all 0.3s ease',
                    border: '1px solid #e8e8e8'
                  }}
                  bodyStyle={{ padding: '16px' }}
                  hoverable
                >
                  <Row gutter={[16, 8]} align="middle">
                    {/* 步骤编号和操作名称 */}
                    <Col xs={24} sm={6} lg={4}>
                      <div style={{ display: 'flex', alignItems: 'center', minHeight: '40px' }}>
                        <Badge 
                          count={step.step_id || index + 1} 
                          style={{ backgroundColor: '#1890ff', marginRight: 8 }}
                        />
                        <Text strong style={{ fontSize: '14px', lineHeight: '1.4' }}>
                          {step.action || '未定义操作'}
                        </Text>
                      </div>
                    </Col>
                    
                    {/* 描述信息 */}
                    <Col xs={24} sm={8} lg={6}>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: 4 }}>
                          <SettingOutlined style={{ marginRight: 4 }} />
                          操作描述
                        </Text>
                        <div style={{ 
                          fontSize: '13px',
                          lineHeight: '1.4',
                          color: '#666',
                          maxHeight: '48px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}>
                          {step.description || '无描述'}
                        </div>
                      </div>
                    </Col>
                    
                    {/* 预期结果 */}
                    <Col xs={24} sm={6} lg={6}>
                      {step.expected_result ? (
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: 4 }}>
                            <CheckCircleOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                            预期结果
                          </Text>
                          <div style={{ 
                            fontSize: '13px',
                            lineHeight: '1.4',
                            color: '#52c41a',
                            maxHeight: '48px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}>
                            {step.expected_result}
                          </div>
                        </div>
                      ) : (
                        <div style={{ color: '#ccc', fontSize: '13px', fontStyle: 'italic' }}>
                          无预期结果
                        </div>
                      )}
                    </Col>
                    
                    {/* 参数信息 */}
                    <Col xs={24} sm={4} lg={8}>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: 4 }}>
                          <CodeOutlined style={{ marginRight: 4 }} />
                          执行参数
                        </Text>
                        {step.parameters && Object.keys(step.parameters).length > 0 ? (
                          <div style={{ fontSize: '12px' }}>
                            {Object.entries(step.parameters).slice(0, 2).map(([key, value]: [string, any]) => (
                              <div key={key} style={{ marginBottom: 2, display: 'flex', alignItems: 'center' }}>
                                <Text code style={{ fontSize: '11px', marginRight: 4 }}>{key}</Text>
                                <Text style={{ fontSize: '11px', color: '#666', flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                </Text>
                                {step.parameter_types && step.parameter_types[key] && (
                                  <Tag color={step.parameter_types[key] === 'dynamic' ? 'orange' : 'blue'} style={{ fontSize: '10px', marginLeft: 4, transform: 'scale(0.8)' }}>
                                    {step.parameter_types[key]}
                                  </Tag>
                                )}
                              </div>
                            ))}
                            {Object.keys(step.parameters).length > 2 && (
                              <Text type="secondary" style={{ fontSize: '11px' }}>
                                +{Object.keys(step.parameters).length - 2} 个参数
                              </Text>
                            )}
                          </div>
                        ) : (
                          <div style={{ color: '#ccc', fontSize: '12px', fontStyle: 'italic' }}>
                            无参数
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                </Card>
              ))}
            </div>
            
            {!stepsExpanded && plan.structured_plan.steps.length > 3 && (
              <div style={{ textAlign: 'center', marginTop: 12 }}>
                <Button 
                  type="dashed" 
                  icon={<DownOutlined />}
                  onClick={() => setStepsExpanded(true)}
                  style={{ fontSize: '13px' }}
                >
                  还有 {plan.structured_plan.steps.length - 3} 个步骤，点击展开查看
                </Button>
              </div>
            )}
          </Card>
        )}

        {/* 执行记录 */}
        <Card 
          title={`执行记录 (${executions.length})`}
          extra={
            executions.length > 0 && (
              <Button
                type="link"
                onClick={() => navigate(ROUTES.USER_EXECUTIONS + `?plan_id=${plan.plan_id}`)}
              >
                查看全部
              </Button>
            )
          }
        >
          {executions.length > 0 ? (
            <Table
              columns={executionColumns}
              dataSource={executions}
              rowKey="execution_id"
              pagination={false}
              scroll={{ x: 800 }}
              size="small"
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              <PlayCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div>该计划还没有执行记录</div>
            </div>
          )}
        </Card>

        {/* 执行统计 */}
        <Card title="执行统计" style={{ marginBottom: 16 }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={8}>
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ marginBottom: 8 }}>
                  <BarChartOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                </div>
                <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#1890ff', marginBottom: 4 }}>
                  {plan.execution_count || 0}
                </div>
                <div style={{ color: '#666', fontSize: '14px' }}>总执行次数</div>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ marginBottom: 8 }}>
                  <CheckCircleOutlined style={{ fontSize: '32px', color: '#52c41a' }} />
                </div>
                <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#52c41a', marginBottom: 4 }}>
                  {executions.filter((e: TestExecution) => e.execution_status === 'success').length}
                </div>
                <div style={{ color: '#666', fontSize: '14px' }}>成功执行</div>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ marginBottom: 8 }}>
                  <CloseCircleOutlined style={{ fontSize: '32px', color: '#ff4d4f' }} />
                </div>
                <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#ff4d4f', marginBottom: 4 }}>
                  {executions.filter((e: TestExecution) => e.execution_status === 'failure').length}
                </div>
                <div style={{ color: '#666', fontSize: '14px' }}>失败执行</div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* 详细步骤弹窗 */}
        <Modal
          title="完整测试流程"
          open={stepsModalVisible}
          onCancel={() => setStepsModalVisible(false)}
          footer={null}
          width={1000}
          style={{ top: 20 }}
          bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
        >
          {plan.structured_plan && plan.structured_plan.steps && (
            <div>
              <div style={{ marginBottom: 20, textAlign: 'center' }}>
                <Text strong style={{ fontSize: '16px' }}>
                  共 {plan.structured_plan.steps.length} 个步骤的完整执行流程
                </Text>
              </div>
              
              <div style={{ position: 'relative' }}>
                {/* 瀑布流连接线 */}
                <div style={{
                  position: 'absolute',
                  left: '20px',
                  top: '40px',
                  bottom: '40px',
                  width: '2px',
                  background: 'linear-gradient(to bottom, #1890ff, #52c41a)',
                  zIndex: 0
                }} />
                
                {plan.structured_plan.steps.map((step: any, index: number) => (
                  <div key={index} style={{ position: 'relative', marginBottom: 24 }}>
                    {/* 步骤圆点 */}
                    <div style={{
                      position: 'absolute',
                      left: '12px',
                      top: '20px',
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      backgroundColor: '#1890ff',
                      border: '3px solid #fff',
                      boxShadow: '0 0 0 2px #1890ff',
                      zIndex: 1
                    }} />
                    
                    {/* 步骤内容卡片 */}
                    <div style={{ marginLeft: '50px' }}>
                      <Card 
                        size="small"
                        style={{ 
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          border: '1px solid #e8e8e8'
                        }}
                      >
                        <div style={{ marginBottom: 12 }}>
                          <Badge 
                            count={step.step_id || index + 1} 
                            style={{ backgroundColor: '#1890ff' }}
                          />
                          <Text strong style={{ marginLeft: 8, fontSize: '16px' }}>
                            {step.action || '未定义操作'}
                          </Text>
                        </div>
                        
                        <Row gutter={[16, 12]}>
                          <Col xs={24} lg={12}>
                            <div>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                <SettingOutlined style={{ marginRight: 4 }} />
                                操作描述：
                              </Text>
                              <div style={{ marginTop: 4, fontSize: '14px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                                {step.description || '无描述'}
                              </div>
                            </div>
                          </Col>
                          
                          {step.expected_result && (
                            <Col xs={24} lg={12}>
                              <div>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  <CheckCircleOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                                  预期结果：
                                </Text>
                                <div style={{ 
                                  marginTop: 4, 
                                  fontSize: '14px', 
                                  padding: '8px', 
                                  backgroundColor: '#f6ffed', 
                                  borderRadius: '4px',
                                  border: '1px solid #b7eb8f'
                                }}>
                                  {step.expected_result}
                                </div>
                              </div>
                            </Col>
                          )}
                        </Row>
                        
                        {step.parameters && Object.keys(step.parameters).length > 0 && (
                          <div style={{ marginTop: 12 }}>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              <CodeOutlined style={{ marginRight: 4 }} />
                              执行参数：
                            </Text>
                            <div style={{ 
                              marginTop: 4, 
                              padding: '8px', 
                              backgroundColor: '#fafafa', 
                              borderRadius: '4px',
                              border: '1px solid #d9d9d9'
                            }}>
                              {Object.entries(step.parameters).map(([key, value]: [string, any]) => (
                                <div key={key} style={{ marginBottom: 4 }}>
                                  <Text code style={{ fontSize: '12px' }}>{key}</Text>
                                  <Text style={{ marginLeft: 8, fontSize: '13px' }}>
                                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                  </Text>
                                  {step.parameter_types && step.parameter_types[key] && (
                                    <Tag color={step.parameter_types[key] === 'dynamic' ? 'orange' : 'blue'} style={{ marginLeft: 8, fontSize: '11px' }}>
                                      {step.parameter_types[key]}
                                    </Tag>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </Card>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Modal>

      </div>
    </AppLayout>
  );
};

export default UserPlanDetail;
