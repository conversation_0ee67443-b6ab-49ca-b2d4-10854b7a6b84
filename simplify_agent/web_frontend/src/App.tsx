import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 设置dayjs中文
dayjs.locale('zh-cn');

// 导入页面组件
import UserDashboard from '@/pages/user/Dashboard';
import UserPlans from '@/pages/user/Plans';
import UserPlanDetail from '@/pages/user/PlanDetail';
import UserExecutions from '@/pages/user/Executions';
import UserExecutionDetail from '@/pages/user/ExecutionDetail';
import UserStatistics from '@/pages/user/Statistics';
import DevLogin from '@/pages/dev/Login';
import DevDashboard from '@/pages/dev/Dashboard';
import DevTables from '@/pages/dev/Tables';
import DevTableDetail from '@/pages/dev/TableDetail';
import DevSQL from '@/pages/dev/SQL';

// 导入认证守卫
import DevAuthGuard from '@/components/auth/DevAuthGuard';

import { ROUTES } from '@/constants';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          {/* 根路径重定向到用户控制台 */}
          <Route path={ROUTES.ROOT} element={<Navigate to={ROUTES.USER_DASHBOARD} replace />} />
          
          {/* 用户视角路由 */}
          <Route path={ROUTES.USER_DASHBOARD} element={<UserDashboard />} />
          <Route path={ROUTES.USER_PLANS} element={<UserPlans />} />
          <Route path={ROUTES.USER_PLAN_DETAIL} element={<UserPlanDetail />} />
          <Route path={ROUTES.USER_EXECUTIONS} element={<UserExecutions />} />
          <Route path={ROUTES.USER_EXECUTION_DETAIL} element={<UserExecutionDetail />} />
          <Route path={ROUTES.USER_STATISTICS} element={<UserStatistics />} />
          
          {/* 开发者视角路由 */}
          <Route path={ROUTES.DEV_LOGIN} element={<DevLogin />} />
          <Route path={ROUTES.DEV_DASHBOARD} element={<DevAuthGuard><DevDashboard /></DevAuthGuard>} />
          <Route path={ROUTES.DEV_TABLES} element={<DevAuthGuard><DevTables /></DevAuthGuard>} />
          <Route path={ROUTES.DEV_TABLE_DETAIL} element={<DevAuthGuard><DevTableDetail /></DevAuthGuard>} />
          <Route path={ROUTES.DEV_SQL} element={<DevAuthGuard><DevSQL /></DevAuthGuard>} />
          
          {/* 404页面 */}
          <Route path="*" element={<Navigate to={ROUTES.USER_DASHBOARD} replace />} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;
