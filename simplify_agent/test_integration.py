#!/usr/bin/env python3
"""
SimplifyAgent 日志服务器集成测试

测试所有组件的集成和协作
"""

import asyncio
import sys
import os
import json
import signal
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

from config import get_config
from log_server import LogServerManager


async def run_integration_test():
    """运行集成测试"""
    print("🚀 SimplifyAgent 日志服务器集成测试开始...")
    
    # 使用默认配置（已经修正了路径）
    config = get_config()
    
    # 创建服务器实例
    server = LogServerManager()
    
    test_passed = True
    
    try:
        # 测试初始化
        print("\n1️⃣ 测试组件初始化...")
        if await server.initialize():
            print("   ✅ 组件初始化成功")
        else:
            print("   ❌ 组件初始化失败")
            test_passed = False
            return
        
        # 启动服务器
        print("\n2️⃣ 启动服务器...")
        
        # 创建启动任务，但设置超时
        try:
            await asyncio.wait_for(
                asyncio.create_task(server.start()),
                timeout=5.0
            )
        except asyncio.TimeoutError:
            print("   ✅ 服务器正常启动 (测试超时退出)")
        
        # 检查服务器状态
        print("\n3️⃣ 检查服务器状态...")
        status = server.get_status()
        print(f"   运行状态: {status['is_running']}")
        print(f"   PID: {status['pid']}")
        print(f"   处理统计: {status['stats']}")
        
        if status['is_running']:
            print("   ✅ 服务器运行正常")
        else:
            print("   ⚠️ 服务器未运行")
        
        # 模拟文件事件测试
        print("\n4️⃣ 测试文件监控...")
        test_file_events(config)
        
        # 等待一段时间让系统处理
        print("   等待事件处理...")
        await asyncio.sleep(2.0)
        
        print("\n5️⃣ 测试组件功能...")
        test_individual_components(config)
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        test_passed = False
    finally:
        # 关闭服务器
        print("\n6️⃣ 关闭服务器...")
        await server.shutdown()
        print("   ✅ 服务器已关闭")
    
    # 输出测试结果
    print(f"\n{'='*50}")
    if test_passed:
        print("🎉 集成测试通过！所有组件功能正常。")
        print("\n✨ SimplifyAgent 日志服务器已准备就绪")
        print("   可以使用以下命令启动服务:")
        print("   python log_server.py")
        print("   python log_server.py --status  # 查看状态")
        print("   python log_server.py --stop    # 停止服务")
    else:
        print("❌ 集成测试失败！请检查组件配置。")
    print(f"{'='*50}")


def test_file_events(config):
    """测试文件事件处理"""
    watch_paths = config.get_watch_paths()
    
    for watch_type, watch_path in watch_paths.items():
        if os.path.exists(watch_path):
            if watch_type == 'json_plan':
                files = list(Path(watch_path).glob('*.json'))
                print(f"   {watch_type}: 发现 {len(files)} 个JSON文件")
            elif watch_type == 'agent_execute':
                folders = [d for d in Path(watch_path).iterdir() if d.is_dir()]
                print(f"   {watch_type}: 发现 {len(folders)} 个执行文件夹")
            elif watch_type == 'judge_report':
                files = list(Path(watch_path).glob('*_report.log'))
                print(f"   {watch_type}: 发现 {len(files)} 个报告文件")
        else:
            print(f"   {watch_type}: 监控路径不存在 - {watch_path}")


def test_individual_components(config):
    """测试各个组件的独立功能"""
    
    # 测试配置系统
    print("   📋 配置系统:")
    is_valid, errors = config.validate_config()
    print(f"     配置有效性: {is_valid}")
    if errors:
        for error in errors[:3]:  # 只显示前3个错误
            print(f"     - {error}")
    
    # 测试文件清理器
    print("   🧹 文件清理器:")
    try:
        from cleaners.file_cleaner import FileCleaner
        cleaner = FileCleaner(config)
        storage_info = cleaner.get_storage_info()
        
        for watch_type, info in storage_info.items():
            if info.get('exists'):
                usage = f"{info.get('file_count', 0)}/{info.get('max_files', 'N/A')}"
                size_mb = info.get('total_size_mb', 0)
                print(f"     {watch_type}: {usage} 文件, {size_mb:.1f}MB")
            else:
                print(f"     {watch_type}: 路径不存在")
    except Exception as e:
        print(f"     文件清理器测试失败: {e}")
    
    # 测试数据解析器
    print("   🔍 数据解析器:")
    try:
        from parsers.data_parser import DataParser
        parser = DataParser(config)
        stats = parser.get_stats()
        print(f"     支持解析器: {', '.join(stats['parsers_available'])}")
        print(f"     数据库连接: {'✅' if stats['database_connected'] else '❌'}")
    except Exception as e:
        print(f"     数据解析器测试失败: {e}")


if __name__ == '__main__':
    try:
        asyncio.run(run_integration_test())
    except KeyboardInterrupt:
        print("\n🛑 测试被中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()