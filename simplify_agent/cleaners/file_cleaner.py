"""
SimplifyAgent 文件清理模块

智能文件清理器，维护各监控目录的文件数量限制，
支持按时间戳自动清理、安全删除和清理策略配置。

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
"""

import os
import shutil
import asyncio
import threading
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

from server_logger import get_logger, PerformanceTimer


class FileCleaner:
    """文件清理器"""
    
    def __init__(self, config: 'LogServerConfig', state_manager: 'FileStateManager' = None):
        """
        初始化文件清理器
        
        Args:
            config: 配置对象
            state_manager: 文件状态管理器
        """
        self.config = config
        self.state_manager = state_manager
        self.logger = get_logger('log_server', 'file_cleaner')
        
        # 运行状态
        self.is_running = False
        self.cleaner_thread = None
        self.stop_event = threading.Event()
        
        # 清理配置
        self.watch_paths = self.config.get_watch_paths()
        self.file_limits = self.config.get_file_limits()
        self.check_interval = self.file_limits.get('check_interval', 300)  # 5分钟检查一次
        
        # 统计信息
        self.stats = {
            'total_checks': 0,
            'files_cleaned': 0,
            'space_freed_mb': 0.0,
            'last_cleanup_time': None,
            'cleanup_history': []  # 保存最近10次清理记录
        }
        
        # 清理策略
        self.cleanup_strategies = {
            'json_plan': self._cleanup_json_files,
            'agent_execute': self._cleanup_execution_folders,
            'judge_report': self._cleanup_report_files
        }
        
        self.logger.info(f"文件清理器初始化完成 (检查间隔: {self.check_interval}秒)")
    
    async def start(self):
        """启动文件清理器"""
        if self.is_running:
            self.logger.warning("文件清理器已在运行")
            return
        
        try:
            self.stop_event.clear()
            
            # 启动清理线程
            self.cleaner_thread = threading.Thread(
                target=self._cleanup_worker,
                name="FileCleaner",
                daemon=True
            )
            self.cleaner_thread.start()
            
            self.is_running = True
            self.logger.info("文件清理器启动成功")
            
        except Exception as e:
            self.logger.error(f"文件清理器启动失败: {e}")
            raise
    
    async def stop(self):
        """停止文件清理器"""
        if not self.is_running:
            return
        
        self.logger.info("停止文件清理器...")
        
        # 停止清理线程
        self.stop_event.set()
        if self.cleaner_thread and self.cleaner_thread.is_alive():
            self.cleaner_thread.join(timeout=10.0)
        
        self.is_running = False
        self._log_final_statistics()
        self.logger.info("文件清理器已停止")
    
    def _cleanup_worker(self):
        """文件清理工作线程"""
        self.logger.info("文件清理工作线程启动")
        
        # 首次启动时延迟30秒执行清理
        if not self.stop_event.wait(30):
            self._perform_cleanup_check()
        
        # 定期清理循环
        while not self.stop_event.wait(self.check_interval):
            try:
                self._perform_cleanup_check()
            except Exception as e:
                self.logger.error(f"文件清理检查异常: {e}")
        
        self.logger.info("文件清理工作线程停止")
    
    def _perform_cleanup_check(self):
        """执行清理检查"""
        with PerformanceTimer(self.logger, "cleanup_check"):
            self.stats['total_checks'] += 1
            cleanup_start = datetime.now()
            
            self.logger.info("开始文件清理检查...")
            
            total_cleaned = 0
            total_space_freed = 0.0
            
            # 遍历所有监控路径
            for watch_type, watch_path in self.watch_paths.items():
                try:
                    if not os.path.exists(watch_path):
                        self.logger.debug(f"监控路径不存在，跳过清理: {watch_path}")
                        continue
                    
                    # 获取清理策略
                    cleanup_func = self.cleanup_strategies.get(watch_type)
                    if not cleanup_func:
                        self.logger.warning(f"未找到清理策略: {watch_type}")
                        continue
                    
                    # 执行清理
                    max_files = self.file_limits.get(f'{watch_type}_max', 20)
                    cleaned_count, space_freed = cleanup_func(watch_path, max_files)
                    
                    total_cleaned += cleaned_count
                    total_space_freed += space_freed
                    
                    if cleaned_count > 0:
                        self.logger.info(f"{watch_type} 清理完成: 删除 {cleaned_count} 个文件/文件夹，释放 {space_freed:.2f}MB")
                    
                except Exception as e:
                    self.logger.error(f"清理 {watch_type} 失败: {e}")
            
            # 更新统计信息
            self.stats['files_cleaned'] += total_cleaned
            self.stats['space_freed_mb'] += total_space_freed
            self.stats['last_cleanup_time'] = cleanup_start
            
            # 清理状态管理器中已删除文件的记录
            if self.state_manager and total_cleaned > 0:
                self.state_manager.cleanup_deleted_files()
            
            # 记录清理历史
            cleanup_record = {
                'timestamp': cleanup_start.isoformat(),
                'files_cleaned': total_cleaned,
                'space_freed_mb': total_space_freed,
                'duration_seconds': (datetime.now() - cleanup_start).total_seconds()
            }
            
            self.stats['cleanup_history'].append(cleanup_record)
            # 保持最近10次记录
            if len(self.stats['cleanup_history']) > 10:
                self.stats['cleanup_history'] = self.stats['cleanup_history'][-10:]
            
            if total_cleaned > 0:
                self.logger.info(f"清理检查完成: 总共删除 {total_cleaned} 个文件/文件夹，释放 {total_space_freed:.2f}MB")
            else:
                self.logger.debug("清理检查完成: 无需清理")
    
    def _cleanup_json_files(self, path: str, max_files: int) -> Tuple[int, float]:
        """清理JSON计划文件"""
        try:
            json_files = []
            
            # 收集所有JSON文件及其信息
            for file_path in Path(path).glob('*.json'):
                if file_path.is_file():
                    stat = file_path.stat()
                    json_files.append({
                        'path': file_path,
                        'mtime': stat.st_mtime,
                        'size': stat.st_size
                    })
            
            # 按修改时间排序（旧的在前）
            json_files.sort(key=lambda x: x['mtime'])
            
            files_to_delete = len(json_files) - max_files
            if files_to_delete <= 0:
                return 0, 0.0
            
            # 删除最旧的文件
            deleted_count = 0
            total_size = 0
            
            for file_info in json_files[:files_to_delete]:
                try:
                    file_path = file_info['path']
                    file_size = file_info['size']
                    
                    # 安全删除
                    if self._safe_delete_file(str(file_path)):
                        deleted_count += 1
                        total_size += file_size
                        
                        self.logger.debug(f"删除JSON文件: {file_path.name}")
                    
                except Exception as e:
                    self.logger.error(f"删除JSON文件失败: {file_info['path']}, 错误: {e}")
            
            return deleted_count, total_size / (1024 * 1024)  # 转换为MB
            
        except Exception as e:
            self.logger.error(f"JSON文件清理异常: {e}")
            return 0, 0.0
    
    def _cleanup_execution_folders(self, path: str, max_folders: int) -> Tuple[int, float]:
        """清理执行日志文件夹"""
        try:
            execution_folders = []
            
            # 收集所有执行日志文件夹
            for folder_path in Path(path).iterdir():
                if folder_path.is_dir() and folder_path.name.startswith('round_'):
                    try:
                        stat = folder_path.stat()
                        folder_size = self._get_folder_size(str(folder_path))
                        
                        execution_folders.append({
                            'path': folder_path,
                            'mtime': stat.st_mtime,
                            'size': folder_size
                        })
                    except Exception as e:
                        self.logger.warning(f"获取文件夹信息失败: {folder_path}, {e}")
            
            # 按修改时间排序（旧的在前）
            execution_folders.sort(key=lambda x: x['mtime'])
            
            folders_to_delete = len(execution_folders) - max_folders
            if folders_to_delete <= 0:
                return 0, 0.0
            
            # 删除最旧的文件夹
            deleted_count = 0
            total_size = 0
            
            for folder_info in execution_folders[:folders_to_delete]:
                try:
                    folder_path = folder_info['path']
                    folder_size = folder_info['size']
                    
                    # 安全删除文件夹
                    if self._safe_delete_folder(str(folder_path)):
                        deleted_count += 1
                        total_size += folder_size
                        
                        self.logger.debug(f"删除执行文件夹: {folder_path.name}")
                    
                except Exception as e:
                    self.logger.error(f"删除执行文件夹失败: {folder_info['path']}, 错误: {e}")
            
            return deleted_count, total_size / (1024 * 1024)  # 转换为MB
            
        except Exception as e:
            self.logger.error(f"执行文件夹清理异常: {e}")
            return 0, 0.0
    
    def _cleanup_report_files(self, path: str, max_files: int) -> Tuple[int, float]:
        """清理评价报告文件"""
        try:
            report_files = []
            
            # 收集所有报告文件
            for file_path in Path(path).glob('*_report.log'):
                if file_path.is_file():
                    stat = file_path.stat()
                    report_files.append({
                        'path': file_path,
                        'mtime': stat.st_mtime,
                        'size': stat.st_size
                    })
            
            # 按修改时间排序（旧的在前）
            report_files.sort(key=lambda x: x['mtime'])
            
            files_to_delete = len(report_files) - max_files
            if files_to_delete <= 0:
                return 0, 0.0
            
            # 删除最旧的文件
            deleted_count = 0
            total_size = 0
            
            for file_info in report_files[:files_to_delete]:
                try:
                    file_path = file_info['path']
                    file_size = file_info['size']
                    
                    # 安全删除
                    if self._safe_delete_file(str(file_path)):
                        deleted_count += 1
                        total_size += file_size
                        
                        self.logger.debug(f"删除报告文件: {file_path.name}")
                    
                except Exception as e:
                    self.logger.error(f"删除报告文件失败: {file_info['path']}, 错误: {e}")
            
            return deleted_count, total_size / (1024 * 1024)  # 转换为MB
            
        except Exception as e:
            self.logger.error(f"报告文件清理异常: {e}")
            return 0, 0.0
    
    def _safe_delete_file(self, file_path: str) -> bool:
        """安全删除文件"""
        try:
            # 检查文件是否被占用
            if self._is_file_in_use(file_path):
                self.logger.warning(f"文件被占用，跳过删除: {file_path}")
                return False
            
            # 执行删除
            os.remove(file_path)
            return True
            
        except FileNotFoundError:
            # 文件已经不存在
            return True
        except PermissionError:
            self.logger.warning(f"无权限删除文件: {file_path}")
            return False
        except Exception as e:
            self.logger.error(f"删除文件异常: {file_path}, 错误: {e}")
            return False
    
    def _safe_delete_folder(self, folder_path: str) -> bool:
        """安全删除文件夹"""
        try:
            # 检查文件夹中的文件是否被占用
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_full_path = os.path.join(root, file)
                    if self._is_file_in_use(file_full_path):
                        self.logger.warning(f"文件夹中有文件被占用，跳过删除: {folder_path}")
                        return False
            
            # 执行删除
            shutil.rmtree(folder_path)
            return True
            
        except FileNotFoundError:
            # 文件夹已经不存在
            return True
        except PermissionError:
            self.logger.warning(f"无权限删除文件夹: {folder_path}")
            return False
        except Exception as e:
            self.logger.error(f"删除文件夹异常: {folder_path}, 错误: {e}")
            return False
    
    def _is_file_in_use(self, file_path: str) -> bool:
        """检查文件是否被占用"""
        try:
            # 尝试以独占方式打开文件
            with open(file_path, 'r+b'):
                pass
            return False
        except (IOError, OSError):
            return True
    
    def _get_folder_size(self, folder_path: str) -> int:
        """获取文件夹大小（字节）"""
        total_size = 0
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        pass
        except Exception:
            pass
        return total_size
    
    def manual_cleanup(self, watch_type: Optional[str] = None) -> Dict[str, Any]:
        """手动执行清理"""
        self.logger.info(f"手动执行清理: {watch_type or '所有类型'}")
        
        cleanup_results = {}
        
        if watch_type:
            # 清理指定类型
            if watch_type in self.watch_paths:
                watch_path = self.watch_paths[watch_type]
                max_files = self.file_limits.get(f'{watch_type}_max', 20)
                cleanup_func = self.cleanup_strategies.get(watch_type)
                
                if cleanup_func and os.path.exists(watch_path):
                    cleaned_count, space_freed = cleanup_func(watch_path, max_files)
                    cleanup_results[watch_type] = {
                        'files_cleaned': cleaned_count,
                        'space_freed_mb': space_freed
                    }
        else:
            # 清理所有类型
            for wt in self.watch_paths.keys():
                result = self.manual_cleanup(wt)
                cleanup_results.update(result)
        
        return cleanup_results
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        storage_info = {}
        
        for watch_type, watch_path in self.watch_paths.items():
            if not os.path.exists(watch_path):
                storage_info[watch_type] = {
                    'exists': False,
                    'file_count': 0,
                    'total_size_mb': 0.0
                }
                continue
            
            try:
                if watch_type == 'agent_execute':
                    # 统计文件夹
                    folders = [f for f in Path(watch_path).iterdir() 
                             if f.is_dir() and f.name.startswith('round_')]
                    file_count = len(folders)
                    total_size = sum(self._get_folder_size(str(folder)) for folder in folders)
                else:
                    # 统计文件
                    if watch_type == 'json_plan':
                        files = list(Path(watch_path).glob('*.json'))
                    else:  # judge_report
                        files = list(Path(watch_path).glob('*_report.log'))
                    
                    file_count = len(files)
                    total_size = sum(f.stat().st_size for f in files if f.exists())
                
                max_files = self.file_limits.get(f'{watch_type}_max', 20)
                
                storage_info[watch_type] = {
                    'exists': True,
                    'file_count': file_count,
                    'max_files': max_files,
                    'total_size_mb': total_size / (1024 * 1024),
                    'usage_ratio': file_count / max_files if max_files > 0 else 0,
                    'needs_cleanup': file_count > max_files
                }
                
            except Exception as e:
                self.logger.error(f"获取存储信息失败: {watch_type}, 错误: {e}")
                storage_info[watch_type] = {
                    'exists': True,
                    'error': str(e)
                }
        
        return storage_info
    
    def _log_final_statistics(self):
        """记录最终统计信息"""
        self.logger.info("=== 文件清理器最终统计 ===")
        self.logger.info(f"总检查次数: {self.stats['total_checks']}")
        self.logger.info(f"清理文件数: {self.stats['files_cleaned']}")
        self.logger.info(f"释放空间: {self.stats['space_freed_mb']:.2f}MB")
        
        if self.stats['cleanup_history']:
            self.logger.info("最近清理记录:")
            for record in self.stats['cleanup_history'][-3:]:  # 显示最近3次
                timestamp = datetime.fromisoformat(record['timestamp']).strftime('%m-%d %H:%M')
                self.logger.info(f"  {timestamp}: {record['files_cleaned']}文件, {record['space_freed_mb']:.1f}MB")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'stats': self.stats.copy(),
            'config': {
                'check_interval': self.check_interval,
                'file_limits': self.file_limits.copy(),
                'watch_paths': self.watch_paths.copy()
            },
            'storage_info': self.get_storage_info(),
            'is_running': self.is_running
        }


# 工具函数
def calculate_folder_size(folder_path: str) -> int:
    """计算文件夹大小的工具函数"""
    total_size = 0
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                except (OSError, IOError):
                    pass
    except Exception:
        pass
    return total_size


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f}KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f}MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.1f}GB"


if __name__ == '__main__':
    # 文件清理器测试
    import asyncio
    from config import get_config
    
    async def test_file_cleaner():
        config = get_config()
        cleaner = FileCleaner(config)
        
        print("=== 文件清理器测试 ===")
        
        # 获取存储信息
        storage_info = cleaner.get_storage_info()
        print("存储信息:")
        for watch_type, info in storage_info.items():
            if info.get('exists'):
                print(f"  {watch_type}: {info['file_count']}/{info.get('max_files', 'N/A')} 文件, "
                      f"{info.get('total_size_mb', 0):.2f}MB, "
                      f"需要清理: {'是' if info.get('needs_cleanup') else '否'}")
        
        # 手动清理测试
        cleanup_results = cleaner.manual_cleanup()
        print(f"手动清理结果: {cleanup_results}")
        
        print("测试完成")
    
    asyncio.run(test_file_cleaner())