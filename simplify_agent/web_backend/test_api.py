#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
后端API测试脚本
"""

import os
import sys
import requests
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# Flask应用测试
def test_flask_app():
    """测试Flask应用是否能正常启动"""
    try:
        from simplify_agent.web_backend.app import create_app
        
        app = create_app('development')
        print("✅ Flask应用创建成功")
        
        # 测试应用配置
        with app.app_context():
            print(f"✅ 数据库路径: {app.config.get('DATABASE_PATH')}")
            print(f"✅ API前缀: {app.config.get('API_PREFIX')}")
            print(f"✅ 调试模式: {app.config.get('DEBUG')}")
        
        return True, app
        
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_dao_integration():
    """测试DAO系统集成"""
    try:
        from simplify_agent.web_backend.services.user_service import UserService
        from simplify_agent.web_backend.services.statistics_service import StatisticsService
        from simplify_agent.web_backend.services.dev_service import DevService
        
        # 测试用户服务
        user_service = UserService()
        executions, total = user_service.get_executions_list(page=1, page_size=5)
        print(f"✅ 用户服务测试成功，查询到 {total} 条执行记录")
        
        # 测试统计服务
        stats_service = StatisticsService()
        overview = stats_service.get_overview_statistics()
        print(f"✅ 统计服务测试成功，总计划数: {overview.get('total_plans', 0)}")
        
        # 测试开发者服务
        dev_service = DevService()
        db_info = dev_service.get_database_info()
        print(f"✅ 开发者服务测试成功，数据库表数: {len(db_info['database_info']['tables'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ DAO系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_endpoints():
    """测试API端点（需要先启动服务器）"""
    
    base_url = "http://127.0.0.1:5000"
    api_prefix = "/api"
    
    print(f"\n🧪 测试API端点（服务器需运行在 {base_url}）...")
    
    # 测试健康检查
    endpoints_to_test = [
        f"{api_prefix}/user/health",
        f"{api_prefix}/statistics/health", 
        f"{api_prefix}/dev/health"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint}: {data.get('message', 'OK')}")
            else:
                print(f"⚠️ {endpoint}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint}: 连接失败 - {e}")
    
    # 测试用户API
    try:
        response = requests.get(f"{base_url}{api_prefix}/user/executions?page=1&page_size=5", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                pagination = data.get('data', {}).get('pagination', {})
                print(f"✅ 用户执行列表API: 查询到 {pagination.get('total_items', 0)} 条记录")
            else:
                print(f"⚠️ 用户执行列表API: {data.get('message', '未知错误')}")
        else:
            print(f"⚠️ 用户执行列表API: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 用户执行列表API: 连接失败 - {e}")
    
    # 测试统计API
    try:
        response = requests.get(f"{base_url}{api_prefix}/statistics/overview", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                print(f"✅ 统计概览API: 总执行数 {stats.get('total_executions', 0)}, 成功率 {stats.get('success_rate', 0)}%")
            else:
                print(f"⚠️ 统计概览API: {data.get('message', '未知错误')}")
        else:
            print(f"⚠️ 统计概览API: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 统计概览API: 连接失败 - {e}")


def create_test_data():
    """创建测试数据"""
    try:
        # 使用DAO系统创建一些测试数据
        from simplify_agent.database.dao import TestPlanDAO, TestExecutionDAO, ToolExecutionDAO, EvaluationDAO
        
        print("\n📊 创建测试数据...")
        
        plan_dao = TestPlanDAO()
        execution_dao = TestExecutionDAO()
        tool_dao = ToolExecutionDAO()
        evaluation_dao = EvaluationDAO()
        
        # 创建测试计划
        test_plan = {
            'plan_id': f'web_test_plan_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'original_request': 'Web后端API测试计划',
            'platform': 'ios',
            'total_steps': 3,
            'plan_summary': '用于验证Web后端API功能的测试计划',
            'structured_plan': {
                'steps': [
                    {'step': 1, 'action': '初始化测试环境'},
                    {'step': 2, 'action': '执行API测试'},
                    {'step': 3, 'action': '验证测试结果'}
                ]
            },
            'generation_metadata': {'created_by': 'api_test_script'},
            'agent_instructions': 'API测试专用计划'
        }
        
        plan_id = plan_dao.create_test_plan(test_plan)
        print(f"✅ 创建测试计划成功，ID: {plan_id}")
        
        # 创建测试执行
        test_execution = {
            'execution_id': f'web_test_exec_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'plan_id': test_plan['plan_id'],
            'original_request': 'Web后端API测试执行',
            'execution_status': 'success',
            'total_rounds': 1,
            'total_duration': 10.5,
            'start_time': datetime.now().isoformat(),
            'end_time': datetime.now().isoformat()
        }
        
        exec_id = execution_dao.create_execution(test_execution)
        print(f"✅ 创建测试执行成功，ID: {exec_id}")
        
        # 创建工具执行记录
        test_tool = {
            'execution_id': test_execution['execution_id'],
            'round_number': 1,
            'tool_name': 'api_test_tool',
            'tool_parameters': {'test_type': 'backend_api'},
            'execution_time': 10.5,
            'tool_status': 'success',
            'tool_result': {'result': 'API测试成功', 'tests_passed': 10},
            'result_summary': 'API后端测试全部通过'
        }
        
        tool_id = tool_dao.create_tool_execution(test_tool)
        print(f"✅ 创建工具执行记录成功，ID: {tool_id}")
        
        # 创建综合评价
        test_evaluation = {
            'execution_id': test_execution['execution_id'],
            'evaluation_round': 'test_round',
            'analysis_model': 'api_test_evaluator',
            'final_success_status': 'success',
            'overall_success_score': 0.95,
            'confidence_score': 0.99,
            'comprehensive_analysis': 'Web后端API测试执行成功，所有接口响应正常',
            'evaluation_summary': 'API测试成功完成'
        }
        
        eval_id = evaluation_dao.create_comprehensive_evaluation(test_evaluation)
        print(f"✅ 创建综合评价成功，ID: {eval_id}")
        
        print(f"✅ 测试数据创建完成！")
        print(f"   - 计划ID: {test_plan['plan_id']}")
        print(f"   - 执行ID: {test_execution['execution_id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧪 开始后端API测试...")
    print("=" * 60)
    
    # 1. 测试Flask应用创建
    print("📊 步骤1: 测试Flask应用...")
    flask_success, app = test_flask_app()
    
    if not flask_success:
        print("❌ Flask应用测试失败，终止测试")
        return
    
    # 2. 测试DAO集成
    print("\n📊 步骤2: 测试DAO系统集成...")
    dao_success = test_dao_integration()
    
    if not dao_success:
        print("❌ DAO系统集成测试失败，终止测试")
        return
    
    # 3. 创建测试数据
    print("\n📊 步骤3: 创建测试数据...")
    test_data_success = create_test_data()
    
    # 4. 测试API端点（可选，需要服务器运行）
    print("\n📊 步骤4: 测试API端点...")
    print("提示: 这需要先运行服务器 (python -m simplify_agent.web_backend.app)")
    print("跳过API端点测试 - 需要单独启动服务器进行测试")
    
    print("\n" + "=" * 60)
    print("🎉 后端API测试完成！")
    
    if flask_success and dao_success:
        print("✅ 基础功能测试通过")
        if test_data_success:
            print("✅ 测试数据创建成功")
        print("\n📝 接下来可以:")
        print("1. 运行服务器: python -m simplify_agent.web_backend.app")
        print("2. 访问API: http://127.0.0.1:5000/api/user/health")
        print("3. 继续开发前端界面")
    else:
        print("❌ 基础功能测试失败，请检查错误信息")


if __name__ == "__main__":
    main()