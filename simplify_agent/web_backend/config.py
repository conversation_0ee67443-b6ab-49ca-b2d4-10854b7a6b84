#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web后端配置文件
"""

import os
from datetime import timedelta


class Config:
    """基础配置"""
    
    # 应用配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'Cuijie5622!'
    
    # 数据库配置
    DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'test_database.db')
    
    # API配置
    API_PREFIX = '/api'
    JSON_AS_ASCII = False
    JSONIFY_PRETTYPRINT_REGULAR = True
    
    # CORS配置 - 允许局域网访问
    CORS_ORIGINS = [
        'http://localhost:3000', 
        'http://127.0.0.1:3000',
        'http://************:3000'  # 局域网IP
    ]
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # 认证配置
    DEV_PASSWORD = 'Cuijie5622!'  # 开发者模式密码
    SESSION_TIMEOUT = timedelta(hours=8)  # 会话超时时间
    
    # 文件服务配置
    UPLOAD_FOLDER = 'static/uploads'
    SCREENSHOT_FOLDER = 'static/screenshots'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    CORS_ORIGINS = ['*']


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'


# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}