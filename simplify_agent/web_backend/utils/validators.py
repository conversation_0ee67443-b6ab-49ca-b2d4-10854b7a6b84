#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据验证工具
"""

from typing import Any, Dict, Optional, Tuple
from datetime import datetime
from flask import request


def validate_pagination(default_page_size: int = 20, max_page_size: int = 100) -> Tuple[int, int]:
    """验证分页参数
    
    Args:
        default_page_size: 默认页面大小
        max_page_size: 最大页面大小
        
    Returns:
        (page, page_size) 元组
    """
    page = safe_int(request.args.get('page', 1))
    page_size = safe_int(request.args.get('page_size', default_page_size))
    
    # 验证页码
    if page < 1:
        page = 1
    
    # 验证页面大小
    if page_size < 1:
        page_size = default_page_size
    elif page_size > max_page_size:
        page_size = max_page_size
    
    return page, page_size


def validate_date_range() -> Tuple[Optional[str], Optional[str]]:
    """验证日期范围参数
    
    Returns:
        (start_date, end_date) 元组，格式为 'YYYY-MM-DD' 或 None
    """
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 验证日期格式
    if start_date:
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
        except ValueError:
            start_date = None
    
    if end_date:
        try:
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            end_date = None
    
    return start_date, end_date


def safe_int(value: Any, default: int = 0) -> int:
    """安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的整数
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的浮点数
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def validate_json_field(data: Dict[str, Any], field: str, required: bool = False) -> Tuple[bool, str]:
    """验证JSON字段
    
    Args:
        data: 数据字典
        field: 字段名
        required: 是否必需
        
    Returns:
        (is_valid, error_message) 元组
    """
    if field not in data:
        if required:
            return False, f"缺少必需字段: {field}"
        return True, ""
    
    if data[field] is None and required:
        return False, f"字段 {field} 不能为空"
    
    return True, ""


def validate_execution_status(status: str) -> bool:
    """验证执行状态
    
    Args:
        status: 状态值
        
    Returns:
        是否有效
    """
    valid_statuses = ['running', 'success', 'failed', 'timeout', 'cancelled']
    return status in valid_statuses


def validate_platform(platform: str) -> bool:
    """验证平台
    
    Args:
        platform: 平台值
        
    Returns:
        是否有效
    """
    valid_platforms = ['ios', 'android']
    return platform in valid_platforms


def validate_score_range(score: float) -> bool:
    """验证评分范围
    
    Args:
        score: 评分值
        
    Returns:
        是否在有效范围内 (0.0-1.0)
    """
    return 0.0 <= score <= 1.0