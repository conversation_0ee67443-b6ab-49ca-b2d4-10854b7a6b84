#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一API响应格式工具
"""

import time
from typing import Any, Dict, Optional
from flask import jsonify


def success_response(data: Any = None, message: str = "操作成功", code: int = 200) -> Dict[str, Any]:
    """成功响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 响应码
        
    Returns:
        标准化的成功响应
    """
    return jsonify({
        'success': True,
        'data': data,
        'message': message,
        'code': code,
        'timestamp': int(time.time())
    })


def error_response(message: str = "操作失败", code: int = 400, data: Any = None) -> Dict[str, Any]:
    """错误响应格式
    
    Args:
        message: 错误消息
        code: 错误码
        data: 额外的错误数据
        
    Returns:
        标准化的错误响应
    """
    return jsonify({
        'success': False,
        'data': data,
        'message': message,
        'code': code,
        'timestamp': int(time.time())
    })


def paginated_response(items: list, total: int, page: int, page_size: int, 
                      message: str = "查询成功") -> Dict[str, Any]:
    """分页响应格式
    
    Args:
        items: 当前页数据列表
        total: 总记录数
        page: 当前页码
        page_size: 每页大小
        message: 响应消息
        
    Returns:
        分页响应数据
    """
    total_pages = (total + page_size - 1) // page_size  # 向上取整
    
    return success_response(
        data={
            'items': items,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_items': total,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        },
        message=message
    )


def validation_error_response(errors: Dict[str, str]) -> Dict[str, Any]:
    """数据验证错误响应
    
    Args:
        errors: 验证错误字典，key为字段名，value为错误信息
        
    Returns:
        验证错误响应
    """
    return error_response(
        message="数据验证失败",
        code=422,
        data={
            'validation_errors': errors
        }
    )


def not_found_response(resource: str = "资源") -> Dict[str, Any]:
    """资源未找到响应
    
    Args:
        resource: 资源名称
        
    Returns:
        404错误响应
    """
    return error_response(
        message=f"{resource}不存在",
        code=404
    )


def unauthorized_response(message: str = "未授权访问") -> Dict[str, Any]:
    """未授权访问响应
    
    Args:
        message: 错误消息
        
    Returns:
        401错误响应
    """
    return error_response(
        message=message,
        code=401
    )


def server_error_response(message: str = "服务器内部错误") -> Dict[str, Any]:
    """服务器错误响应
    
    Args:
        message: 错误消息
        
    Returns:
        500错误响应
    """
    return error_response(
        message=message,
        code=500
    )