#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
开发者视角业务服务
"""

import os
import sys
import sqlite3
import time
from typing import List, Dict, Any, Optional, Tuple

# 添加项目根目录到路径以导入DAO
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from simplify_agent.database.dao import (
    TestPlanDAO, TestExecutionDAO, ToolExecutionDAO, EvaluationDAO
)
from simplify_agent.database.db_connection import get_database_manager


class DevService:
    """开发者视角业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.db_manager = get_database_manager()
        self.test_plan_dao = TestPlanDAO()
        self.test_execution_dao = TestExecutionDAO()
        self.tool_execution_dao = ToolExecutionDAO()
        self.evaluation_dao = EvaluationDAO()
        
        # 表名映射到DAO的字典
        self.table_daos = {
            'test_plans': self.test_plan_dao,
            'test_executions': self.test_execution_dao,
            'tool_executions': self.tool_execution_dao,
            'plan_evaluations': self.evaluation_dao.plan_dao,
            'execution_evaluations': self.evaluation_dao.execution_dao,
            'comprehensive_evaluations': self.evaluation_dao.comprehensive_dao
        }
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库概览信息
        
        Returns:
            数据库信息字典
        """
        try:
            # 获取基础数据库信息
            db_info = self.db_manager.get_database_info()
            
            # 获取健康状态
            health_info = self.db_manager.health_check()
            
            # 获取各表的记录数
            table_counts = {}
            for table_name in db_info['tables']:
                try:
                    if table_name in self.table_daos:
                        count = self.table_daos[table_name].count()
                    else:
                        # 直接查询数据库
                        results = self.db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                        count = results[0]['count'] if results else 0
                    
                    table_counts[table_name] = count
                except Exception as e:
                    table_counts[table_name] = f"Error: {str(e)}"
            
            return {
                'database_info': db_info,
                'health_status': health_info,
                'table_counts': table_counts
            }
            
        except Exception as e:
            raise Exception(f"获取数据库信息失败: {str(e)}")
    
    def get_table_list(self) -> List[Dict[str, Any]]:
        """获取所有表的列表信息
        
        Returns:
            表信息列表
        """
        try:
            # 获取数据库中的所有表
            tables_query = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
            tables_result = self.db_manager.execute_query(tables_query)
            
            table_list = []
            for table_row in tables_result:
                table_name = table_row['name']
                
                # 获取表结构信息
                table_info = self.db_manager.get_table_info(table_name)
                
                # 获取记录数
                try:
                    if table_name in self.table_daos:
                        record_count = self.table_daos[table_name].count()
                    else:
                        count_result = self.db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                        record_count = count_result[0]['count'] if count_result else 0
                except Exception:
                    record_count = 0
                
                table_list.append({
                    'table_name': table_name,
                    'column_count': len(table_info),
                    'record_count': record_count,
                    'columns': [
                        {
                            'name': col['name'],
                            'type': col['type'],
                            'not_null': bool(col['notnull']),
                            'default_value': col['dflt_value'],
                            'primary_key': bool(col['pk'])
                        }
                        for col in table_info
                    ]
                })
            
            return table_list
            
        except Exception as e:
            raise Exception(f"获取表列表失败: {str(e)}")
    
    def get_table_data(self, table_name: str, page: int = 1, page_size: int = 20,
                      search: Optional[str] = None, order_by: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """获取表数据
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 每页大小
            search: 搜索关键词
            order_by: 排序字段
            
        Returns:
            (数据列表, 总记录数) 元组
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 构建查询
            offset = (page - 1) * page_size
            
            # 基础查询
            base_query = f"SELECT * FROM {table_name}"
            count_query = f"SELECT COUNT(*) as count FROM {table_name}"
            
            params = []
            
            # 添加搜索条件（简单的全文搜索）
            if search:
                # 获取表结构来构建搜索条件
                table_info = self.db_manager.get_table_info(table_name)
                text_columns = [col['name'] for col in table_info if col['type'].upper() in ['TEXT', 'VARCHAR']]
                
                if text_columns:
                    search_conditions = []
                    for col in text_columns:
                        search_conditions.append(f"{col} LIKE ?")
                        params.append(f"%{search}%")
                    
                    where_clause = " WHERE " + " OR ".join(search_conditions)
                    base_query += where_clause
                    count_query += where_clause
            
            # 添加排序
            if order_by:
                # 验证排序字段是否存在
                table_info = self.db_manager.get_table_info(table_name)
                valid_columns = [col['name'] for col in table_info]
                
                if order_by.replace(' DESC', '').replace(' ASC', '').strip() in valid_columns:
                    base_query += f" ORDER BY {order_by}"
                else:
                    # 默认排序
                    base_query += " ORDER BY rowid DESC"
            else:
                base_query += " ORDER BY rowid DESC"
            
            # 添加分页
            base_query += f" LIMIT {page_size} OFFSET {offset}"
            
            # 执行查询
            data_results = self.db_manager.execute_query(base_query, tuple(params) if params else None)
            count_results = self.db_manager.execute_query(count_query, tuple(params) if params else None)
            
            # 转换结果
            data_list = [dict(row) for row in data_results]
            total_count = count_results[0]['count'] if count_results else 0
            
            return data_list, total_count
            
        except Exception as e:
            raise Exception(f"获取表数据失败: {str(e)}")
    
    def create_record(self, table_name: str, data: Dict[str, Any]) -> int:
        """创建新记录
        
        Args:
            table_name: 表名
            data: 记录数据
            
        Returns:
            新记录的ID
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 使用对应的DAO或直接数据库操作
            if table_name in self.table_daos:
                dao = self.table_daos[table_name]
                return dao.insert(data)
            else:
                # 直接构建INSERT语句
                columns = list(data.keys())
                placeholders = ','.join(['?' for _ in columns])
                
                query = f"INSERT INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
                params = tuple(data.values())
                
                return self.db_manager.execute_update(query, params)
            
        except Exception as e:
            raise Exception(f"创建记录失败: {str(e)}")
    
    def update_record(self, table_name: str, record_id: int, data: Dict[str, Any]) -> bool:
        """更新记录
        
        Args:
            table_name: 表名
            record_id: 记录ID
            data: 更新数据
            
        Returns:
            是否更新成功
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 使用对应的DAO或直接数据库操作
            if table_name in self.table_daos:
                dao = self.table_daos[table_name]
                result = dao.update(data, {'id': record_id})
                return result > 0
            else:
                # 直接构建UPDATE语句
                set_parts = [f"{key} = ?" for key in data.keys()]
                set_clause = ", ".join(set_parts)
                
                query = f"UPDATE {table_name} SET {set_clause} WHERE id = ?"
                params = tuple(data.values()) + (record_id,)
                
                result = self.db_manager.execute_update(query, params)
                return result > 0
            
        except Exception as e:
            raise Exception(f"更新记录失败: {str(e)}")
    
    def delete_record(self, table_name: str, record_id: int) -> bool:
        """删除记录
        
        Args:
            table_name: 表名
            record_id: 记录ID
            
        Returns:
            是否删除成功
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 使用对应的DAO或直接数据库操作
            if table_name in self.table_daos:
                dao = self.table_daos[table_name]
                result = dao.delete({'id': record_id})
                return result > 0
            else:
                # 直接构建DELETE语句
                query = f"DELETE FROM {table_name} WHERE id = ?"
                result = self.db_manager.execute_update(query, (record_id,))
                return result > 0
            
        except Exception as e:
            raise Exception(f"删除记录失败: {str(e)}")
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构
        
        Args:
            table_name: 表名
            
        Returns:
            表结构信息
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 获取表结构
            table_info = self.db_manager.get_table_info(table_name)
            
            # 获取索引信息
            indexes_query = f"PRAGMA index_list({table_name})"
            indexes_result = self.db_manager.execute_query(indexes_query)
            
            # 获取外键信息
            foreign_keys_query = f"PRAGMA foreign_key_list({table_name})"
            foreign_keys_result = self.db_manager.execute_query(foreign_keys_query)
            
            return {
                'table_name': table_name,
                'columns': [dict(col) for col in table_info],
                'indexes': [dict(idx) for idx in indexes_result],
                'foreign_keys': [dict(fk) for fk in foreign_keys_result]
            }
            
        except Exception as e:
            raise Exception(f"获取表结构失败: {str(e)}")
    
    def add_column(self, table_name: str, column_name: str, 
                  column_type: str, not_null: bool = False,
                  default_value: Optional[str] = None) -> bool:
        """添加表字段
        
        Args:
            table_name: 表名
            column_name: 字段名
            column_type: 字段类型
            not_null: 是否非空
            default_value: 默认值
            
        Returns:
            是否添加成功
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 构建ALTER TABLE语句
            alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
            
            if not_null:
                alter_sql += " NOT NULL"
            
            if default_value is not None:
                alter_sql += f" DEFAULT {default_value}"
            
            self.db_manager.execute_update(alter_sql)
            return True
            
        except Exception as e:
            raise Exception(f"添加字段失败: {str(e)}")
    
    def drop_column(self, table_name: str, column_name: str) -> bool:
        """删除表字段
        
        注意：SQLite不支持直接DROP COLUMN，需要重建表
        
        Args:
            table_name: 表名
            column_name: 字段名
            
        Returns:
            是否删除成功
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 获取当前表结构
            schema = self.get_table_schema(table_name)
            columns = schema.get('columns', [])
            
            # 检查字段是否存在
            target_column = None
            for col in columns:
                if col['name'] == column_name:
                    target_column = col
                    break
            
            if not target_column:
                raise ValueError(f"字段 {column_name} 不存在")
            
            # 不允许删除主键字段
            if target_column.get('pk', False):
                raise ValueError("不能删除主键字段")
            
            # 获取除了要删除的字段外的所有字段
            remaining_columns = [col for col in columns if col['name'] != column_name]
            
            if not remaining_columns:
                raise ValueError("不能删除表的最后一个字段")
            
            # 构建新表的字段定义
            column_definitions = []
            for col in remaining_columns:
                col_def = f"{col['name']} {col['type']}"
                if col.get('notnull', False):
                    col_def += " NOT NULL"
                if col.get('dflt_value') is not None:
                    col_def += f" DEFAULT {col['dflt_value']}"
                if col.get('pk', False):
                    col_def += " PRIMARY KEY"
                column_definitions.append(col_def)
            
            # 字段名列表（用于数据迁移）
            column_names = [col['name'] for col in remaining_columns]
            column_names_str = ', '.join(column_names)
            
            # 执行表重建操作（在事务中）
            temp_table_name = f"{table_name}_temp_{int(time.time())}"
            
            # 使用数据库管理器的事务上下文
            with self.db_manager.get_transaction() as conn:
                cursor = conn.cursor()
                
                # 1. 创建临时表
                create_temp_sql = f"""
                CREATE TABLE {temp_table_name} (
                    {', '.join(column_definitions)}
                )
                """
                cursor.execute(create_temp_sql)
                
                # 2. 复制数据到临时表
                copy_sql = f"""
                INSERT INTO {temp_table_name} ({column_names_str})
                SELECT {column_names_str} FROM {table_name}
                """
                cursor.execute(copy_sql)
                
                # 3. 删除原表
                cursor.execute(f"DROP TABLE {table_name}")
                
                # 4. 重命名临时表
                cursor.execute(f"ALTER TABLE {temp_table_name} RENAME TO {table_name}")
                
                cursor.close()
                
            return True
            
        except Exception as e:
            raise Exception(f"删除字段失败: {str(e)}")
    
    def edit_column(self, table_name: str, old_column_name: str, 
                   new_column_name: str, new_column_type: str, 
                   not_null: bool = False, default_value: Optional[str] = None) -> bool:
        """编辑表字段
        
        注意：SQLite不支持直接ALTER COLUMN，需要重建表
        
        Args:
            table_name: 表名
            old_column_name: 原字段名
            new_column_name: 新字段名
            new_column_type: 新字段类型
            not_null: 是否非空
            default_value: 默认值
            
        Returns:
            是否编辑成功
        """
        try:
            # 验证表是否存在
            if not self.db_manager.table_exists(table_name):
                raise ValueError(f"表 {table_name} 不存在")
            
            # 获取当前表结构
            schema = self.get_table_schema(table_name)
            columns = schema.get('columns', [])
            
            # 检查原字段是否存在
            target_column = None
            for col in columns:
                if col['name'] == old_column_name:
                    target_column = col
                    break
            
            if not target_column:
                raise ValueError(f"字段 {old_column_name} 不存在")
            
            # 不允许编辑主键字段
            if target_column.get('pk', False):
                raise ValueError("不能编辑主键字段")
            
            # 如果是重命名，检查新字段名是否已存在
            if old_column_name != new_column_name:
                for col in columns:
                    if col['name'] == new_column_name:
                        raise ValueError(f"字段名 {new_column_name} 已存在")
            
            # 类型转换安全检查
            old_type = target_column.get('type', '').upper()
            new_type_upper = new_column_type.upper()
            
            # 检查是否是高风险的类型转换
            risky_conversions = [
                ('TEXT', 'INTEGER'), ('TEXT', 'REAL'),
                ('INTEGER', 'TEXT'), ('REAL', 'TEXT')
            ]
            
            if (old_type, new_type_upper.split('(')[0]) in risky_conversions:
                # 这里可以添加数据验证逻辑
                # 暂时只给出警告，不阻止操作
                pass
            
            # 构建新表的字段定义
            column_definitions = []
            column_mappings = []  # 用于数据迁移的字段映射
            
            for col in columns:
                if col['name'] == old_column_name:
                    # 这是要编辑的字段
                    col_def = f"{new_column_name} {new_column_type}"
                    if not_null:
                        col_def += " NOT NULL"
                    if default_value is not None:
                        col_def += f" DEFAULT {default_value}"
                    column_definitions.append(col_def)
                    
                    # 字段映射：如果只是重命名，直接映射；如果改类型，可能需要转换
                    if old_column_name == new_column_name and col['type'].upper() == new_column_type.upper():
                        # 只是约束变化，直接映射
                        column_mappings.append(f"{old_column_name}")
                    else:
                        # 字段名或类型有变化，需要处理转换
                        column_mappings.append(f"{old_column_name}")
                else:
                    # 其他字段保持不变
                    col_def = f"{col['name']} {col['type']}"
                    if col.get('notnull', False):
                        col_def += " NOT NULL"
                    if col.get('dflt_value') is not None:
                        col_def += f" DEFAULT {col['dflt_value']}"
                    if col.get('pk', False):
                        col_def += " PRIMARY KEY"
                    column_definitions.append(col_def)
                    column_mappings.append(f"{col['name']}")
            
            # 新表的字段名列表
            new_column_names = []
            for col in columns:
                if col['name'] == old_column_name:
                    new_column_names.append(new_column_name)
                else:
                    new_column_names.append(col['name'])
            
            # 执行表重建操作（在事务中）
            temp_table_name = f"{table_name}_temp_{int(time.time())}"
            
            # 使用数据库管理器的事务上下文
            with self.db_manager.get_transaction() as conn:
                cursor = conn.cursor()
                
                # 1. 创建临时表
                create_temp_sql = f"""
                CREATE TABLE {temp_table_name} (
                    {', '.join(column_definitions)}
                )
                """
                cursor.execute(create_temp_sql)
                
                # 2. 复制数据到临时表（处理字段映射）
                copy_sql = f"""
                INSERT INTO {temp_table_name} ({', '.join(new_column_names)})
                SELECT {', '.join(column_mappings)} FROM {table_name}
                """
                cursor.execute(copy_sql)
                
                # 3. 删除原表
                cursor.execute(f"DROP TABLE {table_name}")
                
                # 4. 重命名临时表
                cursor.execute(f"ALTER TABLE {temp_table_name} RENAME TO {table_name}")
                
                cursor.close()
                
            return True
            
        except Exception as e:
            raise Exception(f"编辑字段失败: {str(e)}")
    
    def execute_sql(self, sql: str, params: Optional[Tuple] = None) -> Dict[str, Any]:
        """执行自定义SQL语句
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            执行结果
        """
        try:
            # 安全检查：只允许SELECT、PRAGMA等安全查询
            sql_upper = sql.strip().upper()
            if sql_upper.startswith(('SELECT', 'PRAGMA', 'EXPLAIN')):
                results = self.db_manager.execute_query(sql, params)
                return {
                    'success': True,
                    'data': [dict(row) for row in results],
                    'row_count': len(results)
                }
            elif sql_upper.startswith(('INSERT', 'UPDATE', 'DELETE', 'ALTER', 'CREATE', 'DROP')):
                # 修改操作需要特殊权限，这里暂时允许但记录日志
                result = self.db_manager.execute_update(sql, params)
                return {
                    'success': True,
                    'affected_rows': result,
                    'message': '修改操作执行成功'
                }
            else:
                raise ValueError("不支持的SQL操作类型")
            
        except Exception as e:
            raise Exception(f"执行SQL失败: {str(e)}")