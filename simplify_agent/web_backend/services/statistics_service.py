#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统计分析业务服务
"""

import os
import sys
from typing import List, Dict, Any, Optional
from collections import Counter, defaultdict
from datetime import datetime, timedelta

# 添加项目根目录到路径以导入DAO
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from simplify_agent.database.dao import (
    TestPlanDAO, TestExecutionDAO, ToolExecutionDAO, EvaluationDAO
)


class StatisticsService:
    """统计分析业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.test_plan_dao = TestPlanDAO()
        self.test_execution_dao = TestExecutionDAO()
        self.tool_execution_dao = ToolExecutionDAO()
        self.evaluation_dao = EvaluationDAO()
    
    def get_overview_statistics(self) -> Dict[str, Any]:
        """获取概览统计信息
        
        Returns:
            概览统计数据
        """
        try:
            # 基础计数统计
            total_plans = self.test_plan_dao.count()
            total_executions = self.test_execution_dao.count()
            total_tools = self.tool_execution_dao.count()
            
            # 执行状态统计
            execution_stats = self.test_execution_dao.get_execution_statistics()
            
            # 成功率计算
            success_count = 0
            total_completed = 0
            
            if execution_stats and 'status_distribution' in execution_stats:
                for status_info in execution_stats['status_distribution']:
                    status = status_info.get('execution_status')
                    count = status_info.get('count', 0)
                    
                    if status in ['success', 'failed', 'timeout', 'cancelled']:
                        total_completed += count
                        if status == 'success':
                            success_count += count
            
            success_rate = (success_count / total_completed * 100) if total_completed > 0 else 0
            
            # 平均执行时间
            avg_duration = 0
            if execution_stats and 'overall_statistics' in execution_stats:
                avg_duration = execution_stats['overall_statistics'].get('avg_duration', 0) or 0
            
            # 最近7天统计
            recent_stats = self._get_recent_statistics(7)
            
            return {
                'total_plans': total_plans,
                'total_executions': total_executions,
                'total_tools': total_tools,
                'success_rate': round(success_rate, 2),
                'avg_duration': round(avg_duration, 2),
                'recent_executions': recent_stats['recent_executions'],
                'recent_success_rate': recent_stats['success_rate'],
                'status_distribution': execution_stats.get('status_distribution', []) if execution_stats else []
            }
            
        except Exception as e:
            raise Exception(f"获取概览统计失败: {str(e)}")
    
    def get_success_rate_analysis(self) -> Dict[str, Any]:
        """获取成功率分析数据
        
        Returns:
            成功率分析数据
        """
        try:
            # 总体成功率
            all_executions = self.test_execution_dao.find_all()
            
            status_counts = Counter()
            platform_success = defaultdict(lambda: {'total': 0, 'success': 0})
            
            for execution in all_executions:
                status = execution.get('execution_status')
                status_counts[status] += 1
                
                # 获取平台信息（通过plan_id关联）
                plan_id = execution.get('plan_id')
                platform = 'unknown'
                if plan_id:
                    plan = self.test_plan_dao.get_plan_by_plan_id(plan_id)
                    if plan:
                        platform = plan.get('platform', 'unknown')
                
                platform_success[platform]['total'] += 1
                if status == 'success':
                    platform_success[platform]['success'] += 1
            
            # 计算平台成功率
            platform_rates = {}
            for platform, counts in platform_success.items():
                rate = (counts['success'] / counts['total'] * 100) if counts['total'] > 0 else 0
                platform_rates[platform] = {
                    'total': counts['total'],
                    'success': counts['success'],
                    'rate': round(rate, 2)
                }
            
            # 准备饼图数据
            pie_data = []
            total_completed = sum(count for status, count in status_counts.items() 
                                if status in ['success', 'failed', 'timeout', 'cancelled'])
            
            if total_completed > 0:
                for status, count in status_counts.items():
                    if status in ['success', 'failed', 'timeout', 'cancelled']:
                        percentage = count / total_completed * 100
                        pie_data.append({
                            'name': self._translate_status(status),
                            'value': count,
                            'percentage': round(percentage, 2)
                        })
            
            return {
                'overall_stats': dict(status_counts),
                'platform_rates': platform_rates,
                'pie_chart_data': pie_data
            }
            
        except Exception as e:
            raise Exception(f"获取成功率分析失败: {str(e)}")
    
    def get_trend_analysis(self, days: int = 30) -> Dict[str, Any]:
        """获取趋势分析数据
        
        Args:
            days: 分析天数
            
        Returns:
            趋势分析数据
        """
        try:
            # 获取评价趋势
            evaluation_trends = self.evaluation_dao.get_evaluation_trends(days)
            
            # 获取执行数据并按日期分组
            all_executions = self.test_execution_dao.find_all()
            
            # 按日期分组统计
            daily_stats = defaultdict(lambda: {
                'total': 0, 'success': 0, 'failed': 0, 
                'total_duration': 0, 'execution_count': 0
            })
            
            for execution in all_executions:
                created_at = execution.get('created_at', '')
                if not created_at:
                    continue
                
                # 提取日期
                date = created_at.split(' ')[0] if ' ' in created_at else created_at.split('T')[0]
                
                # 检查是否在指定天数内
                try:
                    exec_date = datetime.strptime(date, '%Y-%m-%d')
                    cutoff_date = datetime.now() - timedelta(days=days)
                    
                    if exec_date < cutoff_date:
                        continue
                except ValueError:
                    continue
                
                status = execution.get('execution_status')
                duration = execution.get('total_duration', 0) or 0
                
                daily_stats[date]['total'] += 1
                if status == 'success':
                    daily_stats[date]['success'] += 1
                elif status in ['failed', 'timeout', 'cancelled']:
                    daily_stats[date]['failed'] += 1
                
                if duration > 0:
                    daily_stats[date]['total_duration'] += duration
                    daily_stats[date]['execution_count'] += 1
            
            # 格式化趋势数据
            trend_data = []
            for date in sorted(daily_stats.keys(), reverse=True)[:days]:
                stats = daily_stats[date]
                success_rate = (stats['success'] / stats['total'] * 100) if stats['total'] > 0 else 0
                avg_duration = (stats['total_duration'] / stats['execution_count']) if stats['execution_count'] > 0 else 0
                
                trend_data.append({
                    'date': date,
                    'total_executions': stats['total'],
                    'success_count': stats['success'],
                    'failed_count': stats['failed'],
                    'success_rate': round(success_rate, 2),
                    'avg_duration': round(avg_duration, 2)
                })
            
            return {
                'daily_trends': sorted(trend_data, key=lambda x: x['date']),
                'evaluation_trends': evaluation_trends
            }
            
        except Exception as e:
            raise Exception(f"获取趋势分析失败: {str(e)}")
    
    def get_platform_distribution(self) -> Dict[str, Any]:
        """获取平台分布统计
        
        Returns:
            平台分布数据
        """
        try:
            platform_stats = self.test_plan_dao.get_platform_statistics()
            
            # 格式化数据
            distribution_data = []
            for stat in platform_stats:
                distribution_data.append({
                    'platform': stat.get('platform'),
                    'plan_count': stat.get('plan_count'),
                    'avg_steps': round(stat.get('avg_steps', 0), 2),
                    'first_created': stat.get('first_created'),
                    'last_created': stat.get('last_created')
                })
            
            return {
                'platform_distribution': distribution_data
            }
            
        except Exception as e:
            raise Exception(f"获取平台分布失败: {str(e)}")
    
    def get_tool_usage_analysis(self) -> Dict[str, Any]:
        """获取工具使用分析
        
        Returns:
            工具使用分析数据
        """
        try:
            # 获取工具性能统计
            tool_performance = self.tool_execution_dao.get_tool_performance_stats()
            
            # 获取工具使用趋势
            tool_trends = self.tool_execution_dao.get_tool_usage_trends(days=7)
            
            # 格式化性能数据
            performance_data = []
            for tool in tool_performance:
                performance_data.append({
                    'tool_name': tool.get('tool_name'),
                    'usage_count': tool.get('execution_count'),
                    'avg_time': round(tool.get('avg_execution_time', 0), 2),
                    'success_rate': tool.get('success_rate'),
                    'error_count': tool.get('error_count')
                })
            
            # 按使用频率排序
            performance_data.sort(key=lambda x: x['usage_count'], reverse=True)
            
            return {
                'tool_performance': performance_data,
                'tool_trends': tool_trends
            }
            
        except Exception as e:
            raise Exception(f"获取工具使用分析失败: {str(e)}")
    
    def get_custom_statistics(self, 
                             instruction_pattern: Optional[str] = None,
                             start_date: Optional[str] = None,
                             end_date: Optional[str] = None,
                             platform: Optional[str] = None) -> Dict[str, Any]:
        """获取自定义统计数据
        
        Args:
            instruction_pattern: 指令名称模式
            start_date: 开始日期
            end_date: 结束日期
            platform: 平台筛选
            
        Returns:
            自定义统计数据
        """
        try:
            # 基础查询条件
            conditions = {}
            
            # 根据指令模式搜索
            if instruction_pattern:
                executions = self.test_execution_dao.search_executions_by_request(
                    instruction_pattern, limit=1000
                )
            else:
                executions = self.test_execution_dao.find_all()
            
            # 应用其他筛选条件
            filtered_executions = []
            
            for execution in executions:
                # 平台筛选
                if platform:
                    plan_id = execution.get('plan_id')
                    if plan_id:
                        plan = self.test_plan_dao.get_plan_by_plan_id(plan_id)
                        if not plan or plan.get('platform') != platform:
                            continue
                
                # 日期筛选
                if start_date or end_date:
                    created_at = execution.get('created_at', '')
                    if created_at:
                        exec_date = created_at.split(' ')[0] if ' ' in created_at else created_at.split('T')[0]
                        
                        if start_date and exec_date < start_date:
                            continue
                        if end_date and exec_date > end_date:
                            continue
                
                filtered_executions.append(execution)
            
            # 统计分析
            total_count = len(filtered_executions)
            success_count = len([e for e in filtered_executions if e.get('execution_status') == 'success'])
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            
            # 平均执行时间
            durations = [e.get('total_duration', 0) or 0 for e in filtered_executions if e.get('total_duration')]
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            # 状态分布
            status_distribution = Counter(e.get('execution_status') for e in filtered_executions)
            
            return {
                'total_count': total_count,
                'success_count': success_count,
                'success_rate': round(success_rate, 2),
                'avg_duration': round(avg_duration, 2),
                'status_distribution': dict(status_distribution),
                'matching_executions': [
                    {
                        'execution_id': e.get('execution_id'),
                        'original_request': e.get('original_request'),
                        'execution_status': e.get('execution_status'),
                        'total_duration': e.get('total_duration'),
                        'created_at': e.get('created_at')
                    }
                    for e in filtered_executions[:50]  # 限制返回数量
                ]
            }
            
        except Exception as e:
            raise Exception(f"获取自定义统计失败: {str(e)}")
    
    def _get_recent_statistics(self, days: int) -> Dict[str, Any]:
        """获取最近N天的统计数据
        
        Args:
            days: 天数
            
        Returns:
            最近统计数据
        """
        try:
            # 获取所有执行记录
            all_executions = self.test_execution_dao.find_all()
            
            # 筛选最近N天的记录
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_executions = []
            
            for execution in all_executions:
                created_at = execution.get('created_at', '')
                if not created_at:
                    continue
                
                try:
                    # 解析创建时间
                    if 'T' in created_at:
                        exec_datetime = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    else:
                        exec_datetime = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                    
                    if exec_datetime >= cutoff_date:
                        recent_executions.append(execution)
                except ValueError:
                    continue
            
            # 计算成功率
            total_recent = len(recent_executions)
            success_recent = len([e for e in recent_executions if e.get('execution_status') == 'success'])
            success_rate = (success_recent / total_recent * 100) if total_recent > 0 else 0
            
            return {
                'recent_executions': total_recent,
                'success_rate': round(success_rate, 2)
            }
            
        except Exception as e:
            return {
                'recent_executions': 0,
                'success_rate': 0
            }
    
    def _translate_status(self, status: str) -> str:
        """翻译执行状态为中文
        
        Args:
            status: 英文状态
            
        Returns:
            中文状态
        """
        translations = {
            'success': '成功',
            'failed': '失败',
            'running': '运行中',
            'timeout': '超时',
            'cancelled': '已取消'
        }
        return translations.get(status, status)