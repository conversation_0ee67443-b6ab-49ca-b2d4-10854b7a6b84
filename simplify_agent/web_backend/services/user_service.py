#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户视角业务服务
"""

import os
import sys
from typing import List, Dict, Any, Optional, Tuple

# 添加项目根目录到路径以导入DAO
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from simplify_agent.database.dao import (
    TestPlanDAO, TestExecutionDAO, ToolExecutionDAO, EvaluationDAO
)


class UserService:
    """用户视角业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.test_plan_dao = TestPlanDAO()
        self.test_execution_dao = TestExecutionDAO()
        self.tool_execution_dao = ToolExecutionDAO()
        self.evaluation_dao = EvaluationDAO()
    
    def get_executions_list(self, page: int = 1, page_size: int = 20, 
                           search: Optional[str] = None,
                           status: Optional[str] = None,
                           platform: Optional[str] = None,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """获取测试执行列表
        
        Args:
            page: 页码
            page_size: 每页大小
            search: 搜索关键词
            status: 执行状态筛选
            platform: 平台筛选
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            (执行记录列表, 总数) 元组
        """
        try:
            # 构建查询条件
            conditions = {}
            
            if status and status != 'all':
                conditions['execution_status'] = status
            
            # 如果有搜索条件，使用搜索方法
            if search:
                executions = self.test_execution_dao.search_executions_by_request(search, limit=1000)
                
                # 应用其他筛选条件
                if status and status != 'all':
                    executions = [e for e in executions if e.get('execution_status') == status]
                
                # 日期筛选
                if start_date or end_date:
                    executions = self._filter_by_date(executions, start_date, end_date)
                
                total = len(executions)
            else:
                # 不使用搜索，直接查询
                all_executions = self.test_execution_dao.find_all(conditions=conditions if conditions else None)
                
                # 日期筛选
                if start_date or end_date:
                    all_executions = self._filter_by_date(all_executions, start_date, end_date)
                
                executions = all_executions
                total = len(executions)
            
            # 排序：按创建时间倒序
            executions = sorted(executions, key=lambda x: x.get('created_at', ''), reverse=True)
            
            # 分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_executions = executions[start_idx:end_idx]
            
            # 获取关联信息并格式化数据
            formatted_executions = []
            for execution in page_executions:
                formatted_execution = self._format_execution_for_list(execution)
                formatted_executions.append(formatted_execution)
            
            return formatted_executions, total
            
        except Exception as e:
            raise Exception(f"获取执行列表失败: {str(e)}")
    
    def get_plans_list(self, page: int = 1, page_size: int = 20, 
                      search: Optional[str] = None,
                      platform: Optional[str] = None,
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """获取测试计划列表
        
        Args:
            page: 页码
            page_size: 每页大小
            search: 搜索关键词
            platform: 平台筛选
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            (测试计划列表, 总数) 元组
        """
        try:
            # 构建查询条件
            conditions = {}
            
            if platform and platform != 'all':
                conditions['platform'] = platform
            
            # 如果有搜索条件，使用搜索方法
            if search:
                # 搜索原始请求或计划摘要
                all_plans = self.test_plan_dao.find_all()
                plans = [p for p in all_plans 
                        if search.lower() in p.get('original_request', '').lower() 
                        or search.lower() in p.get('plan_summary', '').lower()]
                
                # 应用其他筛选条件
                if platform and platform != 'all':
                    plans = [p for p in plans if p.get('platform') == platform]
                
                # 日期筛选
                if start_date or end_date:
                    plans = self._filter_by_date(plans, start_date, end_date, date_field='created_at')
                
                total = len(plans)
            else:
                # 不使用搜索，直接查询
                all_plans = self.test_plan_dao.find_all(conditions=conditions if conditions else None)
                
                # 日期筛选
                if start_date or end_date:
                    all_plans = self._filter_by_date(all_plans, start_date, end_date, date_field='created_at')
                
                plans = all_plans
                total = len(plans)
            
            # 排序：按创建时间倒序
            plans = sorted(plans, key=lambda x: x.get('created_at', ''), reverse=True)
            
            # 分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_plans = plans[start_idx:end_idx]
            
            # 获取关联信息并格式化数据
            formatted_plans = []
            for plan in page_plans:
                formatted_plan = self._format_plan_for_list(plan)
                formatted_plans.append(formatted_plan)
            
            return formatted_plans, total
            
        except Exception as e:
            raise Exception(f"获取测试计划列表失败: {str(e)}")
    
    def get_plan_detail(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """获取测试计划详情
        
        Args:
            plan_id: 计划ID
            
        Returns:
            测试计划详情，包含关联的执行记录
        """
        try:
            # 获取计划基本信息
            plan = self.test_plan_dao.get_plan_by_plan_id(plan_id)
            if not plan:
                return None
            
            # 获取关联的执行记录
            executions = self.test_execution_dao.get_executions_by_plan_id(plan_id)
            
            # 格式化执行记录
            formatted_executions = []
            for execution in executions:
                formatted_execution = self._format_execution_for_list(execution)
                formatted_executions.append(formatted_execution)
            
            # 格式化计划详情
            formatted_plan = self._format_plan_detail(plan)
            formatted_plan['executions'] = formatted_executions
            formatted_plan['execution_count'] = len(executions)
            
            return formatted_plan
            
        except Exception as e:
            raise Exception(f"获取测试计划详情失败: {str(e)}")
    
    def get_execution_detail(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取测试执行详情
        
        Args:
            execution_id: 执行ID
            
        Returns:
            详细的执行信息字典
        """
        try:
            # 获取基础执行信息
            execution = self.test_execution_dao.get_execution_by_execution_id(execution_id)
            if not execution:
                return None
            
            # 获取关联的测试计划
            plan = None
            if execution.get('plan_id'):
                plan = self.test_plan_dao.get_plan_by_plan_id(execution['plan_id'])
            
            # 获取工具执行记录
            tools = self.tool_execution_dao.get_tools_by_execution_id(execution_id)
            
            # 获取评价信息
            evaluations = self.evaluation_dao.get_complete_evaluation(execution_id)
            
            # 获取执行总结
            execution_summary = self.tool_execution_dao.get_execution_summary(execution_id)
            
            # 格式化返回数据
            return {
                'execution': execution,
                'plan': plan,
                'tools': self._format_tools_for_detail(tools),
                'evaluations': evaluations,
                'summary': execution_summary
            }
            
        except Exception as e:
            raise Exception(f"获取执行详情失败: {str(e)}")
    
    def delete_execution(self, execution_id: str) -> bool:
        """删除测试执行记录及相关数据
        
        Args:
            execution_id: 执行ID
            
        Returns:
            是否删除成功
        """
        try:
            # 删除评价记录
            self.evaluation_dao.delete_evaluations_by_execution(execution_id)
            
            # 删除工具执行记录
            self.tool_execution_dao.delete_tools_by_execution_id(execution_id)
            
            # 删除测试执行记录
            success = self.test_execution_dao.delete_execution(execution_id)
            
            return success
            
        except Exception as e:
            raise Exception(f"删除执行记录失败: {str(e)}")
    
    def _format_execution_for_list(self, execution: Dict[str, Any]) -> Dict[str, Any]:
        """格式化执行记录用于列表显示
        
        Args:
            execution: 原始执行记录
            
        Returns:
            格式化后的执行记录
        """
        # 获取综合评价来判断成功状态
        comprehensive_eval = self.evaluation_dao.get_comprehensive_evaluation_by_execution(
            execution['execution_id']
        )
        
        overall_success = None
        if comprehensive_eval:
            overall_success = comprehensive_eval.get('final_success_status')
        
        return {
            'execution_id': execution['execution_id'],
            'plan_id': execution.get('plan_id'),
            'original_request': execution['original_request'],
            'execution_status': execution['execution_status'],
            'total_duration': execution.get('total_duration', 0),
            'total_rounds': execution.get('total_rounds', 0),
            'created_at': execution.get('created_at'),
            'start_time': execution.get('start_time'),
            'end_time': execution.get('end_time'),
            'overall_success': overall_success,
            'is_success': overall_success == 'success' if overall_success else execution['execution_status'] == 'success'
        }
    
    def _format_plan_for_list(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """格式化测试计划用于列表显示
        
        Args:
            plan: 原始测试计划记录
            
        Returns:
            格式化后的测试计划
        """
        # 获取关联的执行记录数量
        execution_count = len(self.test_execution_dao.get_executions_by_plan_id(plan['plan_id']))
        
        # 解析结构化计划获取步骤数
        import json
        try:
            structured_plan = json.loads(plan.get('structured_plan', '{}'))
            steps = structured_plan.get('steps', [])
            step_count = len(steps) if isinstance(steps, list) else plan.get('total_steps', 0)
        except:
            step_count = plan.get('total_steps', 0)
        
        return {
            'plan_id': plan['plan_id'],
            'original_request': plan['original_request'],
            'platform': plan['platform'],
            'total_steps': plan.get('total_steps', 0),
            'step_count': step_count,
            'plan_summary': plan.get('plan_summary', ''),
            'created_at': plan.get('created_at'),
            'updated_at': plan.get('updated_at'),
            'execution_count': execution_count
        }
    
    def _format_plan_detail(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """格式化测试计划用于详情显示
        
        Args:
            plan: 原始测试计划记录
            
        Returns:
            格式化后的测试计划详情
        """
        # 解析结构化计划
        import json
        structured_plan = {}
        try:
            structured_plan_raw = plan.get('structured_plan', '{}')
            if isinstance(structured_plan_raw, dict):
                # 如果已经是字典，直接使用
                structured_plan = structured_plan_raw
            elif isinstance(structured_plan_raw, str):
                # 如果是字符串，尝试解析JSON
                structured_plan = json.loads(structured_plan_raw)
            else:
                structured_plan = {}
        except:
            structured_plan = {}
        
        # 解析生成元数据
        generation_metadata = {}
        try:
            if plan.get('generation_metadata'):
                generation_metadata = json.loads(plan['generation_metadata'])
        except:
            pass
        
        return {
            'plan_id': plan['plan_id'],
            'original_request': plan['original_request'],
            'platform': plan['platform'],
            'total_steps': plan.get('total_steps', 0),
            'plan_summary': plan.get('plan_summary', ''),
            'structured_plan': structured_plan,
            'generation_metadata': generation_metadata,
            'agent_instructions': plan.get('agent_instructions', ''),
            'created_at': plan.get('created_at'),
            'updated_at': plan.get('updated_at')
        }
    
    def _format_tools_for_detail(self, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """格式化工具执行记录用于详情显示
        
        Args:
            tools: 原始工具记录列表
            
        Returns:
            按轮次分组的工具记录
        """
        tools_by_round = {}
        
        for tool in tools:
            round_num = tool.get('round_number', 1)
            if round_num not in tools_by_round:
                tools_by_round[round_num] = []
            
            # 格式化工具记录
            formatted_tool = {
                'id': tool.get('id'),
                'tool_name': tool.get('tool_name'),
                'tool_parameters': tool.get('tool_parameters'),
                'execution_time': tool.get('execution_time'),
                'tool_status': tool.get('tool_status'),
                'tool_result': tool.get('tool_result'),
                'result_summary': tool.get('result_summary'),
                'image_url': tool.get('image_url'),
                'local_path': tool.get('local_path'),
                'error_message': tool.get('error_message'),
                'created_at': tool.get('created_at')
            }
            
            tools_by_round[round_num].append(formatted_tool)
        
        # 按轮次排序
        sorted_rounds = {}
        for round_num in sorted(tools_by_round.keys()):
            # 按创建时间排序每轮内的工具
            sorted_rounds[round_num] = sorted(
                tools_by_round[round_num], 
                key=lambda x: x.get('created_at', '')
            )
        
        return {
            'by_rounds': sorted_rounds,
            'total_tools': len(tools)
        }
    
    def _filter_by_date(self, items: List[Dict[str, Any]], 
                       start_date: Optional[str], 
                       end_date: Optional[str]) -> List[Dict[str, Any]]:
        """按日期筛选记录
        
        Args:
            items: 记录列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            筛选后的记录列表
        """
        if not start_date and not end_date:
            return items
        
        filtered_items = []
        
        for item in items:
            created_at = item.get('created_at', '')
            if not created_at:
                continue
            
            # 提取日期部分 (YYYY-MM-DD)
            item_date = created_at.split(' ')[0] if ' ' in created_at else created_at.split('T')[0]
            
            # 日期比较
            if start_date and item_date < start_date:
                continue
            if end_date and item_date > end_date:
                continue
            
            filtered_items.append(item)
        
        return filtered_items