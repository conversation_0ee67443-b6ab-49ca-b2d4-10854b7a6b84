#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
认证中间件
"""

import functools
from datetime import datetime, timedelta
from typing import Dict, Any
from flask import request, session, current_app

from ..utils.response import unauthorized_response


def auth_required(f):
    """通用认证装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        # 这里可以添加通用的认证逻辑
        # 目前先通过，后续可以添加API密钥等认证
        return f(*args, **kwargs)
    
    return decorated_function


def dev_auth_required(f):
    """开发者认证装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查会话中的开发者认证状态
        if not session.get('dev_authenticated'):
            return unauthorized_response("需要开发者权限")
        
        # 检查会话是否超时
        login_time = session.get('dev_login_time')
        if not login_time:
            return unauthorized_response("会话无效")
        
        login_datetime = datetime.fromisoformat(login_time)
        timeout = current_app.config.get('SESSION_TIMEOUT', timedelta(hours=8))
        
        if datetime.now() - login_datetime > timeout:
            session.clear()
            return unauthorized_response("会话已过期，请重新登录")
        
        return f(*args, **kwargs)
    
    return decorated_function


def authenticate_dev_user(password: str) -> Dict[str, Any]:
    """验证开发者密码
    
    Args:
        password: 输入的密码
        
    Returns:
        认证结果字典
    """
    correct_password = current_app.config.get('DEV_PASSWORD', 'simplify_dev_2025')
    
    if password == correct_password:
        # 设置会话
        session['dev_authenticated'] = True
        session['dev_login_time'] = datetime.now().isoformat()
        session.permanent = True
        
        return {
            'success': True,
            'message': '认证成功',
            'expires_in': int(current_app.config.get('SESSION_TIMEOUT', timedelta(hours=8)).total_seconds())
        }
    else:
        return {
            'success': False,
            'message': '密码错误'
        }


def logout_dev_user() -> Dict[str, Any]:
    """开发者登出
    
    Returns:
        登出结果字典
    """
    session.clear()
    return {
        'success': True,
        'message': '已成功登出'
    }