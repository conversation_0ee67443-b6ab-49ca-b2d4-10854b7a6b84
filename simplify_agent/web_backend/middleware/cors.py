#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORS中间件
"""

from flask import Flask
from flask_cors import CORS


def setup_cors(app: Flask) -> None:
    """设置CORS
    
    Args:
        app: Flask应用实例
    """
    CORS(app, 
         origins=app.config.get('CORS_ORIGINS', '*'),
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
         allow_headers=['Content-Type', 'Authorization', 'X-Requested-With'],
         supports_credentials=True)