#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户视角API路由
"""

from flask import Blueprint, request
from ..services.user_service import UserService
from ..utils.response import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..utils.validators import validate_pagination, validate_date_range, validate_execution_status
from ..middleware.auth import auth_required

# 创建蓝图
user_bp = Blueprint('user', __name__)

# 创建服务实例
user_service = UserService()


@user_bp.route('/executions', methods=['GET'])
@auth_required
def get_executions():
    """获取测试执行记录列表
    
    查询参数:
    - page: 页码 (默认1)
    - page_size: 每页大小 (默认20, 最大100)
    - search: 搜索关键词
    - status: 执行状态筛选
    - platform: 平台筛选
    - start_date: 开始日期 (YYYY-MM-DD)
    - end_date: 结束日期 (YYYY-MM-DD)
    """
    try:
        # 验证分页参数
        page, page_size = validate_pagination()
        
        # 获取查询参数
        search = request.args.get('search', '').strip()
        status = request.args.get('status', 'all')
        platform = request.args.get('platform', 'all')
        start_date, end_date = validate_date_range()
        
        # 验证状态参数
        if status != 'all' and not validate_execution_status(status):
            return validation_error_response({
                'status': f'无效的执行状态: {status}'
            })
        
        # 处理搜索参数
        search = search if search else None
        status = status if status != 'all' else None
        platform = platform if platform != 'all' else None
        
        # 调用服务获取数据
        executions, total = user_service.get_executions_list(
            page=page,
            page_size=page_size,
            search=search,
            status=status,
            platform=platform,
            start_date=start_date,
            end_date=end_date
        )
        
        return paginated_response(
            items=executions,
            total=total,
            page=page,
            page_size=page_size,
            message="查询执行记录成功"
        )
        
    except Exception as e:
        return error_response(f"获取执行记录失败: {str(e)}")


@user_bp.route('/executions/<execution_id>', methods=['GET'])
@auth_required
def get_execution_detail(execution_id):
    """获取测试执行详情
    
    路径参数:
    - execution_id: 执行ID
    """
    try:
        if not execution_id:
            return validation_error_response({
                'execution_id': '执行ID不能为空'
            })
        
        # 调用服务获取详情
        detail = user_service.get_execution_detail(execution_id)
        
        if not detail:
            return not_found_response("测试执行记录")
        
        return success_response(
            data=detail,
            message="获取执行详情成功"
        )
        
    except Exception as e:
        return error_response(f"获取执行详情失败: {str(e)}")


@user_bp.route('/executions/<execution_id>', methods=['DELETE'])
@auth_required
def delete_execution(execution_id):
    """删除测试执行记录
    
    路径参数:
    - execution_id: 执行ID
    """
    try:
        if not execution_id:
            return validation_error_response({
                'execution_id': '执行ID不能为空'
            })
        
        # 调用服务删除记录
        success = user_service.delete_execution(execution_id)
        
        if success:
            return success_response(
                message="删除执行记录成功"
            )
        else:
            return not_found_response("测试执行记录")
        
    except Exception as e:
        return error_response(f"删除执行记录失败: {str(e)}")


@user_bp.route('/plans', methods=['GET'])
@auth_required
def get_plans():
    """获取测试计划列表
    
    查询参数:
    - page: 页码 (默认1)
    - page_size: 每页大小 (默认20, 最大100)
    - search: 搜索关键词
    - platform: 平台筛选
    - start_date: 开始日期 (YYYY-MM-DD)
    - end_date: 结束日期 (YYYY-MM-DD)
    """
    try:
        # 验证分页参数
        page, page_size = validate_pagination()
        
        # 获取查询参数
        search = request.args.get('search', '').strip()
        platform = request.args.get('platform', 'all')
        start_date, end_date = validate_date_range()
        
        # 处理搜索参数
        search = search if search else None
        platform = platform if platform != 'all' else None
        
        # 调用服务获取数据
        plans, total = user_service.get_plans_list(
            page=page,
            page_size=page_size,
            search=search,
            platform=platform,
            start_date=start_date,
            end_date=end_date
        )
        
        return paginated_response(
            items=plans,
            total=total,
            page=page,
            page_size=page_size,
            message="查询测试计划成功"
        )
        
    except Exception as e:
        return error_response(f"获取测试计划失败: {str(e)}")


@user_bp.route('/plans/<plan_id>', methods=['GET'])
@auth_required
def get_plan_detail(plan_id):
    """获取测试计划详情
    
    路径参数:
    - plan_id: 计划ID
    """
    try:
        if not plan_id:
            return validation_error_response({
                'plan_id': '计划ID不能为空'
            })
        
        # 获取计划详情和关联的执行记录
        detail = user_service.get_plan_detail(plan_id)
        
        if not detail:
            return not_found_response("测试计划")
        
        return success_response(
            data=detail,
            message="获取测试计划详情成功"
        )
        
    except Exception as e:
        return error_response(f"获取测试计划详情失败: {str(e)}")


@user_bp.route('/executions/<execution_id>/plan', methods=['GET'])
@auth_required
def get_execution_plan(execution_id):
    """获取测试执行的关联计划
    
    路径参数:
    - execution_id: 执行ID
    """
    try:
        if not execution_id:
            return validation_error_response({
                'execution_id': '执行ID不能为空'
            })
        
        # 获取执行详情
        detail = user_service.get_execution_detail(execution_id)
        
        if not detail:
            return not_found_response("测试执行记录")
        
        plan = detail.get('plan')
        if not plan:
            return not_found_response("关联的测试计划")
        
        return success_response(
            data=plan,
            message="获取测试计划成功"
        )
        
    except Exception as e:
        return error_response(f"获取测试计划失败: {str(e)}")


@user_bp.route('/executions/<execution_id>/tools', methods=['GET'])
@auth_required
def get_execution_tools(execution_id):
    """获取测试执行的工具调用记录
    
    路径参数:
    - execution_id: 执行ID
    """
    try:
        if not execution_id:
            return validation_error_response({
                'execution_id': '执行ID不能为空'
            })
        
        # 获取执行详情
        detail = user_service.get_execution_detail(execution_id)
        
        if not detail:
            return not_found_response("测试执行记录")
        
        tools = detail.get('tools', {})
        summary = detail.get('summary', {})
        
        return success_response(
            data={
                'tools': tools,
                'summary': summary
            },
            message="获取工具执行记录成功"
        )
        
    except Exception as e:
        return error_response(f"获取工具执行记录失败: {str(e)}")


@user_bp.route('/executions/<execution_id>/evaluations', methods=['GET'])
@auth_required
def get_execution_evaluations(execution_id):
    """获取测试执行的评价信息
    
    路径参数:
    - execution_id: 执行ID
    """
    try:
        if not execution_id:
            return validation_error_response({
                'execution_id': '执行ID不能为空'
            })
        
        # 获取执行详情
        detail = user_service.get_execution_detail(execution_id)
        
        if not detail:
            return not_found_response("测试执行记录")
        
        evaluations = detail.get('evaluations', {})
        
        return success_response(
            data=evaluations,
            message="获取评价信息成功"
        )
        
    except Exception as e:
        return error_response(f"获取评价信息失败: {str(e)}")


@user_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return success_response(
        data={
            'status': 'healthy',
            'service': 'user_api',
            'version': '1.0.0'
        },
        message="用户API服务正常"
    )