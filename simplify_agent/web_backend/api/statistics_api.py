#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统计分析API路由
"""

from flask import Blueprint, request
from ..services.statistics_service import StatisticsService
from ..utils.response import success_response, error_response, validation_error_response
from ..utils.validators import validate_date_range, safe_int
from ..middleware.auth import auth_required

# 创建蓝图
statistics_bp = Blueprint('statistics', __name__)

# 创建服务实例
statistics_service = StatisticsService()


@statistics_bp.route('/overview', methods=['GET'])
@auth_required
def get_overview():
    """获取统计概览信息
    
    返回整体统计指标，包括总数、成功率、平均耗时等
    """
    try:
        overview_data = statistics_service.get_overview_statistics()
        
        return success_response(
            data=overview_data,
            message="获取统计概览成功"
        )
        
    except Exception as e:
        return error_response(f"获取统计概览失败: {str(e)}")


@statistics_bp.route('/success-rate', methods=['GET'])
@auth_required
def get_success_rate():
    """获取成功率分析数据
    
    返回详细的成功率分析，包括总体成功率、平台成功率、饼图数据等
    """
    try:
        success_rate_data = statistics_service.get_success_rate_analysis()
        
        return success_response(
            data=success_rate_data,
            message="获取成功率分析成功"
        )
        
    except Exception as e:
        return error_response(f"获取成功率分析失败: {str(e)}")


@statistics_bp.route('/trends', methods=['GET'])
@auth_required
def get_trends():
    """获取趋势分析数据
    
    查询参数:
    - days: 分析天数 (默认30天)
    """
    try:
        days = safe_int(request.args.get('days', 30))
        
        # 验证天数范围
        if days < 1:
            days = 30
        elif days > 365:
            days = 365
        
        trends_data = statistics_service.get_trend_analysis(days)
        
        return success_response(
            data=trends_data,
            message=f"获取{days}天趋势分析成功"
        )
        
    except Exception as e:
        return error_response(f"获取趋势分析失败: {str(e)}")


@statistics_bp.route('/platforms', methods=['GET'])
@auth_required  
def get_platform_distribution():
    """获取平台分布统计
    
    返回各平台的测试分布情况
    """
    try:
        platform_data = statistics_service.get_platform_distribution()
        
        return success_response(
            data=platform_data,
            message="获取平台分布统计成功"
        )
        
    except Exception as e:
        return error_response(f"获取平台分布统计失败: {str(e)}")


@statistics_bp.route('/tools', methods=['GET'])
@auth_required
def get_tool_usage():
    """获取工具使用统计
    
    返回工具使用频率、性能统计等数据
    """
    try:
        tool_data = statistics_service.get_tool_usage_analysis()
        
        return success_response(
            data=tool_data,
            message="获取工具使用统计成功"
        )
        
    except Exception as e:
        return error_response(f"获取工具使用统计失败: {str(e)}")


@statistics_bp.route('/custom', methods=['POST'])
@auth_required
def get_custom_statistics():
    """获取自定义统计数据
    
    请求体参数:
    - instruction_pattern: 指令名称模式 (可选)
    - start_date: 开始日期 YYYY-MM-DD (可选)  
    - end_date: 结束日期 YYYY-MM-DD (可选)
    - platform: 平台筛选 (可选)
    """
    try:
        data = request.get_json() or {}
        
        instruction_pattern = data.get('instruction_pattern', '').strip()
        platform = data.get('platform', '').strip()
        
        # 从请求体或查询参数获取日期
        start_date = data.get('start_date') or request.args.get('start_date')
        end_date = data.get('end_date') or request.args.get('end_date')
        
        # 验证日期格式
        if start_date:
            try:
                from datetime import datetime
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return validation_error_response({
                    'start_date': '日期格式错误，应为 YYYY-MM-DD'
                })
        
        if end_date:
            try:
                from datetime import datetime
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return validation_error_response({
                    'end_date': '日期格式错误，应为 YYYY-MM-DD'
                })
        
        # 处理空字符串
        instruction_pattern = instruction_pattern if instruction_pattern else None
        platform = platform if platform else None
        
        # 调用服务获取自定义统计
        custom_data = statistics_service.get_custom_statistics(
            instruction_pattern=instruction_pattern,
            start_date=start_date,
            end_date=end_date,
            platform=platform
        )
        
        return success_response(
            data=custom_data,
            message="获取自定义统计成功"
        )
        
    except Exception as e:
        return error_response(f"获取自定义统计失败: {str(e)}")


@statistics_bp.route('/export', methods=['GET'])
@auth_required
def export_statistics():
    """导出统计报告
    
    查询参数:
    - format: 导出格式 (默认csv)
    - type: 报告类型 (overview/trends/custom)
    """
    try:
        export_format = request.args.get('format', 'csv').lower()
        report_type = request.args.get('type', 'overview').lower()
        
        if export_format not in ['csv', 'json']:
            return validation_error_response({
                'format': '不支持的导出格式，支持: csv, json'
            })
        
        if report_type not in ['overview', 'trends', 'custom']:
            return validation_error_response({
                'type': '不支持的报告类型，支持: overview, trends, custom'
            })
        
        # 根据类型获取对应数据
        if report_type == 'overview':
            data = statistics_service.get_overview_statistics()
        elif report_type == 'trends':
            days = safe_int(request.args.get('days', 30))
            data = statistics_service.get_trend_analysis(days)
        else:  # custom
            # 自定义报告需要额外参数
            data = statistics_service.get_custom_statistics()
        
        # 格式化导出数据
        if export_format == 'csv':
            # 这里应该实现CSV格式转换
            # 为了简化，先返回JSON格式提示
            return success_response(
                data={
                    'export_type': 'csv',
                    'download_url': f'/api/statistics/download/{report_type}.csv',
                    'data': data
                },
                message="CSV导出功能开发中，暂时返回JSON数据"
            )
        else:
            return success_response(
                data=data,
                message=f"导出{report_type}统计报告成功"
            )
        
    except Exception as e:
        return error_response(f"导出统计报告失败: {str(e)}")


@statistics_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return success_response(
        data={
            'status': 'healthy',
            'service': 'statistics_api', 
            'version': '1.0.0'
        },
        message="统计API服务正常"
    )