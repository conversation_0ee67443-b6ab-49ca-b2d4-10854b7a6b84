#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
开发者视角API路由
"""

from flask import Blueprint, request, session
from ..services.dev_service import DevService
from ..utils.response import (
    success_response, error_response, paginated_response,
    not_found_response, validation_error_response, unauthorized_response
)
from ..utils.validators import validate_pagination, safe_int
from ..middleware.auth import dev_auth_required, authenticate_dev_user, logout_dev_user

# 创建蓝图
dev_bp = Blueprint('dev', __name__)

# 创建服务实例
dev_service = DevService()


@dev_bp.route('/auth', methods=['POST'])
def authenticate():
    """开发者身份验证
    
    请求体参数:
    - password: 开发者密码
    """
    try:
        data = request.get_json()
        if not data or 'password' not in data:
            return validation_error_response({
                'password': '密码不能为空'
            })
        
        password = data['password']
        auth_result = authenticate_dev_user(password)
        
        if auth_result['success']:
            return success_response(
                data={
                    'authenticated': True,
                    'expires_in': auth_result['expires_in']
                },
                message=auth_result['message']
            )
        else:
            return unauthorized_response(auth_result['message'])
        
    except Exception as e:
        return error_response(f"身份验证失败: {str(e)}")


@dev_bp.route('/session', methods=['GET'])
def get_session_info():
    """获取当前会话信息"""
    try:
        authenticated = session.get('dev_authenticated', False)
        login_time = session.get('dev_login_time')
        
        return success_response(
            data={
                'authenticated': authenticated,
                'login_time': login_time,
                'session_id': bool(session.get('_permanent'))
            },
            message="获取会话信息成功"
        )
        
    except Exception as e:
        return error_response(f"获取会话信息失败: {str(e)}")


@dev_bp.route('/logout', methods=['POST'])
@dev_auth_required
def logout():
    """开发者登出"""
    try:
        logout_result = logout_dev_user()
        
        return success_response(
            message=logout_result['message']
        )
        
    except Exception as e:
        return error_response(f"登出失败: {str(e)}")


@dev_bp.route('/database/info', methods=['GET'])
@dev_auth_required
def get_database_info():
    """获取数据库概览信息"""
    try:
        db_info = dev_service.get_database_info()
        
        return success_response(
            data=db_info,
            message="获取数据库信息成功"
        )
        
    except Exception as e:
        return error_response(f"获取数据库信息失败: {str(e)}")


@dev_bp.route('/tables', methods=['GET'])
@dev_auth_required
def get_tables():
    """获取所有表信息"""
    try:
        tables = dev_service.get_table_list()
        
        return success_response(
            data={
                'tables': tables,
                'table_count': len(tables)
            },
            message="获取表信息成功"
        )
        
    except Exception as e:
        return error_response(f"获取表信息失败: {str(e)}")


@dev_bp.route('/table/<table_name>/data', methods=['GET'])
@dev_auth_required
def get_table_data(table_name):
    """获取表数据
    
    查询参数:
    - page: 页码 (默认1)
    - page_size: 每页大小 (默认20)
    - search: 搜索关键词
    - order_by: 排序字段
    """
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        # 获取查询参数
        page, page_size = validate_pagination(default_page_size=20, max_page_size=200)
        search = request.args.get('search', '').strip()
        order_by = request.args.get('order_by', '').strip()
        
        # 处理空字符串
        search = search if search else None
        order_by = order_by if order_by else None
        
        # 获取表数据
        data, total = dev_service.get_table_data(
            table_name=table_name,
            page=page,
            page_size=page_size,
            search=search,
            order_by=order_by
        )
        
        return paginated_response(
            items=data,
            total=total,
            page=page,
            page_size=page_size,
            message=f"获取表 {table_name} 数据成功"
        )
        
    except Exception as e:
        return error_response(f"获取表数据失败: {str(e)}")


@dev_bp.route('/table/<table_name>/data', methods=['POST'])
@dev_auth_required
def create_record(table_name):
    """创建新记录
    
    请求体参数:
    - data: 记录数据 (字典格式)
    """
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        request_data = request.get_json()
        if not request_data or 'data' not in request_data:
            return validation_error_response({
                'data': '记录数据不能为空'
            })
        
        record_data = request_data['data']
        if not isinstance(record_data, dict):
            return validation_error_response({
                'data': '记录数据必须为字典格式'
            })
        
        # 创建记录
        record_id = dev_service.create_record(table_name, record_data)
        
        return success_response(
            data={
                'record_id': record_id,
                'table_name': table_name
            },
            message="创建记录成功"
        )
        
    except Exception as e:
        return error_response(f"创建记录失败: {str(e)}")


@dev_bp.route('/table/<table_name>/data/<int:record_id>', methods=['PUT'])
@dev_auth_required  
def update_record(table_name, record_id):
    """更新记录
    
    请求体参数:
    - data: 更新数据 (字典格式)
    """
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        request_data = request.get_json()
        if not request_data or 'data' not in request_data:
            return validation_error_response({
                'data': '更新数据不能为空'
            })
        
        update_data = request_data['data']
        if not isinstance(update_data, dict):
            return validation_error_response({
                'data': '更新数据必须为字典格式'
            })
        
        # 更新记录
        success = dev_service.update_record(table_name, record_id, update_data)
        
        if success:
            return success_response(
                data={
                    'record_id': record_id,
                    'table_name': table_name
                },
                message="更新记录成功"
            )
        else:
            return not_found_response("记录")
        
    except Exception as e:
        return error_response(f"更新记录失败: {str(e)}")


@dev_bp.route('/table/<table_name>/data/<int:record_id>', methods=['DELETE'])
@dev_auth_required
def delete_record(table_name, record_id):
    """删除记录"""
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        # 删除记录
        success = dev_service.delete_record(table_name, record_id)
        
        if success:
            return success_response(
                message="删除记录成功"
            )
        else:
            return not_found_response("记录")
        
    except Exception as e:
        return error_response(f"删除记录失败: {str(e)}")


@dev_bp.route('/table/<table_name>/schema', methods=['GET'])
@dev_auth_required
def get_table_schema(table_name):
    """获取表结构"""
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        schema = dev_service.get_table_schema(table_name)
        
        return success_response(
            data=schema,
            message=f"获取表 {table_name} 结构成功"
        )
        
    except Exception as e:
        return error_response(f"获取表结构失败: {str(e)}")


@dev_bp.route('/table/<table_name>/column', methods=['POST'])
@dev_auth_required
def add_column(table_name):
    """添加表字段
    
    请求体参数:
    - column_name: 字段名
    - column_type: 字段类型
    - not_null: 是否非空 (可选，默认false)
    - default_value: 默认值 (可选)
    """
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        data = request.get_json()
        if not data:
            return validation_error_response({
                'request': '请求体不能为空'
            })
        
        column_name = data.get('column_name', '').strip()
        column_type = data.get('column_type', '').strip()
        not_null = bool(data.get('not_null', False))
        default_value = data.get('default_value')
        
        if not column_name:
            return validation_error_response({
                'column_name': '字段名不能为空'
            })
        
        if not column_type:
            return validation_error_response({
                'column_type': '字段类型不能为空'
            })
        
        # 添加字段
        success = dev_service.add_column(
            table_name=table_name,
            column_name=column_name,
            column_type=column_type,
            not_null=not_null,
            default_value=default_value
        )
        
        if success:
            return success_response(
                data={
                    'table_name': table_name,
                    'column_name': column_name,
                    'column_type': column_type
                },
                message="添加字段成功"
            )
        else:
            return error_response("添加字段失败")
        
    except Exception as e:
        return error_response(f"添加字段失败: {str(e)}")


@dev_bp.route('/table/<table_name>/column/<column_name>', methods=['DELETE'])
@dev_auth_required
def drop_column(table_name, column_name):
    """删除表字段
    
    路径参数:
    - table_name: 表名
    - column_name: 字段名
    """
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        if not column_name:
            return validation_error_response({
                'column_name': '字段名不能为空'
            })
        
        # 删除字段
        success = dev_service.drop_column(table_name, column_name)
        
        if success:
            return success_response(
                data={
                    'table_name': table_name,
                    'column_name': column_name
                },
                message="删除字段成功"
            )
        else:
            return error_response("删除字段失败")
        
    except Exception as e:
        return error_response(f"删除字段失败: {str(e)}")


@dev_bp.route('/table/<table_name>/column/<column_name>', methods=['PUT'])
@dev_auth_required
def edit_column(table_name, column_name):
    """编辑表字段
    
    路径参数:
    - table_name: 表名
    - column_name: 原字段名
    
    请求体参数:
    - new_column_name: 新字段名
    - new_column_type: 新字段类型
    - not_null: 是否非空 (可选，默认false)
    - default_value: 默认值 (可选)
    """
    try:
        if not table_name:
            return validation_error_response({
                'table_name': '表名不能为空'
            })
        
        if not column_name:
            return validation_error_response({
                'column_name': '字段名不能为空'
            })
        
        data = request.get_json()
        if not data:
            return validation_error_response({
                'request': '请求体不能为空'
            })
        
        new_column_name = data.get('new_column_name', '').strip()
        new_column_type = data.get('new_column_type', '').strip()
        not_null = bool(data.get('not_null', False))
        default_value = data.get('default_value')
        
        if not new_column_name:
            return validation_error_response({
                'new_column_name': '新字段名不能为空'
            })
        
        if not new_column_type:
            return validation_error_response({
                'new_column_type': '新字段类型不能为空'
            })
        
        # 编辑字段
        success = dev_service.edit_column(
            table_name=table_name,
            old_column_name=column_name,
            new_column_name=new_column_name,
            new_column_type=new_column_type,
            not_null=not_null,
            default_value=default_value
        )
        
        if success:
            return success_response(
                data={
                    'table_name': table_name,
                    'old_column_name': column_name,
                    'new_column_name': new_column_name,
                    'new_column_type': new_column_type
                },
                message="编辑字段成功"
            )
        else:
            return error_response("编辑字段失败")
        
    except Exception as e:
        return error_response(f"编辑字段失败: {str(e)}")


@dev_bp.route('/sql', methods=['POST'])
@dev_auth_required
def execute_sql():
    """执行自定义SQL语句
    
    请求体参数:
    - sql: SQL语句
    - params: 参数列表 (可选)
    """
    try:
        data = request.get_json()
        if not data or 'sql' not in data:
            return validation_error_response({
                'sql': 'SQL语句不能为空'
            })
        
        sql = data['sql'].strip()
        params = data.get('params', [])
        
        if not sql:
            return validation_error_response({
                'sql': 'SQL语句不能为空'
            })
        
        # 执行SQL
        result = dev_service.execute_sql(sql, tuple(params) if params else None)
        
        return success_response(
            data=result,
            message="SQL执行成功"
        )
        
    except Exception as e:
        return error_response(f"SQL执行失败: {str(e)}")


@dev_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return success_response(
        data={
            'status': 'healthy',
            'service': 'dev_api',
            'version': '1.0.0'
        },
        message="开发者API服务正常"
    )