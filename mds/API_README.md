# Local Agent API 使用说明

## 概述

这个API系统允许通过HTTP请求与本地Agent进行交互，支持异步任务执行和状态跟踪。

## 功能特点

- 🚀 异步任务执行：提交任务后立即返回，不阻塞
- 📊 状态跟踪：完整的任务状态管理和查询
- 🔄 轮次编号：每个任务都有唯一的轮次号
- 📁 本地存储：任务状态保存在本地文件中
- 📋 按轮次日志：每个轮次的日志独立存储在专门目录中
- 🔧 工具支持：支持设备检查、等待等多种工具

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动API服务器

```bash
python start_api_server.py
```

服务器将在 `http://localhost:8000` 启动

### 3. 测试API功能

```bash
python test_api_client.py
```

## API接口

### 提交任务

```bash
POST /submit_task
Content-Type: application/json

{
  "task_description": "检查一下当前连接到我电脑的安卓设备,然后等待15秒后再次检查"
}
```

响应：
```json
{
  "round_id": "round_0001",
  "message": "任务已提交，轮次号: round_0001"
}
```

### 查询任务状态

```bash
GET /task_status/{round_id}
```

响应：
```json
{
  "round_id": "round_0001",
  "status": "completed",
  "task_description": "检查一下当前连接到我电脑的安卓设备,然后等待15秒后再次检查",
  "start_time": "2025-07-08T15:30:00.123456",
  "end_time": "2025-07-08T15:30:20.789012",
  "result": "检查结果..."
}
```

### 获取轮次日志信息

```bash
GET /round_log_info/{round_id}
```

响应：
```json
{
  "round_id": "round_0001",
  "task_description": "检查一下当前连接到我电脑的安卓设备,然后等待15秒后再次检查",
  "status": "completed",
  "start_time": "2025-07-08T15:30:00.123456",
  "end_time": "2025-07-08T15:30:20.789012",
  "log_dir": "log/round_0001_20250708_153000"
}
```

### 其他接口

- `GET /all_tasks` - 获取所有任务
- `GET /running_tasks` - 获取正在运行的任务
- `GET /agent_info` - 获取Agent信息
- `GET /health` - 健康检查

## 状态管理

任务状态存储在 `status/agent_status.json` 文件中，包含以下状态：

- `pending` - 待执行
- `running` - 执行中
- `completed` - 已完成
- `failed` - 执行失败

## 使用示例

### Python客户端

```python
import requests
import time

# 提交任务
response = requests.post("http://localhost:8000/submit_task", json={
    "task_description": "检查Android设备"
})
round_id = response.json()["round_id"]

# 查询状态
while True:
    status = requests.get(f"http://localhost:8000/task_status/{round_id}")
    task_status = status.json()["status"]
    
    if task_status == "completed":
        print("任务完成!")
        break
    elif task_status == "failed":
        print("任务失败!")
        break
    
    time.sleep(1)
```

### curl命令

```bash
# 提交任务
curl -X POST "http://localhost:8000/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "检查Android设备"}'

# 查询状态
curl "http://localhost:8000/task_status/round_0001"
```

## 日志系统

### 按轮次组织的日志

系统现在支持按轮次组织日志，每个轮次的日志都存储在独立的目录中：

```
log/
├── round_0001_20250708_153000/
│   ├── agent.log       # Agent操作日志
│   ├── llm.log         # LLM调用日志
│   ├── llm_detail.log  # LLM详细输入输出日志
│   ├── tools.log       # 工具执行日志
│   └── unified.log     # 统一日志
├── round_0002_20250708_154500/
│   ├── agent.log
│   ├── llm.log
│   ├── llm_detail.log
│   ├── tools.log
│   └── unified.log
└── ...
```

### 日志文件说明

- `agent.log` - Agent操作日志，记录任务执行过程
- `llm.log` - LLM调用日志，记录模型调用信息
- `llm_detail.log` - LLM详细日志，包含完整的输入输出内容
- `tools.log` - 工具执行日志，记录各种工具的调用结果
- `unified.log` - 统一日志，包含所有类型的日志信息

### 获取日志信息

可以通过API获取特定轮次的日志目录信息：

```bash
curl "http://localhost:8000/round_log_info/round_0001"
```

## 注意事项

1. 确保ollama服务正在运行
2. 模型需要支持工具调用功能
3. 任务会在后台异步执行
4. 建议定期清理历史任务记录

## 故障排除

如果遇到问题，请检查：

1. ollama服务是否运行正常
2. 模型是否支持工具调用
3. 依赖是否正确安装
4. 端口8000是否被占用