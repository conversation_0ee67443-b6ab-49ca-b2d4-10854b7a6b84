# 日志管理系统说明

## 概述

本项目采用统一的日志管理系统，将日志按照角色进行分类记录，便于调试和监控。

## 日志分类

### 1. Agent 日志
- **作用**: 记录Agent的调用工具记录和结果
- **文件**: `log/agent.log`
- **内容**: 工具调用、用户输入处理、系统状态等

### 2. Tools 日志
- **作用**: 记录工具被调用后的具体执行流程
- **文件**: `log/tools.log`
- **内容**: 设备检测、命令执行、工具内部处理等

### 3. LLM 日志
- **作用**: 记录大模型的思考和对话输入输出
- **文件**: `log/llm.log`
- **内容**: 模型输入、输出、决策过程等

### 4. LLM 详细日志 ⭐ 新功能
- **作用**: 记录大模型的完整原始输入输出数据
- **文件**: `log/llm_detail.log`
- **内容**: 完整的消息内容、工具调用详情、响应元数据等

### 5. 统一日志
- **作用**: 包含所有角色的日志，便于整体查看
- **文件**: `log/unified.log`
- **内容**: 所有上述日志的汇总

## 日志格式

### 文件日志格式
所有文件日志都采用统一格式：
```
2025-07-08 12:01:39 - INFO - [角色] - 具体内容
```

### 控制台日志格式
控制台输出采用简化格式（不显示角色标识）：
```
2025-07-08 12:01:39 - INFO - [Agent] - 开始执行工具: check_android_devices, 参数: {}
```

### 详细LLM日志格式
LLM详细日志包含完整的输入输出信息：
```
=== LLM 输入 ===
[消息 1] SystemMessage: 你是一个专业的设备管理助手...
[消息 2] HumanMessage: 检查设备

=== LLM 输出 ===
内容: 我来帮你检查设备...
工具调用数量: 1
[工具调用 1]
  - 工具名: check_android_devices
  - 参数: {}
  - ID: call_abc123

=== 工具执行结果 ===
工具名: check_android_devices
结果: {"status": "success", "devices": ["UQG5T20327008560"]}
```

### 示例
常规日志示例：
```
2025-07-08 12:01:39 - INFO - [Agent] - 收到用户输入: 检查设备
2025-07-08 12:01:40 - INFO - [Tools] - [check_android_devices] - 检测到 1 个Android设备: ['UQG5T20327008560']
2025-07-08 12:01:41 - INFO - [LLM] - [llama3.2] - 模型决定调用工具: ['check_android_devices']
```

## 使用方法

### 在代码中使用

```python
from log_manager import log_manager

# Agent日志
log_manager.info_agent("Agent信息", console=True)
log_manager.error_agent("Agent错误")
log_manager.warning_agent("Agent警告")

# Tools日志
log_manager.info_tools("工具执行信息", "tool_name")
log_manager.error_tools("工具错误", "tool_name")
log_manager.warning_tools("工具警告", "tool_name")

# LLM日志
log_manager.info_llm("LLM信息", "model_name")
log_manager.error_llm("LLM错误", "model_name")
log_manager.warning_llm("LLM警告", "model_name")

# LLM详细日志（记录完整输入输出）⭐ 新功能
log_manager.log_llm_input(messages, "model_name")        # 记录完整输入
log_manager.log_llm_output(response, "model_name")       # 记录完整输出
log_manager.log_llm_tool_result("tool_name", result, "model_name")  # 记录工具结果
```

### 参数说明

- `console`: 是否在控制台输出，默认Agent日志会显示，Tools和LLM日志默认不显示
- `tool_name`: 工具名称，用于Tools日志分类
- `model_name`: 模型名称，用于LLM日志分类
- `messages`: LLM输入的消息列表（用于详细日志）
- `response`: LLM返回的响应对象（用于详细日志）
- `result`: 工具执行结果（用于详细日志）

### 控制台输出说明

控制台日志经过简化处理：
- 不显示 `[Console]` 标识，直接显示角色信息
- Agent日志默认显示在控制台（console=True）
- Tools和LLM日志默认不显示在控制台，只记录到文件
- 错误日志会强制显示在控制台

## 日志级别

- **INFO**: 正常信息记录
- **WARNING**: 警告信息
- **ERROR**: 错误信息

## 配置

日志系统在初始化时会自动创建必要的目录和文件：
- 日志目录: `log/`
- 编码: UTF-8
- 时间格式: `%Y-%m-%d %H:%M:%S`
- 自动创建的日志文件：
  - `log/agent.log` - Agent日志
  - `log/tools.log` - Tools日志
  - `log/llm.log` - LLM简要日志
  - `log/llm_detail.log` - LLM详细日志 ⭐ 新增
  - `log/unified.log` - 统一日志

## 测试

运行Agent程序时会自动生成日志：
```bash
python agent.py
```

检查日志文件：
```bash
# 查看最新的Agent日志
tail -f log/agent.log

# 查看LLM详细日志
tail -f log/llm_detail.log

# 查看统一日志
tail -f log/unified.log
```

## 新工具集成

当添加新的工具时，请按照以下方式集成日志：

1. 在工具文件开头导入日志管理器：
```python
from log_manager import log_manager
```

2. 在工具函数中使用相应的日志记录：
```python
def my_new_tool():
    log_manager.info_tools("开始执行新工具", "my_new_tool")
    try:
        # 工具逻辑
        result = do_something()
        log_manager.info_tools(f"工具执行成功: {result}", "my_new_tool")
        return result
    except Exception as e:
        log_manager.error_tools(f"工具执行失败: {e}", "my_new_tool")
        raise
```

这样可以确保所有工具的日志都能统一管理和查看。

## 常见问题解答

### Q1: 控制台显示 `[Console]` 标识是什么意思？
**A**: 这是旧版本的显示方式，新版本已经优化为简化格式，不会显示 `[Console]` 标识。

### Q2: 如何查看模型的完整输入输出？
**A**: 查看 `log/llm_detail.log` 文件，其中记录了模型的完整原始输入输出数据，包括：
- 完整的消息内容
- 工具调用的详细参数
- 响应的元数据信息
- 工具执行结果

### Q3: 日志文件太多怎么办？
**A**: 可以查看 `log/unified.log` 文件，它包含了所有角色的日志汇总。

### Q4: 如何关闭控制台输出？
**A**: 在调用日志方法时设置 `console=False` 参数：
```python
log_manager.info_agent("这条日志不会显示在控制台", console=False)
```

### Q5: LLM详细日志文件很大怎么办？
**A**: LLM详细日志包含完整的对话内容，建议定期清理或使用日志轮转：
```bash
# 清空详细日志（谨慎操作）
> log/llm_detail.log
```

## 更新日志

### v2.0 (当前版本)
- ✅ 优化控制台日志格式，移除 `[Console]` 标识
- ✅ 新增 LLM 详细日志功能，记录完整输入输出
- ✅ 增加工具执行结果的详细记录
- ✅ 改进日志文件分类和管理 