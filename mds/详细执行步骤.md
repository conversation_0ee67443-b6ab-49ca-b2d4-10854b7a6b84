# 详细执行步骤

## 步骤 1: 查找可用的iOS设备
**工具调用:** `find_available_device`  
**参数:** `{"platform": "ios"}`  
**固定值参数:** `{"platform": "ios"}`  
**预期结果:** 成功找到iOS设备

---

## 步骤 2: 启动设备测试会话
**工具调用:** `start_device_test`  
**参数:** `{"udid": "{device_udid}"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 设备测试会话启动成功

---

## 步骤 3: 查找首页地址按钮
**工具调用:** `find_element_on_page`  
**参数:** `{"udid": "{device_udid}", "element": "地址", "scene_desc": "首页地址按钮"}`  
**固定值参数:** `{"element": "地址", "scene_desc": "首页地址按钮"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 成功定位首页地址按钮  
> **重要:** 下一步将点击本步骤找到的元素位置

---

## 步骤 4: 点击首页地址
**工具调用:** `tap_device`  
**参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}", "description": "点击地址"}`  
**动态值参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}`  
- **udid:** 来自find_available_device结果
- **x:** 来自find_element_on_page结果
- **y:** 来自find_element_on_page结果  
**预期结果:** 成功点击地址  
> **设备关联:** 在设备 {device_udid} 上执行

---

## 步骤 5: 等待页面加载
**工具调用:** `wait_seconds`  
**参数:** `{"seconds": 2}`  
**固定值参数:** `{"seconds": 2}`  
**预期结果:** 等待2秒确保页面加载

---

## 步骤 6: 分析地址选择页状态
**工具调用:** `analyze_meituan_page`  
**参数:** `{"udid": "{device_udid}", "action_description": "点击地址后"}`  
**固定值参数:** `{"action_description": "点击地址后"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 记录地址选择页状态

---

## 步骤 7: 获取地址选择页OCR结果
**工具调用:** `ocr_text_only`  
**参数:** `{"udid": "{device_udid}"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 验证地址选择页搜索框默认文案为'搜索城市/区县/地点'

---

## 步骤 8: 查找地址选择页搜索框
**工具调用:** `find_element_on_page`  
**参数:** `{"udid": "{device_udid}", "element": "搜索框", "scene_desc": "地址选择页搜索框"}`  
**固定值参数:** `{"element": "搜索框", "scene_desc": "地址选择页搜索框"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 成功定位地址选择页搜索框  
> **重要:** 下一步将点击本步骤找到的元素位置

---

## 步骤 9: 点击地址选择页搜索框
**工具调用:** `tap_device`  
**参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}", "description": "点击搜索框"}`  
**动态值参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}`  
- **udid:** 来自find_available_device结果
- **x:** 来自find_element_on_page结果
- **y:** 来自find_element_on_page结果  
**预期结果:** 成功点击搜索框  
> **设备关联:** 在设备 {device_udid} 上执行

---

## 步骤 10: 等待页面加载
**工具调用:** `wait_seconds`  
**参数:** `{"seconds": 2}`  
**固定值参数:** `{"seconds": 2}`  
**预期结果:** 等待2秒确保页面加载

---

## 步骤 11: 分析地址搜索页状态
**工具调用:** `analyze_meituan_page`  
**参数:** `{"udid": "{device_udid}", "action_description": "点击搜索框后"}`  
**固定值参数:** `{"action_description": "点击搜索框后"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 记录地址搜索页状态

---

## 步骤 12: 获取地址搜索页OCR结果
**工具调用:** `ocr_text_only`  
**参数:** `{"udid": "{device_udid}"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 验证地址搜索页搜索框默认文案为'搜索城市/区县/地点'

---

## 步骤 13: 查找地址搜索页搜索框
**工具调用:** `find_element_on_page`  
**参数:** `{"udid": "{device_udid}", "element": "搜索框", "scene_desc": "地址搜索页搜索框"}`  
**固定值参数:** `{"element": "搜索框", "scene_desc": "地址搜索页搜索框"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 成功定位地址搜索页搜索框  
> **重要:** 下一步将点击本步骤找到的元素位置

---

## 步骤 14: 点击地址搜索页搜索框
**工具调用:** `tap_device`  
**参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}", "description": "点击搜索框"}`  
**动态值参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}`  
- **udid:** 来自find_available_device结果
- **x:** 来自find_element_on_page结果
- **y:** 来自find_element_on_page结果  
**预期结果:** 成功点击搜索框  
> **设备关联:** 在设备 {device_udid} 上执行

---

## 步骤 15: 在搜索框输入'北京'
**工具调用:** `input_text_smart`  
**参数:** `{"udid": "{device_udid}", "text": "北京"}`  
**固定值参数:** `{"text": "北京"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 成功输入'北京'

---

## 步骤 16: 等待搜索结果加载
**工具调用:** `wait_seconds`  
**参数:** `{"seconds": 2}`  
**固定值参数:** `{"seconds": 2}`  
**预期结果:** 等待2秒确保搜索结果加载

---

## 步骤 17: 查找搜索结果'北京市'
**工具调用:** `find_element_on_page`  
**参数:** `{"udid": "{device_udid}", "element": "北京市", "scene_desc": "搜索结果北京市"}`  
**固定值参数:** `{"element": "北京市", "scene_desc": "搜索结果北京市"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 成功定位搜索结果北京市  
> **重要:** 下一步将点击本步骤找到的元素位置

---

## 步骤 18: 点击搜索结果'北京市'
**工具调用:** `tap_device`  
**参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}", "description": "点击北京市"}`  
**动态值参数:** `{"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}`  
- **udid:** 来自find_available_device结果
- **x:** 来自find_element_on_page结果
- **y:** 来自find_element_on_page结果  
**预期结果:** 成功点击北京市  
> **设备关联:** 在设备 {device_udid} 上执行

---

## 步骤 19: 等待页面返回
**工具调用:** `wait_seconds`  
**参数:** `{"seconds": 2}`  
**固定值参数:** `{"seconds": 2}`  
**预期结果:** 等待2秒确保页面返回

---

## 步骤 20: 分析首页状态
**工具调用:** `analyze_meituan_page`  
**参数:** `{"udid": "{device_udid}", "action_description": "点击北京市后"}`  
**固定值参数:** `{"action_description": "点击北京市后"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 记录首页状态

---

## 步骤 21: 获取首页OCR结果验证地址
**工具调用:** `ocr_text_only`  
**参数:** `{"udid": "{device_udid}"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 验证首页地址展示为'北京'

---

## 步骤 22: 结束设备测试会话
**工具调用:** `end_device_test`  
**参数:** `{"udid": "{device_udid}"}`  
**动态值参数:** `{"udid": "{device_udid}"}`  
- **udid:** 来自find_available_device结果  
**预期结果:** 设备测试会话结束

---

## 重要执行要求

### 步骤顺序
- 严格按照步骤顺序执行，一次执行一个工具

### 等待结果
- 每个工具执行完成后，等待结果再进行下一步

### 参数类型区分
- **固定值参数:** 直接使用，不需要替换
- **动态值参数:** 必须从指定的步骤结果中获取实际值

### 动态参数获取
- 根据parameter_sources指示，从对应步骤结果中提取实际值

### 步骤关联
- 特别注意find_element_on_page找到的元素位置，在后续tap_device中使用

### 坐标使用
- tap_device操作时，优先使用find_element_on_page返回的坐标信息

### 设备一致性
- 确保所有操作都在同一设备(udid)上执行

### 失败重试
- 如果某个步骤失败，尝试1-2次后再继续

### 完成记录
- 完成所有步骤后调用record_agent_summary记录总结
