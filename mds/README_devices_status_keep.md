# 设备状态保持工具 (Device Status Keeper)

## 功能概述

`_devices_status_keep_tools.py` 是一个24小时持续运行的设备状态监控工具，用于维护iOS和Android设备的状态，确保设备始终处于可用状态（ready）。

## 主要功能

### 1. 设备发现与状态管理
- 自动发现连接的iOS和Android设备
- 创建和维护设备状态文件
- 监控设备连接状态
- **自动获取设备信息**：设备名称、系统版本等
- **智能状态转换**：从 `connected` → `preparing` → `ready`

### 2. 服务管理与端口分配
- **iOS设备**：
  - 智能分配Appium端口和WDA端口
  - 启动Appium服务
  - 准备和安装WDA（设备特定配置）
  - 定期重启WDA（每6小时）
  - **端口记录**：在设备状态中记录 `appium_port` 和 `wda_port`

- **Android设备**：
  - 智能分配Appium端口
  - 启动Appium服务
  - 定期重启ADB服务（每4小时）
  - **端口记录**：在设备状态中记录 `appium_port`

### 3. 智能状态过滤
- 自动跳过状态为 `running` 的设备
- 避免在测试期间干扰设备
- **状态分类处理**：
  - `connected`/`waiting`/`setup_failed` → 触发初始化
  - `ready` → 进行维护检查
  - `preparing`/`installing_wda` → 等待完成
  - `running` → 跳过处理

### 4. 日志管理
- 统一日志文件：`log/device_status_keep.log`
- 日志轮转：最大100MB，保留5个备份
- 设备专用日志标记：`[设备名称]`
- 系统日志标记：`[keeper]`

### 5. 设备信息管理
- **系统版本获取**：
  - iOS: 通过 `ideviceinfo` 获取 `ProductVersion`
  - Android: 通过 `adb shell getprop` 获取 `ro.build.version.release`
- **高效信息获取**：一次调用获取设备名称和系统版本
- **设备状态字段**：
  - `device_name`: 设备名称
  - `system_version`: 系统版本
  - `appium_port`: Appium服务端口
  - `wda_port`: WDA服务端口（仅iOS）
  - `status`: 设备状态
  - `platform`: 设备平台

## 使用方法

### 启动工具
```bash
python tools/_devices_status_keep_tools.py
```

### 后台运行
```bash
nohup python tools/_devices_status_keep_tools.py > /dev/null 2>&1 &
```

### 停止工具
- 前台运行：按 `Ctrl+C`
- 后台运行：`kill -TERM <进程ID>`

## 配置说明

### 监控间隔
- 默认检查间隔：5分钟
- 可在 `DeviceStatusKeeper.__init__()` 中修改 `check_interval`

### 端口分配
- iOS Appium基础端口：4724
- iOS WDA基础端口：8101
- Android Appium基础端口：5724（避免与iOS冲突）

### 服务重启间隔
- WDA重启间隔：6小时
- ADB重启间隔：4小时

## 日志格式示例

### Keeper系统日志
```
2025-07-09 11:44:54,863 - INFO - [keeper] - 开始设备状态维护周期
2025-07-09 11:44:55,183 - INFO - [keeper] - 发现 2 个设备: ['UQG5T20327008560', '00008140-000238321401801C']
```

### 设备专用日志
```
2025-07-09 11:14:05,259 - INFO - [iPhone_11] - 开始为设备 iPhone_11 (UDID: 00008030-001550602192802E) 安装WDA
2025-07-09 11:14:05,259 - INFO - [iPhone_11] - 设备 iPhone_11 (UDID: 00008030-001550602192802E) 状态更新为: installing_wda
2025-07-09 11:14:05,272 - INFO - [iPhone_11] - 找到可用端口: 8101
```

## 设备状态说明

### 状态类型
- `connected`: 设备已连接，等待初始化（默认初始状态）
- `waiting`: 设备空闲，等待任务（兼容性状态）
- `preparing`: 正在准备设备（分配端口、启动服务）
- `installing_wda`: 正在安装WDA（iOS设备）
- `ready`: 设备准备就绪，可以接受测试任务
- `running`: 设备正在执行测试（会被跳过）
- `*_failed`: 各种失败状态（`setup_failed`、`appium_start_failed`、`wda_install_failed`等）

### 状态转换流程
1. **新设备连接** → `connected`
2. **开始初始化** → `preparing`
3. **iOS设备安装WDA** → `installing_wda`
4. **初始化完成** → `ready`
5. **开始测试** → `running`
6. **测试完成** → `ready`

### 状态过滤规则
- ✅ **处理状态**：`connected`、`waiting`、`setup_failed`、`appium_start_failed`、`wda_install_failed`
- ⏳ **等待状态**：`preparing`、`installing_wda`
- ⚡ **维护状态**：`ready`
- ❌ **跳过状态**：`running`

## 依赖组件

### 现有工具集成
- `tools/_adb_manage_tools.py` - ADB服务管理
- `tools/_appium_manage_tools.py` - Appium服务管理
- `tools/_wda_manage_tools.py` - WDA服务管理
- `tools/check_devices_tools.py` - 设备检查工具
- `tools/device_status_manage_tools.py` - 设备状态管理

### 外部依赖
- `adb` - Android设备管理
- `idevice_id` - iOS设备列表
- `ideviceinfo` - iOS设备信息
- `appium` - 移动应用自动化框架
- `xcodebuild` - iOS WDA构建工具

## 测试工具

### 功能测试
```bash
python test_devices_status_keep.py
```

测试内容包括：
- 设备发现功能
- 端口分配功能
- 日志记录功能
- 单次维护周期

### 新功能测试
```bash
python test_new_features.py
```

测试内容包括：
- 系统版本获取功能
- 设备状态文件创建（包含系统版本）
- 端口记录功能
- 设备状态摘要展示

### 状态转换测试
```bash
python test_device_status_flow.py
```

测试内容包括：
- 设备状态创建（默认为 `connected`）
- 状态转换逻辑
- Keeper设备处理逻辑
- 设备维护决策

### 便捷启动脚本
```bash
./start_device_keeper.sh
```

启动脚本功能：
- 检查依赖工具
- 检测已运行实例
- 提供交互式启动选项

## 注意事项

1. **权限要求**：需要对连接的设备有调试权限
2. **网络要求**：iOS设备需要网络连接下载WDA依赖
3. **存储空间**：确保日志目录有足够空间
4. **进程管理**：避免重复启动多个实例

## 故障排除

### 常见问题
1. **设备未被发现**：检查设备连接和调试权限
2. **WDA安装失败**：检查开发者证书和设备信任
3. **端口冲突**：检查端口是否被其他服务占用
4. **服务启动失败**：检查相关工具是否正确安装

### 日志查看
```bash
tail -f log/device_status_keep.log
```

## 扩展功能

### 自定义监控间隔
修改 `DeviceStatusKeeper` 类的 `check_interval` 属性。

### 添加新的设备类型
继承 `DeviceManager` 基类并实现相应的设备管理逻辑。

### 自定义日志格式
修改 `DeviceSpecificLogger` 类的日志格式配置。