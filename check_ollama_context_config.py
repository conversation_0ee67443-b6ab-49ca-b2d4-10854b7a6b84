#!/usr/bin/env python3
"""
检查Ollama实例的实际上下文配置脚本
用于诊断上下文窗口配置问题
"""

import requests
import json
from ollama_simple_manager import OLLAMA_CONFIGS

def check_instance_context(port: int, model: str) -> dict:
    """检查单个Ollama实例的上下文配置"""
    try:
        # 忽略代理设置
        proxies = {"http": None, "https": None}
        
        # 检查服务状态
        version_url = f"http://localhost:{port}/api/version"
        version_response = requests.get(version_url, timeout=5, proxies=proxies)
        
        if version_response.status_code != 200:
            return {
                "port": port,
                "model": model,
                "status": "service_unavailable",
                "error": f"服务不可用: {version_response.status_code}"
            }
        
        # 尝试获取模型信息
        show_url = f"http://localhost:{port}/api/show"
        show_data = {"name": model}
        
        try:
            show_response = requests.post(show_url, json=show_data, timeout=10, proxies=proxies)
            model_info = {}
            if show_response.status_code == 200:
                model_data = show_response.json()
                model_info = {
                    "modelfile": model_data.get("modelfile", ""),
                    "parameters": model_data.get("parameters", ""),
                    "template": model_data.get("template", "")
                }
        except Exception as e:
            model_info = {"error": f"获取模型信息失败: {e}"}
        
        # 进行简单的上下文测试
        test_url = f"http://localhost:{port}/api/generate"
        test_data = {
            "model": model,
            "prompt": "测试上下文：记住这个数字12345。请回复'收到数字'",
            "stream": False,
            "options": {
                "num_predict": 50,
                "temperature": 0.1
            }
        }
        
        test_response = requests.post(test_url, json=test_data, timeout=30, proxies=proxies)
        
        if test_response.status_code == 200:
            test_result = test_response.json()
            
            return {
                "port": port,
                "model": model,
                "status": "available",
                "model_info": model_info,
                "test_response": test_result.get("response", ""),
                "context_info": {
                    "eval_count": test_result.get("eval_count", 0),
                    "eval_duration": test_result.get("eval_duration", 0),
                    "prompt_eval_count": test_result.get("prompt_eval_count", 0),
                    "prompt_eval_duration": test_result.get("prompt_eval_duration", 0),
                    "total_duration": test_result.get("total_duration", 0)
                }
            }
        else:
            return {
                "port": port,
                "model": model,
                "status": "test_failed",
                "error": f"测试失败: {test_response.status_code}",
                "response_text": test_response.text
            }
            
    except Exception as e:
        return {
            "port": port,
            "model": model,
            "status": "error",
            "error": str(e)
        }

def main():
    """主函数"""
    print("🔍 Ollama 实例上下文配置检查工具")
    print("="*60)
    
    all_results = []
    
    for config in OLLAMA_CONFIGS:
        port = config["port"]
        model = config["model"]
        
        print(f"\n📋 检查端口 {port}: {model}")
        result = check_instance_context(port, model)
        all_results.append(result)
        
        if result["status"] == "available":
            print(f"✅ 服务可用")
            print(f"   📊 Prompt tokens: {result['context_info']['prompt_eval_count']}")
            print(f"   📝 Response tokens: {result['context_info']['eval_count']}")
            print(f"   💬 回复: {result['test_response'][:100]}...")
            
            # 检查模型信息中的上下文配置
            model_info = result.get('model_info', {})
            if 'parameters' in model_info:
                params = model_info['parameters']
                if 'num_ctx' in params:
                    print(f"   🎯 模型上下文配置: {params}")
                elif 'num_ctx' in str(params):
                    print(f"   🎯 模型参数: {params}")
            
        elif result["status"] == "service_unavailable":
            print(f"❌ 服务不可用: {result['error']}")
        elif result["status"] == "test_failed":
            print(f"⚠️ 测试失败: {result['error']}")
        else:
            print(f"❌ 错误: {result['error']}")
    
    # 汇总报告
    print("\n" + "="*60)
    print("📊 检查结果汇总")
    print("="*60)
    
    available_count = sum(1 for r in all_results if r["status"] == "available")
    print(f"可用实例数: {available_count}/{len(all_results)}")
    
    for result in all_results:
        status_icon = "✅" if result["status"] == "available" else "❌"
        print(f"{status_icon} 端口 {result['port']}: {result['status']}")
    
    # 如果有可用实例，提供下一步建议
    if available_count > 0:
        print("\n💡 下一步建议:")
        print("1. 如果发现上下文配置不足，重启Ollama服务")
        print("2. 运行 python test_ollama_context_realistic.py 进行详细测试")
        print("3. 检查系统资源是否充足")
    else:
        print("\n⚠️ 没有可用的Ollama实例，请检查服务状态")
    
    # 保存详细结果
    import time
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    result_file = f"ollama_context_check_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存到: {result_file}")

if __name__ == "__main__":
    main()
