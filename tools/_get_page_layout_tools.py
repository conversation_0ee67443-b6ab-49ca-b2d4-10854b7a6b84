#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取设备当前页面布局工具，适用于 iOS 和 Android。

提供 `get_ios_page_layout` 和 `get_android_page_layout` 函数供 agent 使用。
"""

import time
import os
import sys
import xml.etree.ElementTree as ET
# from appium.options.ios import XCUITestOptions  # 未使用，注释掉

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入设备驱动管理工具
from tools._device_driver_manage_tools import get_device_driver

# 导入日志管理器
from tools._concurrent_log_manager import get_current_task_log_manager

# --- 辅助函数 ---

# 已移除JSON相关代码，现在直接使用XML转文本

# --- Agent 使用的布局获取函数 ---

def get_ios_page_layout(device_udid: str, 
                         bundle_id: str = None, 
                         appium_server: str = None, 
                         scene_desc: str = None,
                         compressed: bool = True,
                         output_format: str = "json") -> str:
    """
    获取iOS设备当前页面的布局
    
    参数:
        device_udid: 设备UDID
        bundle_id: 目标App的bundle id，从设备状态文件获取
        appium_server: Appium服务地址，从设备状态文件获取端口
        scene_desc: 情景备注（必填）
        compressed: 是否压缩布局信息，默认为True
        output_format: 输出格式 "json"(默认) 或 "text"
    返回:
        保存的文件的绝对路径
    """
    if not scene_desc:
        raise ValueError("scene_desc（情景备注）为必填参数！")
    
    # 从设备状态文件获取配置信息
    from tools._device_status_manage_tools import get_device_status, get_app_id
    device_status = get_device_status(device_udid)
    if not device_status:
        get_current_task_log_manager().error_tools(f"设备 {device_udid} 状态文件不存在", "get_ios_page_layout")
        return f"错误: 设备 {device_udid} 状态文件不存在"
    
    # 使用提供的参数或从设备状态文件获取
    bundle_id = bundle_id or get_app_id(device_udid) or 'com.meituan.imeituan'
    appium_port = device_status.get('appium_port', 4723)
    wda_forward_port = device_status.get('wda_forward_port', 8100)
    
    if not appium_server:
        appium_server = f"http://localhost:{appium_port}/wd/hub"
    
    # 获取设备名称用于创建子文件夹
    device_name = device_status.get('device_name', device_udid)
    
    # 修改存储位置到 log/layout_log/{device_name} 目录
    layout_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "log", "layout_log", device_name)
    os.makedirs(layout_dir, exist_ok=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 根据输出格式确定文件扩展名，文件名不再包含UDID
    file_ext = "txt" if output_format == "text" else "json"
    output_file = os.path.join(layout_dir, f"{timestamp}_{scene_desc}.{file_ext}")
    
    get_current_task_log_manager().info_tools(f"开始获取iOS设备 {device_udid} 的页面布局", "get_ios_page_layout")
    get_current_task_log_manager().info_tools(f"配置信息: bundle_id={bundle_id}, appium_port={appium_port}, wda_forward_port={wda_forward_port}", "get_ios_page_layout")

    try:
        # 获取设备driver
        driver = get_device_driver(device_udid)
        if not driver:
            get_current_task_log_manager().error_tools(f"无法获取 iOS 设备 {device_udid} 的 driver 实例", "get_ios_page_layout")
            raise RuntimeError(f"无法获取 iOS 设备 {device_udid} 的 driver 实例。")
        get_current_task_log_manager().info_tools("成功获取 driver！", "get_ios_page_layout")

        try:
            # 启动App
            get_current_task_log_manager().info_tools(f"启动{bundle_id}应用...", "get_ios_page_layout")
            driver.activate_app(bundle_id)
            time.sleep(5)

            # 获取页面结构（控件树）
            get_current_task_log_manager().info_tools("获取页面控件树...", "get_ios_page_layout")
            page_source = driver.page_source

            if output_format == "text":
                # 直接从XML转换为文本格式
                from tools._xml_to_text_tools import xml_to_text
                text_content = xml_to_text(page_source, platform='ios')
                
                # 保存为文本文件
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(text_content)
                get_current_task_log_manager().info_tools(f"控件树已直接转换为文本格式并保存为 {output_file}", "get_ios_page_layout")
            else:
                # JSON格式已废弃，强制使用文本格式
                get_current_task_log_manager().warning_tools("JSON格式已废弃，自动转换为文本格式", "get_ios_page_layout")
                from tools._xml_to_text_tools import xml_to_text
                text_content = xml_to_text(page_source, platform='ios')
                
                # 保存为文本文件
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(text_content)
                get_current_task_log_manager().info_tools(f"控件树已转换为文本格式并保存为 {output_file}", "get_ios_page_layout")
            
            return output_file
        finally:
            pass  # 不再主动关闭 driver，保持全局缓存连接
    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取iOS设备 {device_udid} 页面布局时发生错误: {e}", "get_ios_page_layout")
        return f"错误: {str(e)}"

def get_android_page_layout(device_udid: str, 
                           package_name: str = None,
                           appium_server: str = None,
                           scene_desc: str = None,
                           compressed: bool = True,
                           output_format: str = "json") -> str:
    """
    获取Android设备当前页面的布局
    
    参数:
        device_udid: 设备UDID
        package_name: 目标App的包名，从设备状态文件获取
        appium_server: Appium服务地址，从设备状态文件获取端口
        scene_desc: 情景备注（必填）
        compressed: 是否压缩布局信息，默认为True
        output_format: 输出格式 "json"(默认) 或 "text"
    返回:
        保存的文件的绝对路径
    """
    if not scene_desc:
        raise ValueError("scene_desc（情景备注）为必填参数！")
    
    # 从设备状态文件获取配置信息
    from tools._device_status_manage_tools import get_device_status, get_app_id
    device_status = get_device_status(device_udid)
    if not device_status:
        get_current_task_log_manager().error_tools(f"设备 {device_udid} 状态文件不存在", "get_android_page_layout")
        return f"错误: 设备 {device_udid} 状态文件不存在"
    
    # 使用提供的参数或从设备状态文件获取
    package_name = package_name or get_app_id(device_udid) or 'com.sankuai.meituan'
    appium_port = device_status.get('appium_port', 4723)
    
    if not appium_server:
        appium_server = f"http://localhost:{appium_port}/wd/hub"
    
    # 获取设备名称用于创建子文件夹
    device_name = device_status.get('device_name', device_udid)
    
    # 修改存储位置到 log/layout_log/{device_name} 目录
    layout_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "log", "layout_log", device_name)
    os.makedirs(layout_dir, exist_ok=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 根据输出格式确定文件扩展名，文件名不再包含UDID
    file_ext = "txt" if output_format == "text" else "json"
    output_file = os.path.join(layout_dir, f"{timestamp}_{scene_desc}.{file_ext}")
        
    get_current_task_log_manager().info_tools(f"开始获取Android设备 {device_udid} 的页面布局", "get_android_page_layout")
    get_current_task_log_manager().info_tools(f"配置信息: package_name={package_name}, appium_port={appium_port}", "get_android_page_layout")

    try:
        # 获取设备driver
        driver = get_device_driver(device_udid)
        if not driver:
            get_current_task_log_manager().error_tools(f"无法获取 Android 设备 {device_udid} 的 driver 实例", "get_android_page_layout")
            raise RuntimeError(f"无法获取 Android 设备 {device_udid} 的 driver 实例。")
        get_current_task_log_manager().info_tools("成功获取 driver！", "get_android_page_layout")

        try:
            # 启动App
            get_current_task_log_manager().info_tools(f"启动{package_name}应用...", "get_android_page_layout")
            driver.activate_app(package_name)
            time.sleep(5)

            # 获取页面结构（控件树）
            get_current_task_log_manager().info_tools("获取页面控件树...", "get_android_page_layout")
            page_source = driver.page_source

            if output_format == "text":
                # 直接从XML转换为文本格式
                from tools._xml_to_text_tools import xml_to_text
                text_content = xml_to_text(page_source, platform='android')
                
                # 保存为文本文件
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(text_content)
                get_current_task_log_manager().info_tools(f"控件树已直接转换为文本格式并保存为 {output_file}", "get_android_page_layout")
            else:
                # JSON格式已废弃，强制使用文本格式
                get_current_task_log_manager().warning_tools("JSON格式已废弃，自动转换为文本格式", "get_android_page_layout")
                from tools._xml_to_text_tools import xml_to_text
                text_content = xml_to_text(page_source, platform='android')
                
                # 保存为文本文件
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(text_content)
                get_current_task_log_manager().info_tools(f"控件树已转换为文本格式并保存为 {output_file}", "get_android_page_layout")
            
            return output_file
        finally:
            pass  # 不再主动关闭 driver，保持全局缓存连接
    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取Android设备 {device_udid} 页面布局时发生错误: {e}", "get_android_page_layout")
        return f"错误: {str(e)}"

def get_page_layout(udid: str, scene_desc: str, compressed: bool = True, output_format: str = "json") -> str:
    """
    统一的页面布局获取接口，自动根据平台选择对应的方法。
    
    Args:
        udid: 设备的序列号(UDID)
        scene_desc: 情景备注
        compressed: 是否压缩布局信息，默认为True
        output_format: 输出格式 "json"(默认) 或 "text"
    
    Returns:
        str: 保存的文件的绝对路径或错误信息
    
    Raises:
        ValueError: 如果设备未连接或平台不支持
        Exception: 如果获取布局过程发生错误
    """
    get_current_task_log_manager().info_tools(f"开始获取设备 {udid} 的页面布局，场景: {scene_desc}", "get_page_layout")
    
    # 从设备驱动管理器获取设备信息
    from tools._device_status_manage_tools import get_device_status
    device_status = get_device_status(udid)
    if not device_status:
        raise ValueError(f"设备 {udid} 状态文件不存在")
    
    platform = device_status.get('platform', '').lower()
    if not platform:
        raise ValueError(f"设备 {udid} 状态文件中未找到平台信息")
    
    get_current_task_log_manager().info_tools(f"设备 {udid} 平台: {platform}", "get_page_layout")
    
    if platform == 'android':
        get_current_task_log_manager().info_tools(f"调用 get_android_page_layout，参数: udid={udid}, scene_desc={scene_desc}, compressed={compressed}, output_format={output_format}", "get_page_layout")
        return get_android_page_layout(udid, scene_desc=scene_desc, compressed=compressed, output_format=output_format)
    elif platform == 'ios':
        get_current_task_log_manager().info_tools(f"调用 get_ios_page_layout，参数: udid={udid}, scene_desc={scene_desc}, compressed={compressed}, output_format={output_format}", "get_page_layout")
        return get_ios_page_layout(udid, scene_desc=scene_desc, compressed=compressed, output_format=output_format)
    else:
        raise ValueError(f"不支持的平台类型: {platform}")


def test_xml_to_text_conversion():
    """
    测试XML到文本转换效果的函数
    """
    print("测试XML到文本转换效果...")
    
    # Android示例XML
    android_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<android.widget.FrameLayout resource-id="com.example:id/button" content-desc="搜索按钮" bounds="[0,0][100,50]" clickable="true" enabled="true" displayed="true">
    <android.widget.TextView text="搜索" content-desc="搜索文本" bounds="[10,10][90,40]" clickable="false" enabled="true" displayed="true"/>
</android.widget.FrameLayout>'''
    
    # iOS示例XML
    ios_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<XCUIElementTypeButton name="搜索按钮" label="搜索入口" enabled="true" visible="true" accessible="true" x="10" y="20" width="100" height="40">
    <XCUIElementTypeStaticText name="搜索" label="搜索" enabled="true" visible="true" x="15" y="25" width="30" height="20"/>
</XCUIElementTypeButton>'''
    
    from tools._xml_to_text_tools import xml_to_text
    
    print("Android XML转文本:")
    android_text = xml_to_text(android_xml, platform='android')
    print(android_text)
    
    print("\n" + "="*50)
    
    print("iOS XML转文本:")
    ios_text = xml_to_text(ios_xml, platform='ios')
    print(ios_text)

if __name__ == "__main__":
    # 测试XML到文本转换效果
    test_xml_to_text_conversion()
    
    print("\n" + "="*50)
    print("测试布局获取功能...")
    
    try:
        # 测试统一接口（文本格式）
        test_udid = "test_device_id"
        print(f"测试设备 {test_udid} 页面布局获取（文本格式）...")
        layout_result = get_page_layout(test_udid, "测试页面", output_format="text")
        print(f"布局获取结果: {layout_result}")
            
    except Exception as e:
        print(f"布局获取测试失败: {e}")
