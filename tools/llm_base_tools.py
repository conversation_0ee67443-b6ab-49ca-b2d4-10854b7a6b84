#!/usr/bin/env python3
"""
测试基础工具
提供统一的测试开始和结束接口，用于管理设备driver的生命周期
"""

import os
import sys
import time
import json
import glob
from typing import Optional, Dict, Any

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools._concurrent_log_manager import get_current_task_log_manager
from tools._device_status_manage_tools import get_device_status, update_device_status
from tools._device_driver_manage_tools import get_device_driver, close_device_driver


def _parse_timestamp_from_filename(filename: str) -> float:
    """
    从文件名解析时间戳，支持多种格式
    
    Args:
        filename: 文件名
        
    Returns:
        时间戳，如果解析失败返回0
    """
    import re
    from datetime import datetime
    
    # 格式1: YYYYMMDD_HHMMSS (judge_report)
    pattern1 = r'(\d{8}_\d{6})'
    match1 = re.search(pattern1, filename)
    if match1:
        try:
            dt = datetime.strptime(match1.group(1), '%Y%m%d_%H%M%S')
            return dt.timestamp()
        except:
            pass
    
    # 格式2: YYYY-MM-DD_HH-MM-SS (ios_tool, tunnel, wda)
    pattern2 = r'(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})'
    match2 = re.search(pattern2, filename)
    if match2:
        try:
            dt = datetime.strptime(match2.group(1), '%Y-%m-%d_%H-%M-%S')
            return dt.timestamp()
        except:
            pass
    
    # 格式3: YYYYMMDD_HHMMSS (layout, screenshot)
    pattern3 = r'(\d{8}_\d{6})'
    match3 = re.search(pattern3, filename)
    if match3:
        try:
            dt = datetime.strptime(match3.group(1), '%Y%m%d_%H%M%S')
            return dt.timestamp()
        except:
            pass
    
    return 0


def _cleanup_files_keep_recent(directory: str, file_pattern: str, device_name: str, keep_count: int = 20, file_type: str = "文件") -> Dict[str, Any]:
    """
    清理目录中的文件，保留最近的指定数量文件
    
    Args:
        directory: 目录路径
        file_pattern: 文件匹配模式
        device_name: 设备名称（用于过滤）
        keep_count: 保留文件数量
        file_type: 文件类型描述
        
    Returns:
        清理统计
    """
    stats = {
        "files_deleted": 0,
        "size_freed": 0,
        "errors": []
    }
    
    if not os.path.exists(directory):
        return stats
    
    try:
        # 获取所有匹配的文件
        all_files = glob.glob(os.path.join(directory, file_pattern))
        
        # 过滤设备相关的文件
        device_files = []
        for file_path in all_files:
            filename = os.path.basename(file_path)
            # 如果device_name为空，处理所有文件；否则只处理包含设备名称的文件
            if not device_name or device_name in filename:
                device_files.append(file_path)
        
        device_desc = device_name if device_name else "所有设备"
        if len(device_files) <= keep_count:
            get_current_task_log_manager().info_tools(
                f"{device_desc} 在 {directory} 中有 {len(device_files)} 个{file_type}，无需清理", 
                "cleanup_files_keep_recent"
            )
            return stats
        
        # 按时间排序（优先使用文件名中的时间戳，否则使用修改时间）
        files_with_time = []
        for file_path in device_files:
            filename = os.path.basename(file_path)
            timestamp = _parse_timestamp_from_filename(filename)
            if timestamp == 0:
                # 如果无法从文件名解析时间，使用文件修改时间
                timestamp = os.path.getmtime(file_path)
            files_with_time.append((file_path, timestamp))
        
        # 按时间戳排序（新到旧）
        files_with_time.sort(key=lambda x: x[1], reverse=True)
        
        # 删除超出保留数量的文件
        files_to_delete = files_with_time[keep_count:]
        
        for file_path, _ in files_to_delete:
            try:
                file_size = os.path.getsize(file_path)
                os.remove(file_path)
                stats["files_deleted"] += 1
                stats["size_freed"] += file_size
                get_current_task_log_manager().info_tools(f"已删除{file_type}: {file_path}", "cleanup_files_keep_recent")
            except Exception as e:
                error_msg = f"删除{file_type}失败 {file_path}: {e}"
                stats["errors"].append(error_msg)
                get_current_task_log_manager().warning_tools(error_msg, "cleanup_files_keep_recent")
        
        if stats["files_deleted"] > 0:
            get_current_task_log_manager().info_tools(
                f"{device_desc} 在 {directory} 中清理了 {stats['files_deleted']} 个{file_type}，保留最新的 {keep_count} 个", 
                "cleanup_files_keep_recent"
            )
    
    except Exception as e:
        error_msg = f"清理目录 {directory} 中的{file_type}时发生异常: {e}"
        stats["errors"].append(error_msg)
        get_current_task_log_manager().error_tools(error_msg, "cleanup_files_keep_recent")
    
    return stats


def _cleanup_test_files(udid: str, device_name: str) -> Dict[str, Any]:
    """
    清理指定设备的测试产物文件，保留最近的20个文件
    
    Args:
        udid: 设备UDID
        device_name: 设备名称
        
    Returns:
        清理结果统计
    """
    cleanup_stats = {
        "screenshot_files_deleted": 0,
        "layout_files_deleted": 0,
        "ios_tool_logs_deleted": 0,
        "appium_logs_deleted": 0,
        "judge_report_deleted": 0,
        "tunnel_logs_deleted": 0,
        "wda_logs_deleted": 0,
        "total_size_freed": 0,
        "errors": []
    }
    
    try:
        # 当前工作目录的根路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 清理配置：目录路径、文件模式、文件类型、统计字段
        cleanup_configs = [
            # 截图文件
            {
                "directory": os.path.join(project_root, "screenshot", device_name),
                "pattern": "*.png",
                "file_type": "截图文件",
                "stats_key": "screenshot_files_deleted"
            },
            # Layout日志文件
            {
                "directory": os.path.join(project_root, "log", "layout_log", device_name),
                "pattern": "*.txt",
                "file_type": "layout日志文件",
                "stats_key": "layout_files_deleted"
            },
            # iOS工具日志
            {
                "directory": os.path.join(project_root, "log", "ios_tool_logs"),
                "pattern": f"ios_tool_*{device_name}*.log",
                "file_type": "iOS工具日志",
                "stats_key": "ios_tool_logs_deleted"
            },
            # Appium日志（通过端口关联，需要特殊处理）
            {
                "directory": os.path.join(project_root, "log", "appium_logs"),
                "pattern": "appium_*.log",
                "file_type": "Appium日志",
                "stats_key": "appium_logs_deleted",
                "special_filter": True  # 需要特殊过滤逻辑
            },
            # Judge报告（可能不直接包含设备名，先跳过特殊处理）
            # Tunnel日志（不直接包含设备名，但可以通过时间关联）
            {
                "directory": os.path.join(project_root, "log", "tunnel_logs"),
                "pattern": "tunnel_*.log",
                "file_type": "Tunnel日志",
                "stats_key": "tunnel_logs_deleted",
                "device_agnostic": True  # 不区分设备
            },
            # WDA日志
            {
                "directory": os.path.join(project_root, "log", "wda_logs"),
                "pattern": f"wda_*{device_name}*.log",
                "file_type": "WDA日志",
                "stats_key": "wda_logs_deleted"
            }
        ]
        
        # 执行清理
        for config in cleanup_configs:
            if config.get("special_filter"):
                # Appium日志需要特殊处理，暂时跳过或使用修改时间
                continue
            elif config.get("device_agnostic"):
                # 不区分设备的日志，清理所有旧文件
                stats = _cleanup_files_keep_recent(
                    config["directory"], 
                    config["pattern"], 
                    "", 
                    keep_count=20, 
                    file_type=config["file_type"]
                )
            else:
                # 正常的设备相关文件清理
                stats = _cleanup_files_keep_recent(
                    config["directory"], 
                    config["pattern"], 
                    device_name, 
                    keep_count=20, 
                    file_type=config["file_type"]
                )
            
            # 汇总统计
            cleanup_stats[config["stats_key"]] = stats["files_deleted"]
            cleanup_stats["total_size_freed"] += stats["size_freed"]
            cleanup_stats["errors"].extend(stats["errors"])
        
        # 特殊处理Appium日志 - 保留最新20个，不区分设备
        appium_stats = _cleanup_files_keep_recent(
            os.path.join(project_root, "log", "appium_logs"),
            "appium_*.log",
            "",  # 不过滤设备
            keep_count=20,
            file_type="Appium日志"
        )
        cleanup_stats["appium_logs_deleted"] = appium_stats["files_deleted"]
        cleanup_stats["total_size_freed"] += appium_stats["size_freed"]
        cleanup_stats["errors"].extend(appium_stats["errors"])
        
        # Judge报告清理 - 保留最新20个，不区分设备
        judge_stats = _cleanup_files_keep_recent(
            os.path.join(project_root, "log", "judge_report"),
            "*.txt",
            "",  # 不过滤设备
            keep_count=20,
            file_type="Judge报告"
        )
        cleanup_stats["judge_report_deleted"] = judge_stats["files_deleted"]
        cleanup_stats["total_size_freed"] += judge_stats["size_freed"] 
        cleanup_stats["errors"].extend(judge_stats["errors"])
        
    except Exception as e:
        error_msg = f"清理测试文件时发生异常: {e}"
        cleanup_stats["errors"].append(error_msg)
        get_current_task_log_manager().error_tools(error_msg, "cleanup_test_files")
    
    return cleanup_stats


def start_test(udid: str) -> str:
    """
    开始测试 - 为指定设备创建和维护driver，并重启应用
    
    Args:
        udid: 设备UDID
        
    Returns:
        JSON字符串，包含操作结果
    """
    try:
        get_current_task_log_manager().info_tools(f"开始为设备 {udid} 启动测试环境", "start_test")
        
        # 检查设备状态
        device_status = get_device_status(udid)
        if not device_status:
            result = {
                "status": "error",
                "udid": udid,
                "message": f"设备 {udid} 状态文件不存在，无法启动测试"
            }
            get_current_task_log_manager().error_tools(result["message"], "start_test")
            return json.dumps(result, ensure_ascii=False)
        
        # 检查设备是否为testing状态
        current_status = device_status.get("status", "unknown")
        if current_status != "testing":
            result = {
                "status": "error",
                "udid": udid,
                "current_status": current_status,
                "message": f"设备 {udid} 当前状态为 {current_status}，不是 testing 状态，无法启动测试"
            }
            get_current_task_log_manager().error_tools(result["message"], "start_test")
            return json.dumps(result, ensure_ascii=False)
        
        # 获取设备信息
        device_name = device_status.get("device_name", "Unknown")
        platform = device_status.get("platform", "unknown")
        
        # 先重启应用
        get_current_task_log_manager().info_tools(f"正在重启设备 {udid} 的应用", "start_test")
        try:
            from tools.app_operate_tools import restart_app
            restart_result = restart_app(udid)
            if restart_result == "success":
                get_current_task_log_manager().info_tools(f"成功重启设备 {udid} 的应用", "start_test")
            else:
                get_current_task_log_manager().warning_tools(f"重启设备 {udid} 应用失败: {restart_result}", "start_test")
        except Exception as e:
            get_current_task_log_manager().error_tools(f"重启设备 {udid} 应用异常: {e}", "start_test")
        
        # 创建并缓存driver
        driver = get_device_driver(udid, auto_create=True)
        if not driver:
            result = {
                "status": "error",
                "udid": udid,
                "device_name": device_name,
                "platform": platform,
                "message": f"无法为设备 {udid} ({device_name}) 创建driver"
            }
            get_current_task_log_manager().error_tools(result["message"], "start_test")
            return json.dumps(result, ensure_ascii=False)
        
        # 更新设备状态，添加测试开始时间
        update_device_status(udid, {
            "test_start_time": time.time(),
            "driver_created": True,
            "driver_created_time": time.time()
        })
        
        # 如果是iOS设备，计算并更新scale_ratio
        if platform.lower() == 'ios':
            try:
                # 检查是否已经计算过scale_ratio
                current_scale_ratio = device_status.get('scale_ratio', 1.0)
                if current_scale_ratio != 1.0:
                    get_current_task_log_manager().info_tools(f"设备 {udid} 的scale_ratio已存在: {current_scale_ratio}，跳过计算", "start_test")
                else:
                    get_current_task_log_manager().info_tools(f"开始计算设备 {udid} 的scale_ratio", "start_test")
                    
                    # 获取屏幕尺寸和缩放比例
                    screen_size = driver.get_window_size()
                    get_current_task_log_manager().info_tools(f"设备 {udid} 实际屏幕尺寸: {screen_size}", "start_test")
                    
                    # 临时截图来计算缩放比例
                    screenshot_save_path = device_status.get('screenshot_save_path', '')
                    if not screenshot_save_path:
                        get_current_task_log_manager().warning_tools(f"设备 {udid} 未设置截图保存路径，跳过scale_ratio计算", "start_test")
                    else:
                        # 确保截图目录存在
                        os.makedirs(screenshot_save_path, exist_ok=True)
                        
                        timestamp = time.strftime('%Y-%m-%d_%H:%M:%S')
                        temp_screenshot_path = os.path.join(screenshot_save_path, f'临时_缩放比例计算_{device_name}_{timestamp}.png')
                        
                        # 使用driver截图
                        screenshot_success = driver.get_screenshot_as_file(temp_screenshot_path)
                        
                        if screenshot_success and os.path.exists(temp_screenshot_path):
                            # 使用PIL获取截图尺寸
                            try:
                                from PIL import Image
                                screenshot_image = Image.open(temp_screenshot_path)
                                screenshot_size = screenshot_image.size
                                get_current_task_log_manager().info_tools(f"设备 {udid} 截图尺寸: {screenshot_size}", "start_test")
                                
                                # 计算缩放比例
                                width_ratio = screenshot_size[0] / screen_size['width']
                                height_ratio = screenshot_size[1] / screen_size['height']
                                scale_ratio = (width_ratio + height_ratio) / 2
                                
                                get_current_task_log_manager().info_tools(f"设备 {udid} 计算得到新的缩放比例: {scale_ratio}", "start_test")
                                
                                # 更新设备状态中的scale_ratio
                                update_device_status(udid, {"scale_ratio": scale_ratio})
                                
                                # 删除临时截图文件
                                try:
                                    os.remove(temp_screenshot_path)
                                    get_current_task_log_manager().info_tools(f"已删除临时截图文件: {temp_screenshot_path}", "start_test")
                                except Exception as e:
                                    get_current_task_log_manager().warning_tools(f"删除临时截图文件失败: {e}", "start_test")
                                
                            except ImportError:
                                get_current_task_log_manager().error_tools("PIL库未安装，无法计算scale_ratio", "start_test")
                            except Exception as e:
                                get_current_task_log_manager().error_tools(f"处理截图文件时出错: {e}", "start_test")
                        else:
                            get_current_task_log_manager().warning_tools(f"设备 {udid} 截图失败，无法计算scale_ratio", "start_test")
                            
            except Exception as e:
                get_current_task_log_manager().error_tools(f"计算设备 {udid} 的scale_ratio时出错: {e}", "start_test")
        
        result = {
            "status": "success",
            "udid": udid,
            "device_name": device_name,
            "platform": platform,
            "message": f"成功为设备 {udid} ({device_name}) 启动测试环境，driver已创建并缓存"
        }
        
        get_current_task_log_manager().info_tools(result["message"], "start_test")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        result = {
            "status": "error",
            "udid": udid,
            "error": str(e),
            "message": f"启动设备 {udid} 测试环境时出错: {str(e)}"
        }
        get_current_task_log_manager().error_tools(result["message"], "start_test")
        return json.dumps(result, ensure_ascii=False)


def end_test(udid: str) -> str:
    """
    结束测试 - 关闭指定设备的driver并清理资源
    
    功能：
    1. 关闭设备的WebDriver连接
    2. 清理测试过程中产生的文件（截图、layout日志等）
    3. 更新设备状态为就绪
    4. 记录测试时长和清理统计
    
    清理规则：
    - 截图文件：删除 screenshot/{device_name}/ 目录下的所有文件
    - Layout文件：删除 log/layout_log/{device_name}/ 目录下的所有文件
    - 临时文件：删除包含设备UDID的临时文件
    
    Args:
        udid: 设备UDID
        
    Returns:
        JSON字符串，包含操作结果和清理统计：
        - status: "success" 或 "error"
        - cleanup_stats: 清理统计信息
        - message: 操作结果描述
    """
    try:
        get_current_task_log_manager().info_tools(f"开始为设备 {udid} 结束测试环境", "end_test")
        
        # 检查设备状态
        device_status = get_device_status(udid)
        if not device_status:
            result = {
                "status": "error",
                "udid": udid,
                "message": f"设备 {udid} 状态文件不存在，无法结束测试"
            }
            get_current_task_log_manager().error_tools(result["message"], "end_test")
            return json.dumps(result, ensure_ascii=False)
        
        # 获取设备信息
        device_name = device_status.get("device_name", "Unknown")
        platform = device_status.get("platform", "unknown")
        test_start_time = device_status.get("test_start_time", 0)
        
        # 关闭driver
        close_success = close_device_driver(udid)
        if not close_success:
            get_current_task_log_manager().warning_tools(f"关闭设备 {udid} 的driver时可能出现问题", "end_test")
        
        # 清理测试产物文件
        get_current_task_log_manager().info_tools(f"开始清理设备 {udid} ({device_name}) 的测试产物文件", "end_test")
        cleanup_stats = _cleanup_test_files(udid, device_name)
        
        # 记录清理结果
        total_files = (cleanup_stats["screenshot_files_deleted"] + 
                      cleanup_stats["layout_files_deleted"] + 
                      cleanup_stats["ios_tool_logs_deleted"] +
                      cleanup_stats["appium_logs_deleted"] +
                      cleanup_stats["judge_report_deleted"] +
                      cleanup_stats["tunnel_logs_deleted"] +
                      cleanup_stats["wda_logs_deleted"])
        
        if total_files > 0:
            size_mb = cleanup_stats["total_size_freed"] / (1024 * 1024)
            cleanup_details = []
            if cleanup_stats["screenshot_files_deleted"] > 0:
                cleanup_details.append(f"截图: {cleanup_stats['screenshot_files_deleted']}")
            if cleanup_stats["layout_files_deleted"] > 0:
                cleanup_details.append(f"layout: {cleanup_stats['layout_files_deleted']}")
            if cleanup_stats["ios_tool_logs_deleted"] > 0:
                cleanup_details.append(f"iOS工具日志: {cleanup_stats['ios_tool_logs_deleted']}")
            if cleanup_stats["appium_logs_deleted"] > 0:
                cleanup_details.append(f"Appium日志: {cleanup_stats['appium_logs_deleted']}")
            if cleanup_stats["judge_report_deleted"] > 0:
                cleanup_details.append(f"Judge报告: {cleanup_stats['judge_report_deleted']}")
            if cleanup_stats["tunnel_logs_deleted"] > 0:
                cleanup_details.append(f"Tunnel日志: {cleanup_stats['tunnel_logs_deleted']}")
            if cleanup_stats["wda_logs_deleted"] > 0:
                cleanup_details.append(f"WDA日志: {cleanup_stats['wda_logs_deleted']}")
            
            get_current_task_log_manager().info_tools(
                f"设备 {udid} 清理完成: 删除 {total_files} 个文件 ({', '.join(cleanup_details)}), "
                f"释放空间: {size_mb:.2f}MB", 
                "end_test"
            )
        else:
            get_current_task_log_manager().info_tools(f"设备 {udid} 无需清理的测试文件", "end_test")
        
        # 如果有清理错误，记录警告
        if cleanup_stats["errors"]:
            get_current_task_log_manager().warning_tools(
                f"设备 {udid} 文件清理过程中发生 {len(cleanup_stats['errors'])} 个错误", 
                "end_test"
            )
        
        # 计算测试时长
        test_duration = 0
        if test_start_time > 0:
            test_duration = time.time() - test_start_time
        
        # 更新设备状态为就绪
        update_device_status(udid, {
            "status": "ready",
            "test_duration": test_duration,
            "test_start_time": 0,
            "driver_created": False,
            "start_time": 0.0  # 重置开始时间
        })
        
        # 构建成功结果，包含清理统计
        cleanup_summary = ""
        if total_files > 0:
            size_mb = cleanup_stats["total_size_freed"] / (1024 * 1024)
            cleanup_summary = f"，清理了 {total_files} 个测试文件，释放空间 {size_mb:.2f}MB"
        
        result = {
            "status": "success",
            "udid": udid,
            "device_name": device_name,
            "platform": platform,
            "test_duration": test_duration,
            "cleanup_stats": {
                "screenshot_files_deleted": cleanup_stats["screenshot_files_deleted"],
                "layout_files_deleted": cleanup_stats["layout_files_deleted"],
                "ios_tool_logs_deleted": cleanup_stats["ios_tool_logs_deleted"],
                "appium_logs_deleted": cleanup_stats["appium_logs_deleted"],
                "judge_report_deleted": cleanup_stats["judge_report_deleted"],
                "tunnel_logs_deleted": cleanup_stats["tunnel_logs_deleted"],
                "wda_logs_deleted": cleanup_stats["wda_logs_deleted"],
                "total_files_deleted": total_files,
                "total_size_freed_mb": cleanup_stats["total_size_freed"] / (1024 * 1024),
                "errors_count": len(cleanup_stats["errors"])
            },
            "message": f"成功结束设备 {udid} ({device_name}) 的测试环境，测试时长: {test_duration:.1f}秒{cleanup_summary}"
        }
        
        get_current_task_log_manager().info_tools(result["message"], "end_test")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        result = {
            "status": "error",
            "udid": udid,
            "error": str(e),
            "message": f"结束设备 {udid} 测试环境时出错: {str(e)}"
        }
        get_current_task_log_manager().error_tools(result["message"], "end_test")
        return json.dumps(result, ensure_ascii=False)


def get_test_driver(udid: str) -> Optional[object]:
    """
    获取测试driver（供其他工具使用）
    
    Args:
        udid: 设备UDID
        
    Returns:
        driver实例或None
    """
    try:
        # 检查设备状态
        device_status = get_device_status(udid)
        if not device_status:
            get_current_task_log_manager().error_tools(f"设备 {udid} 状态文件不存在", "get_test_driver")
            return None
        
        # 检查是否已创建driver
        driver_created = device_status.get("driver_created", False)
        if not driver_created:
            get_current_task_log_manager().error_tools(f"设备 {udid} 尚未启动测试环境，请先调用 start_test", "get_test_driver")
            return None
        
        # 获取缓存的driver（允许自动重新创建）
        # 添加重试逻辑，因为新创建的driver可能需要时间初始化
        for attempt in range(3):
            driver = get_device_driver(udid, auto_create=True)
            if driver:
                get_current_task_log_manager().info_tools(f"成功获取设备 {udid} 的driver: {type(driver)} (尝试 {attempt + 1}/3)", "get_test_driver")
                return driver
            else:
                get_current_task_log_manager().warning_tools(f"设备 {udid} 的driver获取失败，尝试 {attempt + 1}/3", "get_test_driver")
                if attempt < 2:  # 不是最后一次尝试
                    time.sleep(1)  # 等待1秒后重试
        
        get_current_task_log_manager().error_tools(f"设备 {udid} 的driver创建失败，已重试3次", "get_test_driver")
        return None
        
    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取设备 {udid} 的测试driver时出错: {e}", "get_test_driver")
        return None


def record_summary(summary_text: str, summary_type: str = "步骤总结") -> str:
    """
    记录Agent的实时流程总结和想法（用于正常的测试流程记录）
    
    功能：
    1. 将Agent生成的流程总结文本记录到当前会话的日志中
    2. 支持记录步骤完成后的想法、后续计划、最终结论等
    3. 便于追踪Agent的思考过程和决策路径
    
    Args:
        summary_text: Agent生成的流程总结文本（如：当前步骤结果、测试进展、下一步计划等）
        summary_type: 总结类型（如："步骤总结"、"流程分析"、"计划制定"、"最终结论"等）
        
    Returns:
        JSON字符串，包含记录结果
    """
    try:
        get_current_task_log_manager().info_tools(f"记录Agent流程{summary_type}...", "record_summary")
        
        # 记录到日志系统
        get_current_task_log_manager().info_agent(f"📝 {summary_type}: {summary_text}")
        
        # 记录详细的结构化信息
        metadata = {
            "timestamp": time.time(),
            "summary_source": "agent_realtime_summary",
            "summary_type": summary_type,
            "summary_text": summary_text
        }
        
        get_current_task_log_manager().info_tools(f"记录详细信息: {json.dumps(metadata, ensure_ascii=False)}", "record_summary")
        
        result = {
            "status": "success",
            "summary_text": summary_text,
            "summary_type": summary_type,
            "timestamp": time.time(),
            "message": f"成功记录Agent流程{summary_type}"
        }
        
        get_current_task_log_manager().info_tools(f"Agent流程{summary_type}已记录", "record_summary")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        result = {
            "status": "error",
            "summary_text": summary_text,
            "summary_type": summary_type,
            "error": str(e),
            "message": f"记录Agent流程{summary_type}失败: {str(e)}"
        }
        get_current_task_log_manager().error_tools(result["message"], "record_summary")
        return json.dumps(result, ensure_ascii=False)


def record_issue(issue_text: str, issue_type: str = "测试问题", severity: str = "中等") -> str:
    """
    记录测试过程中发现的问题（仅在出现问题时使用）
    
    功能：
    1. 专门用于记录测试过程中发现的异常、bug、问题等
    2. 支持问题分类和严重程度标记
    3. 在日志中以醒目的方式展示问题
    
    Args:
        issue_text: 发现的具体问题描述（如：页面展示异常、功能无法使用、性能问题等）
        issue_type: 问题类型，可选值：
                   - "功能异常": 功能不可用或行为异常
                   - "UI问题": 界面展示问题、布局异常等
                   - "性能问题": 响应慢、卡顿等性能相关
                   - "兼容性问题": 设备或版本兼容性问题
                   - "数据问题": 数据错误、缺失等
                   - "其他问题": 其他未分类问题
        severity: 问题严重程度，可选值："严重"、"中等"、"轻微"
        
    Returns:
        JSON字符串，包含记录结果
    """
    try:
        get_current_task_log_manager().warning_tools(f"记录测试问题 [{issue_type}] - 严重程度: {severity}", "record_issue")
        
        # 根据严重程度选择不同的日志级别
        if severity == "严重":
            get_current_task_log_manager().error_agent(f"🚨 {issue_type}: {issue_text}")
        elif severity == "中等":
            get_current_task_log_manager().warning_agent(f"⚠️ {issue_type}: {issue_text}")
        else:
            get_current_task_log_manager().info_agent(f"ℹ️ {issue_type}: {issue_text}")
        
        # 记录详细的结构化信息
        metadata = {
            "timestamp": time.time(),
            "issue_source": "agent_detected_issue",
            "issue_type": issue_type,
            "severity": severity,
            "issue_text": issue_text
        }
        
        get_current_task_log_manager().warning_tools(f"记录问题详细信息: {json.dumps(metadata, ensure_ascii=False)}", "record_issue")
        
        result = {
            "status": "success",
            "issue_text": issue_text,
            "issue_type": issue_type,
            "severity": severity,
            "timestamp": time.time(),
            "message": f"成功记录测试问题 [{issue_type}] - 严重程度: {severity}"
        }
        
        get_current_task_log_manager().warning_tools(f"测试问题已记录", "record_issue")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        result = {
            "status": "error",
            "issue_text": issue_text,
            "issue_type": issue_type,
            "severity": severity,
            "error": str(e),
            "message": f"记录测试问题失败: {str(e)}"
        }
        get_current_task_log_manager().error_tools(result["message"], "record_issue")
        return json.dumps(result, ensure_ascii=False)


def test_cleanup_function():
    """
    测试清理功能（保留最新20个文件的逻辑）
    """
    print("测试优化后的清理功能...")
    
    # 测试参数
    test_udid = "00008101-00166C3811BA001E"
    test_device_name = "iPhone_12"
    
    # 调用清理函数
    print(f"测试清理设备 {test_udid} ({test_device_name}) 的文件...")
    cleanup_stats = _cleanup_test_files(test_udid, test_device_name)
    
    print(f"清理统计结果:")
    print(f"  截图文件删除: {cleanup_stats['screenshot_files_deleted']}")
    print(f"  布局文件删除: {cleanup_stats['layout_files_deleted']}")
    print(f"  iOS工具日志删除: {cleanup_stats['ios_tool_logs_deleted']}")
    print(f"  Appium日志删除: {cleanup_stats['appium_logs_deleted']}")
    print(f"  Judge报告删除: {cleanup_stats['judge_report_deleted']}")
    print(f"  Tunnel日志删除: {cleanup_stats['tunnel_logs_deleted']}")
    print(f"  WDA日志删除: {cleanup_stats['wda_logs_deleted']}")
    
    total_files = (cleanup_stats['screenshot_files_deleted'] + 
                  cleanup_stats['layout_files_deleted'] + 
                  cleanup_stats['ios_tool_logs_deleted'] +
                  cleanup_stats['appium_logs_deleted'] +
                  cleanup_stats['judge_report_deleted'] +
                  cleanup_stats['tunnel_logs_deleted'] +
                  cleanup_stats['wda_logs_deleted'])
    
    print(f"  总计删除文件: {total_files}")
    print(f"  释放空间: {cleanup_stats['total_size_freed'] / (1024*1024):.2f}MB")
    print(f"  错误数量: {len(cleanup_stats['errors'])}")
    
    if cleanup_stats['errors']:
        print("  错误详情:")
        for error in cleanup_stats['errors']:
            print(f"    - {error}")
    
    return cleanup_stats


if __name__ == "__main__":
    # 测试用例
    print("测试基础工具测试...")
    
    # 测试记录功能
    print("\n测试记录功能...")
    
    # 测试记录总结
    summary_result = record_summary("测试步骤1已完成，界面显示正常", "步骤总结")
    print(f"记录总结结果: {summary_result}")
    
    # 测试记录问题
    issue_result = record_issue("发现按钮点击无响应", "功能异常", "严重")
    print(f"记录问题结果: {issue_result}")
    
    # 测试清理功能
    print("\n测试清理功能...")
    test_cleanup_function()
    
    print("功能测试完成")