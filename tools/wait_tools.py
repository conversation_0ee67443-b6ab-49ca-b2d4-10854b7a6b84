import json
import time
import datetime
import sys
import os

# 添加父目录到路径，以便导入log_manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager


def wait_for_seconds(seconds: int = 5) -> str:
    """等待指定的秒数
    
    Args:
        seconds: 等待的秒数，默认为5秒，范围1-60秒
        
    Returns:
        等待完成的信息的JSON字符串
    """
    # 参数验证
    if not isinstance(seconds, int):
        try:
            seconds = int(seconds)
        except (ValueError, TypeError):
            result = {
                "status": "error",
                "error": "参数类型错误",
                "message": "等待时间必须是整数"
            }
            get_current_task_log_manager().error_tools("参数类型错误: 等待时间必须是整数", "wait_for_seconds")
            return json.dumps(result, ensure_ascii=False)
    
    # 限制等待时间范围
    if seconds < 1:
        seconds = 1
        get_current_task_log_manager().warning_tools(f"等待时间过短，自动调整为 1 秒", "wait_for_seconds")
    elif seconds > 60:
        seconds = 60
        get_current_task_log_manager().warning_tools(f"等待时间过长，自动调整为 60 秒", "wait_for_seconds")
    
    try:
        start_time = datetime.datetime.now()
        get_current_task_log_manager().info_tools(f"开始等待 {seconds} 秒", "wait_for_seconds")
        
        # 执行等待
        time.sleep(seconds)
        
        end_time = datetime.datetime.now()
        actual_duration = (end_time - start_time).total_seconds()
        
        result = {
            "status": "success",
            "requested_seconds": seconds,
            "actual_duration": round(actual_duration, 2),
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "message": f"成功等待了 {round(actual_duration, 2)} 秒"
        }
        
        get_current_task_log_manager().info_tools(f"等待完成，实际耗时: {round(actual_duration, 2)} 秒", "wait_for_seconds")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        result = {
            "status": "error",
            "error": str(e),
            "message": f"等待过程中出错: {str(e)}"
        }
        get_current_task_log_manager().error_tools(f"等待过程中出错: {str(e)}", "wait_for_seconds")
        return json.dumps(result, ensure_ascii=False)


def wait_with_progress(seconds: int = 5, show_progress: bool = True) -> str:
    """带进度显示的等待功能
    
    Args:
        seconds: 等待的秒数，默认为5秒，范围1-60秒
        show_progress: 是否显示进度，默认为True
        
    Returns:
        等待完成的信息的JSON字符串
    """
    # 参数验证
    if not isinstance(seconds, int):
        try:
            seconds = int(seconds)
        except (ValueError, TypeError):
            result = {
                "status": "error",
                "error": "参数类型错误",
                "message": "等待时间必须是整数"
            }
            get_current_task_log_manager().error_tools("参数类型错误: 等待时间必须是整数", "wait_with_progress")
            return json.dumps(result, ensure_ascii=False)
    
    # 限制等待时间范围
    if seconds < 1:
        seconds = 1
        get_current_task_log_manager().warning_tools(f"等待时间过短，自动调整为 1 秒", "wait_with_progress")
    elif seconds > 60:
        seconds = 60
        get_current_task_log_manager().warning_tools(f"等待时间过长，自动调整为 60 秒", "wait_with_progress")
    
    try:
        start_time = datetime.datetime.now()
        get_current_task_log_manager().info_tools(f"开始带进度等待 {seconds} 秒", "wait_with_progress")
        
        progress_steps = []
        
        # 分段等待，记录进度
        step_size = max(1, seconds // 5)  # 最多5个进度点
        current_step = 0
        
        while current_step < seconds:
            remaining_time = min(step_size, seconds - current_step)
            time.sleep(remaining_time)
            current_step += remaining_time
            
            progress = round((current_step / seconds) * 100, 1)
            progress_steps.append({
                "elapsed": current_step,
                "progress": progress,
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            if show_progress:
                get_current_task_log_manager().info_tools(f"等待进度: {progress}% ({current_step}/{seconds}秒)", "wait_with_progress")
        
        end_time = datetime.datetime.now()
        actual_duration = (end_time - start_time).total_seconds()
        
        result = {
            "status": "success",
            "requested_seconds": seconds,
            "actual_duration": round(actual_duration, 2),
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "progress_steps": progress_steps,
            "show_progress": show_progress,
            "message": f"成功完成带进度等待 {round(actual_duration, 2)} 秒，共 {len(progress_steps)} 个进度点"
        }
        
        get_current_task_log_manager().info_tools(f"带进度等待完成，实际耗时: {round(actual_duration, 2)} 秒", "wait_with_progress")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        result = {
            "status": "error",
            "error": str(e),
            "message": f"带进度等待过程中出错: {str(e)}"
        }
        get_current_task_log_manager().error_tools(f"带进度等待过程中出错: {str(e)}", "wait_with_progress")
        return json.dumps(result, ensure_ascii=False)


if __name__ == "__main__":
    """测试等待工具"""
    print("🧪 测试基础等待功能...")
    result1 = wait_for_seconds(3)
    print(f"结果: {result1}")
    
    print("\n🧪 测试带进度等待功能...")
    result2 = wait_with_progress(5, True)
    print(f"结果: {result2}")
