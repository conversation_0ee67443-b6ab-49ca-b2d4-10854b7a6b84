#!/usr/bin/env python3
"""
设备驱动管理工具
用于创建和管理 iOS 和 Android 设备的 Appium WebDriver 连接
"""

import os
import sys
import time
import threading
from typing import Optional, Dict, Any, Union
from appium import webdriver
from appium.options.ios import XCUITestOptions
from appium.options.android import UiAutomator2Options
import urllib3

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools._concurrent_log_manager import get_current_task_log_manager
from tools._device_status_manage_tools import get_device_status, get_app_id
from tools.check_devices_tools import get_ios_device_name, get_android_device_name


class DeviceDriverManager:
    """统一设备驱动管理器"""

    def __init__(self):
        self.active_drivers = {}  # udid -> driver 实例
        self.keepalive_threads = {}  # udid -> 保活线程
        self.keepalive_stop_events = {}  # udid -> 停止事件
        self.keepalive_interval = 10  # 保活间隔（秒），增加到10秒减少连接频率
        self.keepalive_counters = {}  # udid -> 保活计数器

        # 配置urllib3连接池，解决保活机制导致的连接池满问题
        self._configure_urllib3_pool()

    def _configure_urllib3_pool(self):
        """配置urllib3连接池，解决保活机制导致的连接池满问题"""
        try:
            # 禁用urllib3的所有警告，包括连接池满警告
            urllib3.disable_warnings()

            # 设置日志级别，减少urllib3的警告输出
            import logging
            logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)

            get_current_task_log_manager().info_tools("已配置urllib3日志级别，减少保活机制连接池警告", "_configure_urllib3_pool")

        except Exception as e:
            get_current_task_log_manager().warning_tools(f"配置urllib3失败: {e}", "_configure_urllib3_pool")

    def create_ios_driver(self, udid: str, device_name: str = None, platform_version: str = None,
                         bundle_id: str = None, appium_port: int = None) -> Optional[webdriver.Remote]:
        """
        创建 iOS 设备的 Appium WebDriver 实例
        
        Args:
            udid: 设备 UDID
            device_name: 设备名称（可选，会从设备状态文件获取）
            platform_version: iOS 版本（可选，会从设备状态文件获取）
            bundle_id: 应用包ID（可选，会从设备状态文件获取）
            appium_port: Appium 端口（可选，会从设备状态文件获取）
            
        Returns:
            WebDriver 实例或 None（如果创建失败）
        """
        try:
            # 从设备状态文件获取配置信息
            device_status = get_device_status(udid)
            if not device_status:
                get_current_task_log_manager().error_tools(f"设备 {udid} 状态文件不存在", "create_ios_driver")
                return None
            
            # 使用提供的参数或从设备状态文件获取
            device_name = device_name or device_status.get('device_name', 'Unknown')
            platform_version = platform_version or device_status.get('system_version', '18.0')
            bundle_id = bundle_id or get_app_id(udid) or 'com.meituan.imeituan'
            appium_port = appium_port or device_status.get('appium_port', 4723)
            # WDA固定运行在设备8100端口
            wda_device_port = 8100
            
            get_current_task_log_manager().info_tools(f"创建iOS设备 {device_name} ({udid}) 的driver", "create_ios_driver")
            get_current_task_log_manager().info_tools(f"配置信息: appium_port={appium_port}, wda_device_port={wda_device_port} (固定), bundle_id={bundle_id}", "create_ios_driver")
            
            # 创建 XCUITest 选项
            options = XCUITestOptions()
            options.device_name = device_name
            options.platform_version = platform_version
            options.udid = udid
            options.automation_name = 'XCUITest'
            options.bundle_id = bundle_id
            options.no_reset = True
            
            # 设置WDA设备端口 - WDA固定运行在设备8100端口
            options.wda_local_port = wda_device_port
            
            # 使用预构建的WDA，跳过构建过程
            options.use_prebuilt_wda = True
            
            # 防止Appium尝试启动WDA，因为我们已经通过ios工具启动了WDA
            options.use_new_wda = False
            options.wda_startup_retries = 0
            options.wda_startup_retry_interval = 0
            
            # 设置超时参数，防止会话过早关闭
            options.new_command_timeout = 600  # 10分钟无命令超时
            options.session_override = True    # 允许会话覆盖
            
            # 创建 WebDriver 实例 - 连接到Appium Hub
            appium_server_url = f"http://localhost:{appium_port}/wd/hub"
            driver = webdriver.Remote(appium_server_url, options=options)
            
            # 缓存 driver 实例
            self.active_drivers[udid] = driver

            # 启动保活机制
            self._start_keepalive(udid, 'ios')

            get_current_task_log_manager().info_tools(f"成功创建iOS设备 {device_name} ({udid}) 的driver", "create_ios_driver")
            return driver
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"创建iOS设备 {udid} 的driver失败: {e}", "create_ios_driver")
            return None
    
    def create_android_driver(self, udid: str, device_name: str = None, package_name: str = None, 
                             appium_port: int = None) -> Optional[webdriver.Remote]:
        """
        创建 Android 设备的 Appium WebDriver 实例
        
        Args:
            udid: 设备 UDID
            device_name: 设备名称（可选，会从设备状态文件获取）
            package_name: 应用包名（可选，会从设备状态文件获取）
            appium_port: Appium 端口（可选，会从设备状态文件获取）
            
        Returns:
            WebDriver 实例或 None（如果创建失败）
        """
        try:
            # 从设备状态文件获取配置信息
            device_status = get_device_status(udid)
            if not device_status:
                get_current_task_log_manager().error_tools(f"设备 {udid} 状态文件不存在", "create_android_driver")
                return None
            
            # 使用提供的参数或从设备状态文件获取
            device_name = device_name or device_status.get('device_name', 'Unknown')
            package_name = package_name or get_app_id(udid) or 'com.sankuai.meituan'
            appium_port = appium_port or device_status.get('appium_port', 4723)
            
            get_current_task_log_manager().info_tools(f"创建Android设备 {device_name} ({udid}) 的driver", "create_android_driver")
            get_current_task_log_manager().info_tools(f"配置信息: appium_port={appium_port}, package_name={package_name}", "create_android_driver")
            
            # 创建 UiAutomator2 选项
            options = UiAutomator2Options()
            options.udid = udid
            options.automation_name = 'UiAutomator2'
            options.app_package = package_name
            options.no_reset = True

            # 设置超时参数，防止会话过早关闭
            options.new_command_timeout = 600  # 10分钟无命令超时
            
            # 创建 WebDriver 实例
            appium_server_url = f"http://localhost:{appium_port}/wd/hub"
            driver = webdriver.Remote(appium_server_url, options=options)
            
            # 缓存 driver 实例
            self.active_drivers[udid] = driver

            # 启动保活机制
            self._start_keepalive(udid, 'android')

            get_current_task_log_manager().info_tools(f"成功创建Android设备 {device_name} ({udid}) 的driver", "create_android_driver")
            return driver
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"创建Android设备 {udid} 的driver失败: {e}", "create_android_driver")
            return None
    
    def create_driver(self, udid: str, platform: str = None, **kwargs) -> Optional[webdriver.Remote]:
        """
        根据平台创建对应的 WebDriver 实例
        
        Args:
            udid: 设备 UDID
            platform: 平台类型（可选，会从设备状态文件获取）
            **kwargs: 其他配置参数
            
        Returns:
            WebDriver 实例或 None
        """
        try:
            # 从设备状态文件获取平台信息
            if not platform:
                device_status = get_device_status(udid)
                if not device_status:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 状态文件不存在", "create_driver")
                    return None
                platform = device_status.get('platform', '').lower()
            
            if platform == 'ios':
                return self.create_ios_driver(udid, **kwargs)
            elif platform == 'android':
                return self.create_android_driver(udid, **kwargs)
            else:
                get_current_task_log_manager().error_tools(f"不支持的平台类型: {platform}", "create_driver")
                return None
                
        except Exception as e:
            get_current_task_log_manager().error_tools(f"创建设备 {udid} 的driver失败: {e}", "create_driver")
            return None
    
    def get_driver(self, udid: str, auto_create: bool = True) -> Optional[webdriver.Remote]:
        """
        获取指定设备的 WebDriver 实例
        
        Args:
            udid: 设备 UDID
            auto_create: 如果不存在是否自动创建
            
        Returns:
            WebDriver 实例或 None
        """
        try:
            # 检查是否已有活跃的 driver
            if udid in self.active_drivers:
                driver = self.active_drivers[udid]
                # 测试 driver 是否仍然可用
                try:
                    # 使用 get_status() 方法进行健康检查
                    status = driver.get_status()
                    if status.get('ready', False):
                        get_current_task_log_manager().info_tools(f"复用已有的设备 {udid} driver，状态: {status.get('message', 'Unknown')}", "get_driver")
                        return driver
                    else:
                        get_current_task_log_manager().warning_tools(f"设备 {udid} driver状态不正常: {status}", "get_driver")
                        # 清理状态不正常的 driver
                        self.close_driver(udid)
                except Exception as e:
                    get_current_task_log_manager().warning_tools(f"已有的设备 {udid} driver失效，将重新创建，错误: {e}", "get_driver")
                    # 清理失效的 driver
                    self.close_driver(udid)
            
            # 如果需要自动创建
            if auto_create:
                get_current_task_log_manager().info_tools(f"为设备 {udid} 创建新的driver", "get_driver")
                return self.create_driver(udid)
            else:
                get_current_task_log_manager().info_tools(f"设备 {udid} 没有活跃的driver且不自动创建", "get_driver")
                return None
                
        except Exception as e:
            get_current_task_log_manager().error_tools(f"获取设备 {udid} 的driver失败: {e}", "get_driver")
            return None
    
    def close_driver(self, udid: str) -> bool:
        """
        关闭指定设备的 WebDriver 实例
        
        Args:
            udid: 设备 UDID
            
        Returns:
            操作是否成功
        """
        try:
            if udid in self.active_drivers:
                # 停止保活线程
                self._stop_keepalive(udid)

                driver = self.active_drivers[udid]
                try:
                    driver.quit()
                    get_current_task_log_manager().info_tools(f"成功关闭设备 {udid} 的driver", "close_driver")
                except Exception as e:
                    get_current_task_log_manager().warning_tools(f"关闭设备 {udid} 的driver时出错: {e}", "close_driver")
                finally:
                    del self.active_drivers[udid]
                return True
            else:
                get_current_task_log_manager().info_tools(f"设备 {udid} 没有活跃的driver", "close_driver")
                return True
                
        except Exception as e:
            get_current_task_log_manager().error_tools(f"关闭设备 {udid} 的driver失败: {e}", "close_driver")
            return False
    
    def close_all_drivers(self) -> int:
        """
        关闭所有活跃的 WebDriver 实例
        
        Returns:
            成功关闭的 driver 数量
        """
        closed_count = 0
        udids_to_close = list(self.active_drivers.keys())
        
        for udid in udids_to_close:
            if self.close_driver(udid):
                closed_count += 1
        
        get_current_task_log_manager().info_tools(f"关闭了 {closed_count} 个设备的driver", "close_all_drivers")
        return closed_count
    
    def get_active_drivers_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有活跃 driver 的信息
        
        Returns:
            包含 driver 信息的字典
        """
        info = {}
        for udid, driver in self.active_drivers.items():
            try:
                # 获取设备状态信息
                device_status = get_device_status(udid)
                device_name = device_status.get('device_name', 'Unknown') if device_status else 'Unknown'
                platform = device_status.get('platform', 'Unknown') if device_status else 'Unknown'
                
                # 测试 driver 是否可用
                try:
                    current_activity = driver.current_activity
                    driver_status = "active"
                except Exception:
                    current_activity = "unknown"
                    driver_status = "inactive"
                
                info[udid] = {
                    'device_name': device_name,
                    'platform': platform,
                    'driver_status': driver_status,
                    'current_activity': current_activity,
                    'keepalive_active': udid in self.keepalive_threads and self.keepalive_threads[udid].is_alive()
                }
            except Exception as e:
                info[udid] = {
                    'device_name': 'Unknown',
                    'platform': 'Unknown',
                    'driver_status': 'error',
                    'error': str(e),
                    'keepalive_active': udid in self.keepalive_threads and self.keepalive_threads[udid].is_alive()
                }
        
        return info
    
    def health_check(self) -> Dict[str, Any]:
        """
        检查所有 driver 的健康状态
        
        Returns:
            健康检查结果
        """
        result = {
            'total_drivers': len(self.active_drivers),
            'active_drivers': 0,
            'inactive_drivers': 0,
            'details': {}
        }
        
        for udid, driver in self.active_drivers.items():
            try:
                # 测试 driver 连接
                driver.current_activity
                result['active_drivers'] += 1
                result['details'][udid] = 'active'
            except Exception as e:
                result['inactive_drivers'] += 1
                result['details'][udid] = f'inactive: {str(e)}'
        
        get_current_task_log_manager().info_tools(f"设备driver健康检查完成: {result['active_drivers']} 活跃, {result['inactive_drivers']} 失效", "health_check")
        return result

    def _start_keepalive(self, udid: str, platform: str):
        """
        启动设备的保活机制

        Args:
            udid: 设备 UDID
            platform: 平台类型 ('ios' 或 'android')
        """
        try:
            # 如果已经有保活线程在运行，先停止它
            if udid in self.keepalive_threads:
                self._stop_keepalive(udid)

            # 创建停止事件
            stop_event = threading.Event()
            self.keepalive_stop_events[udid] = stop_event

            # 初始化保活计数器
            self.keepalive_counters[udid] = 0

            # 创建并启动保活线程
            keepalive_thread = threading.Thread(
                target=self._keepalive_worker,
                args=(udid, platform, stop_event),
                daemon=True,
                name=f"keepalive-{udid}"
            )
            keepalive_thread.start()
            self.keepalive_threads[udid] = keepalive_thread

            get_current_task_log_manager().info_tools(f"为设备 {udid} ({platform}) 启动保活机制", "_start_keepalive")

        except Exception as e:
            get_current_task_log_manager().error_tools(f"启动设备 {udid} 保活机制失败: {e}", "_start_keepalive")

    def _stop_keepalive(self, udid: str):
        """
        停止设备的保活机制

        Args:
            udid: 设备 UDID
        """
        try:
            # 设置停止事件
            if udid in self.keepalive_stop_events:
                self.keepalive_stop_events[udid].set()
                del self.keepalive_stop_events[udid]

            # 等待线程结束
            if udid in self.keepalive_threads:
                thread = self.keepalive_threads[udid]
                thread.join(timeout=2)  # 最多等待2秒
                del self.keepalive_threads[udid]
                get_current_task_log_manager().info_tools(f"停止设备 {udid} 的保活机制", "_stop_keepalive")

            # 清理计数器
            if udid in self.keepalive_counters:
                del self.keepalive_counters[udid]

        except Exception as e:
            get_current_task_log_manager().error_tools(f"停止设备 {udid} 保活机制失败: {e}", "_stop_keepalive")

    def _keepalive_worker(self, udid: str, platform: str, stop_event: threading.Event):
        """
        保活工作线程

        Args:
            udid: 设备 UDID
            platform: 平台类型
            stop_event: 停止事件
        """
        get_current_task_log_manager().info_tools(f"设备 {udid} ({platform}) 保活线程开始运行", "_keepalive_worker")

        while not stop_event.is_set():
            try:
                # 检查 driver 是否还存在
                if udid not in self.active_drivers:
                    get_current_task_log_manager().info_tools(f"设备 {udid} driver已不存在，停止保活", "_keepalive_worker")
                    break

                driver = self.active_drivers[udid]

                # 发送无害的保活指令
                try:
                    # 使用 get_window_size() 作为保活指令，这个操作对设备无害
                    window_size = driver.get_window_size()

                    # 增加保活计数器
                    if udid not in self.keepalive_counters:
                        self.keepalive_counters[udid] = 0
                    self.keepalive_counters[udid] += 1

                    # 每隔6次（约1分钟）记录一次成功日志，避免日志过多
                    if self.keepalive_counters[udid] % 6 == 1:
                        get_current_task_log_manager().info_tools(f"设备 {udid} 保活正常运行，已执行 {self.keepalive_counters[udid]} 次", "_keepalive_worker")

                except Exception as e:
                    get_current_task_log_manager().warning_tools(f"设备 {udid} 保活指令失败: {e}", "_keepalive_worker")
                    # 如果保活指令失败，可能 driver 已经失效，尝试清理
                    try:
                        if udid in self.active_drivers:
                            get_current_task_log_manager().warning_tools(f"设备 {udid} driver可能已失效，将从缓存中移除", "_keepalive_worker")
                            del self.active_drivers[udid]
                        # 清理计数器
                        if udid in self.keepalive_counters:
                            del self.keepalive_counters[udid]
                    except Exception:
                        pass
                    break

                # 等待下一次保活间隔，或者收到停止信号
                if stop_event.wait(timeout=self.keepalive_interval):
                    break

            except Exception as e:
                get_current_task_log_manager().error_tools(f"设备 {udid} 保活线程异常: {e}", "_keepalive_worker")
                break

        get_current_task_log_manager().info_tools(f"设备 {udid} ({platform}) 保活线程结束", "_keepalive_worker")

    def get_keepalive_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有设备的保活状态信息

        Returns:
            包含保活状态信息的字典
        """
        status = {}
        for udid in self.active_drivers.keys():
            thread_alive = udid in self.keepalive_threads and self.keepalive_threads[udid].is_alive()
            status[udid] = {
                'keepalive_active': thread_alive,
                'thread_name': self.keepalive_threads[udid].name if udid in self.keepalive_threads else None,
                'keepalive_interval': self.keepalive_interval
            }
        return status

    def set_keepalive_interval(self, interval: int):
        """
        设置保活间隔时间

        Args:
            interval: 保活间隔（秒）
        """
        if interval < 1:
            get_current_task_log_manager().warning_tools(f"保活间隔不能小于1秒，当前设置: {interval}", "set_keepalive_interval")
            return

        old_interval = self.keepalive_interval
        self.keepalive_interval = interval
        get_current_task_log_manager().info_tools(f"保活间隔从 {old_interval}秒 更改为 {interval}秒", "set_keepalive_interval")


# 单例实例
device_driver_manager = DeviceDriverManager()


def create_device_driver(udid: str, platform: str = None, **kwargs) -> Optional[webdriver.Remote]:
    """
    创建设备的 WebDriver 实例（便捷函数）
    
    Args:
        udid: 设备 UDID
        platform: 平台类型（可选）
        **kwargs: 其他配置参数
        
    Returns:
        WebDriver 实例或 None
    """
    return device_driver_manager.create_driver(udid, platform, **kwargs)


def get_device_driver(udid: str, auto_create: bool = True) -> Optional[webdriver.Remote]:
    """
    获取设备的 WebDriver 实例（便捷函数）
    
    Args:
        udid: 设备 UDID
        auto_create: 如果不存在是否自动创建
        
    Returns:
        WebDriver 实例或 None
    """
    return device_driver_manager.get_driver(udid, auto_create)


def close_device_driver(udid: str) -> bool:
    """
    关闭设备的 WebDriver 实例（便捷函数）
    
    Args:
        udid: 设备 UDID
        
    Returns:
        操作是否成功
    """
    return device_driver_manager.close_driver(udid)


def close_all_device_drivers() -> int:
    """
    关闭所有设备的 WebDriver 实例（便捷函数）

    Returns:
        成功关闭的 driver 数量
    """
    return device_driver_manager.close_all_drivers()


def get_keepalive_status() -> Dict[str, Dict[str, Any]]:
    """
    获取所有设备的保活状态信息（便捷函数）

    Returns:
        包含保活状态信息的字典
    """
    return device_driver_manager.get_keepalive_status()


def set_keepalive_interval(interval: int):
    """
    设置保活间隔时间（便捷函数）

    Args:
        interval: 保活间隔（秒）
    """
    device_driver_manager.set_keepalive_interval(interval)


if __name__ == "__main__":
    # 测试用例
    print("设备驱动管理器测试...")
    
    # 这里可以添加一些测试代码
    info = device_driver_manager.get_active_drivers_info()
    print(f"活跃的 drivers: {info}")
    
    health = device_driver_manager.health_check()
    print(f"健康检查结果: {health}")