#!/usr/bin/env python3
"""
并发任务管理器
支持真正的任务隔离和并发执行的任务管理系统
"""

import json
import os
import time
import threading
import uuid
from datetime import datetime
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    round_id: str  # 简单数字格式，用于Java后端
    task_description: str
    mis_id: Optional[str]
    status: TaskStatus
    log_dir: str
    start_time: datetime
    end_time: Optional[datetime] = None
    port: Optional[int] = None
    result: Optional[str] = None
    error: Optional[str] = None
    detailed_round_id: Optional[str] = None  # 详细格式，用于内部日志


class ConcurrentTaskManager:
    """并发任务管理器 - 支持真正的任务隔离"""
    
    def __init__(self, max_concurrent_tasks: int = 2, task_timeout_seconds: int = 1800):  # 30分钟超时
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_timeout_seconds = task_timeout_seconds
        self.tasks: Dict[str, TaskInfo] = {}  # task_id -> TaskInfo
        self.running_tasks: Dict[str, TaskInfo] = {}  # task_id -> TaskInfo
        self.round_counter = 0
        self.lock = threading.Lock()
        self.base_log_dir = "log"
        
        # 初始化基础日志目录
        os.makedirs(self.base_log_dir, exist_ok=True)
        
        # 加载历史轮次计数
        self._load_round_counter()
        
        # 启动超时检查线程
        self._start_timeout_checker()
    
    def _load_round_counter(self):
        """加载历史轮次计数"""
        try:
            # 从现有的日志目录中获取最大的轮次号
            max_round = 0
            if os.path.exists(self.base_log_dir):
                items = []
                try:
                    items = os.listdir(self.base_log_dir)
                except (OSError, PermissionError) as e:
                    print(f"⚠️ 无法读取日志目录: {e}")
                    
                for item in items:
                    if item.startswith("round_"):
                        item_path = os.path.join(self.base_log_dir, item)
                        try:
                            if os.path.isdir(item_path):
                                # 解析轮次号，格式：round_XXXXXX_YYYYMMDD_HHMMSS
                                parts = item.split("_")
                                if len(parts) >= 2:
                                    round_num = int(parts[1])
                                    max_round = max(max_round, round_num)
                        except (IndexError, ValueError, OSError):
                            continue
            self.round_counter = max_round
            print(f"🔄 初始化轮次计数器: {self.round_counter}")
        except Exception as e:
            print(f"⚠️ 加载轮次计数器失败: {e}")
            self.round_counter = 0
    
    def can_accept_new_task(self) -> bool:
        """检查是否可以接受新任务"""
        with self.lock:
            return len(self.running_tasks) < self.max_concurrent_tasks
    
    def get_running_tasks_count(self) -> int:
        """获取正在运行的任务数量"""
        with self.lock:
            return len(self.running_tasks)
    
    def get_running_tasks_info(self) -> List[Dict]:
        """获取正在运行的任务信息"""
        with self.lock:
            return [
                {
                    "task_id": task.task_id,
                    "round_id": task.round_id,
                    "task_description": task.task_description[:100] + "..." if len(task.task_description) > 100 else task.task_description,
                    "port": task.port,
                    "start_time": task.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "duration": (datetime.now() - task.start_time).total_seconds()
                }
                for task in self.running_tasks.values()
            ]
    
    def create_new_task(self, task_description: str, mis_id: Optional[str] = None) -> Tuple[str, TaskInfo]:
        """
        创建新任务
        
        Args:
            task_description: 任务描述
            mis_id: MIS ID
            
        Returns:
            (task_id, TaskInfo) 元组
        """
        with self.lock:
            # 生成任务ID和轮次ID
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            self.round_counter += 1
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 为了兼容Java后端，同时生成数字格式的轮次ID和详细的轮次ID
            round_id = str(self.round_counter)  # Java后端需要的简单数字格式
            detailed_round_id = f"round_{self.round_counter:06d}_{timestamp}"  # 内部使用的详细格式
            
            # 创建任务专用的日志目录（使用详细格式）
            log_dir = os.path.join(self.base_log_dir, detailed_round_id)
            os.makedirs(log_dir, exist_ok=True)
            
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                round_id=round_id,  # 简单数字格式
                task_description=task_description,
                mis_id=mis_id,
                status=TaskStatus.PENDING,
                log_dir=log_dir,
                start_time=datetime.now(),
                detailed_round_id=detailed_round_id  # 详细格式用于日志
            )
            
            # 存储任务信息
            self.tasks[task_id] = task_info
            
            # 为了兼容日志分析工具，同时使用多种格式存储任务状态
            # 这样_log_analysis_tools.py就能找到对应的状态信息
            task_dict = {
                "task_id": task_id,
                "round_id": round_id,  # 简单数字格式
                "task_description": task_description,
                "mis_id": mis_id,
                "status": TaskStatus.PENDING.value,
                "log_dir": log_dir,
                "start_time": datetime.now().isoformat(),
                "detailed_round_id": detailed_round_id
            }
            
            # 使用多种格式的round_id作为key存储，方便查找
            possible_round_ids = [
                f"round_{self.round_counter:04d}",  # round_0276
                f"round_{self.round_counter:06d}",  # round_000276
                f"round_{self.round_counter:03d}",  # round_276
                f"round_{self.round_counter}",      # round_276
                str(self.round_counter)             # 276
            ]
            
            if not hasattr(self, 'round_tasks'):
                self.round_tasks = {}
            
            for rid in possible_round_ids:
                self.round_tasks[rid] = task_dict
            
            print(f"📝 创建新任务: {task_id} -> {round_id} (详细: {detailed_round_id})")
            return task_id, task_info
    
    def start_task(self, task_id: str, port: Optional[int] = None) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            port: 分配的端口号
            
        Returns:
            是否启动成功
        """
        with self.lock:
            if task_id not in self.tasks:
                print(f"❌ 任务不存在: {task_id}")
                return False
            
            task = self.tasks[task_id]
            if task.status != TaskStatus.PENDING:
                print(f"❌ 任务状态不正确: {task_id}, 当前状态: {task.status}")
                return False
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.port = port
            task.start_time = datetime.now()
            
            # 添加到运行中的任务列表
            self.running_tasks[task_id] = task
            
            print(f"🚀 启动任务: {task_id} (端口: {port})")
            return True
    
    def complete_task(self, task_id: str, result: Optional[str] = None, error: Optional[str] = None):
        """
        完成任务
        
        Args:
            task_id: 任务ID
            result: 任务结果
            error: 错误信息
        """
        with self.lock:
            if task_id not in self.tasks:
                print(f"❌ 任务不存在: {task_id}")
                return
            
            task = self.tasks[task_id]
            task.end_time = datetime.now()
            task.result = result
            task.error = error
            task.status = TaskStatus.COMPLETED if not error else TaskStatus.FAILED
            
            # 从运行中的任务列表中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            duration = (task.end_time - task.start_time).total_seconds()
            status_icon = "✅" if task.status == TaskStatus.COMPLETED else "❌"
            print(f"{status_icon} 完成任务: {task_id} (耗时: {duration:.1f}秒)")
    
    def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        with self.lock:
            return self.tasks.get(task_id)
    
    def get_task_log_dir(self, task_id: str) -> Optional[str]:
        """获取任务的日志目录"""
        with self.lock:
            task = self.tasks.get(task_id)
            return task.log_dir if task else None
    
    def cleanup_old_tasks(self, max_tasks: int = 100):
        """清理旧任务记录"""
        with self.lock:
            if len(self.tasks) <= max_tasks:
                return
            
            # 按开始时间排序，保留最新的任务
            sorted_tasks = sorted(self.tasks.values(), key=lambda t: t.start_time, reverse=True)
            tasks_to_keep = sorted_tasks[:max_tasks]
            
            # 重建任务字典
            new_tasks = {}
            for task in tasks_to_keep:
                new_tasks[task.task_id] = task
            
            removed_count = len(self.tasks) - len(new_tasks)
            self.tasks = new_tasks
            
            print(f"🧹 清理了 {removed_count} 个旧任务记录")
    
    def _start_timeout_checker(self):
        """启动超时检查线程"""
        def timeout_checker():
            while True:
                time.sleep(60)  # 每分钟检查一次
                self._check_and_cleanup_timeout_tasks()
        
        timeout_thread = threading.Thread(target=timeout_checker, daemon=True)
        timeout_thread.start()
        print(f"⏰ 超时检查线程已启动，超时限制: {self.task_timeout_seconds}秒")
    
    def _check_and_cleanup_timeout_tasks(self):
        """检查并清理超时任务"""
        current_time = datetime.now()
        timeout_tasks = []
        
        with self.lock:
            for task_id, task in list(self.running_tasks.items()):
                duration = (current_time - task.start_time).total_seconds()
                if duration > self.task_timeout_seconds:
                    timeout_tasks.append((task_id, task, duration))
        
        # 在锁外处理超时任务
        for task_id, task, duration in timeout_tasks:
            print(f"⚠️ 发现超时任务: {task_id} (运行时间: {duration:.1f}秒)")
            self.complete_task(task_id, error=f"任务超时 ({duration:.1f}秒)")
            
            # 通知Agent池清理超时任务
            try:
                # 这里需要通知Agent池进行清理
                from concurrent_agent_pool import get_concurrent_agent_pool
                pool = get_concurrent_agent_pool()
                if hasattr(pool, '_force_cleanup_task'):
                    pool._force_cleanup_task(task_id, task.port)
            except Exception as e:
                print(f"⚠️ 通知Agent池清理超时任务失败: {e}")
    
    def force_cleanup_task(self, task_id: str, reason: str = "强制清理"):
        """强制清理任务"""
        with self.lock:
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                print(f"🗑️ 强制清理任务: {task_id} (原因: {reason})")
                self.complete_task(task_id, error=reason)
                return True
            return False
    
    def get_status_summary(self) -> Dict:
        """获取状态摘要"""
        with self.lock:
            return {
                "max_concurrent_tasks": self.max_concurrent_tasks,
                "total_tasks": len(self.tasks),
                "running_tasks": len(self.running_tasks),
                "can_accept_new_task": len(self.running_tasks) < self.max_concurrent_tasks,
                "current_round": self.round_counter,
                "running_task_details": self.get_running_tasks_info()
            }


# 全局并发任务管理器实例
concurrent_task_manager = ConcurrentTaskManager(max_concurrent_tasks=2, task_timeout_seconds=1800)  # 30分钟超时


def get_concurrent_task_manager() -> ConcurrentTaskManager:
    """获取全局并发任务管理器"""
    return concurrent_task_manager


if __name__ == "__main__":
    # 测试并发任务管理器
    print("🧪 测试并发任务管理器")
    
    manager = get_concurrent_task_manager()
    
    # 创建测试任务
    task1_id, task1_info = manager.create_new_task("测试任务1", "test_001")
    task2_id, task2_info = manager.create_new_task("测试任务2", "test_002")
    
    print(f"✅ 创建任务1: {task1_id} -> {task1_info.log_dir}")
    print(f"✅ 创建任务2: {task2_id} -> {task2_info.log_dir}")
    
    # 启动任务
    manager.start_task(task1_id, port=11435)
    manager.start_task(task2_id, port=11436)
    
    # 检查状态
    status = manager.get_status_summary()
    print(f"📊 状态摘要:")
    print(f"  最大并发数: {status['max_concurrent_tasks']}")
    print(f"  当前运行任务: {status['running_tasks']}")
    print(f"  可接受新任务: {status['can_accept_new_task']}")
    
    # 完成任务
    manager.complete_task(task1_id, result="任务1完成")
    manager.complete_task(task2_id, result="任务2完成")
    
    print("✅ 测试完成")