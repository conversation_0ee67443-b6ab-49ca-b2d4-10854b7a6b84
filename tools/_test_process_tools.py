#!/usr/bin/env python3
"""
测试进程工具 - 支持并发的版本
基于新的并发任务管理器和并发日志管理器
"""

import sys
import os
from typing import Dict, Optional

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_task_manager import get_concurrent_task_manager
from tools._concurrent_log_manager import get_global_log_manager


class ConcurrentTestSessionManager:
    """并发测试会话管理器 - 基于新的并发系统"""
    
    def __init__(self):
        self.task_manager = get_concurrent_task_manager()
        self.log_manager = get_global_log_manager()
    
    def start_new_test_session(self, task_description: str = "新测试会话", mis_id: Optional[str] = None) -> Dict:
        """
        启动新的测试会话
        
        Args:
            task_description: 任务描述
            mis_id: MIS ID
            
        Returns:
            包含会话信息的字典
        """
        try:
            # 检查是否可以接受新任务
            if not self.task_manager.can_accept_new_task():
                return {
                    "status": "rejected",
                    "message": f"已达到最大并发任务数({self.task_manager.max_concurrent_tasks})，请稍后再试"
                }
            
            # 创建新任务
            task_id, task_info = self.task_manager.create_new_task(task_description, mis_id)
            
            # 启动任务
            if not self.task_manager.start_task(task_id):
                return {
                    "status": "error",
                    "task_id": task_id,
                    "message": f"启动任务失败: {task_id}"
                }
            
            # 构建返回结果
            result = {
                "status": "success",
                "task_id": task_id,
                "round_id": task_info.round_id,
                "task_description": task_description,
                "log_folder": task_info.log_dir,
                "full_log_path": os.path.abspath(task_info.log_dir),
                "start_time": task_info.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "current_round": self.task_manager.round_counter,
                "message": f"成功启动测试会话 {task_info.round_id}，日志保存到: {task_info.log_dir}"
            }
            
            self.log_manager.info_agent(f"并发测试会话启动成功: {task_info.round_id}")
            return result
            
        except Exception as e:
            error_msg = f"启动测试会话失败: {str(e)}"
            self.log_manager.error_agent(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }
    
    def end_current_test_session(self, task_id: str, summary: str = "任务完成", error: Optional[str] = None) -> Dict:
        """
        结束当前测试会话
        
        Args:
            task_id: 任务ID
            summary: 任务总结
            error: 错误信息
            
        Returns:
            包含结束信息的字典
        """
        try:
            # 完成任务
            self.task_manager.complete_task(task_id, result=summary, error=error)
            
            # 获取任务信息
            task_info = self.task_manager.get_task_info(task_id)
            if not task_info:
                return {
                    "status": "error",
                    "message": f"任务不存在: {task_id}"
                }
            
            final_status = "completed" if not error else "failed"
            
            result = {
                "status": "success",
                "task_id": task_id,
                "round_id": task_info.round_id,
                "final_status": final_status,
                "summary": summary,
                "error": error,
                "duration": (task_info.end_time - task_info.start_time).total_seconds() if task_info.end_time else 0,
                "message": f"测试会话 {task_info.round_id} 已结束: {final_status}"
            }
            
            self.log_manager.info_agent(f"并发测试会话结束: {task_info.round_id} - {final_status}")
            return result
            
        except Exception as e:
            error_msg = f"结束测试会话失败: {str(e)}"
            self.log_manager.error_agent(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }
    
    def get_session_status(self, task_id: Optional[str] = None) -> Dict:
        """
        获取会话状态
        
        Args:
            task_id: 任务ID，如果为None则返回所有任务状态
            
        Returns:
            会话状态信息
        """
        try:
            if task_id:
                # 获取特定任务状态
                task_info = self.task_manager.get_task_info(task_id)
                if not task_info:
                    return {
                        "status": "not_found",
                        "message": f"任务不存在: {task_id}"
                    }
                
                return {
                    "status": "success",
                    "task_id": task_info.task_id,
                    "round_id": task_info.round_id,
                    "task_status": task_info.status.value,
                    "task_description": task_info.task_description,
                    "log_dir": task_info.log_dir,
                    "start_time": task_info.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": task_info.end_time.strftime("%Y-%m-%d %H:%M:%S") if task_info.end_time else None,
                    "port": task_info.port
                }
            else:
                # 获取所有任务状态摘要
                return {
                    "status": "success",
                    "summary": self.task_manager.get_status_summary()
                }
                
        except Exception as e:
            error_msg = f"获取会话状态失败: {str(e)}"
            self.log_manager.error_agent(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }


# 全局并发测试会话管理器实例
test_session_manager = ConcurrentTestSessionManager()


def get_test_session_manager() -> ConcurrentTestSessionManager:
    """获取全局并发测试会话管理器"""
    return test_session_manager


# 为了向后兼容，保留原有的类名
TestSessionManager = ConcurrentTestSessionManager


# 独立函数接口 - 供agent工具调用
def start_new_test_session(task_description: str = "新测试会话", mis_id: str = None) -> str:
    """
    启动新的测试会话 - 供agent工具调用
    
    Args:
        task_description: 任务描述
        mis_id: MIS ID
        
    Returns:
        JSON格式的执行结果
    """
    import json
    result = test_session_manager.start_new_test_session(task_description, mis_id)
    return json.dumps(result, ensure_ascii=False, indent=2)


def end_current_test_session(task_id: str, summary: str = "任务完成", error: str = "") -> str:
    """
    结束当前测试会话 - 供agent工具调用
    
    Args:
        task_id: 任务ID
        summary: 任务总结
        error: 错误信息
        
    Returns:
        JSON格式的执行结果
    """
    import json
    error_val = error if error.strip() else None
    result = test_session_manager.end_current_test_session(task_id, summary, error_val)
    return json.dumps(result, ensure_ascii=False, indent=2)


def get_session_status(task_id: str = "") -> str:
    """
    获取会话状态信息 - 供agent工具调用
    
    Args:
        task_id: 任务ID，空字符串表示获取所有信息
        
    Returns:
        JSON格式的状态信息
    """
    import json
    task_id_val = task_id.strip() if task_id.strip() else None
    result = test_session_manager.get_session_status(task_id_val)
    return json.dumps(result, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    # 测试并发测试会话管理器
    print("🧪 测试并发测试会话管理器")
    
    manager = get_test_session_manager()
    
    # 创建测试会话
    result1 = manager.start_new_test_session("测试任务1", "test_001")
    print(f"✅ 创建会话1: {result1}")
    
    result2 = manager.start_new_test_session("测试任务2", "test_002")
    print(f"✅ 创建会话2: {result2}")
    
    # 检查状态
    status = manager.get_session_status()
    print(f"📊 会话状态: {status}")
    
    print("✅ 测试完成")