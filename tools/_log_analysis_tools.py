#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的日志分析工具
重新设计，避免复杂的状态管理和竞争条件
"""

import json
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set
import logging
from logging.handlers import RotatingFileHandler
import argparse
import re

# 创建专用日志目录
log_dir = Path("log/upload_log")
log_dir.mkdir(parents=True, exist_ok=True)

# 设置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 创建文件处理器，支持日志轮转
file_handler = RotatingFileHandler(
    log_dir / "simple_log_analysis.log",
    maxBytes=100*1024*1024,  # 100MB
    backupCount=2
)
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 添加处理器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

class SimpleLogAnalyzer:
    """简化的日志分析器"""
    
    def __init__(self, log_dir: str = "log", env: str = "local"):
        self.log_dir = Path(log_dir)
        self.env = env
        self.base_url = "http://127.0.0.1:8080" if env == "local" else "http://qaassist.sankuai.com"
        self.upload_url = f"{self.base_url}/compass/api/aiTestAgent/add"
        self.device_update_url = f"{self.base_url}/compass/api/ai-test-record/update-device-by-round"
        self.round_info_url = f"{self.base_url}/compass/api/ai-test-record/list"
        
        # 简化的状态管理
        self.processed_file = Path("log/upload_log/processed_rounds.txt")
        self.processed_rounds = self._load_processed_rounds()
        
        # 监控状态
        self.running = False
        self.check_interval = 30  # 检查间隔
        
        # agent状态文件路径
        self.agent_status_file = Path("status/agent_status.json")
        
    def _load_processed_rounds(self) -> Set[str]:
        """加载已处理的轮次"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r') as f:
                    processed = set(line.strip() for line in f if line.strip())
                logger.info(f"已加载 {len(processed)} 个已处理的轮次")
            except Exception as e:
                logger.error(f"加载已处理轮次失败: {e}")
        return processed
    
    def _save_processed_round(self, round_folder: str):
        """保存已处理的轮次"""
        try:
            with open(self.processed_file, 'a') as f:
                f.write(f"{round_folder}\n")
            self.processed_rounds.add(round_folder)
            logger.debug(f"已保存处理记录: {round_folder}")
        except Exception as e:
            logger.error(f"保存处理记录失败: {e}")
    
    def _load_agent_status(self) -> Optional[Dict]:
        """加载agent状态文件"""
        try:
            if self.agent_status_file.exists():
                with open(self.agent_status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"Agent状态文件不存在: {self.agent_status_file}")
                return None
        except Exception as e:
            logger.error(f"读取agent状态文件失败: {e}")
            return None
    
    def _get_round_info_from_status(self, round_num: int) -> Optional[Dict]:
        """从agent状态文件或并发任务管理器中获取轮次信息"""
        try:
            # 首先尝试从并发任务管理器获取数据
            try:
                from tools._concurrent_task_manager import get_concurrent_task_manager
                task_manager = get_concurrent_task_manager()
                if hasattr(task_manager, 'round_tasks'):
                    # 尝试多种格式的轮次ID
                    possible_round_ids = [
                        f"round_{round_num:04d}",  # round_0001
                        f"round_{round_num:06d}",  # round_000001
                        f"round_{round_num:03d}",  # round_001
                        f"round_{round_num}",      # round_1
                        str(round_num)             # 276
                    ]
                    
                    for round_id in possible_round_ids:
                        if round_id in task_manager.round_tasks:
                            task_info = task_manager.round_tasks[round_id]
                            return {
                                "round_id": task_info.get("round_id", round_id),
                                "prompt": task_info.get("task_description", ""),
                                "mis_id": task_info.get("mis_id", None),
                                "status": task_info.get("status", "unknown"),
                                "result": task_info.get("result", ""),
                                "end_time": task_info.get("end_time", ""),
                                "start_time": task_info.get("start_time", ""),
                                "error": task_info.get("error", None)
                            }
            except Exception as e:
                logger.debug(f"从并发任务管理器获取状态失败: {e}")
            
            # 回退到从agent状态文件获取
            agent_status = self._load_agent_status()
            if not agent_status:
                return None
            
            tasks = agent_status.get("tasks", {})
            
            # 尝试多种格式的轮次ID
            possible_round_ids = [
                f"round_{round_num:04d}",  # round_0001
                f"round_{round_num:06d}",  # round_000001
                f"round_{round_num:03d}",  # round_001
                f"round_{round_num}",      # round_1
            ]
            
            for round_id in possible_round_ids:
                if round_id in tasks:
                    task_info = tasks[round_id]
                    return {
                        "round_id": round_id,
                        "prompt": task_info.get("task_description", ""),
                        "mis_id": task_info.get("mis_id", None),
                        "status": task_info.get("status", "unknown"),
                        "result": task_info.get("result", ""),
                        "end_time": task_info.get("end_time", ""),
                        "start_time": task_info.get("start_time", ""),
                        "error": task_info.get("error", None)
                    }
            
            logger.warning(f"未找到轮次 {round_num} 的状态信息，尝试的ID: {possible_round_ids}")
            return None
            
        except Exception as e:
            logger.error(f"获取轮次状态信息失败: {e}")
            return None
    
    def _send_completion_notification(self, round_info: Dict, log_content: str = None):
        """发送测试完成通知"""
        try:
            # 导入通知工具
            import sys
            import os
            
            # 添加项目根目录到sys.path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            
            from tools._notify_user_tools import send_individual_message
            
            round_num = round_info.get("round_num")
            prompt = round_info.get("prompt", "未知")
            mis_id = round_info.get("mis_id")
            result = round_info.get("result", "")
            end_time = round_info.get("end_time", "")
            start_time = round_info.get("start_time", "")
            status = round_info.get("status", "unknown")
            error = round_info.get("error")
            
            # 如果提供了日志内容，尝试提取任务完成信息
            completion_info = None
            if log_content:
                completion_info = self._extract_task_completion_info(log_content)
            
            # 格式化时间显示
            def format_time(time_str):
                if not time_str:
                    return "未知"
                try:
                    # 尝试解析时间并格式化
                    if "T" in time_str:
                        dt = datetime.fromisoformat(time_str.replace("T", " "))
                        return dt.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        return time_str
                except:
                    return time_str
            
            # 检查是否是超时情况
            timeout_flag = round_info.get("timeout_flag", False)
            
            # 构造格式化的通知消息
            message_lines = [
                "🎉 测试任务完成通知" if not timeout_flag else "⚠️ 测试任务等待超时通知",
                "",
                f"📋 轮次编号：第 {round_num} 轮",
                f"📝 测试内容：{prompt}",
                f"📊 执行状态：{status}",
                f"⏰ 开始时间：{format_time(start_time)}",
                f"⏰ 结束时间：{format_time(end_time)}",
            ]
            
            # 如果有任务完成信息，添加详细信息
            if completion_info:
                message_lines.extend([
                    "",
                    "📈 任务执行统计："
                ])
                
                if completion_info.get('total_rounds'):
                    message_lines.append(f"   📊 总轮数：{completion_info['total_rounds']}")
                
                if completion_info.get('total_duration'):
                    message_lines.append(f"   ⏱️ 总耗时：{completion_info['total_duration']}")
                
                if completion_info.get('execution_status'):
                    message_lines.append(f"   ✅ 最终状态：{completion_info['execution_status']}")
                
                if completion_info.get('end_time'):
                    message_lines.append(f"   🕐 实际结束时间：{completion_info['end_time']}")
            
            # 如果是超时情况，添加超时说明
            if timeout_flag:
                message_lines.extend([
                    "",
                    "⚠️ 注意：此任务等待完成超过15分钟，已提前发送通知",
                    "   任务可能仍在执行中，请检查实际执行状态"
                ])
            
            # 添加执行结果（限制长度避免消息过长）
            if result:
                if len(result) > 200:
                    result_preview = result[:200] + "..."
                else:
                    result_preview = result
                message_lines.extend([
                    "",
                    f"✅ 执行结果：",
                    f"{result_preview}"
                ])
            
            # 如果有错误信息，添加错误详情
            if error:
                message_lines.extend([
                    "",
                    f"❌ 错误信息：{error}"
                ])
            
            message = "\n".join(message_lines)
            
            # 发送通知
            send_result = send_individual_message(
                message=message,
                receivers=[mis_id],
                timeout=15,
                add_host_info=True
            )
            
            logger.info(f"向 {mis_id} 发送轮次 {round_num} 完成通知成功: {send_result}")
            return True
            
        except Exception as e:
            logger.error(f"发送轮次 {round_num} 完成通知失败: {e}")
            return False
    
    def _extract_round_info(self, folder_name: str) -> Optional[Dict]:
        """提取轮次信息"""
        pattern = r"round_(\d+)_(\d{8})_(\d{6})"
        match = re.match(pattern, folder_name)
        if match:
            round_num = int(match.group(1))
            date_str = match.group(2)
            time_str = match.group(3)
            datetime_str = f"{date_str}_{time_str}"
            try:
                folder_datetime = datetime.strptime(datetime_str, "%Y%m%d_%H%M%S")
                return {
                    "round_num": round_num,
                    "folder_datetime": folder_datetime,
                    "folder_name": folder_name
                }
            except ValueError:
                pass
        return None
    
    def _extract_task_completion_info(self, content: str) -> Optional[Dict]:
        """提取任务完成信息，包括结束时间、总耗时等"""
        try:
            # 查找任务完成部分，包含时间戳
            completion_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?============================================================\n任务完成.*?\n============================================================\n(.*?)============================================================'
            match = re.search(completion_pattern, content, re.DOTALL)
            
            if match:
                timestamp_str = match.group(1)
                completion_content = match.group(2)
                info = {'timestamp': timestamp_str}
                
                # 提取结束时间
                end_time_pattern = r'🕐 结束时间: (.+)'
                end_time_match = re.search(end_time_pattern, completion_content)
                if end_time_match:
                    info['end_time'] = end_time_match.group(1).strip()
                
                # 提取总耗时
                duration_pattern = r'⏱️  总耗时: (.+)'
                duration_match = re.search(duration_pattern, completion_content)
                if duration_match:
                    info['total_duration'] = duration_match.group(1).strip()
                
                # 提取总轮数
                rounds_pattern = r'📊 总轮数: (.+)'
                rounds_match = re.search(rounds_pattern, completion_content)
                if rounds_match:
                    info['total_rounds'] = rounds_match.group(1).strip()
                
                # 提取执行状态
                status_pattern = r'✅ 执行状态: (.+)'
                status_match = re.search(status_pattern, completion_content)
                if status_match:
                    info['execution_status'] = status_match.group(1).strip()
                
                return info if len(info) > 1 else None  # 至少要有timestamp和一个其他字段
            
            return None
        except Exception as e:
            logger.error(f"提取任务完成信息失败: {e}")
            return None
    
    def _is_recent_folder(self, folder_name: str) -> bool:
        """判断是否为近期文件夹（10分钟内）"""
        round_info = self._extract_round_info(folder_name)
        if not round_info:
            return False
        
        folder_datetime = round_info["folder_datetime"]
        current_time = datetime.now()
        time_diff = current_time - folder_datetime
        
        return time_diff <= timedelta(minutes=10)
    
    def _has_device_connection(self, log_file: Path) -> bool:
        """检查是否有有效的设备连接"""
        if not log_file.exists():
            return False
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否调用了 find_available_device
            if "🔧 执行工具: find_available_device" not in content:
                return False
            
            # 检查结果是否为 not_found
            if '"status": "not_found"' in content:
                return False
            
            return True
        except Exception as e:
            logger.error(f"检查设备连接失败: {e}")
            return False
    
    def _is_task_completed(self, log_file: Path) -> bool:
        """检查任务是否完成"""
        if not log_file.exists():
            return False
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            return "任务完成" in content
        except Exception:
            return False
    
    def _extract_device_info(self, content: str) -> Optional[Dict]:
        """提取设备信息"""
        # 查找 find_available_device 的执行结果
        pattern = r'"tool_name": "find_available_device"[^}]*?"result": \{[^}]*?"device_name": "([^"]+)"[^}]*?"device_udid": "([^"]+)"'
        match = re.search(pattern, content)
        if match:
            return {
                "device_name": match.group(1),
                "device_udid": match.group(2)
            }
        
        # 备用方案：查找设备名称和UDID
        device_name_pattern = r'"device_name": "([^"]+)"'
        udid_pattern = r'"(?:udid|device_udid)": "([^"]+)"'
        
        device_name_match = re.search(device_name_pattern, content)
        udid_match = re.search(udid_pattern, content)
        
        if device_name_match and udid_match:
            return {
                "device_name": device_name_match.group(1),
                "device_udid": udid_match.group(1)
            }
        
        return None
    
    def _extract_rich_summary(self, tool_name: str, result_data: Dict) -> str:
        """提取丰富的摘要信息，基于统一的工具返回结构标准"""
        summary_parts = []
        
        # 1. 基础描述信息（message字段）
        message = result_data.get('message', '')
        if message:
            summary_parts.append(message)
        
        # 2. 详细内容信息（按Agent.py工具结构优先级）
        # 不同工具的主要信息字段映射
        content_field_mapping = {
            'ocr_text_only': 'text_result',           # OCR识别文本
            'check_page_display': 'text_result',      # 页面检查结果  
            'find_element_on_page': 'text_result',    # 元素查找结果
            'record_agent_summary': 'summary_text',   # Agent摘要文本
            'record_test_issue': 'issue_text',        # 问题描述文本
            'input_text_smart': 'text',               # 输入的文本内容
            'llm_result': 'final_reply',              # 模型最终回复内容
        }
        
        # 优先使用工具特定的内容字段
        content_field = content_field_mapping.get(tool_name, None)
        if content_field and content_field in result_data:
            detail_value = result_data[content_field]
            if detail_value and isinstance(detail_value, str) and detail_value != message:
                cleaned_detail = self._clean_detail_text(detail_value)
                summary_parts.append(cleaned_detail)
        else:
            # 回退到通用字段检查（按优先级）
            fallback_fields = ['text_result', 'content', 'summary_text', 'issue_text', 'text']
            for field in fallback_fields:
                if field in result_data:
                    detail_value = result_data[field]
                    if detail_value and isinstance(detail_value, str) and detail_value != message:
                        cleaned_detail = self._clean_detail_text(detail_value)
                        summary_parts.append(cleaned_detail)
                        break  # 只取第一个有效字段
        
        # 3. 添加操作详情（如坐标、参数等）
        operation_details = self._extract_operation_details(tool_name, result_data)
        if operation_details:
            summary_parts.append(operation_details)
        
        # 4. 如果没有任何信息，使用默认描述
        if not summary_parts:
            summary_parts.append("执行完成")
        
        return " | ".join(summary_parts)
    
    def _clean_detail_text(self, text: str, max_length: int = 150) -> str:
        """清理详细文本信息"""
        # 清理换行符和多余空白
        cleaned = text.replace('\\n', ' ').replace('\n', ' ').strip()
        cleaned = ' '.join(cleaned.split())  # 合并多个空格
        
        # 限制长度
        if len(cleaned) > max_length:
            cleaned = cleaned[:max_length] + "..."
        
        return cleaned
    
    def _extract_operation_details(self, tool_name: str, result_data: Dict) -> str:
        """提取操作详情信息"""
        details = []
        
        # 根据工具类型提取关键操作信息
        if tool_name == 'llm_result':
            # 虚拟工具，不需要额外的操作详情
            return ""
        elif tool_name == 'tap_device':
            x, y = result_data.get('x'), result_data.get('y')
            if x is not None and y is not None:
                details.append(f"坐标({x},{y})")
                
        elif tool_name == 'slide_device':
            from_x, from_y = result_data.get('from_x'), result_data.get('from_y')
            to_x, to_y = result_data.get('to_x'), result_data.get('to_y')
            duration = result_data.get('duration')
            if all(v is not None for v in [from_x, from_y, to_x, to_y]):
                details.append(f"滑动({from_x},{from_y})→({to_x},{to_y})")
                if duration:
                    details.append(f"耗时{duration}s")
                    
        elif tool_name == 'find_element_on_page':
            element = result_data.get('element')
            found = result_data.get('found')
            if element:
                status = "找到" if found else "未找到"
                details.append(f"元素'{element}'{status}")
                
        elif tool_name == 'take_screenshot':
            description = result_data.get('description')
            if description:
                details.append(f"描述:{description}")
                
        elif tool_name == 'input_text_smart':
            text = result_data.get('text')
            element_index = result_data.get('element_index')
            if text:
                text_preview = text[:20] + "..." if len(text) > 20 else text
                details.append(f"输入'{text_preview}'")
                if element_index is not None:
                    details.append(f"元素索引{element_index}")
        
        return " ".join(details)

    def _extract_final_reply(self, content: str) -> Optional[Dict]:
        """提取模型最终回复内容和时间戳"""
        try:
            # 查找最终回复部分，使用更精确的匹配
            reply_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?💬 最终回复开始:\s*\n(.*?)💬 最终回复结束'
            match = re.search(reply_pattern, content, re.DOTALL)
            
            if match:
                timestamp_str = match.group(1)
                reply_content = match.group(2).strip()
                
                # 按行处理，只保留实际的回复内容
                lines = reply_content.split('\n')
                actual_reply_lines = []
                
                for line in lines:
                    # 移除时间戳和日志前缀，获取纯内容
                    # 匹配格式：2025-07-30 18:52:26 - INFO - [Task_14301682704] -     
                    clean_line = re.sub(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} - \w+ - \[Task_[^\]]+\] -\s*', '', line)
                    
                    # 跳过空行
                    if not clean_line.strip():
                        continue
                    
                    # 跳过仅包含时间戳的行
                    if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} - \w+ - \[Task_[^\]]+\] -\s*$', line):
                        continue
                    
                    actual_reply_lines.append(clean_line.strip())
                
                # 合并有效的回复内容，保持换行结构
                if actual_reply_lines:
                    final_content = '\n'.join(actual_reply_lines)
                    
                    # 最终清理多余空格，但保持换行
                    final_content = re.sub(r'[ \t]+', ' ', final_content)  # 只清理空格和制表符
                    final_content = re.sub(r'\n\s*\n', '\n', final_content)  # 清理多余的空行
                    
                    return {
                        'content': final_content.strip(),
                        'timestamp': timestamp_str
                    } if final_content.strip() else None
            
            return None
        except Exception as e:
            logger.error(f"提取最终回复失败: {e}")
            return None

    def _parse_tasks(self, content: str, round_info: Dict, device_info: Dict) -> List[Dict]:
        """解析任务步骤"""
        tasks = []
        
        # 1. 查找所有工具执行记录，包含时间戳
        tool_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?🔧 执行工具: (\w+).*?📊 执行结果: (\{.*?\}).*?💬 结果摘要: ([^\n]+)'
        matches = re.findall(tool_pattern, content, re.DOTALL)
        
        for timestamp_str, tool_name, result_json, summary in matches:
            try:
                # 解析结果JSON
                result_data = json.loads(result_json)
                
                # 提取图片URL
                image_url = result_data.get('image_url', '')
                if not image_url:
                    image_url = result_data.get('text_result', {}).get('image_url', '') if isinstance(result_data.get('text_result'), dict) else ''
                
                # 提取丰富的摘要信息
                rich_summary = self._extract_rich_summary(tool_name, result_data)
                
                # 创建上传记录，使用工具实际执行的时间
                task_record = {
                    "inspectionTime": timestamp_str,
                    "deviceId": device_info["device_name"],
                    "imagePath": image_url or "N/A",
                    "remarks": f"{tool_name}:{rich_summary}",
                    "logLevel": "INFO",
                    "createdAt": timestamp_str,
                    "roundNum": round_info["round_num"]
                }
                
                tasks.append(task_record)
                
            except Exception as e:
                logger.error(f"解析任务失败 {tool_name}: {e}")
                continue
        
        # 2. 处理模型最终回复（作为虚拟工具llm_result）
        final_reply_info = self._extract_final_reply(content)
        if final_reply_info:
            try:
                # 构造虚拟的工具结果数据
                llm_result_data = {
                    "status": "success",
                    "final_reply": final_reply_info['content'],
                    "message": "模型最终回复生成完成"
                }
                
                # 提取丰富的摘要信息（使用虚拟工具名llm_result）
                rich_summary = self._extract_rich_summary("llm_result", llm_result_data)
                
                # 创建虚拟工具的上传记录，使用实际时间戳
                llm_task_record = {
                    "inspectionTime": final_reply_info['timestamp'],
                    "deviceId": device_info["device_name"],
                    "imagePath": "N/A",  # 最终回复通常没有图片
                    "remarks": f"llm_result:{rich_summary}",
                    "logLevel": "INFO",
                    "createdAt": final_reply_info['timestamp'],
                    "roundNum": round_info["round_num"]
                }
                
                tasks.append(llm_task_record)
                logger.info(f"成功解析模型最终回复，内容长度: {len(final_reply_info['content'])} 字符")
                
            except Exception as e:
                logger.error(f"处理模型最终回复失败: {e}")
        
        # 3. 处理任务完成信息（作为虚拟工具task_completion）
        completion_info = self._extract_task_completion_info(content)
        if completion_info:
            try:
                # 构造任务完成信息的摘要
                completion_parts = []
                if completion_info.get('execution_status'):
                    completion_parts.append(f"状态: {completion_info['execution_status']}")
                if completion_info.get('total_duration'):
                    completion_parts.append(f"耗时: {completion_info['total_duration']}")
                if completion_info.get('total_rounds'):
                    completion_parts.append(f"轮数: {completion_info['total_rounds']}")
                if completion_info.get('end_time'):
                    completion_parts.append(f"结束: {completion_info['end_time']}")
                
                completion_summary = " | ".join(completion_parts) if completion_parts else "任务完成信息记录"
                
                # 创建任务完成记录，使用实际时间戳
                completion_task_record = {
                    "inspectionTime": completion_info['timestamp'],
                    "deviceId": device_info["device_name"],
                    "imagePath": "N/A",
                    "remarks": f"task_completion:{completion_summary}",
                    "logLevel": "INFO",
                    "createdAt": completion_info['timestamp'],
                    "roundNum": round_info["round_num"]
                }
                
                tasks.append(completion_task_record)
                logger.info(f"成功解析任务完成信息: {completion_summary}")
                
            except Exception as e:
                logger.error(f"处理任务完成信息失败: {e}")
        
        return tasks
    
    def _upload_task(self, task: Dict) -> bool:
        """上传单个任务"""
        try:
            response = requests.post(
                self.upload_url,
                json=task,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"上传成功: Round {task['roundNum']}, Device {task['deviceId']}")
                return True
            else:
                logger.error(f"上传失败: {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"上传异常: {e}")
            return False
    
    def _update_device_by_round(self, round_num: int, device_name: str) -> bool:
        """更新轮次设备绑定"""
        try:
            data = {
                "roundId": str(round_num),
                "deviceId": device_name
            }
            
            response = requests.post(
                self.device_update_url,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"设备绑定成功: Round {round_num} -> {device_name}")
                return True
            else:
                logger.error(f"设备绑定失败: {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"设备绑定异常: {e}")
            return False
    
    def _get_round_mis_id(self, round_num: int) -> Optional[str]:
        """获取轮次的misId"""
        try:
            params = {
                "current": 1,
                "size": 1,
                "roundNum": round_num
            }
            
            response = requests.get(
                self.round_info_url,
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("data", {}).get("records"):
                    record = data["data"]["records"][0]
                    mis_id = record.get("misId")
                    logger.debug(f"获取到轮次 {round_num} 的misId: {mis_id}")
                    return mis_id
                else:
                    logger.warning(f"未找到轮次 {round_num} 的记录")
                    return None
            else:
                logger.error(f"获取轮次信息失败: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"获取轮次信息异常: {e}")
            return None
    
    def _process_round_folder(self, folder_path: Path) -> bool:
        """处理单个轮次文件夹"""
        folder_name = folder_path.name
        log_file = folder_path / "task_structured.log"
        
        # 检查日志文件是否存在
        if not log_file.exists():
            logger.debug(f"日志文件不存在: {folder_name}")
            return False
        
        try:
            # 读取日志内容
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取轮次信息
            round_info = self._extract_round_info(folder_name)
            if not round_info:
                logger.error(f"无法提取轮次信息: {folder_name}")
                return False
            
            # 检查设备连接
            if not self._has_device_connection(log_file):
                logger.info(f"跳过无设备连接的轮次: {folder_name}")
                return False
            
            # 提取设备信息
            device_info = self._extract_device_info(content)
            if not device_info:
                logger.error(f"无法提取设备信息: {folder_name}")
                return False
            
            # 更新设备绑定
            self._update_device_by_round(round_info["round_num"], device_info["device_name"])
            
            # 解析任务
            tasks = self._parse_tasks(content, round_info, device_info)
            if not tasks:
                logger.warning(f"未找到任务步骤: {folder_name}")
                return False
            
            # 上传任务
            success_count = 0
            for task in tasks:
                if self._upload_task(task):
                    success_count += 1
                time.sleep(0.1)  # 避免请求过快
            
            logger.info(f"轮次 {folder_name} 处理完成: {success_count}/{len(tasks)} 个任务上传成功")
            
            # 注意：对于历史文件夹，通知逻辑已移到run_once中处理
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"处理轮次失败 {folder_name}: {e}")
            return False
    
    def _wait_for_completion(self, folder_path: Path, max_wait: int = 1200) -> bool:
        """等待任务完成"""
        folder_name = folder_path.name
        log_file = folder_path / "task_structured.log"
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            if self._is_task_completed(log_file):
                logger.info(f"任务完成: {folder_name}")
                return True
            
            time.sleep(5)  # 每5秒检查一次
        
        logger.warning(f"等待超时: {folder_name}")
        return False
    
    def run_once(self):
        """运行一次扫描"""
        logger.info("开始扫描轮次文件夹...")
        
        # 获取所有轮次文件夹
        round_folders = [f for f in self.log_dir.iterdir() 
                        if f.is_dir() and f.name.startswith("round_")]
        
        # 按文件夹名称排序
        round_folders.sort(key=lambda x: x.name)
        
        new_folders = []
        waiting_folders = []
        
        for folder in round_folders:
            folder_name = folder.name
            
            # 跳过已处理的轮次
            if folder_name in self.processed_rounds:
                continue
            
            # 检查是否为近期文件夹
            if self._is_recent_folder(folder_name):
                # 近期文件夹，检查是否有设备连接
                if self._has_device_connection(folder / "task_structured.log"):
                    new_folders.append(folder)
                else:
                    waiting_folders.append(folder)
            else:
                # 历史文件夹，直接处理
                new_folders.append(folder)
        
        # 处理有设备连接的文件夹
        for folder in new_folders:
            folder_name = folder.name
            
            # 如果是近期文件夹，等待任务完成
            if self._is_recent_folder(folder_name):
                task_completed = self._wait_for_completion(folder)
                if self._process_round_folder(folder):
                    self._save_processed_round(folder_name)
                    
                    # 处理完成后发送通知，需要标记是否超时
                    round_info = self._extract_round_info(folder_name)
                    if round_info:
                        round_status_info = self._get_round_info_from_status(round_info["round_num"])
                        if round_status_info and round_status_info.get("mis_id"):
                            # 合并轮次信息，添加超时标识
                            complete_round_info = {
                                **round_info,
                                **round_status_info,
                                "timeout_flag": not task_completed
                            }
                            logger.info(f"发送轮次 {round_info['round_num']} 完成通知给 {round_status_info.get('mis_id')}")
                            
                            # 读取日志内容用于提取任务完成信息
                            try:
                                log_file = folder / "task_structured.log"
                                if log_file.exists():
                                    with open(log_file, 'r', encoding='utf-8') as f:
                                        log_content = f.read()
                                    self._send_completion_notification(complete_round_info, log_content)
                                else:
                                    self._send_completion_notification(complete_round_info)
                            except Exception as e:
                                logger.error(f"读取日志文件失败: {e}")
                                self._send_completion_notification(complete_round_info)
            else:
                # 历史文件夹，直接处理
                if self._process_round_folder(folder):
                    self._save_processed_round(folder_name)
                else:
                    # 处理失败，标记为已处理避免重复
                    self._save_processed_round(folder_name)
                    
            # 处理成功的历史文件夹也要发送通知
            if not self._is_recent_folder(folder_name) and folder_name not in self.processed_rounds:
                round_info = self._extract_round_info(folder_name)
                if round_info:
                    round_status_info = self._get_round_info_from_status(round_info["round_num"])
                    if round_status_info and round_status_info.get("mis_id"):
                        # 历史文件夹没有超时概念
                        complete_round_info = {
                            **round_info,
                            **round_status_info,
                            "timeout_flag": False
                        }
                        logger.info(f"发送轮次 {round_info['round_num']} 完成通知给 {round_status_info.get('mis_id')}")
                        
                        # 读取日志内容用于提取任务完成信息
                        try:
                            log_file = folder / "task_structured.log"
                            if log_file.exists():
                                with open(log_file, 'r', encoding='utf-8') as f:
                                    log_content = f.read()
                                self._send_completion_notification(complete_round_info, log_content)
                            else:
                                self._send_completion_notification(complete_round_info)
                        except Exception as e:
                            logger.error(f"读取日志文件失败: {e}")
                            self._send_completion_notification(complete_round_info)
        
        # 报告等待中的文件夹
        if waiting_folders:
            logger.info(f"等待中的轮次: {[f.name for f in waiting_folders]}")
        
        logger.info(f"扫描完成，处理了 {len(new_folders)} 个轮次")
    
    def start_monitoring(self):
        """启动监控"""
        self.running = True
        logger.info(f"启动监控模式，检查间隔: {self.check_interval} 秒")
        
        while self.running:
            try:
                self.run_once()
                time.sleep(self.check_interval)
            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监控")
                self.running = False
                break
            except Exception as e:
                logger.error(f"监控异常: {e}")
                time.sleep(10)
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        logger.info("监控已停止")

def main():
    parser = argparse.ArgumentParser(description="简化的日志分析工具")
    parser.add_argument("--log-dir", default="log", help="日志目录")
    parser.add_argument("--env", choices=["local", "online"], default="local", help="环境")
    parser.add_argument("--monitor", action="store_true", help="启动监控模式")
    parser.add_argument("--interval", type=int, default=30, help="监控间隔（秒）")
    parser.add_argument("--once", action="store_true", help="运行一次扫描")
    
    args = parser.parse_args()
    
    analyzer = SimpleLogAnalyzer(log_dir=args.log_dir, env=args.env)
    analyzer.check_interval = args.interval
    
    if args.once:
        analyzer.run_once()
    elif args.monitor:
        analyzer.start_monitoring()
    else:
        print("请选择 --once 或 --monitor 模式")

if __name__ == "__main__":
    main()