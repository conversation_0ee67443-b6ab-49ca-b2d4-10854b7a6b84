#!/usr/bin/env python3
"""
设备状态保持工具
24小时持续运行，监控设备状态并维护设备为 ready 状态
"""

import os
import sys
import time
import signal
import threading
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入现有的工具类
from tools._config import Config
from tools.check_devices_tools import (
    check_devices_connect_android, 
    check_devices_connect_ios, 
    ensure_device_status_file,
    get_android_device_name,
    get_ios_device_name
)
from tools._device_status_manage_tools import (
    get_device_status, 
    update_device_status
)
from tools._adb_manage_tools import adb_service_manager
from tools._appium_manage_tools import appium_service_manager
from tools._wda_manage_tools import wda_service_manager


class DeviceSpecificLogger:
    """设备专用日志管理器"""
    
    def __init__(self, log_file_path: str, max_bytes: int = 100 * 1024 * 1024):
        self.log_file_path = log_file_path
        self.max_bytes = max_bytes
        self.loggers = {}
        self.setup_main_logger()
    
    def setup_main_logger(self):
        """设置主日志记录器"""
        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log_file_path), exist_ok=True)
        
        # 创建主日志记录器
        self.main_logger = logging.getLogger('device_status_keeper')
        self.main_logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for handler in self.main_logger.handlers[:]:
            self.main_logger.removeHandler(handler)
        
        # 创建旋转文件处理器
        file_handler = RotatingFileHandler(
            self.log_file_path,
            maxBytes=self.max_bytes,
            backupCount=5,
            encoding='utf-8'
        )
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        
        self.main_logger.addHandler(file_handler)
        self.main_logger.addHandler(console_handler)
        
        # 防止日志传播到根记录器
        self.main_logger.propagate = False
    
    def get_device_logger(self, device_name: str) -> logging.Logger:
        """获取设备专用日志记录器"""
        if device_name not in self.loggers:
            logger = logging.getLogger(device_name)
            logger.setLevel(logging.INFO)
            
            # 清除现有处理器
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)
            
            # 使用相同的文件处理器
            file_handler = RotatingFileHandler(
                self.log_file_path,
                maxBytes=self.max_bytes,
                backupCount=5,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
            logger.propagate = False
            
            self.loggers[device_name] = logger
        
        return self.loggers[device_name]
    
    def get_keeper_logger(self) -> logging.Logger:
        """获取keeper日志记录器"""
        return self.main_logger


class DevicePortManager:
    """设备端口管理器"""
    
    def __init__(self, logger: 'DeviceSpecificLogger'):
        self.logger = logger
        self.allocated_ports: Dict[str, Dict[str, int]] = {}  # udid -> {'appium': port, 'wda': port}
        self.port_lock = threading.Lock()
        # 从现有设备状态文件中恢复端口分配信息
        self._restore_port_allocations()
    
    def _restore_port_allocations(self):
        """从现有设备状态文件中恢复端口分配信息"""
        try:
            from tools._device_status_manage_tools import get_all_device_status
            all_device_status = get_all_device_status()
            
            # 排除agent_status.json
            device_status_dict = {udid: status for udid, status in all_device_status.items() 
                                if not udid.startswith('agent_status')}
            
            restored_count = 0
            for udid, status in device_status_dict.items():
                if not isinstance(status, dict):
                    continue
                    
                device_name = status.get('device_name', 'Unknown')
                platform = status.get('platform', '').lower()
                
                # 恢复端口分配信息（仅Appium端口，移除WDA转发端口）
                ports = {}
                if 'appium_port' in status and status['appium_port']:
                    ports['appium'] = status['appium_port']
                
                # 移除wda_forward_port恢复逻辑，WDA固定运行在设备8100端口
                
                if ports:
                    self.allocated_ports[udid] = ports
                    restored_count += 1
                    self.logger.get_keeper_logger().info(f"恢复设备 {device_name} ({udid}) 端口分配: {ports}")
            
            if restored_count > 0:
                self.logger.get_keeper_logger().info(f"成功恢复 {restored_count} 个设备的端口分配信息")
            else:
                self.logger.get_keeper_logger().info("没有找到需要恢复的端口分配信息")
                
        except Exception as e:
            self.logger.get_keeper_logger().error(f"恢复端口分配信息时发生错误: {e}")
    
    def allocate_appium_port(self, udid: str, platform: str) -> int:
        """为设备分配Appium端口"""
        with self.port_lock:
            if udid in self.allocated_ports and 'appium' in self.allocated_ports[udid]:
                return self.allocated_ports[udid]['appium']
            
            if platform.lower() == 'ios':
                base_port = Config.IOS_BASE_APPIUM_PORT
                step = Config.IOS_APPIUM_PORT_INTERVAL
            else:  # android
                base_port = Config.IOS_BASE_APPIUM_PORT + 1000  # 避免端口冲突
                step = Config.APPIUM_PORT_STEP
            
            # 查找已分配的端口
            used_ports = set()
            for device_ports in self.allocated_ports.values():
                if 'appium' in device_ports:
                    used_ports.add(device_ports['appium'])
            
            # 分配新端口
            port = base_port
            while port in used_ports:
                port += step
            
            if udid not in self.allocated_ports:
                self.allocated_ports[udid] = {}
            self.allocated_ports[udid]['appium'] = port
            
            self.logger.get_keeper_logger().info(f"为设备 {udid} 分配Appium端口: {port}")
            return port
    
    
    def get_device_ports(self, udid: str) -> Dict[str, int]:
        """获取设备已分配的端口，实时从设备状态文件同步"""
        try:
            # 从设备状态文件中获取最新端口信息
            device_status = get_device_status(udid)
            if device_status:
                # 只同步Appium端口信息（移除WDA转发端口逻辑）
                if 'appium_port' in device_status and device_status['appium_port']:
                    current_appium_port = device_status['appium_port']
                    if udid not in self.allocated_ports:
                        self.allocated_ports[udid] = {}
                    
                    if self.allocated_ports[udid].get('appium') != current_appium_port:
                        self.allocated_ports[udid]['appium'] = current_appium_port
                        self.logger.get_keeper_logger().info(f"同步设备 {udid} 的Appium端口: {current_appium_port}")
        
        except Exception as e:
            self.logger.get_keeper_logger().error(f"同步设备 {udid} 端口信息时发生错误: {e}")
        
        return self.allocated_ports.get(udid, {})
    
    def release_device_ports(self, udid: str):
        """释放设备端口"""
        with self.port_lock:
            if udid in self.allocated_ports:
                ports = self.allocated_ports.pop(udid)
                self.logger.get_keeper_logger().info(f"释放设备 {udid} 的端口: {ports}")
    
    def resolve_appium_port_conflict(self, udid: str, platform: str) -> int:
        """
        解决Appium端口冲突，直接分配新的可用端口
        
        Args:
            udid (str): 设备UDID
            platform (str): 设备平台
            
        Returns:
            int: 新分配的Appium端口
        """
        with self.port_lock:
            # 获取当前端口
            current_ports = self.allocated_ports.get(udid, {})
            old_port = current_ports.get('appium')
            
            if old_port:
                self.logger.get_keeper_logger().info(f"设备 {udid} Appium端口 {old_port} 冲突，直接分配新端口")
            
            # 分配新端口
            if platform.lower() == 'ios':
                base_port = Config.IOS_BASE_APPIUM_PORT
                step = Config.IOS_APPIUM_PORT_INTERVAL
            else:  # android
                base_port = Config.IOS_BASE_APPIUM_PORT + 1000  # 避免端口冲突
                step = Config.APPIUM_PORT_STEP
            
            # 查找已分配的端口和系统中被占用的端口
            used_ports = set()
            for device_ports in self.allocated_ports.values():
                if 'appium' in device_ports:
                    used_ports.add(device_ports['appium'])
            
            # 分配新端口，跳过已使用的端口和有冲突的旧端口
            port = base_port
            while port in used_ports or port == old_port or self._is_port_occupied(port):
                port += step
            
            # 更新端口分配
            if udid not in self.allocated_ports:
                self.allocated_ports[udid] = {}
            self.allocated_ports[udid]['appium'] = port
            
            if old_port:
                self.logger.get_keeper_logger().info(f"为设备 {udid} 重新分配Appium端口: {old_port} -> {port}")
            else:
                self.logger.get_keeper_logger().info(f"为设备 {udid} 分配Appium端口: {port}")
            return port
    
    def _is_port_occupied(self, port: int) -> bool:
        """
        检查端口是否被占用
        
        Args:
            port (int): 端口号
            
        Returns:
            bool: 端口是否被占用
        """
        try:
            import subprocess
            result = subprocess.run(f"lsof -t -i:{port}", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return result.returncode == 0 and result.stdout.strip()
        except Exception:
            return False


class IOSDeviceManager:
    """iOS设备管理器"""
    
    def __init__(self, logger: 'DeviceSpecificLogger', port_manager: DevicePortManager):
        self.logger = logger
        self.port_manager = port_manager
        # 移除device_timers，改用设备状态文件存储重启时间
    
    def setup_ios_device(self, udid: str, device_name: str) -> bool:
        """设置iOS设备为ready状态"""
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            # 分配端口（仅Appium端口，WDA固定运行在设备8100端口）
            appium_port = self.port_manager.allocate_appium_port(udid, 'ios')
            
            # 更新设备状态和端口信息
            update_device_status(udid, {
                'status': 'preparing',
                'appium_port': appium_port
            })
            
            # 1. 准备WDA
            device_logger.info(f"开始为设备 {device_name} (UDID: {udid}) 准备WDA")
            if not wda_service_manager.prepare_device_wda(device_logger, udid):
                device_logger.error(f"为设备 {device_name} 准备WDA失败")
                update_device_status(udid, {'status': 'wda_prepare_failed'})
                return False
            
            # 2. 启动Appium服务
            device_logger.info(f"启动设备 {device_name} 的Appium服务，端口: {appium_port}")
            success, _ = appium_service_manager.start_appium_server(
                appium_port, device_logger=device_logger
            )
            
            if not success:
                device_logger.error(f"启动设备 {device_name} 的Appium服务失败")
                update_device_status(udid, {'status': 'appium_start_failed'})
                return False
            
            # 3. 启动WDA服务
            device_logger.info(f"开始为设备 {device_name} (UDID: {udid}) 安装WDA")
            update_device_status(udid, {'status': 'installing_wda'})
            
            success_wda = wda_service_manager.start_wda_for_device(
                udid, device_logger, device_name=device_name
            )
            
            if not success_wda:
                device_logger.error(f"为设备 {device_name} 安装WDA失败")
                update_device_status(udid, {'status': 'wda_install_failed'})
                return False
            
            # 4. 记录WDA启动时间
            # 设备状态文件中已经记录了重启时间
            
            # 5. 设置设备为ready状态
            update_device_status(udid, {
                'status': 'ready',
                'wda_last_restart_time': time.time()
            })
            device_logger.info(f"设备 {device_name} (UDID: {udid}) 已准备就绪")
            
            return True
            
        except Exception as e:
            device_logger.error(f"设置iOS设备 {device_name} 失败: {e}")
            update_device_status(udid, {'status': 'setup_failed'})
            return False
    
    def fix_appium_failure(self, udid: str, device_name: str) -> bool:
        """
        专门处理Appium启动失败的情况
        
        Args:
            udid (str): 设备UDID
            device_name (str): 设备名称
            
        Returns:
            bool: 是否成功修复Appium问题
        """
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            device_logger.info(f"开始修复设备 {device_name} 的Appium启动问题")
            
            # 1. 解决端口冲突
            device_logger.info(f"正在解决设备 {device_name} 的Appium端口冲突")
            new_appium_port = self.port_manager.resolve_appium_port_conflict(udid, 'ios')
            
            # 2. 更新设备状态中的端口信息
            update_device_status(udid, {
                'appium_port': new_appium_port,
                'status': 'fixing_appium'
            })
            
            # 3. 重新启动Appium服务
            device_logger.info(f"使用新端口 {new_appium_port} 重新启动设备 {device_name} 的Appium服务")
            success, _ = appium_service_manager.start_appium_server(
                new_appium_port, device_logger=device_logger
            )
            
            if not success:
                device_logger.error(f"使用新端口 {new_appium_port} 启动Appium服务仍然失败")
                update_device_status(udid, {'status': 'appium_fix_failed'})
                return False
            
            # 4. 验证Appium服务状态
            if appium_service_manager.check_appium_status(new_appium_port, device_logger):
                device_logger.info(f"设备 {device_name} Appium服务修复成功，端口: {new_appium_port}")
                update_device_status(udid, {'status': 'ready'})
                return True
            else:
                device_logger.error(f"设备 {device_name} Appium服务修复后验证失败")
                update_device_status(udid, {'status': 'appium_fix_failed'})
                return False
                
        except Exception as e:
            device_logger.error(f"修复设备 {device_name} Appium问题时发生错误: {e}")
            update_device_status(udid, {'status': 'appium_fix_failed'})
            return False
    
    def maintain_ios_device(self, udid: str, device_name: str) -> bool:
        """维护iOS设备状态"""
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            # 新版本：WDA固定运行在8100端口，不再需要WDA转发端口
            # 检查并确保设备有Appium端口
            device_ports = self.port_manager.get_device_ports(udid)
            if not device_ports.get('appium'):
                device_logger.warning(f"设备 {device_name} 未Appium端口，重新分配")
                appium_port = self.port_manager.allocate_appium_port(udid, 'ios')
                
                # 更新设备状态中的端口信息，并初始化重启时间
                update_device_status(udid, {
                    'appium_port': appium_port,
                    'wda_last_restart_time': 0.0  # 初始化重启时间，避免立即触发重启
                })
                
                device_logger.info(f"已为设备 {device_name} 重新分配Appium端口: {appium_port}")
            
            # 1. 检查WDA健康状态（使用新的临时iproxy健康检查）
            device_logger.info(f"[设备维护] 检查设备 {device_name} WDA健康状态 (设备8100端口)")
            wda_healthy = wda_service_manager.check_wda_status(udid, device_logger, device_name=device_name)
            
            if not wda_healthy:
                device_logger.warning(f"设备 {device_name} WDA服务不健康，需要重新安装")
                
                # 更新状态为正在重新安装WDA
                update_device_status(udid, {'status': 'reinstalling_wda'})
                
                # 重新安装WDA
                device_logger.info(f"开始为设备 {device_name} 重新安装WDA")
                success_wda = wda_service_manager.start_wda_for_device(
                    udid, device_logger, device_name=device_name
                )
                
                if not success_wda:
                    device_logger.error(f"设备 {device_name} WDA重新安装失败")
                    update_device_status(udid, {'status': 'wda_install_failed'})
                    return False
                
                # 更新WDA启动时间
                # 设备状态文件中已经记录了重启时间
                
                device_logger.info(f"设备 {device_name} WDA重新安装成功")
                
                # 更新设备状态，记录重启时间
                update_device_status(udid, {
                    'status': 'ready',
                    'wda_last_restart_time': time.time()
                })
            else:
                device_logger.debug(f"设备 {device_name} WDA服务健康")
                
                # WDA健康的情况下，无需发送保活请求（新版本移除保活机制）
                device_logger.debug(f"设备 {device_name} WDA服务健康，无需额外保活操作")
                
                # WDA健康的情况下，检查是否需要定期重启
                # 从设备状态文件中获取重启时间
                device_status = get_device_status(udid)
                if device_status and 'wda_last_restart_time' in device_status:
                    wda_last_restart = device_status['wda_last_restart_time']
                    
                    if isinstance(wda_last_restart, (int, float)) and (time.time() - wda_last_restart) >= Config.WDA_RESTART_INTERVAL:
                        device_logger.info(f"设备 {device_name} WDA需要定期重启")
                        
                        # 获取设备信息
                        device_info = {'udid': udid, 'name': device_name}
                        
                        # 重启WDA（使用新的WDA重启逻辑，不需要传递端口参数）
                        new_restart_time = wda_service_manager.check_and_restart_wda_if_needed(
                            device_info, None, wda_last_restart, 1, device_logger
                        )
                        
                        # 更新设备状态文件中的重启时间
                        update_device_status(udid, {'wda_last_restart_time': new_restart_time})
                        
                        if new_restart_time > wda_last_restart:
                            device_logger.info(f"设备 {device_name} WDA定期重启成功")
                        else:
                            device_logger.warning(f"设备 {device_name} WDA定期重启失败")
                            return False
            
            # 2. 检查Appium服务状态
            appium_port = self.port_manager.get_device_ports(udid).get('appium')
            if appium_port:
                device_logger.debug(f"检查设备 {device_name} Appium服务状态 (端口: {appium_port})")
                
                # 使用新的重试机制检查Appium状态
                if not appium_service_manager.check_appium_status(appium_port, device_logger, with_retry=True):
                    device_logger.warning(f"设备 {device_name} Appium服务异常，尝试重启")
                    
                    # 重启Appium服务
                    appium_service_manager.stop_appium_server(appium_port, device_logger)
                    time.sleep(3)  # 减少等待时间
                    
                    success, _ = appium_service_manager.start_appium_server(
                        appium_port, device_logger=device_logger
                    )
                    
                    if not success:
                        device_logger.error(f"重启设备 {device_name} Appium服务失败")
                        return False
                    
                    device_logger.info(f"设备 {device_name} Appium服务重启成功")
                else:
                    # Appium服务健康时，发送保活请求
                    device_logger.debug(f"设备 {device_name} Appium服务健康")
                    try:
                        appium_service_manager.keep_appium_alive(appium_port, device_logger)
                    except Exception as e:
                        device_logger.warning(f"Appium保活请求失败: {e}")
            
            # 3. 维护成功，更新last_update时间戳
            update_device_status(udid, {'status': 'ready'})
            device_logger.debug(f"设备 {device_name} 维护完成，状态为ready")
            
            return True
            
        except Exception as e:
            device_logger.error(f"维护iOS设备 {device_name} 失败: {e}")
            return False


class AndroidDeviceManager:
    """Android设备管理器"""
    
    def __init__(self, logger: 'DeviceSpecificLogger', port_manager: DevicePortManager):
        self.logger = logger
        self.port_manager = port_manager
        # 移除last_adb_restart，改用设备状态文件存储重启时间
    
    def setup_android_device(self, udid: str, device_name: str) -> bool:
        """设置Android设备为ready状态"""
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            # 分配端口
            appium_port = self.port_manager.allocate_appium_port(udid, 'android')
            
            # 更新设备状态和端口信息（Android设备没有WDA端口）
            update_device_status(udid, {
                'status': 'preparing',
                'appium_port': appium_port
            })
            
            # 1. 启动Appium服务
            device_logger.info(f"启动设备 {device_name} 的Appium服务，端口: {appium_port}")
            success, _ = appium_service_manager.start_appium_server(
                appium_port, device_logger=device_logger
            )
            
            if not success:
                device_logger.error(f"启动设备 {device_name} 的Appium服务失败")
                update_device_status(udid, {'status': 'appium_start_failed'})
                return False
            
            # 2. 设置设备为ready状态
            update_device_status(udid, {
                'status': 'ready',
                'adb_last_restart_time': time.time()
            })
            device_logger.info(f"设备 {device_name} (UDID: {udid}) 已准备就绪")
            
            return True
            
        except Exception as e:
            device_logger.error(f"设置Android设备 {device_name} 失败: {e}")
            update_device_status(udid, {'status': 'setup_failed'})
            return False
    
    def fix_appium_failure(self, udid: str, device_name: str) -> bool:
        """
        专门处理Android设备Appium启动失败的情况
        
        Args:
            udid (str): 设备UDID
            device_name (str): 设备名称
            
        Returns:
            bool: 是否成功修复Appium问题
        """
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            device_logger.info(f"开始修复设备 {device_name} 的Appium启动问题")
            
            # 1. 解决端口冲突
            device_logger.info(f"正在解决设备 {device_name} 的Appium端口冲突")
            new_appium_port = self.port_manager.resolve_appium_port_conflict(udid, 'android')
            
            # 2. 更新设备状态中的端口信息
            update_device_status(udid, {
                'appium_port': new_appium_port,
                'status': 'fixing_appium'
            })
            
            # 3. 重新启动Appium服务
            device_logger.info(f"使用新端口 {new_appium_port} 重新启动设备 {device_name} 的Appium服务")
            success, _ = appium_service_manager.start_appium_server(
                new_appium_port, device_logger=device_logger
            )
            
            if not success:
                device_logger.error(f"使用新端口 {new_appium_port} 启动Appium服务仍然失败")
                update_device_status(udid, {'status': 'appium_fix_failed'})
                return False
            
            # 4. 验证Appium服务状态
            if appium_service_manager.check_appium_status(new_appium_port, device_logger):
                device_logger.info(f"设备 {device_name} Appium服务修复成功，端口: {new_appium_port}")
                update_device_status(udid, {'status': 'ready'})
                return True
            else:
                device_logger.error(f"设备 {device_name} Appium服务修复后验证失败")
                update_device_status(udid, {'status': 'appium_fix_failed'})
                return False
                
        except Exception as e:
            device_logger.error(f"修复设备 {device_name} Appium问题时发生错误: {e}")
            update_device_status(udid, {'status': 'appium_fix_failed'})
            return False
    
    def maintain_android_device(self, udid: str, device_name: str) -> bool:
        """维护Android设备状态"""
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            # 检查是否需要重启ADB
            current_time = time.time()
            device_status = get_device_status(udid)
            
            if device_status and 'adb_last_restart_time' in device_status:
                adb_last_restart = device_status['adb_last_restart_time']
                
                if isinstance(adb_last_restart, (int, float)) and (current_time - adb_last_restart) >= Config.ADB_RESTART_INTERVAL:
                    device_logger.info(f"ADB服务需要定期重启")
                    if adb_service_manager.restart_adb_service():
                        # 更新设备状态文件中的重启时间
                        update_device_status(udid, {'adb_last_restart_time': current_time})
                        device_logger.info(f"ADB服务重启成功")
                    else:
                        device_logger.error(f"ADB服务重启失败")
                        return False
            else:
                # 如果没有记录，初始化为当前时间
                device_logger.info(f"初始化ADB重启时间")
                update_device_status(udid, {'adb_last_restart_time': current_time})
            
            # 检查Appium服务状态
            appium_port = self.port_manager.get_device_ports(udid).get('appium')
            if appium_port:
                if not appium_service_manager.check_appium_status(appium_port, device_logger):
                    device_logger.warning(f"设备 {device_name} Appium服务异常，尝试重启")
                    
                    # 重启Appium服务
                    appium_service_manager.stop_appium_server(appium_port, device_logger)
                    time.sleep(5)
                    
                    success, _ = appium_service_manager.start_appium_server(
                        appium_port, device_logger=device_logger
                    )
                    
                    if not success:
                        device_logger.error(f"重启设备 {device_name} Appium服务失败")
                        return False
            
            # 维护成功，更新last_update时间戳
            update_device_status(udid, {'status': 'ready'})
            
            return True
            
        except Exception as e:
            device_logger.error(f"维护Android设备 {device_name} 失败: {e}")
            return False



class DeviceStatusKeeper:
    """设备状态保持器"""
    
    def __init__(self):
        self.running = False
        self.shutdown_event = threading.Event()  # 用于中断等待
        
        # 设置日志
        log_file = os.path.join(Config.LOG_ROOT, "device_status_keep.log")
        self.logger = DeviceSpecificLogger(log_file)
        self.keeper_logger = self.logger.get_keeper_logger()
        
        # 初始化管理器
        self.port_manager = DevicePortManager(self.logger)
        self.ios_manager = IOSDeviceManager(self.logger, self.port_manager)
        self.android_manager = AndroidDeviceManager(self.logger, self.port_manager)
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _cleanup_device_status(self):
        """清理设备状态，停止WDA服务并将设备状态设置为waiting"""
        try:
            self.keeper_logger.info("开始清理设备状态和WDA服务...")
            
            # 获取所有连接的设备
            devices = self._discover_devices()
            
            for udid, device_info in devices.items():
                try:
                    device_name = device_info['name']
                    platform = device_info['platform']
                    
                    # 检查设备状态是否需要清理
                    device_status = get_device_status(udid)
                    if device_status:
                        current_status = device_status.get('status', '')
                        
                        # 对于iOS设备，停止WDA服务
                        if platform == 'ios' and current_status not in ['offline', 'waiting']:
                            self.keeper_logger.info(f"停止设备 {device_name} ({udid}) 的WDA服务...")
                            device_logger = self.logger.get_device_logger(device_name)
                            
                            # 停止WDA进程（新版本不需要转发端口参数）
                            try:
                                wda_service_manager.stop_wda_process(device_logger, None, udid)
                                self.keeper_logger.info(f"已停止设备 {device_name} WDA服务")
                            except Exception as wda_stop_error:
                                self.keeper_logger.warning(f"停止设备 {device_name} WDA服务时发生错误: {wda_stop_error}")
                            
                        # 将非offline和非waiting状态的设备设置为waiting
                        if current_status not in ['offline', 'waiting']:
                            self.keeper_logger.info(f"清理设备 {device_name} ({udid}) 状态: {current_status} -> waiting")
                            update_device_status(udid, {'status': 'waiting'})
                            
                except Exception as e:
                    self.keeper_logger.error(f"清理设备状态时发生错误 {udid}: {e}")
            
            self.keeper_logger.info("设备状态和WDA服务清理完成")
        except Exception as e:
            self.keeper_logger.error(f"清理设备状态时发生错误: {e}")
    
    def _signal_handler(self, signum, _):
        """信号处理器 - 立即响应Ctrl+C"""
        if self.running:  # 只在第一次接收到信号时执行清理
            self.keeper_logger.info(f"接收到信号 {signum} (Ctrl+C)，立即开始清理...")
            self.running = False
            
            # 立即设置关闭事件，中断等待
            self.shutdown_event.set()
            
            # 清理设备状态和WDA服务
            self._cleanup_device_status()
            
            self.keeper_logger.info("清理完成，准备退出...")
        else:
            self.keeper_logger.info(f"再次接收到信号 {signum}，强制退出...")
            import sys
            sys.exit(0)
    
    def _discover_devices(self) -> Dict[str, Dict[str, str]]:
        """发现当前连接的设备"""
        devices = {}
        
        # 发现Android设备
        android_devices = check_devices_connect_android()
        for udid in android_devices:
            device_name = get_android_device_name(udid)
            devices[udid] = {
                'name': device_name,
                'platform': 'android'
            }
            # 确保设备状态文件存在
            ensure_device_status_file(udid, 'android')
        
        # 发现iOS设备
        ios_devices = check_devices_connect_ios()
        for udid in ios_devices:
            device_name = get_ios_device_name(udid)
            devices[udid] = {
                'name': device_name,
                'platform': 'ios'
            }
            # 确保设备状态文件存在
            ensure_device_status_file(udid, 'ios')
        
        return devices
    
    def _should_skip_device(self, udid: str) -> bool:
        """检查是否应该跳过设备（状态为running或testing）"""
        device_status = get_device_status(udid)
        if not device_status:
            return False
        
        status = device_status.get('status', '')
        # 跳过正在运行测试的设备和正在测试的设备
        return status in ['running', 'testing']
    
    def _maintain_device(self, udid: str, device_info: Dict[str, str]) -> bool:
        """维护单个设备状态"""
        device_name = device_info['name']
        platform = device_info['platform']
        
        device_logger = self.logger.get_device_logger(device_name)
        
        try:
            # 检查是否应该跳过
            if self._should_skip_device(udid):
                device_logger.debug(f"设备 {device_name} 状态为running，跳过维护")
                return True
            
            # 获取设备状态
            device_status = get_device_status(udid)
            if not device_status:
                device_logger.error(f"无法获取设备 {device_name} 的状态")
                return False
            
            current_status = device_status.get('status', '')
            
            # 根据当前状态决定操作
            if current_status == 'ready':
                # 设备已经是ready状态，进行维护
                if platform == 'ios':
                    return self.ios_manager.maintain_ios_device(udid, device_name)
                else:
                    return self.android_manager.maintain_android_device(udid, device_name)
            
            elif current_status == 'appium_start_failed':
                # 专门处理Appium启动失败的情况，不需要重新初始化WDA
                device_logger.info(f"设备 {device_name} Appium启动失败，开始修复Appium服务")
                if platform == 'ios':
                    return self.ios_manager.fix_appium_failure(udid, device_name)
                else:
                    return self.android_manager.fix_appium_failure(udid, device_name)
            
            elif current_status in ['connected', 'waiting', 'setup_failed', 'wda_install_failed', 'wda_prepare_failed', 'appium_fix_failed']:
                # 设备需要初始化或重新初始化
                device_logger.info(f"设备 {device_name} 状态为 {current_status}，开始初始化流程")
                if platform == 'ios':
                    return self.ios_manager.setup_ios_device(udid, device_name)
                else:
                    return self.android_manager.setup_android_device(udid, device_name)
            
            else:
                # 其他状态（如preparing, installing_wda等）正在处理中，不做操作
                device_logger.debug(f"设备 {device_name} 状态为 {current_status}，暂时跳过")
                return True
        
        except Exception as e:
            device_logger.error(f"维护设备 {device_name} 时发生错误: {e}")
            return False
    
    def _get_check_interval(self, devices):
        """
        根据设备状态智能调整检查间隔
        
        Args:
            devices: 当前连接的设备信息
            
        Returns:
            int: 检查间隔（秒）
        """
        min_interval = 300  # 默认5分钟
        
        # 检查所有设备的状态
        for udid, device_info in devices.items():
            device_status = get_device_status(udid)
            if not device_status:
                continue
                
            current_status = device_status.get('status', '')
            platform = device_info.get('platform', '')
            
            # 根据状态和平台决定间隔
            if current_status == 'ready' and platform == 'ios':
                # iOS ready状态需要30秒保活
                min_interval = min(min_interval, 30)
            elif current_status in ['installing_wda', 'reinstalling_wda', 'preparing']:
                # 安装状态3分钟检查一次，避免干扰
                min_interval = min(min_interval, 180)
            elif current_status in ['fixing_appium', 'appium_start_failed', 'appium_fix_failed']:
                # Appium相关问题快速检查
                min_interval = min(min_interval, 30)
            else:
                # 其他状态1分钟检查
                min_interval = min(min_interval, 60)
        
        return min_interval
    
    def _sync_device_status(self):
        """同步设备状态 - 处理offline状态和超时检查"""
        try:
            self.keeper_logger.info("开始设备状态同步")
            
            # 1. 获取所有历史设备状态（排除agent_status.json）
            from tools._device_status_manage_tools import get_all_device_status
            all_device_status = get_all_device_status()
            
            # 排除agent_status.json
            historical_devices = {udid: status for udid, status in all_device_status.items() 
                                if not udid.startswith('agent_status')}
            
            self.keeper_logger.info(f"历史设备数量: {len(historical_devices)}")
            
            # 2. 获取所有已连接设备
            connected_android_devices = check_devices_connect_android()
            connected_ios_devices = check_devices_connect_ios()
            all_connected_devices = set(connected_android_devices + connected_ios_devices)
            
            self.keeper_logger.info(f"已连接设备数量: {len(all_connected_devices)}")
            
            # 3. 处理历史设备状态同步
            current_time = time.time()
            timeout_threshold = 10 * 60  # 10分钟
            
            for udid, status in historical_devices.items():
                device_name = status.get('device_name', 'Unknown')
                current_status = status.get('status', '')
                last_update = status.get('last_update', 0)
                
                # 检查设备是否在已连接设备中
                if udid in all_connected_devices:
                    # 设备在已连接设备中
                    if current_status == 'offline':
                        # 历史设备状态为offline但在已连接设备中，标记为connected
                        self.keeper_logger.info(f"设备 {device_name} ({udid}) 从offline状态恢复为connected")
                        update_device_status(udid, {'status': 'connected'})
                    elif current_status != 'offline':
                        # 检查非offline设备的last_update是否超过10分钟
                        if isinstance(last_update, (int, float)) and (current_time - last_update) > timeout_threshold:
                            self.keeper_logger.info(f"设备 {device_name} ({udid}) 超时，状态从 {current_status} 更新为 connected")
                            update_device_status(udid, {'status': 'connected'})
                else:
                    # 设备不在已连接设备中
                    if current_status != 'offline':
                        # 历史设备不在已连接设备中，标记为offline
                        self.keeper_logger.info(f"设备 {device_name} ({udid}) 未连接，状态从 {current_status} 更新为 offline")
                        update_device_status(udid, {'status': 'offline'})
            
            # 4. 处理已连接设备中新出现的设备
            historical_device_udids = set(historical_devices.keys())
            new_devices = all_connected_devices - historical_device_udids
            
            for udid in new_devices:
                # 判断设备平台
                if udid in connected_android_devices:
                    platform = 'android'
                elif udid in connected_ios_devices:
                    platform = 'ios'
                else:
                    continue
                
                # 为新设备创建状态文件
                self.keeper_logger.info(f"发现新设备 {udid} ({platform})，创建状态文件")
                ensure_device_status_file(udid, platform)
            
            self.keeper_logger.info("设备状态同步完成")
            
        except Exception as e:
            self.keeper_logger.error(f"设备状态同步发生错误: {e}")
    
    def _run_maintenance_cycle(self, devices=None):
        """运行一次维护周期"""
        try:
            self.keeper_logger.info("开始设备状态维护周期")
            
            # 首先进行设备状态同步
            self._sync_device_status()
            
            # 如果没有提供设备信息，则重新发现
            if devices is None:
                devices = self._discover_devices()
            
            if not devices:
                self.keeper_logger.info("未发现连接的设备")
                return
            
            self.keeper_logger.info(f"发现 {len(devices)} 个设备: {list(devices.keys())}")
            
            # 维护每个设备
            for udid, device_info in devices.items():
                if not self.running:
                    break
                
                device_name = device_info['name']
                platform = device_info['platform']
                
                self.keeper_logger.info(f"维护设备: {device_name} ({platform}, UDID: {udid})")
                
                success = self._maintain_device(udid, device_info)
                
                if success:
                    self.keeper_logger.info(f"设备 {device_name} 维护成功")
                else:
                    self.keeper_logger.error(f"设备 {device_name} 维护失败")
        
        except Exception as e:
            self.keeper_logger.error(f"设备状态维护周期发生错误: {e}")
    
    def start(self):
        """启动设备状态保持器"""
        self.keeper_logger.info("设备状态保持器启动（智能间隔调整）")
        
        self.running = True
        
        try:
            while self.running:
                start_time = time.time()
                
                # 首先发现设备以确定动态间隔
                devices = self._discover_devices()
                check_interval = self._get_check_interval(devices)
                
                self.keeper_logger.info(f"当前检查间隔: {check_interval} 秒")
                
                # 运行维护周期（传递设备信息避免重复发现）
                self._run_maintenance_cycle(devices)
                
                # 如果不在运行状态，立即退出
                if not self.running:
                    self.keeper_logger.info("收到停止信号，立即退出维护循环")
                    break
                
                # 计算等待时间
                elapsed_time = time.time() - start_time
                wait_time = max(0, check_interval - elapsed_time)
                
                if wait_time > 0:
                    self.keeper_logger.info(f"维护周期完成，等待 {wait_time:.1f} 秒后继续 (按Ctrl+C立即退出)")
                    # 使用Event.wait()替代time.sleep()，可以被中断
                    if self.shutdown_event.wait(timeout=wait_time):
                        # 如果事件被设置（接收到关闭信号），立即退出
                        self.keeper_logger.info("收到关闭信号，立即退出等待")
                        break
        
        except KeyboardInterrupt:
            self.keeper_logger.info("接收到键盘中断信号")
            # 设置为不运行状态
            self.running = False
            # 执行清理
            self._cleanup_device_status()
        except Exception as e:
            self.keeper_logger.error(f"设备状态保持器发生错误: {e}")
            # 设置为不运行状态
            self.running = False
            # 在异常时也执行清理
            self._cleanup_device_status()
        finally:
            self.keeper_logger.info("设备状态保持器已停止")
    
    def stop(self):
        """停止设备状态保持器"""
        self.running = False
        # 设置关闭事件，中断等待
        self.shutdown_event.set()


def main():
    """主函数"""
    print("设备状态保持工具启动中...")
    
    # 创建并启动设备状态保持器
    keeper = DeviceStatusKeeper()
    
    try:
        keeper.start()
    except KeyboardInterrupt:
        print("\n接收到中断信号，正在停止...")
    finally:
        keeper.stop()
        print("设备状态保持工具已停止")


if __name__ == "__main__":
    main()