import json
import os
import time
import shutil
import sys
from datetime import datetime
from typing import Dict, Optional

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from log_manager import log_manager
from status_manager import StatusManager, TaskStatus


class TestSessionManager:
    """测试会话管理器，负责创建新轮次和管理对应的日志文件夹"""
    
    def __init__(self):
        self.status_manager = StatusManager()
        self.log_dir = "log"
    
    def _restart_specific_device_app(self, udid: str):
        """重启指定设备的美团应用"""
        try:
            # 导入必要的模块
            from tools.app_operate_tools import restart_app
            
            # 重启指定设备的应用
            try:
                result = restart_app(udid)
                if result == "success":
                    log_manager.info_tools(f"成功重启设备 {udid} 的美团应用", "restart_specific_device_app")
                    return True
                else:
                    log_manager.warning_tools(f"重启设备 {udid} 应用失败: {result}", "restart_specific_device_app")
                    return False
            except Exception as e:
                log_manager.error_tools(f"重启设备 {udid} 应用异常: {e}", "restart_specific_device_app")
                return False
                
        except Exception as e:
            log_manager.error_tools(f"重启设备 {udid} 应用过程中发生异常: {e}", "restart_specific_device_app")
            return False
        
    def start_new_test_session(self, task_description: str = "新测试会话", mis_id: Optional[str] = None) -> Dict:
        """
        启动新的测试会话
        
        Args:
            task_description: 任务描述
            
        Returns:
            包含会话信息的字典
        """
        try:
            # 1. 创建新任务轮次
            round_id = self.status_manager.create_new_task(task_description, mis_id)
            
            # 2. 获取轮次信息
            round_info = self.status_manager.get_round_log_info(round_id)
            if not round_info:
                raise Exception(f"无法获取轮次 {round_id} 的信息")
            
            # 3. 创建专用的日志文件夹
            log_folder = round_info["log_dir"]
            full_log_path = os.path.join(os.getcwd(), log_folder)
            
            if not os.path.exists(full_log_path):
                os.makedirs(full_log_path, exist_ok=True)
                log_manager.info_tools(f"创建日志文件夹: {full_log_path}", "start_test_session")
            
            # 4. 更新 log_manager 的日志目录为新的轮次目录
            switch_result = log_manager.switch_log_directory(full_log_path)
            if switch_result["status"] != "success":
                raise Exception(f"切换日志目录失败: {switch_result['message']}")
            old_log_dir = switch_result["old_directory"]
            
            # 5. 更新任务状态为运行中
            self.status_manager.update_task_status(round_id, TaskStatus.RUNNING)
            
            # 6. 不再自动重启美团应用，改为在 start_device_test 中针对性重启
            
            # 7. 记录会话开始
            start_message = f"测试会话开始 - 轮次: {round_id}, 任务: {task_description}"
            log_manager.info_agent(start_message)
            
            # 8. 构建返回结果
            result = {
                "status": "success",
                "round_id": round_id,
                "task_description": task_description,
                "log_folder": log_folder,
                "full_log_path": full_log_path,
                "start_time": round_info["start_time"],
                "previous_log_dir": old_log_dir,
                "current_round": self.status_manager.get_current_round(),
                "message": f"成功启动测试会话 {round_id}，日志保存到: {log_folder}"
            }
            
            log_manager.info_tools(f"测试会话启动成功: {round_id}", "start_test_session")
            return result
            
        except Exception as e:
            error_result = {
                "status": "error",
                "error": str(e),
                "message": f"启动测试会话失败: {str(e)}"
            }
            log_manager.error_tools(f"启动测试会话失败: {str(e)}", "start_test_session")
            return error_result
    
    def end_current_test_session(self, round_id: str, result: str = "成功完成", error: Optional[str] = None) -> Dict:
        """
        结束当前测试会话
        
        Args:
            round_id: 轮次ID
            result: 执行结果
            error: 错误信息（如果有）
            
        Returns:
            包含结束信息的字典
        """
        try:
            # 1. 更新任务状态
            final_status = TaskStatus.FAILED if error else TaskStatus.COMPLETED
            self.status_manager.update_task_status(round_id, final_status, result, error)
            
            # 2. 获取任务信息
            task_info = self.status_manager.get_task_info(round_id)
            
            # 3. 记录会话结束
            end_message = f"测试会话结束 - 轮次: {round_id}, 状态: {final_status.value}"
            log_manager.info_agent(end_message)
            
            result_dict = {
                "status": "success",
                "round_id": round_id,
                "final_status": final_status.value,
                "result": result,
                "error": error,
                "task_info": task_info,
                "message": f"测试会话 {round_id} 已结束，状态: {final_status.value}"
            }
            
            log_manager.info_tools(f"测试会话结束: {round_id}", "end_test_session")
            return result_dict
            
        except Exception as e:
            error_result = {
                "status": "error",
                "error": str(e),
                "message": f"结束测试会话失败: {str(e)}"
            }
            log_manager.error_tools(f"结束测试会话失败: {str(e)}", "end_test_session")
            return error_result
    
    def get_session_status(self, round_id: Optional[str] = None) -> Dict:
        """
        获取会话状态信息
        
        Args:
            round_id: 轮次ID，如果为None则返回所有会话信息
            
        Returns:
            包含状态信息的字典
        """
        try:
            if round_id:
                task_info = self.status_manager.get_task_info(round_id)
                if not task_info:
                    return {
                        "status": "error",
                        "message": f"未找到轮次 {round_id} 的信息"
                    }
                
                return {
                    "status": "success",
                    "round_id": round_id,
                    "task_info": task_info,
                    "message": f"轮次 {round_id} 状态: {task_info['status']}"
                }
            else:
                all_tasks = self.status_manager.get_all_tasks()
                current_round = self.status_manager.get_current_round()
                running_tasks = self.status_manager.get_running_tasks()
                
                return {
                    "status": "success",
                    "current_round": current_round,
                    "total_tasks": len(all_tasks),
                    "running_tasks_count": len(running_tasks),
                    "running_tasks": running_tasks,
                    "all_tasks": all_tasks,
                    "message": f"当前轮次: {current_round}, 总任务数: {len(all_tasks)}, 运行中: {len(running_tasks)}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": f"获取会话状态失败: {str(e)}"
            }


# 全局测试会话管理器实例
test_session_manager = TestSessionManager()


def start_new_test_session(task_description: str = "新测试会话", mis_id: Optional[str] = None) -> str:
    """
    启动新的测试会话 - 供agent工具调用
    
    Args:
        task_description: 任务描述
        
    Returns:
        JSON格式的执行结果
    """
    result = test_session_manager.start_new_test_session(task_description, mis_id)
    return json.dumps(result, ensure_ascii=False, indent=2)


def end_current_test_session(round_id: str, result: str = "成功完成", error: str = "") -> str:
    """
    结束当前测试会话 - 供agent工具调用
    
    Args:
        round_id: 轮次ID
        result: 执行结果
        error: 错误信息
        
    Returns:
        JSON格式的执行结果
    """
    error_val = error if error.strip() else None
    result_dict = test_session_manager.end_current_test_session(round_id, result, error_val)
    return json.dumps(result_dict, ensure_ascii=False, indent=2)


def get_session_status(round_id: str = "") -> str:
    """
    获取会话状态信息 - 供agent工具调用
    
    Args:
        round_id: 轮次ID，空字符串表示获取所有信息
        
    Returns:
        JSON格式的状态信息
    """
    round_id_val = round_id.strip() if round_id.strip() else None
    result = test_session_manager.get_session_status(round_id_val)
    return json.dumps(result, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    """测试测试会话管理功能"""
    print("🧪 测试会话管理功能...")
    
    # 测试启动新会话
    print("\n1. 启动新测试会话...")
    result1 = start_new_test_session("测试会话管理功能")
    print(f"结果: {result1}")
    
    # 从结果中提取round_id
    import json
    parsed_result = json.loads(result1)
    if parsed_result["status"] == "success":
        round_id = parsed_result["round_id"]
        print(f"\n2. 获取会话状态...")
        result2 = get_session_status(round_id)
        print(f"结果: {result2}")
        
        print(f"\n3. 结束测试会话...")
        result3 = end_current_test_session(round_id, "测试完成", "")
        print(f"结果: {result3}")
    
    print("\n4. 获取所有会话状态...")
    result4 = get_session_status("")
    print(f"结果: {result4}")