import os
import time
import logging
import subprocess
import requests
import json
import hmac
import hashlib
import base64
import urllib.parse as urlparse
from io import StringIO
# 移除外部依赖，稍后会创建相应的工具
from appium import webdriver
from appium.options.ios import XCUITestOptions

# 导入当前项目的日志管理
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager

# 导入设备状态管理工具
from tools._device_status_manage_tools import get_device_status

# --- 辅助函数 ---

# 导入设备检查工具进行防御性验证
from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios

def execute_adb_command(udid: str, command: str):
    """执行adb命令"""
    full_command = f"adb -s {udid} {command}"
    try:
        process = subprocess.run(full_command, shell=True, capture_output=True, text=True, check=True, encoding='utf-8')
        return process.stdout.strip(), process.stderr.strip()
    except subprocess.CalledProcessError as e:
        get_current_task_log_manager().error_tools(f"执行 '{full_command}' 时出错: {e}", "execute_adb_command")
        get_current_task_log_manager().error_tools(f"Stderr: {e.stderr}", "execute_adb_command")
        raise # 重新抛出异常，让调用者处理
    except Exception as e:
        get_current_task_log_manager().error_tools(f"执行 '{full_command}' 时发生未知错误: {e}", "execute_adb_command")
        raise # 重新抛出异常

# --- 图片上传相关类和函数 ---

class Venus(object):
    base_url = 'http://pic-in.vip.sankuai.com/storage/'
    bucket = "ptautotest"
    client_id = "ptautotest"
    client_secret = "1292ee16e73a9d36aa52cbd45b3d0bcc"

    def __init__(self, bucket, client_id, client_secret):
        self.bucket = bucket
        self.client_id = client_id
        self.client_secret = client_secret

    def upload(self, **kwargs):
        assert kwargs.__contains__('filename') or kwargs.__contains__('data')
        url = self.base_url + self.bucket
        date = gmttime(10)
        headers = {
            'Date': date,
            'Authorization': self._getAuthorization('POST', url, date, self.client_id, self.client_secret),
            'Host': 'pic-in.vip.sankuai.com',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        if kwargs.__contains__('filename'):
            filename = kwargs.get('filename')
            assert isinstance(filename, str)
            files = (filename, open(filename, 'rb'))
        elif kwargs.__contains__('data'):
            data = kwargs.get('data')
            assert isinstance(data, str)
            files = ("fake.png", StringIO(data))
        return requests.post(url=url, headers=headers, files={'file': files}).content

    def _getAuthorization(self, method, uri, date, clientId, secret):
        string_to_sign = method + " " + urlparse.urlparse(uri).path + "\n" + date
        hash = hmac.new(secret.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1).digest()
        signature = str(base64.b64encode(hash), 'utf-8').replace("\n", "")   # for python3
        return "MWS" + " " + clientId + ":" + signature


def gmttime(duration):
    """生成GMT时间字符串"""
    now = time.time()
    now += duration
    date = time.gmtime(now)
    return time.strftime('%a, %d %b %Y %H:%M:%S GMT', date)


def get_image_url(file_path):
    """
    获取图片URL，上传图片到美团图片服务
    """
    # 上传图片
    bucket = "ptautotest"
    client_id = "ptautotest"
    client_secret = "1292ee16e73a9d36aa52cbd45b3d0bcc"
    venus = Venus(bucket, client_id, client_secret)
    for attempt in range(2):
        result = venus.upload(filename=file_path)
        try:
            result = json.loads(result)
        except json.JSONDecodeError:
            get_current_task_log_manager().error_tools(f"无法解析上传结果: {result}", "get_image_url")
            if attempt == 1:
                return None
            continue
        fileKey = result.get("data", {}).get("originalLink")
        if fileKey:
            return fileKey
        if attempt == 0:
            time.sleep(2)
    raise Exception(f"上传图片失败，文件路径: {file_path}")


def get_screenshot_android(udid: str, driver=None) -> dict:
    """
    获取指定UDID的Android设备的屏幕截图并自动上传。
    优先使用ADB方式截图，失败后尝试使用WebDriver方式。

    Args:
        udid: 目标Android设备的序列号(UDID)。
        driver: 可选的WebDriver实例，ADB截图失败时使用。

    Returns:
        dict: 包含本地路径和图片URL的字典，格式为:
        {
            "local_path": "本地截图文件路径",
            "image_url": "上传后的图片URL"
        }

    Raises:
        ValueError: 如果设备未连接或截图失败。
        Exception: 如果发生其他ADB或系统错误。
    """
    connected_devices = check_devices_connect_android()
    if not connected_devices:
        raise ValueError("错误：没有检测到连接的Android设备。")
    if udid not in connected_devices:
        raise ValueError(f"错误：设备 {udid} 未连接或未检测到。")

    try:
        # 从设备状态文件获取截图保存路径
        device_status = get_device_status(udid)
        if not device_status:
            raise ValueError(f"设备 {udid} 状态文件不存在，无法获取截图保存路径")
        
        screenshot_save_path = device_status.get('screenshot_save_path', '')
        if not screenshot_save_path:
            raise ValueError(f"设备 {udid} 状态文件中未设置截图保存路径")
        
        # 确保截图目录存在
        os.makedirs(screenshot_save_path, exist_ok=True)

        # 生成截图文件名（不再包含UDID，因为已经用设备文件夹区分）
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        screenshot_path = os.path.join(screenshot_save_path, f'android_{timestamp}.png')

        # 首先尝试使用ADB方式截图
        adb_success = False
        try:
            get_current_task_log_manager().info_tools(f"正在通过ADB获取Android设备 {udid} 的截图...", "get_screenshot_android")
            
            # 设备临时截图路径
            device_tmp_path = '/sdcard/temp_screenshot.png'

            # 1. 在设备上截图
            stdout, stderr = execute_adb_command(udid, f"shell screencap -p {device_tmp_path}")
            if stderr:
                get_current_task_log_manager().warning_tools(f"adb screencap stderr: {stderr}", "get_screenshot_android")

            # 2. 拉取截图到本地
            stdout, stderr = execute_adb_command(udid, f"pull {device_tmp_path} {screenshot_path}")
            if stderr:
                get_current_task_log_manager().warning_tools(f"adb pull stderr: {stderr}", "get_screenshot_android")

            # 3. 删除设备上的临时截图
            execute_adb_command(udid, f"shell rm {device_tmp_path}")

            # 4. 检查本地截图文件
            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 0:
                get_current_task_log_manager().info_tools(f"ADB截图成功，保存到: {screenshot_path}", "get_screenshot_android")
                adb_success = True
            else:
                get_current_task_log_manager().warning_tools(f"ADB截图失败，文件不存在或为空", "get_screenshot_android")
                
        except Exception as e:
            get_current_task_log_manager().warning_tools(f"ADB截图失败: {e}", "get_screenshot_android")
        
        # 如果ADB截图失败且提供了driver，尝试使用WebDriver截图
        if not adb_success and driver:
            try:
                get_current_task_log_manager().info_tools(f"尝试使用WebDriver获取Android设备 {udid} 的截图...", "get_screenshot_android")
                success = driver.get_screenshot_as_file(screenshot_path)
                if success and os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 0:
                    get_current_task_log_manager().info_tools(f"WebDriver截图成功，保存到: {screenshot_path}", "get_screenshot_android")
                    adb_success = True
                else:
                    get_current_task_log_manager().error_tools(f"WebDriver截图失败，文件不存在或为空", "get_screenshot_android")
            except Exception as e:
                get_current_task_log_manager().error_tools(f"WebDriver截图失败: {e}", "get_screenshot_android")
        
        # 如果所有方式都失败了
        if not adb_success:
            raise ValueError(f"错误：无法从设备 {udid} 获取截图（ADB和WebDriver方式均失败）")

        get_current_task_log_manager().info_tools(f"Android截图已保存到: {screenshot_path}", "get_screenshot_android")
        
        # 5. 上传图片获取URL
        get_current_task_log_manager().info_tools(f"正在上传Android设备 {udid} 的截图...", "get_screenshot_android")
        try:
            image_url = get_image_url(screenshot_path)
            get_current_task_log_manager().info_tools(f"Android截图已上传，URL: {image_url}", "get_screenshot_android")
        except Exception as e:
            get_current_task_log_manager().error_tools(f"Android设备 {udid} 截图上传失败: {e}", "get_screenshot_android")
            # 即使上传失败，也返回本地路径
            image_url = None
        
        # 记录截图操作到日志
        get_current_task_log_manager().info_tools(f"Android设备 {udid} 截图操作完成", "get_screenshot_android")
        
        return {
            "local_path": screenshot_path,
            "image_url": image_url
        }

    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取Android设备 {udid} 截图过程中发生错误: {e}", "get_screenshot_android")
        # 记录截图失败到日志
        get_current_task_log_manager().error_tools(f"Android设备 {udid} 截图操作失败", "get_screenshot_android")
        raise


def get_screenshot_ios(udid: str, driver) -> dict:
    """
    获取指定UDID的iOS设备的屏幕截图并自动上传。

    Args:
        udid: 目标iOS设备的UDID。
        driver: WebDriver实例

    Returns:
        dict: 包含本地路径和图片URL的字典，格式为:
        {
            "local_path": "本地截图文件路径", 
            "image_url": "上传后的图片URL"
        }

    Raises:
        ValueError: 如果设备未连接或截图失败。
        Exception: 如果发生其他Appium或系统错误。
    """
    # 添加函数入口调试
    import inspect
    frame = inspect.currentframe()
    args, _, _, values = inspect.getargvalues(frame)
    get_current_task_log_manager().info_tools(f"=== get_screenshot_ios 函数入口调试 ===", "get_screenshot_ios")
    get_current_task_log_manager().info_tools(f"函数参数名: {args}", "get_screenshot_ios")
    get_current_task_log_manager().info_tools(f"参数值: {values}", "get_screenshot_ios")
    get_current_task_log_manager().info_tools(f"udid参数: {udid}", "get_screenshot_ios")
    get_current_task_log_manager().info_tools(f"driver参数: {driver}", "get_screenshot_ios")
    get_current_task_log_manager().info_tools(f"driver类型: {type(driver)}", "get_screenshot_ios")
    
    connected_devices = check_devices_connect_ios()
    if not connected_devices:
        raise ValueError("错误：没有检测到连接的iOS设备。")
    if udid not in connected_devices:
        raise ValueError(f"错误：设备 {udid} 未连接或未检测到。")

    try:
        # 从设备状态文件获取截图保存路径
        device_status = get_device_status(udid)
        if not device_status:
            raise ValueError(f"设备 {udid} 状态文件不存在，无法获取截图保存路径")
        
        screenshot_save_path = device_status.get('screenshot_save_path', '')
        if not screenshot_save_path:
            raise ValueError(f"设备 {udid} 状态文件中未设置截图保存路径")
        
        # 确保截图目录存在
        os.makedirs(screenshot_save_path, exist_ok=True)

        # 生成截图文件名（不再包含UDID，因为已经用设备文件夹区分）
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        screenshot_path = os.path.join(screenshot_save_path, f'ios_{timestamp}.png')

        # 必须提供driver
        if not driver:
            get_current_task_log_manager().error_tools(f"driver 为空，参数详情: udid={udid}, driver={driver}", "get_screenshot_ios")
            raise ValueError(f"未提供driver实例，请先调用 start_device_test 工具启动测试环境")

        get_current_task_log_manager().info_tools(f"成功连接到iOS设备 {udid}。", "get_screenshot_ios")
        
        # 在截图前记录WDA端口信息（健康检查功能暂时禁用）
        wda_forward_port = device_status.get('wda_forward_port')
        if wda_forward_port:
            get_current_task_log_manager().info_tools(f"设备 {udid} WDA转发端口：{wda_forward_port}", "get_screenshot_ios")
        
        get_current_task_log_manager().info_tools(f"正在获取iOS设备 {udid} 的截图...", "get_screenshot_ios")
        success = driver.get_screenshot_as_file(screenshot_path)

        if not success or not os.path.exists(screenshot_path) or os.path.getsize(screenshot_path) == 0:
            raise ValueError(f"错误：无法获取设备 {udid} 的截图或截图文件为空。")

        get_current_task_log_manager().info_tools(f"iOS截图已保存到: {screenshot_path}", "get_screenshot_ios")
        
        # 上传图片获取URL
        get_current_task_log_manager().info_tools(f"正在上传iOS设备 {udid} 的截图...", "get_screenshot_ios")
        try:
            image_url = get_image_url(screenshot_path)
            get_current_task_log_manager().info_tools(f"iOS截图已上传，URL: {image_url}", "get_screenshot_ios")
        except Exception as e:
            get_current_task_log_manager().error_tools(f"iOS设备 {udid} 截图上传失败: {e}", "get_screenshot_ios")
            # 即使上传失败，也返回本地路径
            image_url = None
        
        # 记录截图操作到日志
        get_current_task_log_manager().info_tools(f"iOS设备 {udid} 截图操作完成", "get_screenshot_ios")
        
        return {
            "local_path": screenshot_path,
            "image_url": image_url
        }

    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取iOS设备 {udid} 截图过程中发生错误: {e}", "get_screenshot_ios")
        # 记录截图失败到日志
        get_current_task_log_manager().error_tools(f"iOS设备 {udid} 截图操作失败", "get_screenshot_ios")
        raise # 将异常传递给上层调用者
    finally:
        pass  # 不再主动关闭 driver，保持全局缓存连接


def get_screenshot(udid: str) -> dict:
    """
    统一的截图接口，自动根据平台选择对应的截图方法并上传。
    
    Args:
        udid: 设备的序列号(UDID)
    
    Returns:
        dict: 包含本地路径和图片URL的字典，格式为:
        {
            "local_path": "本地截图文件路径",
            "image_url": "上传后的图片URL"
        }
    
    Raises:
        ValueError: 如果设备未连接或平台不支持
        Exception: 如果截图或上传过程发生错误
    """
    get_current_task_log_manager().info_tools(f"=== 新版本 get_screenshot 函数被调用 ===", "get_screenshot")
    get_current_task_log_manager().info_tools(f"开始对设备 {udid} 进行截图...", "get_screenshot")
    
    # 从设备状态获取平台信息
    from tools._device_status_manage_tools import get_device_status
    device_status = get_device_status(udid)
    if not device_status:
        raise ValueError(f"设备 {udid} 状态文件不存在")
    
    platform = device_status.get('platform', '').lower()
    if not platform:
        raise ValueError(f"设备 {udid} 状态文件中未找到平台信息")
    
    # 获取测试driver
    from tools.llm_base_tools import get_test_driver
    driver = get_test_driver(udid)
    if not driver:
        raise ValueError(f"设备 {udid} 尚未启动测试环境，请先调用 start_device_test 工具")
    
    get_current_task_log_manager().info_tools(f"设备 {udid} 平台: {platform}, driver类型: {type(driver)}", "get_screenshot")
    get_current_task_log_manager().info_tools(f"driver对象详情: {driver}", "get_screenshot")
    
    if platform == 'android':
        get_current_task_log_manager().info_tools(f"调用 get_screenshot_android，参数: udid={udid}, driver={driver}", "get_screenshot")
        return get_screenshot_android(udid, driver)
    elif platform == 'ios':
        get_current_task_log_manager().info_tools(f"调用 get_screenshot_ios，参数: udid={udid}, driver={driver}", "get_screenshot")
        get_current_task_log_manager().info_tools(f"使用关键字参数调用", "get_screenshot")
        result = get_screenshot_ios(udid=udid, driver=driver)
        get_current_task_log_manager().info_tools(f"get_screenshot_ios 返回结果: {result}", "get_screenshot")
        return result
    else:
        raise ValueError(f"不支持的平台类型: {platform}")


if __name__ == "__main__":
    # 示例用法
    print("测试截图工具...")
    
    try:
        # Android 截图测试
        android_devices = check_devices_connect_android()
        if android_devices:
            test_udid = android_devices[0]
            print(f"测试Android设备 {test_udid} 截图...")
            screenshot_path = get_screenshot(test_udid)
            print(f"Android截图成功: {screenshot_path}")
        
        # iOS 截图测试
        ios_devices = check_devices_connect_ios()
        if ios_devices:
            test_udid = ios_devices[0]
            print(f"测试iOS设备 {test_udid} 截图...")
            screenshot_path = get_screenshot(test_udid)
            print(f"iOS截图成功: {screenshot_path}")
            
    except Exception as e:
        print(f"截图测试失败: {e}") 