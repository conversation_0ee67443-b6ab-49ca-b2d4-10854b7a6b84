import os
import json
import fcntl  # 添加文件锁支持
import contextlib
from typing import Any, Dict, Optional, List
import datetime
import logging
import glob
import time

# 获取日志对象
logger = logging.getLogger("device_status_manager")
logger.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")

# 如果logger没有处理器，添加一个处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置不传播到父记录器，避免重复日志
    logger.propagate = False

# 设备状态文件夹的相对路径
DEVICE_STATUS_DIR = os.path.join(os.path.dirname(__file__), '../status')

# 时间字段列表（需要进行格式转换的字段）
TIME_FIELDS = [
    "start_time",
    "last_update", 
    "last_app_version_check_time",
    "wda_last_restart_time",
    "adb_last_restart_time",
    "test_start_time",
    "driver_created_time"
]

# 字段顺序定义（用于JSON文件的字段排序）
FIELD_ORDER = [
    "device_name",
    "platform",
    "bundle_id",  # iOS专用
    "package_name",  # Android专用
    "start_time",
    "round_num",
    "session_round_num", 
    "status",
    "last_update",
    "test_duration",
    "app_issue",
    "test_issue",
    "completed_icon_num",
    "total_icon_num",
    "update_process",
    "update_pid",
    "scale_ratio",
    "last_app_version_check_time",
    "system_version",
    "device_screenshot_dir",
    "device_error_dir",
    "screenshot_save_path",
    "total_icons_cumulative",
    "completed_icons_cumulative",
    "completed_icons",  # 添加遗漏的字段
    "total_icons",  # 添加遗漏的字段
    "appium_port",  # Appium服务端口
    "wda_forward_port",  # WDA转发端口（仅iOS设备使用）
    "local_wda_port",  # 本地WDA端口（用于driver连接）
    "wda_port",  # 设备上WDA运行的端口
    "wda_last_restart_time",  # WDA最后重启时间（仅iOS设备使用）
    "adb_last_restart_time",   # ADB最后重启时间（仅Android设备使用）
    "test_start_time",  # 测试开始时间
    "driver_created_time"  # 驱动创建时间
]

# 默认的设备状态模板
DEFAULT_STATUS = {
    "device_name": "",
    "platform": "",
    "start_time": 0.0,
    "round_num": 1,
    "session_round_num": 1,
    "status": "connected",  # 新连接的设备默认为connected状态，需要经过初始化才能变为ready
    "last_update": 0.0,
    "test_duration": 0.0,
    "app_issue": False,
    "test_issue": False,
    "completed_icon_num": 0,
    "total_icon_num": 0,
    "update_process": "",
    "update_pid": 0,
    "scale_ratio": 1.0,
    "last_app_version_check_time": 0.0,
    "system_version": "",
    "device_screenshot_dir": "",
    "device_error_dir": "",
    "screenshot_save_path": "",
    "total_icons_cumulative": 0,
    "completed_icons_cumulative": 0,
    "completed_icons": 0,  # 添加新字段的默认值
    "total_icons": 0,  # 添加新字段的默认值
    "appium_port": 0,  # Appium服务端口
    "wda_forward_port": 0,  # WDA转发端口（仅iOS设备使用）
    "local_wda_port": 0,  # 本地WDA端口（用于driver连接）
    "wda_port": 8100,  # 设备上WDA运行的端口
    "wda_last_restart_time": 0.0,  # WDA最后重启时间（仅iOS设备使用）
    "adb_last_restart_time": 0.0,   # ADB最后重启时间（仅Android设备使用）
    "test_start_time": 0.0,  # 测试开始时间
    "driver_created_time": 0.0  # 驱动创建时间
}

# 平台特定的默认字段
PLATFORM_SPECIFIC_DEFAULTS = {
    "ios": {
        "bundle_id": "com.meituan.imeituan"
    },
    "android": {
        "package_name": "com.sankuai.meituan"
    }
}

def timestamp_to_readable(timestamp: float) -> str:
    """
    将时间戳转换为可读格式
    
    Args:
        timestamp: 时间戳（秒）
        
    Returns:
        str: 格式化的时间字符串（如 20250711_234941）
    """
    if timestamp == 0 or timestamp is None:
        return "00000000_000000"
    
    try:
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y%m%d_%H%M%S")
    except (ValueError, OSError):
        return "00000000_000000"

def readable_to_timestamp(time_str: str) -> float:
    """
    将可读格式转换为时间戳
    
    Args:
        time_str: 格式化的时间字符串（如 20250711_234941）
        
    Returns:
        float: 时间戳（秒）
    """
    if not time_str or time_str == "00000000_000000":
        return 0.0
    
    try:
        dt = datetime.datetime.strptime(time_str, "%Y%m%d_%H%M%S")
        return dt.timestamp()
    except ValueError:
        return 0.0

def convert_timestamps_to_readable(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将数据字典中的时间戳字段转换为可读格式（用于写入JSON）
    
    Args:
        data: 包含时间戳字段的字典
        
    Returns:
        Dict[str, Any]: 转换后的字典
    """
    result = data.copy()
    for field in TIME_FIELDS:
        if field in result:
            if isinstance(result[field], (int, float)):
                result[field] = timestamp_to_readable(result[field])
    return result

def convert_readable_to_timestamps(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将数据字典中的可读格式时间转换为时间戳（用于从JSON读取）
    
    Args:
        data: 包含可读格式时间字段的字典
        
    Returns:
        Dict[str, Any]: 转换后的字典
    """
    result = data.copy()
    for field in TIME_FIELDS:
        if field in result:
            if isinstance(result[field], str):
                result[field] = readable_to_timestamp(result[field])
    return result

class DeviceStatusManager:
    def __init__(self, status_dir: Optional[str] = None):
        self.status_dir = status_dir or DEVICE_STATUS_DIR
        os.makedirs(self.status_dir, exist_ok=True)

    @contextlib.contextmanager
    def _file_lock(self, file_path: str, mode: str = 'r+'):
        """
        文件锁上下文管理器，确保多进程安全访问文件
        
        Args:
            file_path: 文件路径
            mode: 文件打开模式
        """
        # 确保文件存在
        if not os.path.exists(file_path) and ('w' in mode or '+' in mode or 'a' in mode):
            # 如果文件不存在且是写模式，先创建文件
            with open(file_path, 'a', encoding='utf-8') as f:
                pass
        
        try:
            # 打开文件
            with open(file_path, mode, encoding='utf-8') as f:
                # 获取文件锁
                fcntl.flock(f.fileno(), fcntl.LOCK_EX)
                try:
                    yield f
                finally:
                    # 释放文件锁
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)
        except Exception as e:
            logger.error(f"文件锁操作失败 {file_path}: {e}")
            raise

    def _order_dict_by_field_order(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        按照预定义的字段顺序重新排列字典，并转换时间格式为可读格式
        
        Args:
            data: 原始字典数据
            
        Returns:
            Dict[str, Any]: 按顺序排列并转换时间格式的字典
        """
        # 先转换时间格式
        converted_data = convert_timestamps_to_readable(data)
        
        ordered_dict = {}
        
        # 首先按照预定义顺序添加字段
        for field in FIELD_ORDER:
            if field in converted_data:
                ordered_dict[field] = converted_data[field]
        
        # 然后添加任何不在预定义顺序中的字段（如果有的话）
        for key, value in converted_data.items():
            if key not in ordered_dict:
                ordered_dict[key] = value
                
        return ordered_dict

    def _get_status_path(self, udid: str) -> str:
        return os.path.join(self.status_dir, f"{udid}.json")

    def get_status(self, udid: str) -> Dict[str, Any] | bool:
        """
        查询设备状态，如果不存在则返回 False。
        时间字段将自动从可读格式转换为时间戳格式。
        """
        path = self._get_status_path(udid)
        if not os.path.exists(path):
            return False
        
        try:
            with self._file_lock(path, 'r') as f:
                data = json.load(f)
                # 转换时间格式为时间戳
                return convert_readable_to_timestamps(data)
        except Exception as e:
            logger.error(f"读取设备状态失败 {udid}: {e}")
            return False

    def create_status(self, udid: str, device_name: str, platform: str, round_num: int = 1, session_round_num: int = 1, **kwargs) -> Dict[str, Any]:
        """
        创建设备状态文件，必须传入 udid、device_name、platform，其他字段可选。
        如果已存在则直接返回现有内容。
        """
        # 如果 udid、device_name、platform 至少有一个为空就返回 False
        if not udid or not device_name or not platform:
            logger.error(f"创建设备状态失败: udid={udid}, device_name={device_name}, platform={platform}")
            return False
        path = self._get_status_path(udid)
        
        # 如果文件已存在，先读取现有内容
        if os.path.exists(path):
            try:
                with self._file_lock(path, 'r') as f:
                    data = json.load(f)
                    # 转换时间格式为时间戳后返回
                    return convert_readable_to_timestamps(data)
            except Exception as e:
                logger.error(f"读取现有设备状态文件失败 {udid}: {e}")
                # 如果读取失败，继续创建新文件
        
        # 创建新的设备状态
        status = DEFAULT_STATUS.copy()
        status.update({
            "device_name": device_name,
            "platform": platform,
            "round_num": round_num,
            "session_round_num": session_round_num,
            "total_icons_cumulative": 0,
            "completed_icons_cumulative": 0
        })
        
        # 设置截图保存路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        screenshot_save_path = os.path.join(project_root, "screenshot", device_name)
        status["screenshot_save_path"] = screenshot_save_path
        
        # 确保截图目录存在
        try:
            os.makedirs(screenshot_save_path, exist_ok=True)
            logger.info(f"创建截图目录: {screenshot_save_path}")
        except Exception as e:
            logger.error(f"创建截图目录失败 {screenshot_save_path}: {e}")
        
        # 添加平台特定的默认字段
        if platform.lower() in PLATFORM_SPECIFIC_DEFAULTS:
            status.update(PLATFORM_SPECIFIC_DEFAULTS[platform.lower()])
        
        status.update(kwargs)
        
        # 按照预定义顺序排列字段
        ordered_status = self._order_dict_by_field_order(status)
        
        try:
            with self._file_lock(path, 'w') as f:
                json.dump(ordered_status, f, ensure_ascii=False, indent=2)
            return ordered_status
        except Exception as e:
            logger.error(f"创建设备状态文件失败 {udid}: {e}")
            return False

    def update_status(self, udid: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新设备状态
        
        Args:
            udid: 设备唯一标识符
            updates: 要更新的字段字典
            
        Returns:
            Dict[str, Any]: 更新后的完整状态字典
        """
        status_path = self._get_status_path(udid)
        
        if not os.path.exists(status_path):
            raise ValueError(f"设备状态不存在: {udid}")
        
        try:
            # 使用读写模式打开文件，既能读取又能写入
            with self._file_lock(status_path, 'r+') as f:
                # 读取现有状态
                f.seek(0)
                json_data = json.load(f)
                # 转换时间格式为时间戳
                status = convert_readable_to_timestamps(json_data)
                
                # 更新字段
                status.update(updates)
                status['last_update'] = time.time()
                
                # 按照预定义顺序排列字段（会自动转换时间格式为可读格式）
                ordered_status = self._order_dict_by_field_order(status)
                
                # 清空文件并写入新内容
                f.seek(0)
                f.truncate()
                json.dump(ordered_status, f, ensure_ascii=False, indent=2)
                
                # 返回时间戳格式的数据
                return convert_readable_to_timestamps(ordered_status)
        except Exception as e:
            logger.error(f"更新设备状态失败 {udid}: {e}")
            raise

    def add_icon_count(self, udid: str, total_add: int = 0, completed_add: int = 0) -> bool:
        """
        累加图标计数
        
        Args:
            udid: 设备UDID
            total_add: 要累加的总图标数
            completed_add: 要累加的完成图标数
            
        Returns:
            bool: 操作是否成功
        """
        try:
            status = self.get_status(udid)
            if not status:
                return False
            
            updates = {}
            if total_add > 0:
                current_total = status.get('total_icons_cumulative', 0)
                updates['total_icons_cumulative'] = current_total + total_add
            
            if completed_add > 0:
                current_completed = status.get('completed_icons_cumulative', 0)
                updates['completed_icons_cumulative'] = current_completed + completed_add
            
            if updates:
                self.update_status(udid, updates)
            
            return True
        except Exception:
            return False

    def get_all_device_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有设备的状态信息
        时间字段将自动从可读格式转换为时间戳格式。
        
        Returns:
            Dict[str, Dict[str, Any]]: 设备UDID -> 设备状态字典
        """
        all_status = {}
        pattern = os.path.join(self.status_dir, "*.json")
        
        for status_file in glob.glob(pattern):
            try:
                udid = os.path.basename(status_file).replace('.json', '')
                with open(status_file, 'r', encoding='utf-8') as f:
                    status = json.load(f)
                    # 转换时间格式为时间戳
                    all_status[udid] = convert_readable_to_timestamps(status)
            except Exception as e:
                logger.error(f"读取设备状态文件 {status_file} 失败: {e}")
                continue
        
        return all_status

    def get_devices_by_platform(self, platform: str) -> Dict[str, Dict[str, Any]]:
        """
        获取指定平台的所有设备状态
        
        Args:
            platform: 平台名称（ios/android）
            
        Returns:
            Dict[str, Dict[str, Any]]: 设备UDID -> 设备状态字典
        """
        all_status = self.get_all_device_status()
        return {
            udid: status for udid, status in all_status.items() 
            if status.get('platform', '').lower() == platform.lower()
        }

    def get_devices_count_by_platform(self) -> Dict[str, int]:
        """
        获取各平台设备数量
        
        Returns:
            Dict[str, int]: 平台 -> 设备数量
        """
        all_status = self.get_all_device_status()
        count = {'ios': 0, 'android': 0}
        
        for status in all_status.values():
            platform = status.get('platform', '').lower()
            if platform in count:
                count[platform] += 1
        
        return count

    def get_devices_by_status(self, target_status: str) -> Dict[str, Dict[str, Any]]:
        """
        获取指定状态的所有设备
        
        Args:
            target_status: 目标状态
            
        Returns:
            Dict[str, Dict[str, Any]]: 设备UDID -> 设备状态字典
        """
        all_status = self.get_all_device_status()
        return {
            udid: status for udid, status in all_status.items() 
            if status.get('status') == target_status
        }

    def get_recent_status_changes(self, time_threshold: float = 60.0) -> List[Dict[str, Any]]:
        """
        获取最近状态变化的设备
        
        Args:
            time_threshold: 时间阈值（秒），默认1分钟
            
        Returns:
            List[Dict[str, Any]]: 最近状态变化的设备列表
        """
        current_time = time.time()
        all_status = self.get_all_device_status()
        recent_changes = []
        
        for udid, status in all_status.items():
            last_update = status.get('last_update', 0)
            if isinstance(last_update, (int, float)) and (current_time - last_update) <= time_threshold:
                change_info = {
                    'udid': udid,
                    'device_name': status.get('device_name', 'Unknown'),
                    'platform': status.get('platform', 'unknown'),
                    'status': status.get('status', 'unknown'),
                    'last_update': last_update,
                    'time_since_update': current_time - last_update
                }
                recent_changes.append(change_info)
        
        # 按最后更新时间排序（最新的在前）
        recent_changes.sort(key=lambda x: x['last_update'], reverse=True)
        return recent_changes

    def get_device_summary(self) -> Dict[str, Any]:
        """
        获取设备状态汇总信息
        
        Returns:
            Dict[str, Any]: 包含各种统计信息的汇总字典
        """
        all_status = self.get_all_device_status()
        
        # 基本统计
        summary = {
            'total_devices': len(all_status),
            'platform_count': self.get_devices_count_by_platform(),
            'status_count': {},
            'recent_activity': [],
            'device_list': []
        }
        
        # 状态统计
        for status in all_status.values():
            device_status = status.get('status', 'unknown')
            summary['status_count'][device_status] = summary['status_count'].get(device_status, 0) + 1
        
        # 最近活动
        summary['recent_activity'] = self.get_recent_status_changes(300)  # 5分钟内的变化
        
        # 设备列表
        for udid, status in all_status.items():
            device_info = {
                'udid': udid,
                'device_name': status.get('device_name', 'Unknown'),
                'platform': status.get('platform', 'unknown'),
                'status': status.get('status', 'unknown'),
                'round_num': status.get('round_num', 0),
                'session_round_num': status.get('session_round_num', 0),
                'last_update': status.get('last_update', 0),
                'test_duration': status.get('test_duration', 0),
                'app_issue': status.get('app_issue', False),
                'test_issue': status.get('test_issue', False)
            }
            summary['device_list'].append(device_info)
        
        return summary

    def check_device_connectivity(self, timeout_seconds: float = 600.0) -> Dict[str, List[str]]:
        """
        检查设备连接状态，识别可能断开连接的设备
        
        Args:
            timeout_seconds: 超时时间（秒），默认10分钟
            
        Returns:
            Dict[str, List[str]]: 包含 'online' 和 'offline' 设备UDID列表
        """
        current_time = time.time()
        all_status = self.get_all_device_status()
        
        result = {
            'online': [],
            'offline': [],
            'suspicious': []  # 状态可疑的设备
        }
        
        for udid, status in all_status.items():
            last_update = status.get('last_update', 0)
            device_status = status.get('status', 'unknown')
            
            if isinstance(last_update, (int, float)):
                time_since_update = current_time - last_update
                
                if time_since_update > timeout_seconds:
                    result['offline'].append(udid)
                elif device_status in ['running', 'completed', 'partial_completed']:
                    result['online'].append(udid)
                else:
                    result['suspicious'].append(udid)
            else:
                result['suspicious'].append(udid)
        
        return result

    def get_devices_by_categories(self, offline_threshold_seconds: int = 3600) -> Dict[str, List[str]]:
        """
        按状态类别获取设备信息（包括离线检测）
        
        Args:
            offline_threshold_seconds: 离线判断阈值（秒），默认60分钟
            
        Returns:
            Dict[str, List[str]]: 各状态类别的设备信息列表
            {
                'waiting_devices': [...],
                'running_devices': [...], 
                'completed_devices': [...],
                'error_devices': [...],
                'offline_devices': [...],
                'inconsistent_devices': [...]
            }
        """
        current_time = time.time()
        all_status = self.get_all_device_status()
        
        result = {
            'waiting_devices': [],
            'running_devices': [],
            'completed_devices': [],
            'error_devices': [],
            'offline_devices': [],
            'inconsistent_devices': []
        }
        
        for udid, status in all_status.items():
            device_name = status.get('device_name', 'Unknown')
            platform = status.get('platform', 'Unknown')
            status_str = status.get('status', 'Unknown')
            round_num = status.get('round_num', 0)
            last_update = status.get('last_update', 0)
            update_process = status.get('update_process', 'Unknown')
            update_pid = status.get('update_pid', 'Unknown')
            
            # 计算最后更新时间距离现在的时间差
            time_diff = current_time - last_update
            time_diff_str = f"{int(time_diff // 60)}分{int(time_diff % 60)}秒前"
            
            # 设备信息字符串
            device_info = f"{device_name} ({platform}, 轮次: {round_num}, 更新: {time_diff_str}, 进程: {update_process}[{update_pid}])"
            
            # 检查设备是否已掉线（最后更新时间超过设定时间）
            if time_diff > offline_threshold_seconds:
                result['offline_devices'].append(device_info)
                continue
            
            # 检查状态不一致的设备（running但长时间未更新）
            if status_str == 'running' and time_diff > 600 and time_diff <= offline_threshold_seconds:  # 10分钟到60分钟之间
                inconsistent_info = f"{device_name} (状态: {status_str}, 最后更新: {int(time_diff // 60)}分钟前)"
                result['inconsistent_devices'].append(inconsistent_info)
            
            # 根据状态分类
            if status_str == 'waiting':
                result['waiting_devices'].append(device_info)
            elif status_str == 'running':
                result['running_devices'].append(device_info)
            elif status_str == 'completed':
                result['completed_devices'].append(device_info)
            else:
                result['error_devices'].append(device_info)
        
        return result

    def get_process_status_info(self, offline_threshold_seconds: int = 3600) -> Dict[str, Any]:
        """
        从设备状态中获取进程状态信息
        
        Args:
            offline_threshold_seconds: 离线判断阈值（秒），默认60分钟
            
        Returns:
            Dict[str, Any]: 包含进程状态信息的字典
        """
        current_time = time.time()
        all_status = self.get_all_device_status()
        
        result = {
            'ios_processes': 0,
            'android_processes': 0,
            'process_details': []
        }
        
        for udid, status in all_status.items():
            device_status_str = status.get('status', '')
            platform = status.get('platform', '').lower()
            last_update = status.get('last_update', 0)
            device_name = status.get('device_name', 'Unknown')
            update_process = status.get('update_process', 'Unknown')
            update_pid = status.get('update_pid', 'Unknown')
            
            # 跳过掉线设备
            if (current_time - last_update) > offline_threshold_seconds:
                continue
            
            # 如果设备状态为running或waiting，且最后更新时间在阈值内，且进程ID不是Unknown，认为是活跃的
            is_active = (device_status_str in ['running', 'waiting'] and 
                        (current_time - last_update) < offline_threshold_seconds and 
                        update_pid != 'Unknown')
            
            # 创建进程详情
            process_detail = {
                'type': platform,
                'device': device_name,
                'pid': update_pid,
                'command': f"{update_process}进程",
                'status': device_status_str,
                'last_update': last_update,
                'active': is_active
            }
            
            # 添加到进程详情列表
            result['process_details'].append(process_detail)
            
            # 统计活跃进程数量
            if is_active:
                if platform == 'ios':
                    result['ios_processes'] += 1
                elif platform == 'android':
                    result['android_processes'] += 1
        
        return result

    def cleanup_offline_devices(self, timeout_hours: float = 24.0) -> List[Dict[str, Any]]:
        """
        清理超过指定时间未更新的设备状态文件
        
        Args:
            timeout_hours: 超时时间（小时），默认24小时
            
        Returns:
            List[Dict[str, Any]]: 被清理的设备信息列表
        """
        timeout_seconds = timeout_hours * 3600
        current_time = time.time()
        all_status = self.get_all_device_status()
        cleaned_devices = []
        
        for udid, status in all_status.items():
            last_update = status.get('last_update', 0)
            time_diff = current_time - last_update
            
            # 如果设备掉线超过指定时间，删除状态文件
            if time_diff > timeout_seconds:
                device_name = status.get('device_name', 'Unknown')
                device_info = {
                    'udid': udid,
                    'device_name': device_name,
                    'time_diff': time_diff,
                    'hours_offline': int(time_diff // 3600)
                }
                cleaned_devices.append(device_info)
                
                # 删除设备状态文件
                try:
                    device_status_file = self._get_status_path(udid)
                    if os.path.exists(device_status_file):
                        os.remove(device_status_file)
                        logger.info(f"已删除设备状态文件: {device_status_file}")
                except Exception as e:
                    logger.error(f"删除设备状态文件时出错: {str(e)}")
        
        return cleaned_devices

    def get_platform_device_counts(self, offline_threshold_seconds: int = 3600) -> Dict[str, int]:
        """
        获取各平台在线设备数量（不包括掉线设备）
        
        Args:
            offline_threshold_seconds: 离线判断阈值（秒），默认60分钟
            
        Returns:
            Dict[str, int]: 平台 -> 在线设备数量
        """
        current_time = time.time()
        all_status = self.get_all_device_status()
        
        count = {'ios': 0, 'android': 0, 'total': 0}
        
        for status in all_status.values():
            last_update = status.get('last_update', 0)
            
            # 跳过掉线设备
            if (current_time - last_update) > offline_threshold_seconds:
                continue
                
            platform = status.get('platform', '').lower()
            if platform in count:
                count[platform] += 1
                count['total'] += 1
        
        return count

    def get_app_package_name(self, udid: str) -> str | bool:
        """
        根据设备udid获取对应平台的应用包名
        
        Args:
            udid: 设备UDID
            
        Returns:
            str | bool: 
                - iOS设备返回bundle_id
                - Android设备返回package_name
                - 设备不存在或平台不支持返回False
        """
        status = self.get_status(udid)
        if not status:
            logger.warning(f"设备 {udid} 状态不存在")
            return False
        
        platform = status.get('platform', '').lower()
        
        if platform == 'ios':
            bundle_id = status.get('bundle_id')
            if not bundle_id:
                logger.warning(f"iOS设备 {udid} 缺少bundle_id字段")
            return bundle_id or False
        elif platform == 'android':
            package_name = status.get('package_name')
            if not package_name:
                logger.warning(f"Android设备 {udid} 缺少package_name字段")
            return package_name or False
        else:
            logger.warning(f"设备 {udid} 平台 {platform} 不支持")
            return False

    def get_app_id(self, udid: str) -> str | bool:
        """
        统一获取应用标识符的方法（推荐使用）
        
        Args:
            udid: 设备UDID
            
        Returns:
            str | bool: 
                - iOS设备返回bundle_id
                - Android设备返回package_name
                - 设备不存在或平台不支持返回False
        """
        return self.get_app_package_name(udid)

    def initialize_device_status(self, udid: str) -> Dict[str, Any] | bool:
        """
        初始化设备状态，保留指定字段，其余字段重置为默认值
        
        Args:
            udid: 设备UDID
            
        Returns:
            Dict[str, Any] | bool: 
                - 成功返回初始化后的状态字典
                - 失败返回False
        """
        if not udid:
            logger.error("初始化设备状态失败: udid不能为空")
            return False
            
        # 检查设备状态文件是否存在
        status_path = self._get_status_path(udid)
        if not os.path.exists(status_path):
            logger.error(f"设备状态文件不存在: {udid}")
            logger.error("无法初始化不存在的设备，请先创建设备状态")
            return False
        
        try:
            # 使用读写模式打开文件
            with self._file_lock(status_path, 'r+') as f:
                # 读取现有状态
                f.seek(0)
                json_data = json.load(f)
                # 转换时间格式为时间戳
                current_status = convert_readable_to_timestamps(json_data)
                
                # 保留的字段列表
                preserved_fields = ['device_name', 'platform', 'round_num', 'session_round_num', 'scale_ratio', 'last_app_version_check_time', 'system_version', 'device_screenshot_dir', 'device_error_dir', 'screenshot_save_path', 'wda_last_restart_time', 'adb_last_restart_time']
                
                # 创建新的状态字典，从默认状态开始
                new_status = DEFAULT_STATUS.copy()
                
                # 保留指定字段的值
                for field in preserved_fields:
                    if field in current_status:
                        new_status[field] = current_status[field]
                
                # 添加平台特定的默认字段
                platform = new_status.get('platform', '').lower()
                if platform in PLATFORM_SPECIFIC_DEFAULTS:
                    new_status.update(PLATFORM_SPECIFIC_DEFAULTS[platform])
                
                # 更新最后修改时间
                new_status['last_update'] = time.time()
                
                # 按照预定义顺序排列字段（会自动转换时间格式为可读格式）
                ordered_status = self._order_dict_by_field_order(new_status)
                
                # 清空文件并写入新内容
                f.seek(0)
                f.truncate()
                json.dump(ordered_status, f, ensure_ascii=False, indent=2)
                
                logger.info(f"设备状态已初始化: {udid}")
                logger.info(f"保留字段: {preserved_fields}")
                
                # 返回时间戳格式的数据
                return convert_readable_to_timestamps(ordered_status)
        except Exception as e:
            logger.error(f"初始化设备状态失败 {udid}: {e}")
            return False

    def reorder_all_device_status_files(self) -> Dict[str, bool]:
        """
        重新排序所有设备状态文件，确保字段顺序一致
        
        Returns:
            Dict[str, bool]: UDID -> 是否成功重新排序
        """
        all_status = self.get_all_device_status()
        results = {}
        
        for udid, status in all_status.items():
            try:
                # 按照预定义顺序重新排列字段（会自动转换时间格式为可读格式）
                ordered_status = self._order_dict_by_field_order(status)
                
                # 重新写入文件
                status_path = self._get_status_path(udid)
                with self._file_lock(status_path, 'w') as f:
                    json.dump(ordered_status, f, ensure_ascii=False, indent=2)
                
                results[udid] = True
                logger.info(f"设备状态文件已重新排序: {udid}")
            except Exception as e:
                logger.error(f"重新排序设备状态文件失败 {udid}: {e}")
                results[udid] = False
        
        return results

# 单例实例
manager = DeviceStatusManager()

# 对外暴露的同步接口
get_device_status = manager.get_status
update_device_status = manager.update_status
create_device_status = manager.create_status
initialize_device_status = manager.initialize_device_status
get_all_device_status = manager.get_all_device_status
get_devices_by_platform = manager.get_devices_by_platform
get_devices_count_by_platform = manager.get_devices_count_by_platform
get_devices_by_status = manager.get_devices_by_status
get_recent_status_changes = manager.get_recent_status_changes
get_device_summary = manager.get_device_summary
check_device_connectivity = manager.check_device_connectivity
get_devices_by_categories = manager.get_devices_by_categories
get_process_status_info = manager.get_process_status_info
cleanup_offline_devices = manager.cleanup_offline_devices
get_platform_device_counts = manager.get_platform_device_counts
get_app_package_name = manager.get_app_package_name
get_app_id = manager.get_app_id

if __name__ == "__main__":
    # print(manager.get_status("00008140-000238321401801C"))
    # print(manager.get_status("UQG5T20327008560"))
    # print(manager.get_status("test"))
    # print(manager.create_status("test_create", "TestDevice", "android"))
    # print(manager.update_status("test_create", {"status": "running"}))
    status = manager.get_status("00008140-000238321401801C")
    print(status.get("last_app_version_check_time"))
    last_app_version_check_time = datetime.datetime.fromtimestamp(status.get("last_app_version_check_time"))
    current_time = datetime.datetime.now()
    time_diff = current_time - last_app_version_check_time
    print(time_diff.total_seconds())
    # print(manager.get_status("test_create"))