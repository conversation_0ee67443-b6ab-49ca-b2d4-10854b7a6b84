#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
应用操作工具，适用于 iOS 和 Android。

提供 `restart_app` 函数供 agent 使用。
"""

import time
import os
import sys

# 导入当前项目的日志管理
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager

# 导入设备状态管理工具
from tools._device_status_manage_tools import get_device_status, update_device_status, get_app_id

# 导入screenshot_tools中的execute_adb_command函数
from tools.screenshot_tools import execute_adb_command

# 导入设备驱动管理工具
from tools._device_driver_manage_tools import get_device_driver

# --- Agent 使用的应用操作函数 --- #

def restart_app(udid: str, driver=None) -> str:
    """
    重启指定设备上的应用。

    参数:
        udid: 设备序列号 (UDID)。
        driver: 可选的驱动对象，iOS平台需要。

    返回:
        str: 操作结果，可能的值有：
            - "success": 操作成功
            - "unknown_device": 设备不存在
            - "operation_failed": 操作失败
            - "device_offline": 设备掉线
    """
    if not udid:
        get_current_task_log_manager().error_tools("restart_app: 必须提供udid参数", "restart_app")
        return "unknown_device"
    
    status = get_device_status(udid)
    if status is False:
        get_current_task_log_manager().error_tools(f"设备UDID '{udid}' 不是已知设备，跳过重启操作", "restart_app")
        return "unknown_device"
    
    platform = status.get("platform", "").lower()
    device_name = status.get('device_name', udid)
    app_id = get_app_id(udid)
    
    if not app_id:
        get_current_task_log_manager().error_tools(f"设备 {udid} 缺少应用包名配置", "restart_app")
        return "operation_failed"
    
    try:
        if platform == 'ios':
            if not driver:
                # 尝试获取设备驱动
                driver = get_device_driver(udid)
            if not driver:
                get_current_task_log_manager().error_tools("restart_app: iOS平台需要提供driver参数", "restart_app")
                return "operation_failed"
            
            get_current_task_log_manager().info_tools(f"正在为设备{device_name}重启iOS应用{app_id}...", "restart_app")
            driver.terminate_app(app_id)
            time.sleep(2)
            driver.activate_app(app_id)
            get_current_task_log_manager().info_tools(f"iOS应用{app_id}重启完成", "restart_app")
            return "success"
            
        elif platform == 'android':
            get_current_task_log_manager().info_tools(f"正在为设备{device_name}重启Android应用{app_id}...", "restart_app")
            
            # 强制停止应用
            _, stderr1 = execute_adb_command(udid, f"shell am force-stop {app_id}")
            if stderr1:
                get_current_task_log_manager().warning_tools(f"强制停止应用命令失败: {stderr1}", "restart_app")
                if "not found" in stderr1 or "device offline" in stderr1:
                    raise Exception(f"设备掉线: {stderr1}")
            
            time.sleep(2)
            
            # 启动应用
            _, stderr2 = execute_adb_command(udid, f"shell monkey -p {app_id} -c android.intent.category.LAUNCHER 1")
            
            # 只有当stderr包含真正的错误信息时才报告失败
            # monkey命令在某些设备（如华为）上会在stderr输出调试信息，这些不是错误
            if stderr2 and ("Error" in stderr2 or "error" in stderr2 or "not found" in stderr2 or "device offline" in stderr2 or "failed" in stderr2.lower() or "denied" in stderr2.lower()):
                get_current_task_log_manager().warning_tools(f"启动应用命令失败: {stderr2}", "restart_app")
                if "not found" in stderr2 or "device offline" in stderr2:
                    raise Exception(f"设备掉线: {stderr2}")
            elif stderr2:
                # 如果stderr有输出但不包含错误关键词，则记录为调试信息而不是警告
                get_current_task_log_manager().info_tools(f"monkey命令调试输出: {stderr2}", "restart_app")
            
            get_current_task_log_manager().info_tools(f"Android应用{app_id}重启完成", "restart_app")
            return "success"
            
        else:
            get_current_task_log_manager().error_tools(f"restart_app: 不支持的平台类型: {platform}", "restart_app")
            return "operation_failed"
            
    except Exception as e:
        get_current_task_log_manager().error_tools(f"{platform}应用重启操作异常: {e}", "restart_app")
        
        # 检查设备是否在线
        try:
            from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios
            if platform == 'android':
                is_online = check_devices_connect_android(udid)
            elif platform == 'ios':
                is_online = check_devices_connect_ios(udid)
            else:
                is_online = False
                
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                return "device_offline"
        except:
            pass  # 设备检查失败不影响主要功能
            
        return "operation_failed"

def _check_app_state(udid: str, driver=None) -> str:
    """
    检查应用状态（前台/后台/闪退）
    
    Args:
        udid: 设备UDID
        driver: 可选的驱动对象，iOS平台需要
        
    Returns:
        str: 应用状态 ("foreground", "background", "crashed", "unknown")
    """
    status = get_device_status(udid)
    if status is False:
        return "unknown"
    
    platform = status.get("platform", "").lower()
    app_id = get_app_id(udid)
    
    if not app_id:
        return "unknown"
    
    try:
        if platform == 'ios':
            if not driver:
                # 尝试获取设备驱动
                driver = get_device_driver(udid)
            if not driver:
                return "unknown"
            
            app_state = driver.query_app_state(app_id)
            get_current_task_log_manager().debug_tools(f"iOS应用状态: {app_state}", "_check_app_state")
            
            if app_state == 4:
                return "foreground"
            elif app_state == 3:
                return "background"
            elif app_state == 1:
                return "crashed"
            else:
                return "unknown"
                
        elif platform == 'android':
            import re
            # 检查当前前台应用
            stdout, _ = execute_adb_command(udid, "shell dumpsys window | grep mCurrentFocus")
            
            current_package = None
            if stdout:
                lines = stdout.strip().split('\n')
                valid_lines = [line for line in lines if 'null' not in line]
                for line in valid_lines:
                    # 用正则提取包名
                    match = re.search(r'mCurrentFocus=.* (\S+?)/(\S+?)[\s}]', line)
                    if match:
                        current_package = match.group(1)
                        break
            
            is_foreground = current_package and current_package.startswith(app_id)
            if is_foreground:
                return "foreground"
            else:
                # 检查进程是否存在
                ps_out, _ = execute_adb_command(udid, f"shell ps | grep {app_id}")
                if ps_out and app_id in ps_out:
                    return "background"
                else:
                    return "crashed"
        else:
            return "unknown"
            
    except Exception as e:
        get_current_task_log_manager().error_tools(f"检查应用状态异常: {e}", "_check_app_state")
        return "unknown"

def background_switch(udid: str, driver=None) -> str:
    """
    让美团应用退到后台，等待3秒后再切回前台
    
    Args:
        udid: 设备UDID
        driver: 可选的驱动对象，iOS平台需要
        
    Returns:
        str: 操作结果，可能的值有：
            - "success": 操作成功
            - "unknown_device": 设备不存在
            - "operation_failed": 操作失败
            - "device_offline": 设备掉线
    """
    if not udid:
        get_current_task_log_manager().error_tools("background_switch: 必须提供udid参数", "background_switch")
        return "unknown_device"
    
    status = get_device_status(udid)
    if status is False:
        get_current_task_log_manager().error_tools(f"设备UDID '{udid}' 不是已知设备，跳过后台切换操作", "background_switch")
        return "unknown_device"
    
    platform = status.get("platform", "").lower()
    device_name = status.get('device_name', udid)
    app_id = get_app_id(udid)
    
    if not app_id:
        get_current_task_log_manager().error_tools(f"设备 {udid} 未配置应用包名", "background_switch")
        return "operation_failed"
    
    get_current_task_log_manager().info_tools(f"开始执行后台切换操作 - 设备: {device_name} ({udid})", "background_switch")
    
    try:
        # 1. 检查初始状态
        initial_state = _check_app_state(udid, driver)
        get_current_task_log_manager().info_tools(f"初始应用状态: {initial_state}", "background_switch")
        
        if initial_state != "foreground":
            get_current_task_log_manager().warning_tools(f"应用当前不在前台，状态: {initial_state}", "background_switch")
            return "operation_failed"
        
        # 2. 执行退后台操作
        if platform == 'ios':
            if not driver:
                # 尝试获取设备驱动
                driver = get_device_driver(udid)
            if not driver:
                get_current_task_log_manager().error_tools("background_switch: iOS平台需要提供driver参数", "background_switch")
                return "operation_failed"
            
            get_current_task_log_manager().info_tools("执行iOS退后台操作: 切换到桌面", "background_switch")
            driver.activate_app("com.apple.springboard")
            
        elif platform == 'android':
            get_current_task_log_manager().info_tools("执行Android退后台操作: 按Home键", "background_switch")
            _, stderr = execute_adb_command(udid, "shell input keyevent KEYCODE_HOME")
            if stderr:
                get_current_task_log_manager().warning_tools(f"Android退后台命令stderr: {stderr}", "background_switch")
        else:
            get_current_task_log_manager().error_tools(f"不支持的平台类型: {platform}", "background_switch")
            return "operation_failed"
        
        # 3. 等待一段时间并检查是否成功退后台
        time.sleep(1)  # 等待1秒让状态稳定
        background_state = _check_app_state(udid, driver)
        get_current_task_log_manager().info_tools(f"退后台后应用状态: {background_state}", "background_switch")
        
        if background_state not in ["background", "crashed"]:
            get_current_task_log_manager().warning_tools(f"应用可能未成功退后台，当前状态: {background_state}", "background_switch")
        
        # 4. 等待3秒
        get_current_task_log_manager().info_tools("等待3秒...", "background_switch")
        time.sleep(3)
        
        # 5. 执行拉回前台操作
        if platform == 'ios':
            get_current_task_log_manager().info_tools("执行iOS拉回前台操作", "background_switch")
            driver.activate_app(app_id)
            
        elif platform == 'android':
            get_current_task_log_manager().info_tools("执行Android拉回前台操作", "background_switch")
            _, stderr = execute_adb_command(udid, f"shell monkey -p {app_id} -c android.intent.category.LAUNCHER 1")
            if stderr and ("Error" in stderr or "error" in stderr or "not found" in stderr or "failed" in stderr.lower()):
                get_current_task_log_manager().warning_tools(f"Android拉回前台命令stderr: {stderr}", "background_switch")
        
        # 6. 等待一段时间并检查是否成功拉回前台
        time.sleep(2)  # 等待2秒让应用启动
        final_state = _check_app_state(udid, driver)
        get_current_task_log_manager().info_tools(f"拉回前台后应用状态: {final_state}", "background_switch")
        
        if final_state == "foreground":
            get_current_task_log_manager().info_tools(f"后台切换操作成功完成 - 设备: {device_name}", "background_switch")
            return "success"
        else:
            get_current_task_log_manager().error_tools(f"应用未能成功拉回前台，最终状态: {final_state}", "background_switch")
            return "operation_failed"
            
    except Exception as e:
        get_current_task_log_manager().error_tools(f"{platform}后台切换操作异常: {e}", "background_switch")
        
        # 检查设备是否在线
        try:
            from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios
            if platform == 'android':
                is_online = check_devices_connect_android(udid)
            elif platform == 'ios':
                is_online = check_devices_connect_ios(udid)
            else:
                is_online = False
                
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                return "device_offline"
        except:
            pass  # 设备检查失败不影响主要功能
            
        return "operation_failed"