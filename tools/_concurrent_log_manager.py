#!/usr/bin/env python3
"""
并发日志管理器
为每个任务创建独立的日志管理器实例，实现真正的日志隔离
"""

import threading
from typing import Dict, Optional
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from log_manager import LogManager


class ConcurrentLogManager:
    """并发日志管理器 - 管理多个任务的独立日志管理器"""
    
    def __init__(self):
        self.task_log_managers: Dict[str, LogManager] = {}
        self.lock = threading.Lock()
        
        # 保留一个全局日志管理器用于系统级日志
        self.global_log_manager = LogManager()
    
    def create_task_log_manager(self, task_id: str, log_dir: str) -> LogManager:
        """
        为指定任务创建独立的日志管理器
        
        Args:
            task_id: 任务ID
            log_dir: 日志目录路径
            
        Returns:
            任务专用的日志管理器
        """
        with self.lock:
            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)
            
            # 创建任务专用的日志管理器
            task_log_manager = LogManager(log_dir=log_dir)
            
            # 存储到管理器中
            self.task_log_managers[task_id] = task_log_manager
            
            self.global_log_manager.info_agent(f"为任务 {task_id} 创建独立日志管理器，目录: {log_dir}")
            task_log_manager.info_agent(f"任务 {task_id} 日志管理器已初始化")
            
            return task_log_manager
    
    def get_task_log_manager(self, task_id: str) -> Optional[LogManager]:
        """获取指定任务的日志管理器"""
        with self.lock:
            return self.task_log_managers.get(task_id)
    
    def remove_task_log_manager(self, task_id: str):
        """移除并清理指定任务的日志管理器"""
        with self.lock:
            if task_id in self.task_log_managers:
                task_log_manager = self.task_log_managers[task_id]
                
                # 记录清理信息
                task_log_manager.info_agent(f"任务 {task_id} 日志管理器即将清理")
                
                # 清理日志管理器的所有handlers
                task_log_manager._cleanup_loggers()
                
                # 从字典中移除
                del self.task_log_managers[task_id]
                
                self.global_log_manager.info_agent(f"移除任务 {task_id} 的日志管理器")
    
    def get_global_log_manager(self) -> LogManager:
        """获取全局日志管理器（用于系统级日志）"""
        return self.global_log_manager
    
    def cleanup_all_task_log_managers(self):
        """清理所有任务日志管理器"""
        with self.lock:
            for task_id in list(self.task_log_managers.keys()):
                self.remove_task_log_manager(task_id)
            
            self.global_log_manager.info_agent("清理所有任务日志管理器完成")
    
    def get_active_task_count(self) -> int:
        """获取活跃任务的日志管理器数量"""
        with self.lock:
            return len(self.task_log_managers)


# 全局并发日志管理器实例
concurrent_log_manager = ConcurrentLogManager()


def create_task_log_manager(task_id: str, log_dir: str) -> LogManager:
    """
    创建任务专用的日志管理器
    
    Args:
        task_id: 任务ID
        log_dir: 日志目录路径
        
    Returns:
        任务专用的LogManager实例
    """
    return concurrent_log_manager.create_task_log_manager(task_id, log_dir)


def get_task_log_manager(task_id: str) -> Optional[LogManager]:
    """
    获取任务专用的日志管理器
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务专用的LogManager实例或None
    """
    return concurrent_log_manager.get_task_log_manager(task_id)


def cleanup_task_log_manager(task_id: str):
    """清理指定任务的日志管理器"""
    concurrent_log_manager.remove_task_log_manager(task_id)


def get_global_log_manager() -> LogManager:
    """获取全局日志管理器"""
    return concurrent_log_manager.get_global_log_manager()


# 线程本地存储，用于存储当前线程的任务日志管理器
import threading
_thread_local = threading.local()


def set_current_task_log_manager(log_manager: LogManager):
    """设置当前线程的任务日志管理器"""
    _thread_local.log_manager = log_manager


def get_current_task_log_manager() -> Optional[LogManager]:
    """获取当前线程的任务日志管理器，如果没有则返回全局日志管理器"""
    if hasattr(_thread_local, 'log_manager'):
        return _thread_local.log_manager
    return get_global_log_manager()


if __name__ == "__main__":
    # 测试并发日志管理器
    print("🧪 测试并发日志管理器")
    
    # 创建两个并发任务的日志管理器
    task1_log_manager = create_task_log_manager("task_001", "log/test_task_001")
    task2_log_manager = create_task_log_manager("task_002", "log/test_task_002")
    
    # 测试日志隔离
    task1_log_manager.info_agent("这是任务1的日志信息")
    task2_log_manager.info_agent("这是任务2的日志信息")
    
    task1_log_manager.info_agent("任务1继续记录日志")
    task2_log_manager.info_agent("任务2继续记录日志")
    
    # 检查活跃任务数量
    print(f"活跃任务数量: {concurrent_log_manager.get_active_task_count()}")
    
    # 清理
    cleanup_task_log_manager("task_001")
    cleanup_task_log_manager("task_002")
    
    print(f"清理后活跃任务数量: {concurrent_log_manager.get_active_task_count()}")
    print("✅ 测试完成")