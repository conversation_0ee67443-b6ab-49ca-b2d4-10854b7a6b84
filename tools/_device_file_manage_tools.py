"""
设备文件管理工具
负责清理截图文件夹、管理日志文件夹等文件系统操作
"""

import os
import re
import shutil
import sys
from typing import Optional

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools._concurrent_log_manager import get_current_task_log_manager
from tools._device_status_manage_tools import get_device_status


def cleanup_device_screenshots(udid: Optional[str] = None) -> bool:
    """
    清理指定设备的截图文件夹
    
    Args:
        udid: 设备UDID，如果为None则跳过清理
        
    Returns:
        bool: 清理是否成功
    """
    if not udid:
        get_current_task_log_manager().info_tools("没有设备信息，跳过截图清理", "cleanup_device_screenshots")
        return True
    
    try:
        get_current_task_log_manager().info_tools(f"开始清理设备截图文件夹: {udid}", "cleanup_device_screenshots")
        
        # 获取设备状态信息
        device_status = get_device_status(udid)
        if not device_status:
            get_current_task_log_manager().warning_tools(f"未找到设备状态: {udid}", "cleanup_device_screenshots")
            return False
        
        device_name = device_status.get("device_name", "")
        if not device_name:
            get_current_task_log_manager().warning_tools(f"设备名称为空: {udid}", "cleanup_device_screenshots")
            return False
        
        # 构建截图文件夹路径
        screenshot_dir = os.path.join(os.getcwd(), "screenshot", device_name)
        
        if os.path.exists(screenshot_dir):
            # 获取清理前的文件数量
            file_count = len([f for f in os.listdir(screenshot_dir) 
                            if os.path.isfile(os.path.join(screenshot_dir, f))])
            
            # 清空截图文件夹
            for filename in os.listdir(screenshot_dir):
                file_path = os.path.join(screenshot_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    
            get_current_task_log_manager().info_tools(f"成功清理截图文件夹: {screenshot_dir}", "cleanup_device_screenshots")
            get_current_task_log_manager().info_tools(f"清理了 {file_count} 个截图文件", "cleanup_device_screenshots")
        else:
            get_current_task_log_manager().info_tools(f"截图文件夹不存在，无需清理: {screenshot_dir}", "cleanup_device_screenshots")
            
        return True
        
    except Exception as e:
        get_current_task_log_manager().error_tools(f"清理截图文件夹时出错: {e}", "cleanup_device_screenshots")
        return False


def cleanup_layout_logs(max_files: int = 20) -> bool:
    """
    清理layout_log目录中的旧文件

    Args:
        max_files: 最大保留文件数，默认20

    Returns:
        bool: 清理是否成功
    """
    try:
        get_current_task_log_manager().info_tools(f"开始清理layout日志文件，最大保留 {max_files} 个", "cleanup_layout_logs")

        layout_log_dir = os.path.join(os.getcwd(), "log", "layout_log")
        if not os.path.exists(layout_log_dir):
            get_current_task_log_manager().info_tools("layout_log目录不存在，无需清理", "cleanup_layout_logs")
            return True

        # 获取所有layout日志文件
        layout_files = []
        for filename in os.listdir(layout_log_dir):
            file_path = os.path.join(layout_log_dir, filename)
            if os.path.isfile(file_path) and filename.endswith('.json'):
                # 获取文件修改时间
                mtime = os.path.getmtime(file_path)
                layout_files.append({
                    'name': filename,
                    'path': file_path,
                    'mtime': mtime
                })

        total_files = len(layout_files)
        get_current_task_log_manager().info_tools(f"发现 {total_files} 个layout日志文件", "cleanup_layout_logs")

        if total_files <= max_files:
            get_current_task_log_manager().info_tools(f"文件数量（{total_files}）未超过限制（{max_files}），无需清理", "cleanup_layout_logs")
            return True

        # 按修改时间排序，最新的在前
        layout_files.sort(key=lambda x: x['mtime'], reverse=True)

        # 需要删除的文件
        to_delete_count = total_files - max_files
        files_to_delete = layout_files[max_files:]

        get_current_task_log_manager().info_tools(f"需要删除 {to_delete_count} 个最旧的layout日志文件", "cleanup_layout_logs")

        # 删除旧文件
        deleted_count = 0
        for file_info in files_to_delete:
            try:
                os.remove(file_info['path'])
                get_current_task_log_manager().info_tools(f"已删除layout日志: {file_info['name']}", "cleanup_layout_logs")
                deleted_count += 1
            except Exception as e:
                get_current_task_log_manager().error_tools(f"删除layout日志失败 {file_info['name']}: {e}", "cleanup_layout_logs")

        get_current_task_log_manager().info_tools(f"layout日志清理完成，删除了 {deleted_count} 个文件，保留 {total_files - deleted_count} 个文件", "cleanup_layout_logs")
        return True

    except Exception as e:
        get_current_task_log_manager().error_tools(f"清理layout日志时出错: {e}", "cleanup_layout_logs")
        return False


def cleanup_appium_logs(max_files: int = 10) -> bool:
    """
    清理appium_logs目录中的旧文件

    Args:
        max_files: 最大保留文件数，默认10

    Returns:
        bool: 清理是否成功
    """
    try:
        get_current_task_log_manager().info_tools(f"开始清理appium日志文件，最大保留 {max_files} 个", "cleanup_appium_logs")

        appium_log_dir = os.path.join(os.getcwd(), "log", "appium_logs")
        if not os.path.exists(appium_log_dir):
            get_current_task_log_manager().info_tools("appium_logs目录不存在，无需清理", "cleanup_appium_logs")
            return True

        # 获取所有appium日志文件
        appium_files = []
        for filename in os.listdir(appium_log_dir):
            file_path = os.path.join(appium_log_dir, filename)
            if os.path.isfile(file_path) and filename.startswith('appium_') and filename.endswith('.log'):
                # 获取文件修改时间
                mtime = os.path.getmtime(file_path)
                appium_files.append({
                    'name': filename,
                    'path': file_path,
                    'mtime': mtime
                })

        total_files = len(appium_files)
        get_current_task_log_manager().info_tools(f"发现 {total_files} 个appium日志文件", "cleanup_appium_logs")

        if total_files <= max_files:
            get_current_task_log_manager().info_tools(f"文件数量（{total_files}）未超过限制（{max_files}），无需清理", "cleanup_appium_logs")
            return True

        # 按修改时间排序，最新的在前
        appium_files.sort(key=lambda x: x['mtime'], reverse=True)

        # 需要删除的文件
        to_delete_count = total_files - max_files
        files_to_delete = appium_files[max_files:]

        get_current_task_log_manager().info_tools(f"需要删除 {to_delete_count} 个最旧的appium日志文件", "cleanup_appium_logs")

        # 删除旧文件
        deleted_count = 0
        for file_info in files_to_delete:
            try:
                os.remove(file_info['path'])
                get_current_task_log_manager().info_tools(f"已删除appium日志: {file_info['name']}", "cleanup_appium_logs")
                deleted_count += 1
            except Exception as e:
                get_current_task_log_manager().error_tools(f"删除appium日志失败 {file_info['name']}: {e}", "cleanup_appium_logs")

        get_current_task_log_manager().info_tools(f"appium日志清理完成，删除了 {deleted_count} 个文件，保留 {total_files - deleted_count} 个文件", "cleanup_appium_logs")
        return True

    except Exception as e:
        get_current_task_log_manager().error_tools(f"清理appium日志时出错: {e}", "cleanup_appium_logs")
        return False


def cleanup_log_folders(max_rounds: int = 20) -> bool:
    """
    清理过多的日志文件夹

    Args:
        max_rounds: 最大保留轮次数，默认20

    Returns:
        bool: 清理是否成功
    """
    try:
        get_current_task_log_manager().info_tools(f"开始清理日志文件夹，最大保留 {max_rounds} 轮", "cleanup_log_folders")

        log_dir = os.path.join(os.getcwd(), "log")
        if not os.path.exists(log_dir):
            get_current_task_log_manager().info_tools("日志目录不存在，无需清理", "cleanup_log_folders")
            return True

        # 获取所有轮次文件夹
        round_pattern = re.compile(r'^round_(\d+)_(\d+)_(\d+)$')
        round_folders = []

        for item in os.listdir(log_dir):
            item_path = os.path.join(log_dir, item)
            if os.path.isdir(item_path):
                match = round_pattern.match(item)
                if match:
                    round_num = int(match.group(1))
                    date_part = int(match.group(2))
                    time_part = int(match.group(3))
                    round_folders.append({
                        'name': item,
                        'path': item_path,
                        'round_num': round_num,
                        'date_part': date_part,
                        'time_part': time_part
                    })

        # 按轮次编号排序（旧的在前）
        round_folders.sort(key=lambda x: x['round_num'])

        total_rounds = len(round_folders)
        get_current_task_log_manager().info_tools(f"发现 {total_rounds} 个轮次日志文件夹", "cleanup_log_folders")

        if total_rounds <= max_rounds:
            get_current_task_log_manager().info_tools(f"轮次数量（{total_rounds}）未超过限制（{max_rounds}），无需清理", "cleanup_log_folders")
            return True

        # 需要删除的轮次数量
        to_delete_count = total_rounds - max_rounds
        folders_to_delete = round_folders[:to_delete_count]

        get_current_task_log_manager().info_tools(f"需要删除 {to_delete_count} 个最早的轮次日志", "cleanup_log_folders")

        # 删除最早的轮次
        deleted_count = 0
        for folder_info in folders_to_delete:
            try:
                shutil.rmtree(folder_info['path'])
                get_current_task_log_manager().info_tools(f"已删除轮次日志: {folder_info['name']}", "cleanup_log_folders")
                deleted_count += 1
            except Exception as e:
                get_current_task_log_manager().error_tools(f"删除轮次日志失败 {folder_info['name']}: {e}", "cleanup_log_folders")

        get_current_task_log_manager().info_tools(f"日志清理完成，删除了 {deleted_count} 个轮次，保留 {total_rounds - deleted_count} 个轮次", "cleanup_log_folders")
        return True

    except Exception as e:
        get_current_task_log_manager().error_tools(f"清理日志文件夹时出错: {e}", "cleanup_log_folders")
        return False


if __name__ == "__main__":
    """测试文件管理工具"""
    print("🧪 测试设备文件管理工具...")

    # 测试清理功能
    print("\n🧹 测试清理功能:")
    print("截图清理（无设备）:", cleanup_device_screenshots(None))
    print("Layout日志清理:", cleanup_layout_logs(20))
    print("Appium日志清理:", cleanup_appium_logs(10))
    print("轮次日志清理:", cleanup_log_folders(20))
