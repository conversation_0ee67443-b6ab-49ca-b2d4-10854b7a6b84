#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API OCR 识别工具，用于设备截图和OCR识别。

提供统一的OCR接口，支持 Android 和 iOS 设备。
"""

import os
import sys
import time
import json
import tempfile
import requests
from typing import List, Dict, Any, Tuple
from PIL import Image
import numpy as np

# 尝试导入 easyocr，如果失败则设为 None
try:
    import easyocr
    import cv2
    EASYOCR_AVAILABLE = True
except ImportError:
    easyocr = None
    cv2 = None
    EASYOCR_AVAILABLE = False

# 导入当前项目的日志管理
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager

# 导入设备状态管理工具
from tools._device_status_manage_tools import get_device_status

# 导入截图工具
from tools.screenshot_tools import get_screenshot

# 导入设备检查工具
from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios

# --- 辅助函数 ---

def _is_point_in_popup(center: Tuple[float, float], popups: Dict[str, Dict[str, Tuple[int, int]]]) -> bool:
    """检查一个点是否位于任何一个弹窗区域内。"""
    center_x, center_y = center
    for popup_info in popups.values():
        left_top = popup_info['left_top']
        right_bottom = popup_info['right_bottom']
        if (left_top[0] <= center_x <= right_bottom[0] and
            left_top[1] <= center_y <= right_bottom[1]):
            return True
    return False

# 导入弹窗检测工具
from tools._pop_up_tools import detect_popups

# --- 连通性检查函数 ---

def check_qaassist_connectivity() -> bool:
    """
    检查 qaassist API 的连通性
    
    Returns:
        bool: True 表示可以连通，False 表示无法连通
    """
    try:
        # 使用一个简单的 HEAD 请求检查连通性，避免传输数据
        test_url = "http://qaassist.sankuai.com/compass/api"
        response = requests.head(test_url, timeout=5)
        # 只要能收到响应（不管状态码），说明服务可达
        get_current_task_log_manager().info_tools(f"qaassist API 连通性检查成功，状态码: {response.status_code}", "check_qaassist_connectivity")
        return True
    except Exception as e:
        get_current_task_log_manager().warning_tools(f"qaassist API 连通性检查失败: {e}", "check_qaassist_connectivity")
        return False

# --- EasyOCR 备用函数 ---

def easyocr_recognize_text(image_path: str, platform: str = '', scale_ratio: float = 1.0) -> Dict[str, Any]:
    """
    使用 EasyOCR 识别图片中的文本（备用方案）
    
    Args:
        image_path: 图片路径
        platform: 设备平台 ('ios' 或 'android')
        scale_ratio: iOS 设备的缩放比例
        
    Returns:
        dict: 标准化的OCR结果，与 api_ocr 返回格式保持一致
    """
    if not EASYOCR_AVAILABLE:
        get_current_task_log_manager().error_tools("EasyOCR 库未安装，无法使用备用OCR方案", "easyocr_recognize_text")
        return {
            "has_popups": False,
            "elements": [],
            "joined_text": "EasyOCR 库未安装，无法进行OCR识别",
            "final_text": "EasyOCR 库未安装，无法进行OCR识别",
            "ocr_time": None,
            "scale_ratio": scale_ratio,
            "image_url": None,
            "local_path": image_path
        }
    
    get_current_task_log_manager().info_tools(f"开始使用 EasyOCR 识别图片: {image_path}", "easyocr_recognize_text")
    
    try:
        # 初始化 EasyOCR 读取器
        reader = easyocr.Reader(['ch_sim', 'en'])
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            get_current_task_log_manager().error_tools(f"无法读取图像: {image_path}", "easyocr_recognize_text")
            return {
                "has_popups": False,
                "elements": [],
                "joined_text": "无法读取图像",
                "final_text": "无法读取图像",
                "ocr_time": None,
                "scale_ratio": scale_ratio,
                "image_url": None,
                "local_path": image_path
            }
        
        # 使用 easyocr 识别文本
        start_time = time.time()
        results = reader.readtext(image)
        ocr_time = time.time() - start_time
        
        # 检测弹窗
        has_popups = False
        popup_coords = None
        try:
            popups = detect_popups(image_path)
            has_popups = bool(popups)
            if has_popups and popups:
                # 使用第一个弹窗的坐标
                first_popup = next(iter(popups.values()))
                popup_coords = first_popup
        except Exception as e:
            get_current_task_log_manager().error_tools(f"弹窗检测失败: {e}", "easyocr_recognize_text")
        
        # 如果没有检测到弹窗，尝试使用简单的亮度分析
        if not has_popups:
            try:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                mean_brightness = np.mean(gray)
                std_brightness = np.std(gray)
                threshold = mean_brightness + std_brightness
                bright_mask = gray > threshold
                
                height, width = gray.shape
                top_region_height = int(height * 0.1)
                
                # 分析顶部区域
                top_region_mask = bright_mask[:top_region_height, :]
                top_region_detected = False
                
                for y in range(top_region_height):
                    row = top_region_mask[y]
                    if np.sum(row) > width * 0.8:
                        top_region_detected = True
                        break
                
                if top_region_detected:
                    y_coords, x_coords = np.where(top_region_mask)
                    if len(x_coords) > 0:
                        min_x, max_x = np.min(x_coords), np.max(x_coords)
                        min_y, max_y = np.min(y_coords), np.max(y_coords)
                        popup_coords = {
                            'left_top': (min_x, min_y),
                            'right_bottom': (max_x, max_y)
                        }
                        has_popups = True
                        
            except Exception as e:
                get_current_task_log_manager().error_tools(f"亮度分析失败: {e}", "easyocr_recognize_text")
        
        # 转换结果格式
        ocr_elements = []
        popup_texts = []
        other_texts = []
        
        for result in results:
            bbox, text, prob = result
            
            # 计算文本框中心点
            points = np.array(bbox)
            center_x = np.mean(points[:, 0])
            center_y = np.mean(points[:, 1])
            
            # 对于 iOS 设备，需要对坐标进行 scale_ratio 换算
            if platform.lower() == 'ios':
                center_x = center_x / scale_ratio
                center_y = center_y / scale_ratio
            
            # 检查文本是否在弹窗区域内
            is_in_popup = False
            if popup_coords:
                left_top = popup_coords['left_top']
                right_bottom = popup_coords['right_bottom']
                
                if (left_top[0] <= center_x <= right_bottom[0] and
                    left_top[1] <= center_y <= right_bottom[1]):
                    is_in_popup = True
            
            text_info = {
                "info": text,
                "center": (float(center_x), float(center_y)),
                "elem_det_type": "text",
                "in_popup": is_in_popup and has_popups,
                "confidence": float(prob)
            }
            
            # 过滤低置信度的文本
            if prob >= 0.5:  # EasyOCR 的置信度阈值
                ocr_elements.append(text_info)
                
                if is_in_popup:
                    popup_texts.append(text_info)
                else:
                    other_texts.append(text_info)
        
        # 生成连接文本
        joined_text = ''
        if ocr_elements:
            joined_text = '；'.join([
                f"{elem['info']} [{elem['elem_det_type']}]({int(round(elem['center'][0]))},{int(round(elem['center'][1]))})" 
                for elem in ocr_elements
            ])
        
        # 按Y坐标排序
        popup_texts.sort(key=lambda x: x["center"][1])
        other_texts.sort(key=lambda x: x["center"][1])
        
        def format_elem(idx, elem):
            return f"{idx+1}.{elem['info']} [{elem['elem_det_type']}]({int(round(elem['center'][0]))},{int(round(elem['center'][1]))})"
        
        # 生成最终描述文本
        if has_popups:
            parts = []
            if popup_texts:
                parts.append(
                    "当前页面有1个弹窗，其中的元素有：" +
                    ' '.join([format_elem(i, elem) for i, elem in enumerate(popup_texts)])
                )
            if other_texts:
                parts.append(
                    "其他不在弹窗内的文本有：" +
                    ' '.join([format_elem(i, elem) for i, elem in enumerate(other_texts)])
                )
            if parts:
                final_text = ' '.join(parts)
            else:
                final_text = "当前页面有1个弹窗，但未检测到文本。"
        else:
            if popup_texts or other_texts:
                all_elements = popup_texts + other_texts
                final_text = "页面文本有：" + ' '.join([
                    format_elem(i, elem) for i, elem in enumerate(all_elements)
                ])
            else:
                final_text = "页面未检测到文本。"
        
        get_current_task_log_manager().info_tools(f"EasyOCR 识别完成，共识别到 {len(ocr_elements)} 个文本元素", "easyocr_recognize_text")
        
        return {
            "has_popups": has_popups,
            "elements": ocr_elements,
            "joined_text": joined_text,
            "final_text": final_text,
            "ocr_time": ocr_time,
            "scale_ratio": scale_ratio,
            "image_url": None,
            "local_path": image_path
        }
        
    except Exception as e:
        get_current_task_log_manager().error_tools(f"EasyOCR 识别失败: {e}", "easyocr_recognize_text")
        return {
            "has_popups": False,
            "elements": [],
            "joined_text": f"EasyOCR 识别失败: {str(e)}",
            "final_text": f"EasyOCR 识别失败: {str(e)}",
            "ocr_time": None,
            "scale_ratio": scale_ratio,
            "image_url": None,
            "local_path": image_path
        }

# --- Agent 使用的OCR函数 ---

def show_page_ocr_result(udid: str) -> Dict[str, Any]:
    """
    返回当前页面的OCR结果，可用于判断当前页面的展示内容。
    该函数为阻塞式调用，agent 必须等待其返回结果后再进行后续操作。
    
    Args:
        udid: 设备的序列号(UDID)

    Returns:
        dict: 标准化OCR和弹窗检测结果，格式为:
        {
            "has_popups": bool,
            "elements": list,
            "joined_text": str,
            "final_text": str,
            "ocr_time": float,
            "scale_ratio": float
        }

    Raises:
        ValueError: 如果设备未连接或截图失败
        Exception: 如果OCR过程发生错误
    """
    get_current_task_log_manager().info_tools(f"开始获取设备 {udid} 的页面OCR结果...", "show_page_ocr_result")
    
    # 从设备状态获取平台信息
    device_status = get_device_status(udid)
    if not device_status:
        raise ValueError(f"设备 {udid} 状态文件不存在")
    
    platform = device_status.get('platform', '').lower()
    if not platform:
        raise ValueError(f"设备 {udid} 状态文件中未找到平台信息")
    
    get_current_task_log_manager().info_tools(f"设备 {udid} 平台: {platform}", "show_page_ocr_result")
    
    # 获取设备的 scale_ratio
    if platform == 'ios':
        # 从设备状态获取真实的 scale_ratio
        scale_ratio = device_status.get('scale_ratio', 3.0)  # iOS 设备默认缩放比例为3.0
        get_current_task_log_manager().info_tools(f"iOS设备 {udid} 获取到 scale_ratio: {scale_ratio}", "show_page_ocr_result")
    else:
        scale_ratio = 1.0  # Android 设备默认缩放比例

    start_time = time.time()
    
    # 检查 qaassist API 连通性
    get_current_task_log_manager().info_tools("检查 qaassist API 连通性...", "show_page_ocr_result")
    is_qaassist_available = check_qaassist_connectivity()
    
    if is_qaassist_available:
        get_current_task_log_manager().info_tools("qaassist API 可用，使用远程 OCR 服务", "show_page_ocr_result")
        return _api_ocr_qaassist(udid, platform, scale_ratio, start_time)
    else:
        get_current_task_log_manager().info_tools("qaassist API 不可用，使用 EasyOCR 备用方案", "show_page_ocr_result")
        return _api_ocr_easyocr(udid, platform, scale_ratio, start_time)


def _api_ocr_qaassist(udid: str, platform: str, scale_ratio: float, start_time: float) -> Dict[str, Any]:
    """
    使用 qaassist API 进行 OCR 识别
    """
    # 获取截图（需要上传）
    try:
        screenshot_result = get_screenshot(udid)
        local_path = screenshot_result["local_path"]
        image_url = screenshot_result["image_url"]
        
        if not image_url:
            get_current_task_log_manager().warning_tools("截图上传失败，切换到 EasyOCR 备用方案", "_api_ocr_qaassist")
            return _api_ocr_easyocr(udid, platform, scale_ratio, start_time)
            
    except Exception as e:
        error_msg = f"截图获取失败: {e}"
        get_current_task_log_manager().error_tools(error_msg, "_api_ocr_qaassist")
        return {"has_popups": False, "elements": [], "joined_text": "", "final_text": "", "ocr_time": None, "scale_ratio": scale_ratio}

    # 检测弹窗
    has_popups = False
    popups = {}
    try:
        popups = detect_popups(local_path)
        has_popups = bool(popups)
    except Exception as e:
        get_current_task_log_manager().error_tools(f"弹窗检测失败: {e}", "_api_ocr_qaassist")

    # 调用OCR API
    api_url = "http://qaassist.sankuai.com/compass/api/ocr/getUiPageParseResult"
    params = {"PicUrl": image_url}
    ocr_start_time = time.time()
    try:
        get_current_task_log_manager().info_tools(f"正在调用OCR API识别图片: {image_url}", "_api_ocr_qaassist")
        resp = requests.get(api_url, params=params, timeout=15)
        resp.raise_for_status()
        data = resp.json()
        ocr_duration = time.time() - ocr_start_time
        get_current_task_log_manager().info_tools(f"OCR API调用成功，耗时: {ocr_duration:.2f}秒", "_api_ocr_qaassist")
    except Exception as e:
        error_msg = f"API OCR请求失败: {e}"
        get_current_task_log_manager().error_tools(error_msg, "_api_ocr_qaassist")
        get_current_task_log_manager().warning_tools("API OCR 请求失败，切换到 EasyOCR 备用方案", "_api_ocr_qaassist")
        return _api_ocr_easyocr(udid, platform, scale_ratio, start_time)

    # 解析OCR结果
    result_info = data.get("result_info", {})
    result_list = result_info.get("result_info_list", [])
    ocr_elements = []
    ocr_elements_with_prob = []
    
    for elem in result_list:
        prob = elem.get("probability", 0)
        region = elem.get("elem_det_region", [])
        text = elem.get("elem_detail_info", "")
        elem_type = elem.get("elem_det_type", "unknown")
        
        # 只保留高置信度的元素
        if prob < 0.6:
            continue
            
        if len(region) == 4:
            x1, y1, x2, y2 = region
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            # 对于 iOS 设备，需要对坐标进行 scale_ratio 换算
            if platform == 'ios':
                center_x = center_x / scale_ratio
                center_y = center_y / scale_ratio
                
        else:
            center_x, center_y = 0, 0
            
        display_text = text if text.strip() else "<无文本>"
        ocr_elements_with_prob.append({
            "info": display_text,
            "center": (center_x, center_y),
            "elem_det_type": elem_type,
            "probability": prob
        })
        
    # 按概率排序，返回所有结果
    ocr_elements_with_prob.sort(key=lambda x: x["probability"], reverse=True)
    ocr_elements = [
        {k: v for k, v in elem.items() if k != "probability"}
        for elem in ocr_elements_with_prob
    ]
    
    get_current_task_log_manager().info_tools(f"qaassist OCR识别完成，共识别到 {len(ocr_elements)} 个高置信度元素", "_api_ocr_qaassist")

    # 标记弹窗内的元素
    for elem in ocr_elements:
        in_popup = _is_point_in_popup(elem["center"], popups)
        elem["in_popup"] = in_popup and has_popups
        
    # 生成连接文本
    joined_text = ''
    if ocr_elements:
        joined_text = '；'.join([
            f"{elem['info']} [{elem['elem_det_type']}]({int(round(elem['center'][0]))},{int(round(elem['center'][1]))})" for elem in ocr_elements
        ])
        
    # 分类元素
    in_popup_elements = []
    other_elements = []
    for elem in ocr_elements:
        if elem.get("in_popup"):
            in_popup_elements.append(elem)
        else:
            other_elements.append(elem)
            
    # 按Y坐标排序
    in_popup_elements.sort(key=lambda x: x["center"][1])
    other_elements.sort(key=lambda x: x["center"][1])
    
    def format_elem(idx, elem):
        return f"{idx+1}.{elem['info']} [{elem['elem_det_type']}]({int(round(elem['center'][0]))},{int(round(elem['center'][1]))})"
    
    # 生成最终描述文本
    if has_popups:
        popup_count = len(popups)
        parts = []
        if in_popup_elements:
            parts.append(
                f"当前页面有{popup_count}个弹窗，其中的元素有：" +
                ' '.join([
                    format_elem(i, elem) for i, elem in enumerate(in_popup_elements)
                ])
            )
        if other_elements:
            parts.append(
                "其他不在弹窗内的文本有：" +
                ' '.join([
                    format_elem(i, elem) for i, elem in enumerate(other_elements)
                ])
            )
        if parts:
            final_text = ' '.join(parts)
        else:
            final_text = f"当前页面有{popup_count}个弹窗，但未检测到文本。"
    else:
        if in_popup_elements or other_elements:
            all_elements = in_popup_elements + other_elements
            final_text = "页面文本有：" + ' '.join([
                format_elem(i, elem) for i, elem in enumerate(all_elements)
            ])
        else:
            final_text = "页面未检测到文本。"
    
    # 准备返回结果
    result = {
        "has_popups": has_popups,
        "elements": ocr_elements,
        "joined_text": joined_text,
        "final_text": final_text,
        "ocr_time": time.time() - ocr_start_time,
        "scale_ratio": scale_ratio,
        "image_url": image_url,
        "local_path": local_path
    }
    
    get_current_task_log_manager().info_tools("qaassist OCR识别完成", "_api_ocr_qaassist")
    
    return result


def _api_ocr_easyocr(udid: str, platform: str, scale_ratio: float, start_time: float) -> Dict[str, Any]:
    """
    使用 EasyOCR 进行 OCR 识别
    """
    # 获取截图（仅本地，无需上传）
    try:
        screenshot_result = get_screenshot(udid)
        local_path = screenshot_result["local_path"]
    except Exception as e:
        error_msg = f"截图获取失败: {e}"
        get_current_task_log_manager().error_tools(error_msg, "_api_ocr_easyocr")
        return {"has_popups": False, "elements": [], "joined_text": "", "final_text": "", "ocr_time": None, "scale_ratio": scale_ratio}

    # 使用 EasyOCR 进行识别
    get_current_task_log_manager().info_tools("使用 EasyOCR 进行本地识别", "_api_ocr_easyocr")
    result = easyocr_recognize_text(local_path, platform, scale_ratio)
    
    get_current_task_log_manager().info_tools("EasyOCR 识别完成", "_api_ocr_easyocr")
    return result


def api_ocr_final_text(udid: str) -> str:
    """
    统一OCR接口，只返回最终文本描述。
    
    Args:
        udid: 设备的序列号(UDID)

    Returns:
        str: OCR和弹窗检测的最终文本描述

    Raises:
        ValueError: 如果设备未连接或OCR失败
        Exception: 如果OCR过程发生错误
    """
    get_current_task_log_manager().info_tools(f"开始对设备 {udid} 进行OCR识别（仅返回文本）", "api_ocr_final_text")
    
    try:
        result = show_page_ocr_result(udid)
        final_text = result["final_text"]
        
        get_current_task_log_manager().info_tools(f"设备 {udid} OCR文本识别完成", "api_ocr_final_text")
        
        return final_text
    except Exception as e:
        error_msg = f"api_ocr_final_text 执行失败: {e}"
        get_current_task_log_manager().error_tools(error_msg, "api_ocr_final_text")
        raise


def api_ocr_test(image_url: str) -> Dict[str, Any]:
    """
    测试OCR接口，直接使用图片URL而无需设备截图。
    
    Args:
        image_url: 图片URL
    
    Returns:
        dict: 标准化OCR和弹窗检测结果。
    """
    get_current_task_log_manager().info_tools(f"开始对图片进行OCR测试: {image_url}", "api_ocr_test")
    
    has_popups = False
    popups = {}
    
    # 下载图片到临时文件
    try:
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            local_path = temp_file.name
            
        response = requests.get(image_url, stream=True)
        response.raise_for_status()
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                
        # 检测弹窗
        try:
            popups = detect_popups(local_path)
            has_popups = bool(popups)
        except Exception as e:
            get_current_task_log_manager().error_tools(f"弹窗检测失败: {e}", "api_ocr_test")
            
    except Exception as e:
        get_current_task_log_manager().error_tools(f"图片下载失败: {e}", "api_ocr_test")
        return {"has_popups": False, "elements": [], "joined_text": "", "final_text": "", "ocr_time": None}
        
    # 调用OCR API
    api_url = "http://qaassist.sankuai.com/compass/api/ocr/getUiPageParseResult"
    params = {"PicUrl": image_url}
    try:
        resp = requests.get(api_url, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
    except Exception as e:
        get_current_task_log_manager().error_tools(f"API OCR请求失败: {e}", "api_ocr_test")
        return {"has_popups": has_popups, "elements": [], "joined_text": "", "final_text": "", "ocr_time": None}
        
    result_info = data.get("result_info", {})
    result_list = result_info.get("result_info_list", [])
    ocr_elements = []
    ocr_elements_with_prob = []
    
    for elem in result_list:
        prob = elem.get("probability", 0)
        region = elem.get("elem_det_region", [])
        text = elem.get("elem_detail_info", "")
        elem_type = elem.get("elem_det_type", "unknown")
        
        if prob < 0.6:
            continue
            
        if len(region) == 4:
            x1, y1, x2, y2 = region
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            center_point = (center_x, center_y)
        else:
            center_point = (0, 0)
            
        display_text = text if text.strip() else "<无文本>"
        ocr_elements_with_prob.append({
            "info": display_text,
            "center": center_point,
            "elem_det_type": elem_type,
            "probability": prob
        })
        
    # 按概率排序，返回所有结果
    ocr_elements_with_prob.sort(key=lambda x: x["probability"], reverse=True)
    ocr_elements = [
        {k: v for k, v in elem.items() if k != "probability"}
        for elem in ocr_elements_with_prob
    ]
    
    get_current_task_log_manager().info_tools(f"OCR测试完成，识别到 {len(ocr_elements)} 个元素", "api_ocr_test")
            
    for elem in ocr_elements:
        in_popup = _is_point_in_popup(elem["center"], popups)
        elem["in_popup"] = in_popup and has_popups
        
    joined_text = ''
    if ocr_elements:
        joined_text = '；'.join([
            f"{elem['info']} [{elem['elem_det_type']}]({int(round(elem['center'][0]))},{int(round(elem['center'][1]))})" for elem in ocr_elements
        ])
        
    in_popup_elements = []
    other_elements = []
    for elem in ocr_elements:
        if elem.get("in_popup"):
            in_popup_elements.append(elem)
        else:
            other_elements.append(elem)
            
    in_popup_elements.sort(key=lambda x: x["center"][1])
    other_elements.sort(key=lambda x: x["center"][1])
    
    def format_elem(idx, elem):
        return f"{idx+1}.{elem['info']} [{elem['elem_det_type']}]({int(round(elem['center'][0]))},{int(round(elem['center'][1]))})"
    
    if has_popups:
        popup_count = len(popups)
        parts = []
        if in_popup_elements:
            parts.append(
                f"当前页面有{popup_count}个弹窗，其中的元素有：" +
                ' '.join([
                    format_elem(i, elem) for i, elem in enumerate(in_popup_elements)
                ])
            )
        if other_elements:
            parts.append(
                "其他不在弹窗内的文本有：" +
                ' '.join([
                    format_elem(i, elem) for i, elem in enumerate(other_elements)
                ])
            )
        if parts:
            final_text = ' '.join(parts)
        else:
            final_text = f"当前页面有{popup_count}个弹窗，但未检测到文本。"
    else:
        if in_popup_elements or other_elements:
            all_elements = in_popup_elements + other_elements
            final_text = "页面文本有：" + ' '.join([
                format_elem(i, elem) for i, elem in enumerate(all_elements)
            ])
        else:
            final_text = "页面未检测到文本。"
            
    # 清理临时文件
    try:
        if os.path.exists(local_path):
            os.remove(local_path)
    except Exception as e:
        get_current_task_log_manager().error_tools(f"清理临时文件失败: {e}", "api_ocr_test")
    
    get_current_task_log_manager().info_tools("OCR测试识别完成", "api_ocr_test")
    
    return {
        "has_popups": has_popups,
        "elements": ocr_elements,
        "joined_text": joined_text,
        "final_text": final_text,
        "ocr_time": None
    }
    

def api_ocr_test_final_text(image_url: str) -> str:
    """
    测试OCR接口，只返回最终文本描述。
    
    Args:
        image_url: 图片URL
    
    Returns:
        str: OCR和弹窗检测的最终文本描述。
    """
    result = api_ocr_test(image_url)
    return result["final_text"]


def locate_element_from_ocr(udid: str, target_text: str) -> Dict[str, Any]:
    """
    通过OCR搜索包含指定文本的元素信息。

    Args:
        udid: 设备序列号
        target_text: 要搜索的目标文本（子串匹配，大小写不敏感）

    Returns:
        dict: 结构化结果，包含搜索结果及元素详细信息
            {
              "status": "success",
              "udid": str,
              "target_text": str,
              "found": bool,
              "matches": [ {info, center, elem_det_type, in_popup, (confidence|probability)?} ],
              "image_url": str | None,
              "local_path": str | None,
              "final_reply": str
            }
    """
    tool_name = "locate_element_from_ocr"
    try:
        get_current_task_log_manager().info_tools(
            f"开始通过OCR搜索元素，设备: {udid}，目标文本: '{target_text}'",
            tool_name,
        )

        if not target_text or not isinstance(target_text, str):
            msg = "目标文本无效"
            get_current_task_log_manager().error_tools(msg, tool_name)
            return {
                "status": "error",
                "udid": udid,
                "target_text": target_text,
                "found": False,
                "matches": [],
                "image_url": None,
                "local_path": None,
                "final_reply": f"没找到包含 {target_text} 文本的元素",
                "message": msg,
            }

        # 复用统一OCR流程
        ocr_result = show_page_ocr_result(udid)

        elements = ocr_result.get("elements", []) or []
        image_url = ocr_result.get("image_url")
        local_path = ocr_result.get("local_path")

        # 兼容字段：有的分支带 confidence，有的带 probability
        normalized_matches: List[Dict[str, Any]] = []

        query = target_text.strip().casefold()
        for elem in elements:
            text_info = str(elem.get("info", ""))
            if not text_info:
                continue
            if query and query in text_info.casefold():
                match_entry = {
                    "info": text_info,
                    "center": elem.get("center"),
                    "elem_det_type": elem.get("elem_det_type"),
                    "in_popup": elem.get("in_popup", False),
                }
                # 保留置信度字段（若存在）
                if "confidence" in elem:
                    match_entry["confidence"] = elem.get("confidence")
                if "probability" in elem:
                    match_entry["probability"] = elem.get("probability")
                normalized_matches.append(match_entry)

        found = len(normalized_matches) > 0
        if found:
            element_details = []
            for match in normalized_matches:
                center = match.get("center", (0, 0))
                elem_type = match.get("elem_det_type", "unknown")
                info = match.get("info", "")
                in_popup = match.get("in_popup", False)
                popup_status = "（在弹窗内）" if in_popup else ""
                element_details.append(f"{info} [{elem_type}]({int(center[0])},{int(center[1])}){popup_status}")
            
            details = "，".join(element_details)
            final_reply = f"找到了包含 {target_text} 文本的元素，为：{details}"
        else:
            final_reply = f"没找到包含 {target_text} 文本的元素"

        result = {
            "status": "success",
            "udid": udid,
            "target_text": target_text,
            "found": found,
            "matches": normalized_matches,
            "image_url": image_url,
            "local_path": local_path,
            "final_reply": final_reply,
        }

        get_current_task_log_manager().info_tools(
            f"OCR元素搜索完成，找到: {found}，匹配个数: {len(normalized_matches)}",
            tool_name,
        )
        return result

    except Exception as e:
        get_current_task_log_manager().error_tools(f"OCR元素搜索出现异常: {e}", tool_name)
        return {
            "status": "error",
            "udid": udid,
            "target_text": target_text,
            "found": False,
            "matches": [],
            "image_url": None,
            "local_path": None,
            "final_reply": f"没找到包含 {target_text} 文本的元素",
            "message": str(e),
        }


def test_easyocr_from_udid(udid: str) -> Dict[str, Any]:
    """
    测试 EasyOCR 功能，直接对指定设备截图并使用 EasyOCR 识别
    
    Args:
        udid: 设备序列号
        
    Returns:
        dict: EasyOCR 识别结果
    """
    get_current_task_log_manager().info_tools(f"开始测试设备 {udid} 的 EasyOCR 功能", "test_easyocr_from_udid")
    
    try:
        # 获取截图
        screenshot_result = get_screenshot(udid)
        local_path = screenshot_result["local_path"]
        
        # 使用 EasyOCR 识别
        result = easyocr_recognize_text(local_path)
        
        get_current_task_log_manager().info_tools(f"设备 {udid} EasyOCR 测试完成", "test_easyocr_from_udid")
        return result
        
    except Exception as e:
        error_msg = f"EasyOCR 测试失败: {e}"
        get_current_task_log_manager().error_tools(error_msg, "test_easyocr_from_udid")
        scale_ratio = get_device_status(udid)["scale_ratio"]
        return {
            "has_popups": False,
            "elements": [],
            "joined_text": error_msg,
            "final_text": error_msg,
            "ocr_time": None,
            "scale_ratio": scale_ratio,
            "image_url": None,
            "local_path": None
        }


# 为了向后兼容，保留旧的函数名作为别名
api_ocr = show_page_ocr_result
text_validation_by_ocr = locate_element_from_ocr


if __name__ == "__main__":
    # 测试 api_ocr 函数
    test_image_url = "https://p1.meituan.net/ptautotest/e6ff59dfaf7d9aec9f8d46446940ef981315106.png"
    
    print("====== 开始测试 api_ocr_test 函数 ======")
    result = api_ocr_test(test_image_url)
    print("\n完整返回结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("\n最终文本:")
    print(result["final_text"])
    
    print("\n====== 测试 api_ocr_test_final_text 函数 ======")
    final_text = api_ocr_test_final_text(test_image_url)
    print("最终文本:")
    print(final_text)
    print("====== 测试结束 ======")