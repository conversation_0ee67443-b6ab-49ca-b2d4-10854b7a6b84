import requests
import logging
import time
import socket
import platform
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json


def get_host_id():
    return "Mac-Studio"

# 获取日志对象
logger = logging.getLogger("notify_user")
logger.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")

# 如果logger没有处理器，添加一个处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置不传播到父记录器，避免重复日志
    logger.propagate = False

# 服务器标识符，可以在程序启动时设置
# 初始值从env_config中获取，如果有设置的话
try:
    host_id = get_host_id()
    SERVER_IDENTIFIER = host_id if host_id else "MBP-D95QHDYH5K-2250"
except Exception as e:
    logger.error(f"获取主机ID时出错: {e}")
    SERVER_IDENTIFIER = "MBP-D95QHDYH5K-2250"

# 主机名称缓存
_HOST_NAME = None
# 主机名称文件存储在log目录下
_HOST_NAME_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "log", "host_name.txt")

# 邮件配置
DEFAULT_EMAIL_ACCOUNT = '<EMAIL>'  # 默认备用邮箱账号
DEFAULT_EMAIL_PASSWORD = 'vmpyyrajsljfbecf'  # 邮箱密码或授权码
DEFAULT_SMTP_SERVER = 'smtp.qq.com'  # SMTP服务器
DEFAULT_SMTP_PORT = 465  # SMTP端口


def add_host_info_to_message(message):
    """
    在消息中添加主机信息
    :param message: 原始消息内容
    :return: 添加了主机信息的消息内容
    """
    # 获取环境配置中的主机名称
    hostname = get_host_id()
    
    # 如果设置了服务器标识符，优先使用标识符
    if SERVER_IDENTIFIER:
        server_info = f"【{SERVER_IDENTIFIER}】\n"
    else:
        server_info = f"【{hostname}】\n"
    
    # 将服务器信息添加到消息开头
    return server_info + message

def send_individual_message(message, receivers, timeout=10, add_host_info=True, fallback_emails=None):
    """
    发送个人消息，如果失败则尝试发送邮件
    :param message: 消息内容
    :param receivers: 接收者MIS号列表
    :param timeout: 请求超时时间（秒）
    :param add_host_info: 是否添加主机信息，默认为True
    :param fallback_emails: 备用邮箱列表，当DX消息发送失败时使用，默认为None（会使用默认QQ邮箱）
    :return: 响应结果
    """
    start_time = time.time()
    base_url = "http://qaassist.sankuai.com/compass/api/dx"
    url = f"{base_url}/send/individual"
    
    # 如果需要，添加主机信息
    if add_host_info:
        message = add_host_info_to_message(message)
    
    params = {
        'message': message,
        'receivers': receivers  # receivers 是字符串数组，如 ['zhangsan', 'lisi']
    }
    
    # 记录发送前的信息
    logger.info(f"正在发送消息到 {url}")
    logger.info(f"接收者: {receivers}")
    logger.info(f"消息内容: {message[:100]}..." if len(message) > 100 else f"消息内容: {message}")
    logger.info(f"请求参数: {params}")
    
    # 标记消息发送是否成功
    message_sent_success = False
    
    try:
        # 发送请求，设置超时时间
        response = requests.post(url, params=params, timeout=timeout)
        
        # 记录响应信息
        logger.info(f"发送个人消息响应状态码: {response.status_code}")
        logger.info(f"发送个人消息响应内容: {response.text}")
        
        # 记录请求耗时
        elapsed_time = time.time() - start_time
        logger.info(f"发送消息耗时: {elapsed_time:.2f}秒")
        
        # 检查响应状态码和响应内容中的rescode
        if response.status_code == 200:
            # 简单检查rescode=0或"rescode":0，考虑不同的格式
            if ('rescode=0' in response.text or 
                '"rescode":0' in response.text.replace(' ', '') or 
                "'rescode':0" in response.text.replace(' ', '') or
                'rescode=0,' in response.text):
                message_sent_success = True
                return response.text
            else:
                # 提取错误信息
                error_msg = f"API错误: {response.text}"
                logger.error(f"发送个人消息失败，API返回: {response.text}")
        else:
            logger.error(f"发送个人消息失败，状态码: {response.status_code}")
            error_msg = f"Error: HTTP {response.status_code}"
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - start_time
        error_msg = f"发送个人消息超时，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
    except requests.exceptions.ConnectionError:
        elapsed_time = time.time() - start_time
        error_msg = f"发送个人消息连接错误，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"发送个人消息失败: {str(e)}，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
        # 记录详细的错误信息
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
    
    # 如果消息发送失败，尝试通过邮件发送
    if not message_sent_success:
        # 如果没有提供备用邮箱，使用默认邮箱
        emails_to_use = fallback_emails if fallback_emails else [DEFAULT_EMAIL_ACCOUNT]
        
        logger.info(f"尝试通过邮件发送消息至: {emails_to_use}")
        
        # 构建邮件主题
        subject = f"通知消息 - 原接收者: {', '.join(receivers)}"
        
        # 发送邮件
        email_result = send_email(subject, message, emails_to_use)
        
        if email_result:
            logger.info("通过邮件成功发送消息")
            return "Message sent via email"
        else:
            logger.error("通过邮件发送消息也失败")
    
    # 如果所有方式都失败，返回错误信息
    return error_msg

def send_group_message(message, room_id, add_host_info=True, fallback_emails=None):
    """
    发送群组消息，如果失败则尝试发送邮件
    :param message: 消息内容
    :param room_id: 群组ID
    :param add_host_info: 是否添加主机信息，默认为True
    :param fallback_emails: 备用邮箱列表，当DX消息发送失败时使用，默认为None（会使用默认QQ邮箱）
    :return: 响应结果
    """
    start_time = time.time()
    base_url = "http://qaassist.sankuai.com/compass/api/dx"
    url = f"{base_url}/send/group"
    
    # 如果需要，添加主机信息
    if add_host_info:
        message = add_host_info_to_message(message)
    
    params = {
        'message': message,
        'roomId': room_id  # roomId 是长整型
    }
    
    # 记录发送前的信息
    logger.info(f"正在发送群组消息到 {url}")
    logger.info(f"群组ID: {room_id}")
    logger.info(f"消息内容: {message[:100]}..." if len(message) > 100 else f"消息内容: {message}")
    logger.info(f"请求参数: {params}")
    
    # 标记消息发送是否成功
    message_sent_success = False
    
    try:
        # 发送请求，设置超时时间
        response = requests.post(url, params=params)
        
        # 记录响应信息
        logger.info(f"发送群组消息响应状态码: {response.status_code}")
        logger.info(f"发送群组消息响应内容: {response.text}")
        
        # 记录请求耗时
        elapsed_time = time.time() - start_time
        logger.info(f"发送消息耗时: {elapsed_time:.2f}秒")
        
        # 检查响应状态码和响应内容中的rescode
        if response.status_code == 200:
            # 简单检查rescode=0或"rescode":0，考虑不同的格式
            if ('rescode=0' in response.text or 
                '"rescode":0' in response.text.replace(' ', '') or 
                "'rescode':0" in response.text.replace(' ', '') or
                'rescode=0,' in response.text):
                message_sent_success = True
                return response.text
            else:
                # 提取错误信息
                error_msg = f"API错误: {response.text}"
                logger.error(f"发送群组消息失败，API返回: {response.text}")
        else:
            logger.error(f"发送群组消息失败，状态码: {response.status_code}")
            error_msg = f"Error: HTTP {response.status_code}"
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"发送群组消息失败: {str(e)}，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
        # 记录详细的错误信息
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
    
    # 如果消息发送失败，尝试通过邮件发送
    if not message_sent_success:
        # 如果没有提供备用邮箱，使用默认邮箱
        emails_to_use = fallback_emails if fallback_emails else [DEFAULT_EMAIL_ACCOUNT]
        
        logger.info(f"尝试通过邮件发送群组消息至: {emails_to_use}")
        
        # 构建邮件主题
        subject = f"群组通知消息 - 原群组ID: {room_id}"
        
        # 发送邮件
        email_result = send_email(subject, message, emails_to_use)
        
        if email_result:
            logger.info("通过邮件成功发送群组消息")
            return "Group message sent via email"
        else:
            logger.error("通过邮件发送群组消息也失败")
    
    # 如果所有方式都失败，返回错误信息
    return error_msg

# 发送邮件到qq邮箱
def send_email(subject, body, to_email):
    """
    发送邮件
    :param subject: 邮件主题
    :param body: 邮件内容
    :param to_email: 收件人邮箱或邮箱列表
    :return: 成功返回True，失败返回False
    """
    # 使用模块级配置信息
    smtp_server = DEFAULT_SMTP_SERVER
    smtp_port = DEFAULT_SMTP_PORT
    username = DEFAULT_EMAIL_ACCOUNT
    password = DEFAULT_EMAIL_PASSWORD

    # 确保to_email是列表
    if isinstance(to_email, str):
        to_email = [to_email]

    # 创建邮件内容
    msg = MIMEText(body, 'plain', 'utf-8')
    msg['Subject'] = subject
    msg['From'] = username
    msg['To'] = ','.join(to_email)

    try:
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(username, password)
        server.sendmail(username, to_email, msg.as_string())
        logger.info("邮件发送成功")
        return True
    except Exception as e:
        logger.error(f"邮件发送失败：{e}")
        return False
    finally:
        server.quit()

def send_individual_kingkong_message(
    message, 
    receivers=['cuijie12'], 
    timeout=10, 
    add_host_info=True, 
    fallback_emails=None
):
    """
    发送 kingkong 类型的个人消息，如果失败则尝试发送邮件
    :param message: 消息内容
    :param receivers: 接收者MIS号列表
    :param timeout: 请求超时时间（秒）
    :param add_host_info: 是否添加主机信息，默认为True
    :param fallback_emails: 备用邮箱列表，当DX消息发送失败时使用，默认为None（会使用默认QQ邮箱）
    :return: 响应结果
    """
    start_time = time.time()
    base_url = "http://qaassist.sankuai.com/compass/api/dx"
    url = f"{base_url}/send/individual-kingkong"
    
    if add_host_info:
        message = add_host_info_to_message(message)
    
    params = {
        'message': message,
        'receivers': receivers
    }
    
    logger.info(f"正在发送 kingkong 个人消息到 {url}")
    logger.info(f"接收者: {receivers}")
    logger.info(f"消息内容: {message[:100]}..." if len(message) > 100 else f"消息内容: {message}")
    logger.info(f"请求参数: {params}")
    
    message_sent_success = False
    
    try:
        response = requests.post(url, params=params, timeout=timeout)
        logger.info(f"发送 kingkong 个人消息响应状态码: {response.status_code}")
        logger.info(f"发送 kingkong 个人消息响应内容: {response.text}")
        elapsed_time = time.time() - start_time
        logger.info(f"发送消息耗时: {elapsed_time:.2f}秒")
        if response.status_code == 200:
            if ('rescode=0' in response.text or 
                '"rescode":0' in response.text.replace(' ', '') or 
                "'rescode':0" in response.text.replace(' ', '') or
                'rescode=0,' in response.text):
                message_sent_success = True
                return response.text
            else:
                error_msg = f"API错误: {response.text}"
                logger.error(f"发送 kingkong 个人消息失败，API返回: {response.text}")
        else:
            logger.error(f"发送 kingkong 个人消息失败，状态码: {response.status_code}")
            error_msg = f"Error: HTTP {response.status_code}"
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - start_time
        error_msg = f"发送 kingkong 个人消息超时，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
    except requests.exceptions.ConnectionError:
        elapsed_time = time.time() - start_time
        error_msg = f"发送 kingkong 个人消息连接错误，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"发送 kingkong 个人消息失败: {str(e)}，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
    
    if not message_sent_success:
        emails_to_use = fallback_emails if fallback_emails else [DEFAULT_EMAIL_ACCOUNT]
        logger.info(f"尝试通过邮件发送 kingkong 消息至: {emails_to_use}")
        subject = f"Kingkong通知消息 - 原接收者: {', '.join(receivers)}"
        email_result = send_email(subject, message, emails_to_use)
        if email_result:
            logger.info("通过邮件成功发送 kingkong 消息")
            return "Kingkong message sent via email"
        else:
            logger.error("通过邮件发送 kingkong 消息也失败")
    return error_msg

def send_group_kingkong_message(
    message, 
    room_id=***********, 
    add_host_info=True, 
    fallback_emails=None
):
    """
    发送 kingkong 类型的群组消息，如果失败则尝试发送邮件
    :param message: 消息内容
    :param room_id: 群组ID
    :param add_host_info: 是否添加主机信息，默认为True
    :param fallback_emails: 备用邮箱列表，当DX消息发送失败时使用，默认为None（会使用默认QQ邮箱）
    :return: 响应结果
    """
    start_time = time.time()
    base_url = "http://qaassist.sankuai.com/compass/api/dx"
    url = f"{base_url}/send/group-kingkong"
    
    if add_host_info:
        message = add_host_info_to_message(message)
    
    params = {
        'message': message,
        'roomId': room_id
    }
    
    logger.info(f"正在发送 kingkong 群组消息到 {url}")
    logger.info(f"群组ID: {room_id}")
    logger.info(f"消息内容: {message[:100]}..." if len(message) > 100 else f"消息内容: {message}")
    logger.info(f"请求参数: {params}")
    
    message_sent_success = False
    
    try:
        response = requests.post(url, params=params)
        logger.info(f"发送 kingkong 群组消息响应状态码: {response.status_code}")
        logger.info(f"发送 kingkong 群组消息响应内容: {response.text}")
        elapsed_time = time.time() - start_time
        logger.info(f"发送消息耗时: {elapsed_time:.2f}秒")
        if response.status_code == 200:
            if ('rescode=0' in response.text or 
                '"rescode":0' in response.text.replace(' ', '') or 
                "'rescode':0" in response.text.replace(' ', '') or
                'rescode=0,' in response.text):
                message_sent_success = True
                return response.text
            else:
                error_msg = f"API错误: {response.text}"
                logger.error(f"发送 kingkong 群组消息失败，API返回: {response.text}")
        else:
            logger.error(f"发送 kingkong 群组消息失败，状态码: {response.status_code}")
            error_msg = f"Error: HTTP {response.status_code}"
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"发送 kingkong 群组消息失败: {str(e)}，已耗时 {elapsed_time:.2f}秒"
        logger.error(error_msg)
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
    
    if not message_sent_success:
        emails_to_use = fallback_emails if fallback_emails else [DEFAULT_EMAIL_ACCOUNT]
        logger.info(f"尝试通过邮件发送 kingkong 群组消息至: {emails_to_use}")
        subject = f"Kingkong群组通知消息 - 原群组ID: {room_id}"
        email_result = send_email(subject, message, emails_to_use)
        if email_result:
            logger.info("通过邮件成功发送 kingkong 群组消息")
            return "Kingkong group message sent via email"
        else:
            logger.error("通过邮件发送 kingkong 群组消息也失败")
    return error_msg


if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    # # 测试发送个人消息
    # receivers = ['cuijie12']  # 替换为实际的 MIS 号
    # message = "你好，这是一条测试消息"
    # logger.info("开始测试发送个人消息...")
    # result = send_individual_message(message, receivers)
    # logger.info(f"个人消息发送结果: {result}")

    # # 测试发送群组消息
    # room_id = ***********  # 替换为实际的群组ID
    # group_message = "大家好，这是一条群组测试消息"
    # logger.info("开始测试发送群组消息...")
    # result = send_group_message(group_message, room_id)
    # logger.info(f"群组消息发送结果: {result}")

    # # 测试发送邮件
    # subject = "测试邮件"
    # body = "这是一封来自 QQ 邮箱的测试邮件。"
    # to_email = '<EMAIL>'
    # logger.info("开始测试发送邮件...")
    # send_email(subject, body, to_email)
    
    # 测试1: 发送给一个存在的用户（应该成功发送）
    receivers = ['cuijie12']  # 这似乎是一个存在的用户
    group_id = ***********
    message = "这是一条测试消息，应该成功发送"
    
    logger.info("测试1: 开始测试发送个人消息(给存在的用户)...")
    result = send_group_kingkong_message(message, group_id)
    logger.info(f"消息发送结果: {result}")
