#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备滑动工具，适用于 iOS 和 Android。

提供 `slide_android` 和 `slide_ios` 函数供 agent 使用。
"""

import subprocess
import os
import sys
import json
from typing import Tuple, Optional

# 导入当前项目的日志管理
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager

# 导入设备状态管理工具
from tools._device_status_manage_tools import get_device_status

# 导入screenshot_tools中的execute_adb_command函数
from tools.screenshot_tools import execute_adb_command


# --- Agent 使用的滑动函数 --- #

def slide_android(udid: str, from_x: float, from_y: float, to_x: float, to_y: float, duration: float = 0.5) -> bool:
    """
    在指定的 Android 设备上执行滑动操作，坐标为屏幕比例（0~1）。
    
    参数:
        udid: 设备序列号 (UDID)。
        from_x: 起始点的 x 比例（0~1）。
        from_y: 起始点的 y 比例（0~1）。
        to_x: 结束点的 x 比例（0~1）。
        to_y: 结束点的 y 比例（0~1）。
        duration: 滑动持续时间，单位为秒，默认 0.5 秒。
    
    返回:
        bool: 如果滑动命令可能成功，则为 True，否则为 False。
    """
    try:
        # 参数验证和类型转换
        try:
            from_x = float(from_x)
            from_y = float(from_y)
            to_x = float(to_x)
            to_y = float(to_y)
            duration = float(duration)
        except (ValueError, TypeError) as e:
            get_current_task_log_manager().error_tools(f"Android滑动参数类型转换失败，参数: from_x={from_x}, from_y={from_y}, to_x={to_x}, to_y={to_y}, duration={duration}, 错误: {e}", "slide_android")
            return False
        
        # 参数范围验证
        if not (0 <= from_x <= 1 and 0 <= from_y <= 1 and 0 <= to_x <= 1 and 0 <= to_y <= 1):
            get_current_task_log_manager().error_tools(f"Android滑动坐标参数超出范围[0,1]，参数: from_x={from_x}, from_y={from_y}, to_x={to_x}, to_y={to_y}", "slide_android")
            return False
        
        # 获取屏幕分辨率
        width = height = None
        from tools.llm_base_tools import get_test_driver
        driver = get_test_driver(udid)
        if driver:
            try:
                size = driver.get_window_size()
                get_current_task_log_manager().info_tools(f"设备 {udid} 获取到的屏幕尺寸数据: {size}", "slide_android")
                
                # 验证屏幕尺寸数据
                if not isinstance(size, dict) or 'width' not in size or 'height' not in size:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 屏幕尺寸数据格式错误: {size}", "slide_android")
                    raise Exception("屏幕尺寸数据格式错误")
                
                width, height = size["width"], size["height"]
                
                # 验证尺寸数据类型和范围
                try:
                    width = int(width)
                    height = int(height)
                except (ValueError, TypeError) as e:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 屏幕尺寸数据类型转换失败: width={width}, height={height}, 错误: {e}", "slide_android")
                    raise Exception("屏幕尺寸数据类型错误")
                
                if width <= 0 or height <= 0:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 屏幕尺寸数据无效: width={width}, height={height}", "slide_android")
                    raise Exception("屏幕尺寸数据无效")
                    
            except Exception as e:
                get_current_task_log_manager().warning_tools(f"从driver获取设备 {udid} 屏幕分辨率失败: {e}", "slide_android")
                width = height = None
        
        if width is None or height is None:
            # 兜底方案，常见分辨率
            width, height = 1080, 1920
            get_current_task_log_manager().warning_tools(f"无法获取设备 {udid} 的屏幕分辨率，使用默认值 {width}x{height}", "slide_android")
        
        # 计算实际像素坐标
        from_x_int = int(from_x * width)
        from_y_int = int(from_y * height)
        to_x_int = int(to_x * width)
        to_y_int = int(to_y * height)
        duration_ms = int(float(duration) * 1000)
        if duration_ms < 10:
            get_current_task_log_manager().warning_tools(f"Android 滑动时间过短 ({duration_ms}ms)，已调整为 10ms", "slide_android")
            duration_ms = 10
        
        get_current_task_log_manager().info_tools(f"设备 {udid} 坐标计算: ({from_x}, {from_y}) -> ({from_x_int}, {from_y_int}), ({to_x}, {to_y}) -> ({to_x_int}, {to_y_int})", "slide_android")
        get_current_task_log_manager().info_tools(f"尝试在 Android 设备 {udid} 上滑动：从 [{from_x_int}, {from_y_int}] 到 [{to_x_int}, {to_y_int}], 持续 {duration_ms}ms (比例参数)", "slide_android")
        swipe_command = f"shell input swipe {from_x_int} {from_y_int} {to_x_int} {to_y_int} {duration_ms}"
        stdout, stderr = execute_adb_command(udid, swipe_command)
        if stderr and "error" in stderr.lower():
            error_msg = f"Android 滑动失败。Stderr: {stderr}"
            get_current_task_log_manager().error_tools(f"在 {udid} 上的 {error_msg}", "slide_android")
            return False
        elif stderr:
            get_current_task_log_manager().warning_tools(f"在 {udid} 上的 Android 滑动产生了非严重 stderr: {stderr}", "slide_android")
        
        get_current_task_log_manager().info_tools(f"在 {udid} 上的 Android 滑动操作可能已成功。", "slide_android")
        
        # 记录滑动操作到日志
        get_current_task_log_manager().info_tools(f"Android设备 {udid} 滑动操作完成", "slide_android")
        
        return True
    except Exception as e:
        error_msg = f"滑动时发生异常: 从 [{from_x}, {from_y}] 到 [{to_x}, {to_y}], 错误: {e}"
        get_current_task_log_manager().error_tools(f"在 {udid} 上{error_msg}", "slide_android")
        # 记录滑动失败到日志
        get_current_task_log_manager().error_tools(f"Android设备 {udid} 滑动操作失败", "slide_android")
        return False

def slide_ios(udid: str, from_x: float, from_y: float, to_x: float, to_y: float, duration: Optional[float] = None) -> bool:
    """
    使用 Appium driver 优先滑动 iOS 设备，失败时降级 idevicedebug。所有坐标参数为屏幕比例（0~1）。
    """
    if duration is None:
        duration = 0.5
    
    try:
        # 参数验证和类型转换
        try:
            from_x = float(from_x)
            from_y = float(from_y)
            to_x = float(to_x)
            to_y = float(to_y)
            duration = float(duration)
        except (ValueError, TypeError) as e:
            get_current_task_log_manager().error_tools(f"iOS滑动参数类型转换失败，参数: from_x={from_x}, from_y={from_y}, to_x={to_x}, to_y={to_y}, duration={duration}, 错误: {e}", "slide_ios")
            return False
        
        # 参数范围验证
        if not (0 <= from_x <= 1 and 0 <= from_y <= 1 and 0 <= to_x <= 1 and 0 <= to_y <= 1):
            get_current_task_log_manager().error_tools(f"iOS滑动坐标参数超出范围[0,1]，参数: from_x={from_x}, from_y={from_y}, to_x={to_x}, to_y={to_y}", "slide_ios")
            return False
        
        # 获取测试driver
        from tools.llm_base_tools import get_test_driver
        driver = get_test_driver(udid)
        width = height = None
        
        # 在使用Appium driver前检查WDA健康状态
        if driver:
            try:
                from tools._device_status_manage_tools import get_device_status
                device_status = get_device_status(udid)
                wda_forward_port = device_status.get('wda_forward_port') if device_status else None
                
                if wda_forward_port:
                    get_current_task_log_manager().info_tools(f"设备 {udid} WDA转发端口：{wda_forward_port}", "slide_ios")
            except Exception as e:
                get_current_task_log_manager().warning_tools(f"WDA检查失败: {e}", "slide_ios")
        
        # 优先尝试用 Appium driver
        if driver:
            try:
                size = driver.get_window_size()
                get_current_task_log_manager().info_tools(f"设备 {udid} 获取到的屏幕尺寸数据: {size}", "slide_ios")
                
                # 验证屏幕尺寸数据
                if not isinstance(size, dict) or 'width' not in size or 'height' not in size:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 屏幕尺寸数据格式错误: {size}", "slide_ios")
                    raise Exception("屏幕尺寸数据格式错误")
                
                width, height = size["width"], size["height"]
                
                # 验证尺寸数据类型和范围
                try:
                    width = int(width)
                    height = int(height)
                except (ValueError, TypeError) as e:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 屏幕尺寸数据类型转换失败: width={width}, height={height}, 错误: {e}", "slide_ios")
                    raise Exception("屏幕尺寸数据类型错误")
                
                if width <= 0 or height <= 0:
                    get_current_task_log_manager().error_tools(f"设备 {udid} 屏幕尺寸数据无效: width={width}, height={height}", "slide_ios")
                    raise Exception("屏幕尺寸数据无效")
                
                # 计算物理坐标
                from_x_px = int(from_x * width)
                from_y_px = int(from_y * height)
                to_x_px = int(to_x * width)
                to_y_px = int(to_y * height)
                duration_sec = float(duration)
                
                get_current_task_log_manager().info_tools(f"设备 {udid} 坐标计算: ({from_x}, {from_y}) -> ({from_x_px}, {from_y_px}), ({to_x}, {to_y}) -> ({to_x_px}, {to_y_px})", "slide_ios")
                
                # 确保duration不会太短
                if duration_sec < 0.01:
                    get_current_task_log_manager().warning_tools(f"滑动时间过短 ({duration_sec}秒)，已调整为0.01秒", "slide_ios")
                    duration_sec = 0.01
                
                get_current_task_log_manager().info_tools(f"iOS滑动: 从[{from_x_px}, {from_y_px}]到[{to_x_px}, {to_y_px}], 持续时间: {duration_sec}秒", "slide_ios")
                
                driver.execute_script('mobile: dragFromToForDuration', {
                    'fromX': from_x_px,
                    'fromY': from_y_px,
                    'toX': to_x_px,
                    'toY': to_y_px,
                    'duration': duration_sec
                })
                get_current_task_log_manager().info_tools(f"iOS滑动成功: 从[{from_x_px}, {from_y_px}]到[{to_x_px}, {to_y_px}], 持续时间: {duration_sec}秒", "slide_ios")
                
                # 记录滑动操作到日志
                get_current_task_log_manager().info_tools(f"iOS设备 {udid} 滑动操作完成", "slide_ios")
                
                return True
            except Exception as e:
                get_current_task_log_manager().warning_tools(f"通过 Appium driver 滑动 iOS 设备 {udid} 失败，降级 idevicedebug。错误: {e}", "slide_ios")
        
        # --- 使用 idevicedebug --- #
        # 兜底获取分辨率
        if width is None or height is None:
            # 兜底方案，常见分辨率
            width, height = 1170, 2532
            get_current_task_log_manager().warning_tools(f"无法获取设备 {udid} 的屏幕分辨率，使用默认值 {width}x{height}", "slide_ios")
        
        from_x_px = int(from_x * width)
        from_y_px = int(from_y * height)
        to_x_px = int(to_x * width)
        to_y_px = int(to_y * height)
        duration_sec = float(duration)
        if duration_sec < 0.01:
            get_current_task_log_manager().warning_tools(f"iOS 滑动时间过短 ({duration_sec}秒)，已调整为 0.01 秒", "slide_ios")
            duration_sec = 0.01
        
        get_current_task_log_manager().info_tools(f"尝试在 iOS 设备 {udid} 上滑动：像素坐标从 [{from_x_px}, {from_y_px}] 到 [{to_x_px}, {to_y_px}], 持续 {duration_sec}s (比例参数)", "slide_ios")
        json_param = json.dumps({
            "fromX": from_x_px,
            "fromY": from_y_px,
            "toX": to_x_px,
            "toY": to_y_px,
            "duration": duration_sec
        })
        command = f'idevicedebug -u {udid} run com.facebook.WebDriverAgentRunner.xctrunner "mobile: dragFromToForDuration" "{json_param}"'
        try:
            process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate(timeout=duration_sec + 15)
            stdout_decoded = stdout.decode('utf-8', errors='ignore').strip()
            stderr_decoded = stderr.decode('utf-8', errors='ignore').strip()
            if stderr_decoded and ("error" in stderr_decoded.lower() or "failed" in stderr_decoded.lower()):
                error_msg = f"iOS 滑动 (idevicedebug) 失败。Stderr: {stderr_decoded}"
                get_current_task_log_manager().error_tools(f"在 {udid} 上的 {error_msg}", "slide_ios")
                return False
            elif stderr_decoded:
                get_current_task_log_manager().warning_tools(f"在 {udid} 上的 iOS 滑动 (idevicedebug) 产生非严重 stderr: {stderr_decoded}", "slide_ios")
            if stdout_decoded:
                get_current_task_log_manager().info_tools(f"在 {udid} 上的 iOS 滑动 (idevicedebug) stdout: {stdout_decoded}", "slide_ios")
            get_current_task_log_manager().info_tools(f"在 {udid} 上的 iOS 滑动 (idevicedebug) 操作可能已成功。", "slide_ios")
            
            # 记录滑动操作到日志
            get_current_task_log_manager().info_tools(f"iOS设备 {udid} 滑动操作完成", "slide_ios")
            
            return True
        except subprocess.TimeoutExpired:
            error_msg = "iOS 滑动 (idevicedebug) 超时"
            get_current_task_log_manager().error_tools(f"在 {udid} 上的 {error_msg}。", "slide_ios")
            return False
        except FileNotFoundError:
            error_msg = "未找到 'idevicedebug' 命令"
            get_current_task_log_manager().error_tools(f"错误：{error_msg}。请确保已安装 libimobiledevice 并将其添加到 PATH 中。", "slide_ios")
            return False
        except Exception as idevicedebug_e:
            error_msg = f"执行 iOS 滑动 (idevicedebug) 时发生异常: {idevicedebug_e}"
            get_current_task_log_manager().error_tools(f"在 {udid} 上{error_msg}", "slide_ios")
            return False
    except Exception as outer_e:
        error_msg = f"滑动时发生外部异常: 从 [{from_x}, {from_y}] 到 [{to_x}, {to_y}], 错误: {outer_e}"
        get_current_task_log_manager().error_tools(f"在 {udid} 上{error_msg}", "slide_ios")
        # 记录滑动失败到日志
        get_current_task_log_manager().error_tools(f"iOS设备 {udid} 滑动操作失败", "slide_ios")
        return False


def slide(udid: str, from_x: float, from_y: float, to_x: float, to_y: float, duration: float = 0.5) -> bool:
    """
    统一的滑动接口，自动根据平台选择对应的滑动方法。
    
    Args:
        udid: 设备的序列号(UDID)
        from_x: 起始点的 x 坐标比例（0~1）
        from_y: 起始点的 y 坐标比例（0~1）
        to_x: 结束点的 x 坐标比例（0~1）
        to_y: 结束点的 y 坐标比例（0~1）
        duration: 滑动持续时间，单位为秒，默认 0.5 秒
    
    Returns:
        bool: 滑动操作是否成功
    
    Raises:
        ValueError: 如果设备未连接或平台不支持
        Exception: 如果滑动过程发生错误
    """
    get_current_task_log_manager().info_tools(f"开始对设备 {udid} 进行滑动操作，从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})，持续 {duration}秒", "slide")
    
    # 从设备状态获取平台信息
    device_status = get_device_status(udid)
    if not device_status:
        raise ValueError(f"设备 {udid} 状态文件不存在")
    
    platform = device_status.get('platform', '').lower()
    if not platform:
        raise ValueError(f"设备 {udid} 状态文件中未找到平台信息")
    
    get_current_task_log_manager().info_tools(f"设备 {udid} 平台: {platform}", "slide")
    
    if platform == 'android':
        get_current_task_log_manager().info_tools(f"调用 slide_android，参数: udid={udid}, from_x={from_x}, from_y={from_y}, to_x={to_x}, to_y={to_y}, duration={duration}", "slide")
        return slide_android(udid, from_x, from_y, to_x, to_y, duration)
    elif platform == 'ios':
        get_current_task_log_manager().info_tools(f"调用 slide_ios，参数: udid={udid}, from_x={from_x}, from_y={from_y}, to_x={to_x}, to_y={to_y}, duration={duration}", "slide")
        return slide_ios(udid, from_x, from_y, to_x, to_y, duration)
    else:
        raise ValueError(f"不支持的平台类型: {platform}")


if __name__ == "__main__":
    # 示例用法
    print("测试滑动工具...")
    
    try:
        # 测试滑动操作
        test_udid = "test_device_id"
        print(f"测试设备 {test_udid} 从 (0.2, 0.5) 滑动到 (0.8, 0.5)...")
        success = slide(test_udid, 0.2, 0.5, 0.8, 0.5, 1.0)
        print(f"滑动成功: {success}")
            
    except Exception as e:
        print(f"滑动测试失败: {e}") 