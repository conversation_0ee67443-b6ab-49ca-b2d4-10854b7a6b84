#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备点击工具，适用于 iOS 和 Android。

提供 `tap_android` 和 `tap_ios` 函数供 agent 使用。
"""

import subprocess
import os
import sys
from typing import Tuple

# 导入当前项目的日志管理
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager

# 导入设备状态管理工具
from tools._device_status_manage_tools import get_device_status

# 导入screenshot_tools中的execute_adb_command函数
from tools.screenshot_tools import execute_adb_command

# --- Agent 使用的点击函数 --- #

def tap_android(udid: str, x: int, y: int) -> bool:
    """
    在指定的 Android 设备上执行点击操作。

    参数:
        udid: 设备序列号 (UDID)。
        x: 点击位置的 x 坐标。
        y: 点击位置的 y 坐标。

    返回:
        bool: 如果点击命令可能成功，则为 True，否则为 False。
    """
    try:
        # 确保坐标是整数
        x_int, y_int = int(float(x)), int(float(y))
        get_current_task_log_manager().info_tools(f"尝试点击 Android 设备 {udid} 的坐标 [{x_int}, {y_int}]", "tap_android")

        # 执行点击命令
        tap_command = f"shell input tap {x_int} {y_int}"
        stdout, stderr = execute_adb_command(udid, tap_command)

        # 检查 stderr 中的错误。成功通常不产生 stderr。
        if stderr and "error" in stderr.lower(): # 基本检查常见的错误指示符
            error_msg = f"Android 点击失败。Stderr: {stderr}"
            get_current_task_log_manager().error_tools(f"在 {udid} 上的 {error_msg}", "tap_android")
            return False
        elif stderr: # 记录非严重 stderr
             get_current_task_log_manager().warning_tools(f"在 {udid} 上的 Android 点击产生了非严重 stderr: {stderr}", "tap_android")

        get_current_task_log_manager().info_tools(f"在 {udid} 上的 Android 点击坐标 [{x_int}, {y_int}] 可能已成功。", "tap_android")
        
        # 记录点击操作到日志
        get_current_task_log_manager().info_tools(f"Android设备 {udid} 点击操作完成", "tap_android")
        
        return True # 如果 stderr 中没有严重错误，则假定成功

    except Exception as e:
        error_msg = f"点击坐标 [{x}, {y}] 时发生异常: {e}"
        get_current_task_log_manager().error_tools(f"在 {udid} 上{error_msg}", "tap_android")
        # 记录点击失败到日志
        get_current_task_log_manager().error_tools(f"Android设备 {udid} 点击操作失败", "tap_android")
        return False

def tap_ios(udid: str, x: int, y: int) -> bool:
    """
    使用 Appium driver 优先点击 iOS 设备，失败时降级 idevicedebug。
    
    注意：传入的坐标应该已经在 OCR 阶段进行了必要的换算，这里直接使用。
    
    参数:
        udid: iOS 设备的序列号
        x: 点击位置的 x 坐标（已换算）
        y: 点击位置的 y 坐标（已换算）
    
    返回:
        bool: 如果点击命令可能成功，则为 True，否则为 False。
    """
    try:
        # 确保坐标是整数
        tap_x, tap_y = int(float(x)), int(float(y))
        
        get_current_task_log_manager().info_tools(f"尝试点击 iOS 设备 {udid} 的坐标 [{tap_x}, {tap_y}]", "tap_ios")
        
        # 获取测试driver
        from tools.llm_base_tools import get_test_driver
        driver = get_test_driver(udid)
        
        # 获取设备状态信息（用于WDA端口信息记录）
        device_status = get_device_status(udid)
        
        # 在使用Appium driver前记录WDA端口信息（健康检查功能暂时禁用）
        if device_status:
            wda_forward_port = device_status.get('wda_forward_port')
            if driver and wda_forward_port:
                get_current_task_log_manager().info_tools(f"设备 {udid} WDA转发端口：{wda_forward_port}", "tap_ios")
        
        # 优先尝试用 Appium driver
        if driver:
            # 方法1: 尝试使用 mobile: tap 扩展 (Appium 2.0+ 应该支持)
            try:
                driver.execute_script("mobile: tap", {"x": tap_x, "y": tap_y})
                get_current_task_log_manager().info_tools(f"通过 Appium driver (mobile: tap) 点击 iOS 设备 {udid} 的坐标 [{tap_x}, {tap_y}] 成功。", "tap_ios")
                
                # 记录点击操作到日志
                get_current_task_log_manager().info_tools(f"iOS设备 {udid} 点击操作完成", "tap_ios")
                
                return True
            except Exception as e1:
                get_current_task_log_manager().warning_tools(f"mobile: tap 方法失败，尝试 W3C Actions API。错误: {e1}", "tap_ios")
                
                # 方法2: 使用 W3C Actions API 作为备选
                try:
                    from selenium.webdriver.common.actions.action_builder import ActionBuilder
                    from selenium.webdriver.common.actions.pointer_input import PointerInput
                    from selenium.webdriver.common.actions import interaction
                    
                    actions = ActionBuilder(driver)
                    pointer = PointerInput(interaction.POINTER_TOUCH, "finger")
                    actions.add_pointer_input(pointer)
                    actions.pointer_action.move_to_location(tap_x, tap_y)
                    actions.pointer_action.pointer_down()
                    actions.pointer_action.pointer_up()
                    actions.perform()
                    
                    get_current_task_log_manager().info_tools(f"通过 Appium driver (W3C Actions) 点击 iOS 设备 {udid} 的坐标 [{tap_x}, {tap_y}] 成功。", "tap_ios")
                    
                    # 记录点击操作到日志
                    get_current_task_log_manager().info_tools(f"iOS设备 {udid} 点击操作完成", "tap_ios")
                    
                    return True
                except Exception as e2:
                    get_current_task_log_manager().warning_tools(f"通过 Appium driver 点击失败 (mobile: tap 和 W3C Actions 都失败)，降级 idevicedebug。错误: mobile: tap -> {e1}, W3C Actions -> {e2}", "tap_ios")
        
        # --- 使用 idevicedebug --- #
        get_current_task_log_manager().info_tools(f"尝试使用 idevicedebug 点击 iOS 设备 {udid} 的坐标 [{tap_x}, {tap_y}]。", "tap_ios")
        json_param = f'{{"x": {tap_x}, "y": {tap_y}}}'
        command = f'idevicedebug -u {udid} run com.facebook.WebDriverAgentRunner.xctrunner "mobile: tap" "{json_param}"'
        try:
            process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate(timeout=15) # 添加超时
            stdout_decoded = stdout.decode('utf-8', errors='ignore').strip()
            stderr_decoded = stderr.decode('utf-8', errors='ignore').strip()
            if stderr_decoded and ("error" in stderr_decoded.lower() or "failed" in stderr_decoded.lower()):
                error_msg = f"iOS 点击 (idevicedebug) 失败。Stderr: {stderr_decoded}"
                get_current_task_log_manager().error_tools(f"在 {udid} 上的 {error_msg}", "tap_ios")
                return False
            elif stderr_decoded:
                 get_current_task_log_manager().warning_tools(f"在 {udid} 上的 iOS 点击 (idevicedebug) 产生非严重 stderr: {stderr_decoded}", "tap_ios")
            if stdout_decoded:
                get_current_task_log_manager().info_tools(f"在 {udid} 上的 iOS 点击 (idevicedebug) stdout: {stdout_decoded}", "tap_ios")
            get_current_task_log_manager().info_tools(f"在 {udid} 上的 iOS 点击 (idevicedebug) 的坐标 [{tap_x}, {tap_y}] 可能已成功。", "tap_ios")
            
            # 记录点击操作到日志
            get_current_task_log_manager().info_tools(f"iOS设备 {udid} 点击操作完成", "tap_ios")
            
            return True
        except subprocess.TimeoutExpired:
            error_msg = "iOS 点击 (idevicedebug) 超时"
            get_current_task_log_manager().error_tools(f"在 {udid} 上的 {error_msg}。", "tap_ios")
            return False
        except FileNotFoundError:
             error_msg = "未找到 'idevicedebug' 命令"
             get_current_task_log_manager().error_tools(f"错误：{error_msg}。请确保已安装 libimobiledevice 并将其添加到 PATH 中。", "tap_ios")
             return False
        except Exception as idevicedebug_e:
            error_msg = f"执行 iOS 点击 (idevicedebug) 时发生异常: {idevicedebug_e}"
            get_current_task_log_manager().error_tools(f"在 {udid} 上{error_msg}", "tap_ios")
            return False
    except Exception as outer_e:
        error_msg = f"点击坐标 [{x}, {y}] 时发生外部异常: {outer_e}"
        get_current_task_log_manager().error_tools(f"在 {udid} 上{error_msg}", "tap_ios")
        # 记录点击失败到日志
        get_current_task_log_manager().error_tools(f"iOS设备 {udid} 点击操作失败", "tap_ios")
        return False

def tap(udid: str, x: int, y: int) -> bool:
    """
    统一的点击接口，自动根据平台选择对应的点击方法。
    
    Args:
        udid: 设备的序列号(UDID)
        x: 点击位置的 x 坐标
        y: 点击位置的 y 坐标
    
    Returns:
        bool: 点击操作是否成功
    
    Raises:
        ValueError: 如果设备未连接或平台不支持
        Exception: 如果点击过程发生错误
    """
    get_current_task_log_manager().info_tools(f"开始对设备 {udid} 进行点击操作，坐标: ({x}, {y})", "tap")
    
    # 从设备状态获取平台信息
    device_status = get_device_status(udid)
    if not device_status:
        raise ValueError(f"设备 {udid} 状态文件不存在")
    
    platform = device_status.get('platform', '').lower()
    if not platform:
        raise ValueError(f"设备 {udid} 状态文件中未找到平台信息")
    
    get_current_task_log_manager().info_tools(f"设备 {udid} 平台: {platform}", "tap")
    
    if platform == 'android':
        get_current_task_log_manager().info_tools(f"调用 tap_android，参数: udid={udid}, x={x}, y={y}", "tap")
        return tap_android(udid, x, y)
    elif platform == 'ios':
        get_current_task_log_manager().info_tools(f"调用 tap_ios，参数: udid={udid}, x={x}, y={y}", "tap")
        return tap_ios(udid, x, y)
    else:
        raise ValueError(f"不支持的平台类型: {platform}")


if __name__ == "__main__":
    # 示例用法
    print("测试点击工具...")
    
    try:
        # 测试点击操作
        test_udid = "test_device_id"
        print(f"测试设备 {test_udid} 在 (100, 200) 处点击...")
        success = tap(test_udid, 100, 200)
        print(f"点击成功: {success}")
            
    except Exception as e:
        print(f"点击测试失败: {e}") 