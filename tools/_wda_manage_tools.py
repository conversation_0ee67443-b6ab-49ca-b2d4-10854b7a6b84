import os
import time
import logging
import subprocess
import threading
import fcntl
from datetime import datetime
import requests
from tools._device_status_manage_tools import update_device_status

# 内部配置类，如果无法导入完整配置，将使用这些默认值
class Config:
    """配置类，管理所有常量"""
    
    # 路径相关常量
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
    LOG_ROOT = os.path.join(PROJECT_ROOT, "log")
    
    # 锁文件路径
    WDA_LOCK_FILE = os.path.join(PROJECT_ROOT, "wda_install.lock")
    
    # WDA相关常量
    WDA_RESTART_INTERVAL = 6 * 60 * 60  # 6小时，单位为秒
    WDA_KEEPALIVE_INTERVAL = 30  # WDA保活间隔，单位为秒
    WDA_WAIT_TIME = 30  # WDA服务完全启动等待时间（秒）
    WDA_PROCESS_TIMEOUT = 120
    WDA_STOP_WAIT_TIME = 30  # 停止WDA后等待时间（秒）
    WDA_PORT_STEP = 1  # WDA端口步长
    WDA_INITIAL_WAIT_TIME = 70  # WDA初始等待时间（秒）
    WDA_KILL_PROCESS_WAIT_TIME = 1  # 终止WDA进程后等待时间（秒）
    WDA_IPROXY_WAIT_TIME = 3  # 等待iproxy启动的时间（秒）
    WDA_IPROXY_TERMINATE_TIMEOUT = 5  # iproxy进程终止超时时间（秒）
    WDA_RESTART_RETRY_WAIT = 30  # WDA重启失败后重试等待时间（秒）
    WDA_MAX_RETRIES = 3  # WDA启动最大重试次数
    WDA_RESTART_MAX_RETRIES = 5  # WDA重启最大重试次数
    WDA_PORT_RETRY_MAX = 2  # WDA端口重试最大次数
    
    # 服务启动相关常量
    SERVICE_PROCESS_TIMEOUT = 10  # 服务进程通信超时时间（秒）

# 尝试导入实际的Config
try:
    from _config import Config as FullConfig
    # 更新本地Config类
    Config.SCRIPT_DIR = FullConfig.SCRIPT_DIR
    Config.PROJECT_ROOT = FullConfig.PROJECT_ROOT
    Config.LOG_ROOT = FullConfig.LOG_ROOT
    Config.WDA_LOCK_FILE = FullConfig.WDA_LOCK_FILE
    Config.WDA_RESTART_INTERVAL = FullConfig.WDA_RESTART_INTERVAL
    Config.WDA_WAIT_TIME = FullConfig.WDA_WAIT_TIME
    Config.WDA_PROCESS_TIMEOUT = FullConfig.WDA_PROCESS_TIMEOUT
    Config.WDA_STOP_WAIT_TIME = FullConfig.WDA_STOP_WAIT_TIME
    Config.WDA_PORT_STEP = FullConfig.WDA_PORT_STEP
    Config.WDA_INITIAL_WAIT_TIME = FullConfig.WDA_INITIAL_WAIT_TIME
    Config.WDA_KILL_PROCESS_WAIT_TIME = FullConfig.WDA_KILL_PROCESS_WAIT_TIME
    Config.WDA_IPROXY_WAIT_TIME = FullConfig.WDA_IPROXY_WAIT_TIME
    Config.WDA_IPROXY_TERMINATE_TIMEOUT = FullConfig.WDA_IPROXY_TERMINATE_TIMEOUT
    Config.WDA_RESTART_RETRY_WAIT = FullConfig.WDA_RESTART_RETRY_WAIT
    Config.WDA_MAX_RETRIES = FullConfig.WDA_MAX_RETRIES
    Config.WDA_RESTART_MAX_RETRIES = FullConfig.WDA_RESTART_MAX_RETRIES
    Config.SERVICE_PROCESS_TIMEOUT = FullConfig.SERVICE_PROCESS_TIMEOUT
except ImportError:
    # 如果导入失败，使用默认值
    pass

# 导入日志管理器
from tools._concurrent_log_manager import get_current_task_log_manager
try:  
    from log_manager import LogManager, global_logger
except ImportError:
    # 如果导入失败，创建一个简单的日志管理器
    class LogManager:
        def __init__(self, log_root=None):
            self.log_root = log_root or Config.LOG_ROOT
            self.wda_logs_dir = os.path.join(self.log_root, "wda_logs")
            self.ios_tool_logs_dir = os.path.join(self.log_root, "ios_tool_logs")
            # 确保日志目录存在
            os.makedirs(self.wda_logs_dir, exist_ok=True)
            os.makedirs(self.ios_tool_logs_dir, exist_ok=True)
        
        def create_wda_log_file(self, udid, device_name=None):
            current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            if device_name:
                # 替换设备名称中可能的非法字符
                safe_device_name = device_name.replace('/', '_').replace('\\', '_').replace(':', '_')
                safe_device_name = safe_device_name.replace('*', '_').replace('?', '_').replace('"', '_')
                safe_device_name = safe_device_name.replace('<', '_').replace('>', '_').replace('|', '_')
                log_file = os.path.join(self.wda_logs_dir, f"wda_{current_time}_{safe_device_name}.log")
            else:
                log_file = os.path.join(self.wda_logs_dir, f"wda_{udid}_{current_time}.log")

            if not os.path.exists(log_file):
                with open(log_file, "w") as f:
                    init_message = f"Log file created at {current_time}\n"
                    f.write(init_message)

            # 在创建新日志文件后，自动清理旧的WDA日志文件，保留最近10个
            self.cleanup_wda_logs(device_name=device_name, max_logs=10)

            return log_file
        
        def create_ios_tool_log_file(self, udid, device_name=None):
            """创建iOS工具日志文件"""
            current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            if device_name:
                # 替换设备名称中可能的非法字符
                safe_device_name = device_name.replace('/', '_').replace('\\', '_').replace(':', '_')
                safe_device_name = safe_device_name.replace('*', '_').replace('?', '_').replace('"', '_')
                safe_device_name = safe_device_name.replace('<', '_').replace('>', '_').replace('|', '_')
                log_file = os.path.join(self.ios_tool_logs_dir, f"ios_tool_{current_time}_{safe_device_name}.log")
            else:
                log_file = os.path.join(self.ios_tool_logs_dir, f"ios_tool_{udid}_{current_time}.log")

            if not os.path.exists(log_file):
                with open(log_file, "w") as f:
                    init_message = f"iOS Tool Log file created at {current_time}\n"
                    init_message += f"Device UDID: {udid}\n"
                    init_message += f"Device Name: {device_name or 'Unknown'}\n"
                    init_message += "=" * 50 + "\n\n"
                    f.write(init_message)

            # 在创建新日志文件后，自动清理旧的iOS工具日志文件，保留最近10个
            self.cleanup_ios_tool_logs(device_name=device_name, max_logs=10)

            return log_file

        def cleanup_wda_logs(self, device_name=None, max_logs=10):
            """清理WDA日志文件，保留最近的N个文件"""
            try:
                if not os.path.exists(self.wda_logs_dir):
                    return

                # 获取所有WDA日志文件
                wda_log_files = []
                for filename in os.listdir(self.wda_logs_dir):
                    if filename.startswith("wda_") and filename.endswith(".log"):
                        file_path = os.path.join(self.wda_logs_dir, filename)
                        if os.path.isfile(file_path):
                            # 如果指定了设备名称，则只清理该设备的日志
                            if device_name and device_name not in filename:
                                continue

                            # 获取文件修改时间
                            mtime = os.path.getmtime(file_path)
                            wda_log_files.append({
                                'path': file_path,
                                'filename': filename,
                                'mtime': mtime
                            })

                if len(wda_log_files) <= max_logs:
                    return  # 文件数量未超过限制，无需清理

                # 按修改时间排序，最新的在前
                wda_log_files.sort(key=lambda x: x['mtime'], reverse=True)

                # 删除超出限制的旧文件
                files_to_delete = wda_log_files[max_logs:]
                for file_info in files_to_delete:
                    try:
                        os.remove(file_info['path'])
                        print(f"已删除旧WDA日志文件: {file_info['filename']}")
                    except Exception as e:
                        print(f"删除WDA日志文件失败 {file_info['filename']}: {e}")

            except Exception as e:
                print(f"清理WDA日志时出错: {str(e)}")
        
        def cleanup_ios_tool_logs(self, device_name=None, max_logs=10):
            """清理iOS工具日志文件，保留最近的N个文件"""
            try:
                if not os.path.exists(self.ios_tool_logs_dir):
                    return

                # 获取所有iOS工具日志文件
                ios_tool_log_files = []
                for filename in os.listdir(self.ios_tool_logs_dir):
                    if filename.startswith("ios_tool_") and filename.endswith(".log"):
                        file_path = os.path.join(self.ios_tool_logs_dir, filename)
                        if os.path.isfile(file_path):
                            # 如果指定了设备名称，则只清理该设备的日志
                            if device_name and device_name not in filename:
                                continue

                            # 获取文件修改时间
                            mtime = os.path.getmtime(file_path)
                            ios_tool_log_files.append({
                                'path': file_path,
                                'filename': filename,
                                'mtime': mtime
                            })

                if len(ios_tool_log_files) <= max_logs:
                    return  # 文件数量未超过限制，无需清理

                # 按修改时间排序，最新的在前
                ios_tool_log_files.sort(key=lambda x: x['mtime'], reverse=True)

                # 删除超出限制的旧文件
                files_to_delete = ios_tool_log_files[max_logs:]
                for file_info in files_to_delete:
                    try:
                        os.remove(file_info['path'])
                        print(f"已删除旧iOS工具日志文件: {file_info['filename']}")
                    except Exception as e:
                        print(f"删除iOS工具日志文件失败 {file_info['filename']}: {e}")

            except Exception as e:
                print(f"清理iOS工具日志时出错: {str(e)}")
            
    # 创建全局日志记录器
    global_logger = logging.getLogger("wda_manager")
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")
    handler.setFormatter(formatter)
    global_logger.addHandler(handler)
    global_logger.setLevel(logging.INFO)

class WdaServiceManager:
    """WDA服务管理器，负责WDA服务的重启和状态检查"""
    
    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.lock_file = None
        self.log_manager = LogManager()  
    
    def get_latest_wda_version(self):
        """
        获取最新的 WebDriverAgent 版本号
        
        Returns:
            str: 版本号，如果检测失败则返回 None
        """
        try:
            import glob
            
            # 查找所有 WebDriverAgent 版本目录
            wda_pattern = os.path.join(Config.PROJECT_ROOT, "WebDriverAgent-*")
            wda_dirs = glob.glob(wda_pattern)
            
            if not wda_dirs:
                return None
            
            # 提取版本号并排序，选择最新版本
            versions = []
            for wda_dir in wda_dirs:
                basename = os.path.basename(wda_dir)
                if basename.startswith("WebDriverAgent-"):
                    version = basename.replace("WebDriverAgent-", "")
                    versions.append(version)
            
            if not versions:
                return None
            
            # 简单的版本号排序（按字典序排序）
            versions.sort()
            latest_version = versions[-1]
            
            return latest_version
            
        except Exception as e:
            return None

    def prepare_device_wda(self, device_logger, udid=None):
        """准备设备特定的 WDA 目录和配置
        
        Args:
            device_logger: 设备日志记录器
            udid: 设备 UDID，如果为 None，则准备所有设备的 WDA
        """
        try:
            prepare_script = os.path.join(Config.PROJECT_ROOT, "shell", "prepare_device_wda.sh")
            if not os.path.exists(prepare_script):
                device_logger.error(f"准备脚本不存在: {prepare_script}")
                return False
                
            # 确保脚本有执行权限
            os.chmod(prepare_script, 0o755)
            
            # 执行准备脚本
            if udid:
                device_logger.info(f"开始为设备 {udid} 准备特定的 WDA 目录和配置...")
            else:
                device_logger.info("开始为所有设备准备特定的 WDA 目录和配置...")
            
            cmd = [prepare_script]
            if udid:
                # 如果指定了 UDID，则只准备该设备的 WDA
                device_logger.info(f"为设备 {udid} 准备 WDA 目录和配置")
                cmd.append(udid)
            
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace'
            )
            
            if result.returncode == 0:
                if udid:
                    # 自动检测 WDA 版本并检查设备特定的 WDA 目录是否已创建
                    wda_version = self.get_latest_wda_version()
                    if wda_version:
                        device_wda_path = os.path.join(Config.PROJECT_ROOT, f"WDA-{udid}-{wda_version}")
                        if os.path.exists(device_wda_path) and os.path.exists(os.path.join(device_wda_path, "WebDriverAgent.xcodeproj")):
                            device_logger.info(f"设备 {udid} 的 WDA 目录已成功创建: {device_wda_path} (版本: {wda_version})")
                        else:
                            device_logger.error(f"设备 {udid} 的 WDA 目录未成功创建: {device_wda_path} (版本: {wda_version})")
                            return False
                    else:
                        device_logger.error(f"无法检测 WDA 版本，跳过目录检查")
                        return False
                
                device_logger.info("设备特定的 WDA 目录和配置准备完成")
                device_logger.info(result.stdout)
                return True
            else:
                device_logger.error(f"准备设备特定的 WDA 目录和配置失败: {result.stderr}")
                return False
                
        except Exception as e:
            device_logger.error(f"准备设备特定的 WDA 目录和配置时发生错误: {str(e)}")
            return False
        
    def acquire_wda_lock(self, device_logger):
        """获取WDA安装锁，确保同一时间只有一个设备在安装WDA
        
        Args:
            device_logger: 设备日志记录器
            
        Returns:
            file: 锁文件对象，用于后续释放锁
        """
        device_logger.info("尝试获取WDA安装锁...")
        lock_file = open(Config.WDA_LOCK_FILE, 'w+')
        try:
            fcntl.flock(lock_file, fcntl.LOCK_EX)
            device_logger.info("已获取WDA安装锁，可以开始安装WDA")
            return lock_file
        except Exception as e:
            device_logger.error(f"获取WDA安装锁失败: {e}")
            lock_file.close()
            return None

    def release_wda_lock(self, lock_file, device_logger):
        """释放WDA安装锁
        
        Args:
            lock_file: 锁文件对象
            device_logger: 设备日志记录器
        """
        if lock_file:
            try:
                fcntl.flock(lock_file, fcntl.LOCK_UN)
                lock_file.close()
                device_logger.info("已释放WDA安装锁")
            except Exception as e:
                device_logger.error(f"释放WDA安装锁失败: {e}")

    def _start_wda_process(self, udid: str, port, log_file: str, device_logger, ios_tool_log_file: str = None) -> bool:
        """启动WDA进程（新版本：WDA固定运行在设备8100端口）
        
        Args:
            udid: 设备UDID
            port: 端口参数（新版本中为None，保留参数以兼容调用）
            log_file: WDA日志文件路径
            device_logger: 设备日志记录器
            ios_tool_log_file: iOS工具日志文件路径
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 启动WDA的脚本路径
            wda_script = os.path.join(Config.PROJECT_ROOT, "shell", "start_wda.sh")
            
            device_logger.info(f"[WDA启动] 调用WDA启动脚本，设备 {udid}，WDA将运行在设备8100端口")
            
            # 新版本不传递端口参数，WDA固定运行在设备8100端口
            if ios_tool_log_file:
                cmd = [
                    "/bin/bash",
                    wda_script,
                    udid,
                    log_file,
                    ios_tool_log_file
                ]
            else:
                cmd = [
                    "/bin/bash",
                    wda_script,
                    udid,
                    log_file
                ]

            device_logger.info(f"[WDA启动] 启动 WDA (新版本): UDID={udid}, 设备WDA端口=8100")
            device_logger.info(f"WDA日志文件: {log_file}")
            if ios_tool_log_file:
                device_logger.info(f"iOS工具日志文件: {ios_tool_log_file}")

            with open(log_file, "a") as log:
                # 记录启动时间和命令
                log.write(f"\n\n=== 启动 WDA 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                log.write(f"命令: {' '.join(cmd)}\n")
                log.write(f"设备UDID: {udid}\n")
                log.write(f"macOS端口: {port}\n")
                log.write(f"设备端口: 8100 (固定)\n")
                if ios_tool_log_file:
                    log.write(f"iOS工具日志文件: {ios_tool_log_file}\n")
                log.write("\n")
                
                # 启动WDA脚本（后台运行）
                process = subprocess.Popen(cmd, stdout=log, stderr=log)
                device_logger.info(f"WDA脚本已启动，PID: {process.pid}")
                
                # 等待WDA服务启动（脚本包含编译、IPA生成、安装、ios runwda等步骤）
                # 新流程时间分解：
                # - xcodebuild编译: ~30秒
                # - IPA打包: ~5-10秒  
                # - ios install: ~15秒 (减少15秒)
                # - ios runwda启动: ~10-15秒
                # - 脚本内部等待: ~26秒
                # - 缓冲时间: ~15秒 (减少15秒)
                # 总计约100-120秒，使用120秒
                extended_wait_time = 120  # 优化后120秒
                device_logger.info(f"等待 WDA 服务完全启动")
                device_logger.info(f"流程包含: 编译(~30s) + 打包(~10s) + 安装(~15s) + 启动(~15s) + 缓冲(~15s)")
                device_logger.info(f"总等待时间: {extended_wait_time}秒")
                time.sleep(extended_wait_time)
                
                # 检查shell脚本进程状态
                shell_exit_code = process.poll()
                if shell_exit_code is None:
                    device_logger.info(f"Shell脚本仍在运行: UDID={udid}, PID={process.pid}")
                    
                    # 等待更长时间以确保WDA服务完全启动
                    device_logger.info(f"等待WDA服务完全启动...")
                    additional_wait = 10  # 额外等待10秒
                    for i in range(additional_wait):
                        if process.poll() is not None:
                            device_logger.info(f"Shell脚本在等待期间完成: 返回码={process.returncode}")
                            break
                        time.sleep(1)
                    
                    # 如果脚本仍在运行，等待其完成
                    if process.poll() is None:
                        device_logger.info(f"Shell脚本仍在运行，等待其完成...")
                        try:
                            process.wait(timeout=30)  # 最多再等30秒
                        except subprocess.TimeoutExpired:
                            device_logger.warning(f"Shell脚本超时，但继续检查WDA服务")
                elif shell_exit_code == 0:
                    device_logger.info(f"Shell脚本成功完成: UDID={udid}, 返回码={shell_exit_code}")
                else:
                    device_logger.error(f"Shell脚本异常退出: 返回码={shell_exit_code}")
                    return False
                
                # 无论shell脚本是否完成，都继续检查WDA服务
                device_logger.info(f"检查WDA服务和进程状态...")
                
                # 等待WDA服务完全启动
                device_logger.info(f"等待WDA服务完全启动...")
                time.sleep(5)
                
                # 监控独立日志文件
                self._monitor_shell_process_logs(udid, device_logger, log_file)
                
                # 检查shell脚本启动的进程状态
                process_status = self._check_shell_process_status(udid, device_logger, log_file)
                
                # 使用临时iproxy健康检查WDA服务
                device_logger.info(f"[WDA健康检查] 使用临时iproxy检查WDA服务状态，设备: {udid}")
                if self._check_wda_health_with_temp_iproxy(udid, device_logger):
                    device_logger.info(f"[WDA健康检查] WDA启动成功: UDID={udid}, WDA运行在设备8100端口")
                    if process_status:
                        device_logger.info(f"进程状态正常: WDA进程={process_status['wda_count']}")
                    return True
                else:
                    device_logger.warning(f"[WDA健康检查] WDA服务健康检查失败，分析原因...")
                    
                    # 分析进程状态
                    if process_status:
                        if process_status['wda_count'] == 0:
                            device_logger.error(f"WDA进程未运行，可能启动失败")
                        else:
                            device_logger.warning(f"WDA进程运行中但服务不可访问，可能需要等待更长时间启动")
                    
                    device_logger.warning(f"WDA安装后服务不可访问，可能需要等待更长时间启动")
                    # 再次监控日志以获取更多信息
                    self._monitor_shell_process_logs(udid, device_logger, log_file)
                    return False
                    
        except Exception as ex:
            device_logger.error(f"启动WDA时发生异常: {ex}")
            return False

    def start_wda_for_device(self, udid, device_logger, max_retries=None, device_name=None):
        """为指定设备启动WDA服务（新版本：WDA固定运行在设备8100端口）
        
        Args:
            udid: 设备UDID
            device_logger: 设备日志记录器
            max_retries: 最大重试次数
            device_name: 设备名称
            
        Returns:
            bool: WDA服务是否成功启动
        """
        if max_retries is None:
            max_retries = Config.WDA_MAX_RETRIES
        
        # 获取WDA安装锁
        lock_file = self.acquire_wda_lock(device_logger)
        if not lock_file:
            device_logger.error("获取WDA安装锁失败，无法启动WDA")
            return False
        
        try:
            device_logger.info(f"[WDA启动] 为设备 {device_name or udid} 启动WDA服务（固定设备8100端口）")
            
            attempts = 0
            while attempts < max_retries:
                device_logger.info(f"[WDA启动] 第 {attempts + 1}/{max_retries} 次尝试启动WDA")
                
                # 创建日志文件（WDA日志和iOS工具日志）
                log_file = self.log_manager.create_wda_log_file(udid, device_name)
                ios_tool_log_file = self.log_manager.create_ios_tool_log_file(udid, device_name)
                
                # 启动WDA（不传递端口参数，shell脚本会使用默认8100端口）
                success = self._start_wda_process(udid, None, log_file, device_logger, ios_tool_log_file)
                
                if success:
                    # 检查WDA服务状态（使用临时iproxy健康检查）
                    device_logger.info(f"[WDA启动] WDA进程启动成功，进行健康检查")
                    if self.check_wda_status(udid, device_logger, device_name=device_name):
                        device_logger.info(f"[WDA启动] 设备 {device_name or udid} WDA服务启动成功")
                        return True
                    else:
                        device_logger.warning(f"[WDA启动] 设备 {device_name or udid} WDA服务健康检查未通过")
                else:
                    device_logger.warning(f"[WDA启动] 设备 {device_name or udid} WDA进程启动失败")
                
                # 重试前等待
                attempts += 1
                if attempts < max_retries:
                    device_logger.info(f"[WDA启动] 等待 {Config.WDA_RESTART_RETRY_WAIT} 秒后重试...")
                    time.sleep(Config.WDA_RESTART_RETRY_WAIT)
            
            device_logger.error(f"[WDA启动] 设备 {device_name or udid} 在 {max_retries} 次尝试后，WDA启动失败")
            return False
        finally:
            # 无论成功与否，都释放WDA安装锁
            self.release_wda_lock(lock_file, device_logger)

    def check_wda_status(self, device_udid, device_logger, device_name=None):
        """
        检查 WDA 服务状态（使用临时iproxy进行健康检查）
        
        新的健康检查流程：
        1. 查找可用的临时macOS端口
        2. 使用iproxy建立临时转发：设备8100 -> macOS临时端口
        3. 向WDA发送/status请求，检查响应
        4. 立即关闭iproxy进程
        5. 返回健康检查结果

        Args:
            device_udid (str): 设备UDID
            device_logger (logging.Logger): 设备日志记录器
            device_name (str, optional): 设备名称，用于日志记录

        Returns:
            bool: WDA 服务是否健康
        """
        if not device_udid:
            device_logger.error("[WDA健康检查] 未提供设备UDID，无法进行WDA状态检查")
            return False
        
        device_logger.info(f"[WDA健康检查] 开始检查设备 {device_name or device_udid} 的WDA服务健康状态")
        
        return self._check_wda_health_with_temp_iproxy(device_udid, device_logger, device_name)

    def _test_wda_endpoint(self, port, device_logger):
        """
        测试指定端口的WDA服务是否可访问

        Args:
            port (int): 端口号
            device_logger (logging.Logger): 设备日志记录器

        Returns:
            bool: WDA服务是否可访问
        """
        try:
            status_url = f"http://127.0.0.1:{port}/status"
            device_logger.debug(f"测试WDA端点: {status_url}")

            # 发送HTTP请求，设置较短的超时时间
            response = requests.get(status_url, timeout=3)

            if response.status_code == 200:
                device_logger.debug(f"WDA端点 {port} 响应正常")
                return True
            else:
                device_logger.debug(f"WDA端点 {port} 响应异常，状态码: {response.status_code}")
                return False

        except Exception as e:
            device_logger.debug(f"测试WDA端点 {port} 时发生错误: {e}")
            return False
    
    def keep_wda_alive(self, wda_forward_port, device_logger):
        """
        保持WDA服务活跃，防止ios runwda因空闲而自动终止
        
        Args:
            wda_forward_port (int): WDA转发端口
            device_logger (logging.Logger): 设备日志记录器
            
        Returns:
            bool: 保活请求是否成功
        """
        try:
            status_url = f"http://127.0.0.1:{wda_forward_port}/status"
            device_logger.debug(f"WDA保活请求: {status_url}")
            
            # 发送status请求保持WDA活跃
            response = requests.get(status_url, timeout=5)
            
            if response.status_code == 200:
                device_logger.debug(f"WDA保活成功，端口 {wda_forward_port}")
                return True
            else:
                device_logger.warning(f"WDA保活失败，端口 {wda_forward_port}，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            device_logger.warning(f"WDA保活请求失败，端口 {wda_forward_port}: {e}")
            return False
    
    def _setup_ios_forward(self, local_port, device_udid, device_logger):
        """
        使用ios forward建立端口转发
        
        Args:
            local_port (int): macOS本地端口
            device_udid (str): 设备UDID
            device_logger (logging.Logger): 设备日志记录器
            
        Returns:
            bool: 是否成功建立端口转发
        """
        ios_forward_process = None
        try:
            # 构建ios forward命令
            ios_forward_cmd = [
                "/Users/<USER>/.nvm/versions/node/v20.18.3/bin/ios", 
                "forward", 
                str(local_port), 
                "8100", 
                f"--udid={device_udid}"
            ]
            device_logger.info(f"启动ios forward转发: {' '.join(ios_forward_cmd)}")
            
            # 启动ios forward进程
            ios_forward_process = subprocess.Popen(
                ios_forward_cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE
            )
            
            # 等待ios forward启动
            time.sleep(Config.WDA_IPROXY_WAIT_TIME)
            
            # 检查ios forward进程是否正常运行
            if ios_forward_process.poll() is not None:
                stdout, stderr = ios_forward_process.communicate()
                device_logger.warning(f"ios forward进程异常退出，返回码: {ios_forward_process.returncode}")
                device_logger.warning(f"stdout: {stdout.decode()}")
                device_logger.warning(f"stderr: {stderr.decode()}")
                return False
            
            # 测试端口转发是否成功
            if self._test_wda_endpoint(local_port, device_logger):
                device_logger.info(f"ios forward端口转发成功: 设备8100 -> macOS{local_port}")
                return True
            else:
                device_logger.warning(f"ios forward端口转发建立，但WDA服务仍不可访问")
                return False
                
        except Exception as e:
            device_logger.error(f"建立ios forward转发时发生错误: {e}")
            return False
        finally:
            # 如果转发失败，清理ios forward进程
            if ios_forward_process and ios_forward_process.poll() is None:
                try:
                    process_pid = ios_forward_process.pid
                    device_logger.debug(f"正在清理ios forward进程 PID={process_pid}")
                    
                    ios_forward_process.terminate()
                    try:
                        ios_forward_process.wait(timeout=Config.WDA_IPROXY_TERMINATE_TIMEOUT)
                        device_logger.debug(f"ios forward进程 PID={process_pid} 已正常终止")
                    except subprocess.TimeoutExpired:
                        device_logger.warning(f"ios forward进程 PID={process_pid} 终止超时，强制杀死")
                        ios_forward_process.kill()
                        try:
                            ios_forward_process.wait(timeout=2)
                            device_logger.debug(f"ios forward进程 PID={process_pid} 已被强制终止")
                        except subprocess.TimeoutExpired:
                            device_logger.error(f"无法终止ios forward进程 PID={process_pid}")
                        
                except Exception as e:
                    device_logger.warning(f"清理ios forward进程时发生错误: {e}")
        
        return False
    
    def _check_wda_health_with_temp_iproxy(self, device_udid, device_logger, device_name=None):
        """
        使用临时iproxy进行WDA健康检查
        
        Args:
            device_udid (str): 设备UDID
            device_logger (logging.Logger): 设备日志记录器
            device_name (str, optional): 设备名称
            
        Returns:
            bool: WDA服务是否健康
        """
        device_display_name = device_name or device_udid
        device_logger.info(f"[WDA健康检查] 为设备 {device_display_name} 使用临时iproxy进行健康检查")
        
        # 1. 查找可用的临时端口
        temp_port = self._find_available_temp_port(device_logger)
        if not temp_port:
            device_logger.error(f"[WDA健康检查] 无法为设备 {device_display_name} 找到可用的临时端口")
            return False
        
        device_logger.info(f"[WDA健康检查] 设备 {device_display_name} 使用临时端口 {temp_port} 进行健康检查")
        
        iproxy_process = None
        try:
            # 2. 启动临时iproxy进程
            device_logger.info(f"[WDA健康检查] 启动临时iproxy: 设备8100 -> macOS{temp_port}")
            iproxy_cmd = ["iproxy", "-u", device_udid, f"{temp_port}:8100"]
            
            iproxy_process = subprocess.Popen(
                iproxy_cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE
            )
            
            # 3. 等待iproxy启动
            time.sleep(Config.WDA_IPROXY_WAIT_TIME)
            
            # 检查iproxy进程是否正常运行
            if iproxy_process.poll() is not None:
                stdout, stderr = iproxy_process.communicate()
                device_logger.warning(f"[WDA健康检查] 设备 {device_display_name} iproxy进程启动失败，返回码: {iproxy_process.returncode}")
                device_logger.warning(f"[WDA健康检查] iproxy错误输出: {stderr.decode('utf-8', errors='ignore')}")
                return False
            
            device_logger.info(f"[WDA健康检查] 设备 {device_display_name} iproxy进程启动成功，PID: {iproxy_process.pid}")
            
            # 4. 测试WDA服务
            device_logger.info(f"[WDA健康检查] 向设备 {device_display_name} WDA服务发送健康检查请求")
            wda_healthy = self._test_wda_endpoint(temp_port, device_logger)
            
            if wda_healthy:
                device_logger.info(f"[WDA健康检查] 设备 {device_display_name} WDA服务健康检查通过")
            else:
                device_logger.warning(f"[WDA健康检查] 设备 {device_display_name} WDA服务健康检查失败")
            
            return wda_healthy
            
        except Exception as e:
            device_logger.error(f"[WDA健康检查] 设备 {device_display_name} 健康检查过程发生错误: {e}")
            return False
        finally:
            # 5. 确保iproxy进程被清理
            if iproxy_process:
                self._cleanup_iproxy_process(iproxy_process, device_logger, device_display_name)
    
    def _find_available_temp_port(self, device_logger):
        """
        查找可用的临时端口用于WDA健康检查
        
        Args:
            device_logger (logging.Logger): 设备日志记录器
            
        Returns:
            int or None: 可用端口号，如果找不到返回None
        """
        # 在8101-8200范围内查找可用端口
        for port in range(8101, 8201):
            if self._is_port_available(port):
                device_logger.debug(f"[WDA健康检查] 找到可用临时端口: {port}")
                return port
        
        device_logger.error(f"[WDA健康检查] 在8101-8200范围内未找到可用的临时端口")
        return None
    
    def _cleanup_iproxy_process(self, iproxy_process, device_logger, device_display_name):
        """
        清理iproxy进程
        
        Args:
            iproxy_process: iproxy进程对象
            device_logger (logging.Logger): 设备日志记录器
            device_display_name (str): 设备显示名称
        """
        if not iproxy_process:
            return
            
        try:
            process_pid = iproxy_process.pid
            device_logger.info(f"[WDA健康检查] 清理设备 {device_display_name} 的临时iproxy进程 PID={process_pid}")
            
            # 尝试正常终止进程
            iproxy_process.terminate()
            try:
                iproxy_process.wait(timeout=Config.WDA_IPROXY_TERMINATE_TIMEOUT)
                device_logger.info(f"[WDA健康检查] 设备 {device_display_name} 临时iproxy进程 PID={process_pid} 已正常终止")
            except subprocess.TimeoutExpired:
                device_logger.warning(f"[WDA健康检查] 设备 {device_display_name} 临时iproxy进程 PID={process_pid} 终止超时，强制杀死")
                iproxy_process.kill()
                try:
                    iproxy_process.wait(timeout=2)
                    device_logger.info(f"[WDA健康检查] 设备 {device_display_name} 临时iproxy进程 PID={process_pid} 已被强制终止")
                except subprocess.TimeoutExpired:
                    device_logger.error(f"[WDA健康检查] 无法终止设备 {device_display_name} 临时iproxy进程 PID={process_pid}")
        except Exception as e:
            device_logger.warning(f"[WDA健康检查] 清理设备 {device_display_name} 临时iproxy进程时发生错误: {e}")
    

    def _is_port_available(self, port):
        """
        检查端口是否可用

        Args:
            port (int): 端口号

        Returns:
            bool: 端口是否可用
        """
        try:
            result = subprocess.run(f"lsof -i:{port}", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return result.returncode != 0  # 返回码非0表示端口未被占用
        except Exception:
            return False


    def _monitor_shell_process_logs(self, udid, device_logger, base_log_path):
        """
        监控shell脚本执行过程中的独立日志文件
        
        Args:
            udid: 设备UDID
            device_logger: 设备日志记录器
            base_log_path: 基础日志路径
        """
        try:
            # 构建独立日志文件路径
            wda_log_file = f"{base_log_path.replace('.log', '')}_wda.log"
            forward_log_file = f"{base_log_path.replace('.log', '')}_forward.log"
            
            device_logger.info(f"监控独立日志文件:")
            device_logger.info(f"  WDA日志: {wda_log_file}")
            device_logger.info(f"  转发日志: {forward_log_file}")
            
            # 监控WDA日志
            if os.path.exists(wda_log_file):
                with open(wda_log_file, 'r') as f:
                    wda_content = f.read()
                if wda_content:
                    device_logger.info(f"WDA进程日志内容:")
                    for line in wda_content.strip().split('\n')[-10:]:  # 显示最后10行
                        device_logger.info(f"  WDA: {line}")
            
            # 监控转发日志
            if os.path.exists(forward_log_file):
                with open(forward_log_file, 'r') as f:
                    forward_content = f.read()
                if forward_content:
                    device_logger.info(f"端口转发日志内容:")
                    for line in forward_content.strip().split('\n')[-10:]:  # 显示最后10行
                        device_logger.info(f"  转发: {line}")
                        
        except Exception as e:
            device_logger.warning(f"监控日志文件时发生错误: {e}")
    
    def _check_shell_process_status(self, udid, device_logger, base_log_path):
        """
        检查shell脚本启动的进程状态
        
        Args:
            udid: 设备UDID
            device_logger: 设备日志记录器
            base_log_path: 基础日志路径
            
        Returns:
            dict: 包含进程状态信息的字典
        """
        try:
            # 检查是否有运行中的WDA进程
            result = subprocess.run(
                ["ps", "aux"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                processes = result.stdout
                
                # 查找WDA相关进程
                wda_processes = []
                forward_processes = []
                
                for line in processes.split('\n'):
                    if 'ios runwda' in line and udid in line:
                        wda_processes.append(line.strip())
                    elif 'ios forward' in line and udid in line:
                        forward_processes.append(line.strip())
                
                device_logger.info(f"找到 {len(wda_processes)} 个WDA进程")
                device_logger.info(f"找到 {len(forward_processes)} 个转发进程")
                
                for i, process in enumerate(wda_processes):
                    device_logger.info(f"  WDA进程 {i+1}: {process}")
                
                for i, process in enumerate(forward_processes):
                    device_logger.info(f"  转发进程 {i+1}: {process}")
                
                return {
                    'wda_processes': wda_processes,
                    'forward_processes': forward_processes,
                    'wda_count': len(wda_processes),
                    'forward_count': len(forward_processes)
                }
            else:
                device_logger.warning(f"检查进程状态失败: {result.stderr}")
                return None
                
        except Exception as e:
            device_logger.error(f"检查进程状态时发生错误: {e}")
            return None

    def stop_wda_process(self, device_logger, wda_port=None, device_udid=None):
        """停止WDA进程，确保所有相关进程被彻底终止
        
        Args:
            device_logger: 设备日志记录器
            wda_port: WDA服务端口号（可选）
            device_udid: 设备UDID（可选）
        """
        try:
            # 使用ps命令查找所有WebDriverAgent相关进程
            cmd = "ps -ax | grep WebDriverAgent | grep -v grep"
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            if result.returncode != 0 or not result.stdout.strip():
                device_logger.info("未找到任何WebDriverAgent进程")
                return True
            
            # 解析进程列表
            wda_processes = result.stdout.strip().split('\n')
            killed_processes = 0
            
            for process_line in wda_processes:
                # 检查是否需要根据设备UDID过滤
                if device_udid and f"id={device_udid}" not in process_line and f"-destination id={device_udid}" not in process_line:
                    continue
                    
                # 检查是否需要根据端口过滤
                if wda_port and f"USE_PORT={wda_port}" not in process_line:
                    continue
                    
                # 提取PID（第一列）
                parts = process_line.strip().split()
                if not parts:
                    continue
                    
                pid = parts[0]
                
                try:
                    # 终止进程
                    device_logger.info(f"尝试终止WebDriverAgent进程 PID={pid}")
                    subprocess.run(['kill', '-9', pid], check=False)
                    killed_processes += 1
                    
                    # 验证进程是否已终止
                    time.sleep(Config.WDA_KILL_PROCESS_WAIT_TIME)  # 给系统一点时间处理kill命令
                    if subprocess.run(['ps', '-p', pid], stdout=subprocess.PIPE, stderr=subprocess.PIPE).returncode != 0:
                        device_logger.info(f"已成功终止WebDriverAgent进程 PID={pid}")
                    else:
                        device_logger.warning(f"无法终止WebDriverAgent进程 PID={pid}，进程仍在运行")
                except Exception as e:
                    device_logger.warning(f"终止WebDriverAgent进程 PID={pid} 时出错: {e}")
            
            # 终止ios forward进程
            if wda_port or device_udid:
                device_logger.info("尝试终止相关ios forward进程...")
                ios_forward_cmd = "ps -ax | grep 'ios forward' | grep -v grep"
                ios_forward_result = subprocess.run(ios_forward_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                if ios_forward_result.returncode == 0 and ios_forward_result.stdout.strip():
                    ios_forward_processes = ios_forward_result.stdout.strip().split('\n')
                    
                    for ios_forward_line in ios_forward_processes:
                        # 过滤出与当前设备或端口相关的ios forward进程
                        if (device_udid and device_udid in ios_forward_line) or (wda_port and str(wda_port) in ios_forward_line):
                            ios_forward_parts = ios_forward_line.strip().split()
                            if ios_forward_parts:
                                ios_forward_pid = ios_forward_parts[0]
                                try:
                                    device_logger.info(f"尝试终止ios forward进程 PID={ios_forward_pid}")
                                    subprocess.run(['kill', '-9', ios_forward_pid], check=False)
                                except Exception as e:
                                    device_logger.warning(f"终止ios forward进程 PID={ios_forward_pid} 时出错: {e}")
            
            device_logger.info(f"已尝试终止 {killed_processes} 个WebDriverAgent相关进程")
            return killed_processes > 0
            
        except Exception as e:
            device_logger.error(f"停止WDA进程时发生错误: {e}")
            return False

    def check_and_restart_wda_if_needed(self, device, wda_port, wda_last_restart_time, current_round, device_logger):
        """检查并在需要时重启WDA服务
        
        Args:
            device: 设备信息字典
            wda_port: WDA端口
            wda_last_restart_time: 上次WDA重启时间
            current_round: 当前轮次
            device_logger: 设备日志记录器
            
        Returns:
            float: 新的WDA重启时间（如果重启了），否则返回原来的时间
        """
        device_udid = device['udid']
        current_time = time.time()
        
        if current_time - wda_last_restart_time >= Config.WDA_RESTART_INTERVAL:
            device_logger.info(f"WDA 已运行 {(current_time - wda_last_restart_time) / 3600:.2f} 小时，执行定期重启...")
            
            # 在重启WDA前清理该设备的旧日志文件
            device_logger.info(f"在重启WDA前清理设备 {device['name']} (UDID: {device_udid}) 的旧日志文件")
            try:
                from tools._concurrent_log_manager import get_current_task_log_manager as global_log_manager
                global_log_manager().cleanup_device_wda_logs(device_udid=device_udid, device_name=device['name'], hours=24)
            except Exception as e:
                device_logger.warning(f"清理WDA日志时出错: {e}")
                # 使用本地清理方法作为备选
                self.log_manager.cleanup_wda_logs(device_name=device['name'], max_logs=10)
            
            # 停止 WDA 进程
            self.stop_wda_process(device_logger, wda_port, device_udid)
            time.sleep(Config.WDA_STOP_WAIT_TIME)  # 改为同步 sleep
            
            # 重新启动 WDA
            success_wda = self.start_wda_for_device(device["udid"], device_logger, max_retries=5, device_name=device["name"])
            
            if success_wda:
                device_logger.info(f"WDA 定期重启成功，设备: {device['name']} (UDID: {device_udid})")
                return time.time()
            else:
                device_logger.error(f"WDA 定期重启失败，设备: {device['name']} (UDID: {device_udid})")
                update_device_status(device_udid, {'status': 'wda_restart_failed', 'last_update': time.time()})
                
                
                # 等待一段时间后再次尝试
                time.sleep(Config.WDA_RESTART_RETRY_WAIT)  # 改为同步 sleep
                
                # 再次尝试启动 WDA
                success_wda = self.start_wda_for_device(device["udid"], device_logger, max_retries=3, device_name=device["name"])
                if success_wda:
                    device_logger.info(f"WDA 第二次尝试重启成功，设备: {device['name']} (UDID: {device_udid})")
                    return time.time()
                else:
                    device_logger.error(f"WDA 第二次尝试重启也失败，设备: {device['name']} (UDID: {device_udid})")
                    
        # 返回原来的重启时间
        return wda_last_restart_time
    
    def _detect_actual_wda_port(self, device_udid, original_port, device_logger):
        """
        检测指定设备实际正在使用的WDA端口
        
        Args:
            device_udid (str): 设备UDID
            original_port (int): 原始分配的端口
            device_logger (logging.Logger): 设备日志记录器
            
        Returns:
            int: 实际WDA端口，如果未找到返回None
        """
        try:
            # 检查是否有该设备相关的ios forward进程
            ios_forward_cmd = "ps -ax | grep 'ios forward' | grep -v grep"
            result = subprocess.run(ios_forward_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                forward_processes = result.stdout.strip().split('\n')
                
                for process_line in forward_processes:
                    # 检查是否包含当前设备的UDID
                    if device_udid in process_line:
                        # 尝试从进程命令行中提取端口号
                        # 格式类似: ios forward 8102 8100 --udid=00008101-00166C3811BA001E
                        parts = process_line.split()
                        for i, part in enumerate(parts):
                            if part == 'forward' and i + 1 < len(parts):
                                try:
                                    actual_port = int(parts[i + 1])
                                    device_logger.info(f"从进程列表中检测到设备 {device_udid} 实际使用端口: {actual_port}")
                                    
                                    # 验证该端口确实可以访问WDA服务
                                    if self._test_wda_endpoint(actual_port, device_logger):
                                        device_logger.info(f"确认端口 {actual_port} 可访问WDA服务")
                                        return actual_port
                                    else:
                                        device_logger.warning(f"端口 {actual_port} 无法访问WDA服务")
                                except ValueError:
                                    continue
            
            # 如果从进程列表中未找到，尝试扫描邻近端口
            device_logger.info(f"从进程列表未找到端口，开始扫描 {original_port} 邻近端口")
            scan_range = range(max(8101, original_port - 5), min(8201, original_port + 10))
            
            for port in scan_range:
                if port != original_port:  # 跳过原始端口，因为已经确认失败
                    if self._test_wda_endpoint(port, device_logger):
                        device_logger.info(f"扫描发现端口 {port} 可访问WDA服务")
                        return port
            
            device_logger.warning(f"未能检测到设备 {device_udid} 的实际WDA端口")
            return None
            
        except Exception as e:
            device_logger.error(f"检测实际WDA端口时发生错误: {e}")
            return None
    
    def _update_device_wda_port(self, device_udid, new_port, device_logger):
        """
        更新设备状态文件中的WDA端口信息
        
        Args:
            device_udid (str): 设备UDID
            new_port (int): 新的WDA端口
            device_logger (logging.Logger): 设备日志记录器
        """
        try:
            from tools._device_status_manage_tools import update_device_status
            
            # 更新设备状态中的WDA端口信息
            update_device_status(device_udid, {
                'wda_forward_port': new_port,
                'local_wda_port': new_port,  # 保持一致性
            })
            
            device_logger.info(f"已更新设备 {device_udid} 的WDA端口信息为: {new_port}")
            
        except Exception as e:
            device_logger.error(f"更新设备WDA端口信息时发生错误: {e}")

# 创建WDA服务管理器全局实例
wda_service_manager = WdaServiceManager(Config(), global_logger) 