#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能文本输入工具，适用于 iOS 和 Android。

提供智能输入功能：
- 自动查找页面中的输入元素
- 如果只有一个输入元素，直接输入
- 如果有多个输入元素，返回所有元素信息供选择
- 支持指定元素索引进行输入
"""

import os
import sys
import time
import json
import xml.etree.ElementTree as ET
from typing import Dict, Any, List

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools._concurrent_log_manager import get_current_task_log_manager
from tools._device_status_manage_tools import get_device_status
from tools._device_driver_manage_tools import get_device_driver
from tools.llm_base_tools import get_test_driver


def find_input_elements_smart(driver, platform: str) -> List[Dict[str, Any]]:
    """
    智能查找输入元素，支持iOS和Android
    
    Args:
        driver: Appium WebDriver 实例
        platform: 平台类型 ('ios' 或 'android')
        
    Returns:
        List[Dict]: 输入元素列表，每个元素包含坐标、属性等信息
    """
    try:
        get_current_task_log_manager().info_tools(f"开始查找{platform}输入元素", "find_input_elements_smart")
        
        # 获取页面源码
        page_source = driver.page_source
        root = ET.fromstring(page_source)
        
        input_elements = []
        
        if platform == 'android':
            # Android中常见的输入元素类型
            input_tags = [
                'android.widget.EditText',
                'android.widget.AutoCompleteTextView',
                'android.widget.MultiAutoCompleteTextView'
            ]
            
            for element in root.iter():
                if element.tag in input_tags:
                    # 检查元素是否可见和可用
                    enabled = element.get('enabled', 'false') == 'true'
                    displayed = element.get('displayed', 'false') == 'true'
                    
                    if enabled and displayed:
                        bounds = element.get('bounds', '')
                        if bounds:
                            try:
                                # 解析bounds坐标 [x1,y1][x2,y2]
                                bounds_parts = bounds.replace('[', '').replace(']', ',').split(',')
                                x1, y1, x2, y2 = map(int, bounds_parts[:4])
                                center_x = (x1 + x2) // 2
                                center_y = (y1 + y2) // 2
                                
                                element_info = {
                                    'index': len(input_elements),
                                    'tag': element.tag,
                                    'text': element.get('text', ''),
                                    'hint': element.get('hint', ''),
                                    'resource_id': element.get('resource-id', ''),
                                    'content_desc': element.get('content-desc', ''),
                                    'bounds': bounds,
                                    'center_x': center_x,
                                    'center_y': center_y,
                                    'enabled': enabled,
                                    'displayed': displayed,
                                    'description': _format_android_element_description(element, len(input_elements))
                                }
                                input_elements.append(element_info)
                            except (ValueError, IndexError) as e:
                                get_current_task_log_manager().warning_tools(f"解析bounds失败: {bounds}, 错误: {e}", "find_input_elements_smart")
        
        elif platform == 'ios':
            # iOS中常见的输入元素类型
            input_tags = [
                'XCUIElementTypeTextField',
                'XCUIElementTypeTextView',
                'XCUIElementTypeSearchField',
                'XCUIElementTypeSecureTextField'
            ]

            for element in root.iter():
                # 只检查标准输入元素，不再包含搜索相关的图片元素
                is_input_element = element.tag in input_tags

                if is_input_element:
                    # 检查元素是否可见和可用
                    enabled = element.get('enabled', 'false') == 'true'
                    visible = element.get('visible', 'false') == 'true'

                    if enabled and visible:
                        # 获取坐标信息
                        x = element.get('x')
                        y = element.get('y')
                        width = element.get('width')
                        height = element.get('height')

                        if x and y and width and height:
                            try:
                                x, y, width, height = int(x), int(y), int(width), int(height)
                                center_x = x + width // 2
                                center_y = y + height // 2

                                element_info = {
                                    'index': len(input_elements),
                                    'tag': element.tag,
                                    'name': element.get('name', ''),
                                    'label': element.get('label', ''),
                                    'value': element.get('value', ''),
                                    'placeholder': element.get('placeholder-value', ''),
                                    'traits': element.get('traits', ''),
                                    'x': x,
                                    'y': y,
                                    'width': width,
                                    'height': height,
                                    'center_x': center_x,
                                    'center_y': center_y,
                                    'enabled': enabled,
                                    'visible': visible,
                                    'description': _format_ios_element_description(element, len(input_elements))
                                }
                                input_elements.append(element_info)
                            except (ValueError, TypeError) as e:
                                get_current_task_log_manager().warning_tools(f"解析坐标失败: x={x}, y={y}, width={width}, height={height}, 错误: {e}", "find_input_elements_smart")
        
        get_current_task_log_manager().info_tools(f"{platform}页面共找到 {len(input_elements)} 个输入元素", "find_input_elements_smart")
        return input_elements
        
    except Exception as e:
        get_current_task_log_manager().error_tools(f"查找{platform}输入元素失败: {e}", "find_input_elements_smart")
        return []


def _format_android_element_description(element, index: int) -> str:
    """格式化Android元素描述"""
    text = element.get('text', '')
    hint = element.get('hint', '')
    resource_id = element.get('resource-id', '')
    element_type = element.get('tag', '未知类型')
    
    description = f"[{index}] 类型: {element_type}"
    if hint: description += f", 提示: '{hint}'"
    if text: description += f", 文本: '{text}'"
    if resource_id: description += f", 资源ID: '{resource_id}'"
    
    return description


def _format_ios_element_description(element, index: int) -> str:
    """格式化iOS元素描述"""
    name = element.get('name', '')
    label = element.get('label', '')
    placeholder = element.get('placeholder-value', '')
    element_type = element.get('tag', '未知类型')

    description = f"[{index}] 类型: {element_type}"
    if placeholder: description += f", 占位符: '{placeholder}'"
    if name: description += f", 名称: '{name}'"
    if label and label != name: description += f", 标签: '{label}'"

    return description


def perform_input_android(driver, element_info: Dict[str, Any], text: str) -> bool:
    """在Android设备上执行输入操作"""
    try:
        center_x = element_info['center_x']
        center_y = element_info['center_y']

        # 减少详细日志输出
        # get_current_task_log_manager().info_tools(f"点击Android输入元素坐标: ({center_x}, {center_y})", "perform_input_android")
        driver.tap([(center_x, center_y)])
        time.sleep(1)  # 等待获得焦点

        # 查找输入元素并清空现有内容
        from appium.webdriver.common.appiumby import AppiumBy

        try:
            # 尝试通过resource-id查找元素
            resource_id = element_info.get('resource_id', '')
            if resource_id:
                input_element = driver.find_element(AppiumBy.ID, resource_id)
                # get_current_task_log_manager().info_tools(f"通过resource-id找到输入元素: {resource_id}", "perform_input_android")
            else:
                # 如果没有resource-id，尝试通过class查找第一个EditText
                input_element = driver.find_element(AppiumBy.CLASS_NAME, "android.widget.EditText")
                # get_current_task_log_manager().info_tools(f"通过class找到输入元素", "perform_input_android")
        except Exception as e:
            # 简化错误日志，避免输出大量UI元素树信息
            error_msg = str(e)
            if len(error_msg) > 200:
                simplified_msg = f"无法找到输入元素: {error_msg[:200]}..."
            else:
                simplified_msg = f"无法找到输入元素: {error_msg}"
            get_current_task_log_manager().warning_tools(simplified_msg, "perform_input_android")
            input_element = None

        if input_element:
            try:
                # 清空现有内容
                input_element.clear()
                # get_current_task_log_manager().info_tools(f"成功清空输入元素内容", "perform_input_android")
            except Exception as e:
                get_current_task_log_manager().warning_tools(f"清空输入元素内容失败: {e}", "perform_input_android")

            # 输入文本
            # 输入文本 - 减少详细日志
            # get_current_task_log_manager().info_tools(f"输入文本: '{text}'", "perform_input_android")
            try:
                input_element.send_keys(text)
                get_current_task_log_manager().info_tools(f"成功输入文本: '{text}'", "perform_input_android")
            except Exception as e:
                # 简化错误日志，避免输出大量UI元素树信息
                error_msg = str(e)
                if len(error_msg) > 200:
                    simplified_msg = f"输入文本失败: {error_msg[:200]}..."
                else:
                    simplified_msg = f"输入文本失败: {error_msg}"
                get_current_task_log_manager().error_tools(simplified_msg, "perform_input_android")
                return False
        else:
            get_current_task_log_manager().error_tools(f"无法找到输入元素，输入失败", "perform_input_android")
            return False

        # 隐藏键盘
        try:
            driver.hide_keyboard()
        except:
            pass

        return True

    except Exception as e:
        get_current_task_log_manager().error_tools(f"Android输入操作失败: {e}", "perform_input_android")
        return False


def perform_input_ios(driver, element_info: Dict[str, Any], text: str, scale_ratio: float) -> bool:
    """在iOS设备上执行输入操作"""
    try:
        center_x = element_info['center_x']
        center_y = element_info['center_y']
        
        # 根据 scale_ratio 换算坐标
        scaled_x = int(center_x / scale_ratio)
        scaled_y = int(center_y / scale_ratio)
        
        # 减少详细日志输出
        # get_current_task_log_manager().info_tools(f"原始坐标: ({center_x}, {center_y}), scale_ratio: {scale_ratio}, 换算后坐标: ({scaled_x}, {scaled_y})", "perform_input_ios")
        
        # 点击输入元素以获得焦点
        try:
            driver.execute_script("mobile: tap", {"x": scaled_x, "y": scaled_y})
        except Exception as e:
            get_current_task_log_manager().warning_tools(f"mobile:tap失败，尝试其他方法: {e}", "perform_input_ios")
            driver.tap([(scaled_x, scaled_y)])
        
        time.sleep(1)  # 等待获得焦点
        
        # 查找输入元素并清空现有内容
        from appium.webdriver.common.appiumby import AppiumBy

        try:
            # 尝试通过name查找元素
            element_name = element_info.get('name', '')
            if element_name:
                input_element = driver.find_element(AppiumBy.NAME, element_name)
                # get_current_task_log_manager().info_tools(f"通过name找到输入元素: {element_name}", "perform_input_ios")
            else:
                # 如果没有name，尝试通过class查找第一个TextField
                input_element = driver.find_element(AppiumBy.CLASS_NAME, "XCUIElementTypeTextField")
                # get_current_task_log_manager().info_tools(f"通过class找到输入元素", "perform_input_ios")
        except Exception as e:
            # 简化错误日志，避免输出大量UI元素树信息
            error_msg = str(e)
            if "No matches found for Elements matching predicate" in error_msg:
                simplified_msg = "无法找到输入元素，可能页面已发生变化"
            elif len(error_msg) > 200:
                simplified_msg = f"无法找到输入元素: {error_msg[:200]}..."
            else:
                simplified_msg = f"无法找到输入元素: {error_msg}"
            get_current_task_log_manager().warning_tools(simplified_msg, "perform_input_ios")
            input_element = None

        if input_element:
            try:
                # 清空现有内容
                input_element.clear()
                # get_current_task_log_manager().info_tools(f"成功清空输入元素内容", "perform_input_ios")
            except Exception as e:
                get_current_task_log_manager().warning_tools(f"清空输入元素内容失败: {e}", "perform_input_ios")

            # 输入文本
            # 输入文本 - 减少详细日志
            # get_current_task_log_manager().info_tools(f"输入文本: '{text}'", "perform_input_ios")
            try:
                input_element.send_keys(text)
                get_current_task_log_manager().info_tools(f"成功输入文本: '{text}'", "perform_input_ios")
            except Exception as e:
                # 简化错误日志，避免输出大量UI元素树信息
                error_msg = str(e)
                if "No matches found for Elements matching predicate" in error_msg:
                    simplified_msg = "输入元素已不可用，可能页面已发生变化"
                elif len(error_msg) > 200:
                    simplified_msg = f"输入文本失败: {error_msg[:200]}..."
                else:
                    simplified_msg = f"输入文本失败: {error_msg}"
                get_current_task_log_manager().error_tools(simplified_msg, "perform_input_ios")
                return False
        else:
            get_current_task_log_manager().error_tools(f"无法找到输入元素，输入失败", "perform_input_ios")
            return False
        
        # 隐藏键盘
        try:
            driver.hide_keyboard()
        except:
            pass
        
        return True

    except Exception as e:
        get_current_task_log_manager().error_tools(f"iOS输入操作失败: {e}", "perform_input_ios")
        return False


def smart_input_text(udid: str, text: str, element_index: int = None) -> str:
    """
    智能文本输入函数

    功能逻辑：
    1. 自动查找页面中的输入元素
    2. 如果只有一个输入元素，直接输入
    3. 如果有多个输入元素且未指定索引，返回所有元素信息供选择
    4. 如果指定了元素索引，在对应元素中输入

    重要使用说明：
    - 第一次调用时，不要传递 element_index 参数，让函数自动判断
    - 如果返回 "multiple_elements_found" 状态，说明页面有多个输入元素
    - 此时需要第二次调用，并指定具体的 element_index（从返回的元素列表中选择）
    - 只有在第一次调用返回多个元素时，才需要在第二次调用中指定 element_index

    Args:
        udid: 设备的序列号(UDID)
        text: 要输入的文本内容（支持中文）
        element_index: 可选，指定输入元素的索引。仅在第一次调用返回多个元素后的第二次调用时使用

    Returns:
        str: JSON字符串，包含操作结果
    """
    try:
        get_current_task_log_manager().info_tools(f"开始智能文本输入操作，设备: {udid}, 文本: '{text}', 元素索引: {element_index}", "smart_input_text")

        # 从设备状态获取平台信息
        device_status = get_device_status(udid)
        if not device_status:
            result = {
                "status": "error",
                "udid": udid,
                "text": text,
                "message": f"设备 {udid} 状态文件不存在"
            }
            get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
            return json.dumps(result, ensure_ascii=False)

        platform = device_status.get('platform', '').lower()
        if not platform:
            result = {
                "status": "error",
                "udid": udid,
                "text": text,
                "message": f"设备 {udid} 状态文件中未找到平台信息"
            }
            get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
            return json.dumps(result, ensure_ascii=False)

        # 获取设备driver
        if platform == 'android':
            driver = get_device_driver(udid)
        elif platform == 'ios':
            driver = get_test_driver(udid)
        else:
            result = {
                "status": "error",
                "udid": udid,
                "platform": platform,
                "text": text,
                "message": f"不支持的平台类型: {platform}"
            }
            get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
            return json.dumps(result, ensure_ascii=False)

        if not driver:
            result = {
                "status": "error",
                "udid": udid,
                "platform": platform,
                "text": text,
                "message": f"无法获取设备 {udid} 的driver"
            }
            get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
            return json.dumps(result, ensure_ascii=False)

        # 查找输入元素
        input_elements = find_input_elements_smart(driver, platform)

        if not input_elements:
            result = {
                "status": "no_input_elements",
                "udid": udid,
                "platform": platform,
                "text": text,
                "input_elements_count": 0,
                "message": f"在设备 {udid} ({platform}) 页面中未找到任何输入元素"
            }
            get_current_task_log_manager().warning_tools(result["message"], "smart_input_text")
            return json.dumps(result, ensure_ascii=False)

        # 情况1：只有一个输入元素，直接输入
        if len(input_elements) == 1 and element_index is None:
            target_element = input_elements[0]
            get_current_task_log_manager().info_tools(f"页面只有一个输入元素，直接输入: {target_element['description']}", "smart_input_text")

            # 执行输入操作
            if platform == 'android':
                success = perform_input_android(driver, target_element, text)
            else:  # iOS
                scale_ratio = device_status.get('scale_ratio', 1.0)
                success = perform_input_ios(driver, target_element, text, scale_ratio)

            if success:
                result = {
                    "status": "success",
                    "udid": udid,
                    "platform": platform,
                    "text": text,
                    "input_elements_count": 1,
                    "used_element_index": 0,
                    "used_element": target_element,
                    "message": f"成功在设备 {udid} ({platform}) 的唯一输入元素中输入文本: '{text}'"
                }
                get_current_task_log_manager().info_tools(result["message"], "smart_input_text")
            else:
                result = {
                    "status": "input_failed",
                    "udid": udid,
                    "platform": platform,
                    "text": text,
                    "input_elements_count": 1,
                    "target_element": target_element,
                    "message": f"在设备 {udid} ({platform}) 的输入元素中输入文本失败"
                }
                get_current_task_log_manager().error_tools(result["message"], "smart_input_text")

            return json.dumps(result, ensure_ascii=False)

        # 情况2：有多个输入元素且未指定索引，返回所有元素信息
        elif len(input_elements) > 1 and element_index is None:
            result = {
                "status": "multiple_elements_found",
                "udid": udid,
                "platform": platform,
                "text": text,
                "input_elements_count": len(input_elements),
                "input_elements": input_elements,
                "message": f"在设备 {udid} ({platform}) 页面中找到 {len(input_elements)} 个输入元素，请指定要使用的元素索引"
            }
            get_current_task_log_manager().info_tools(result["message"], "smart_input_text")
            return json.dumps(result, ensure_ascii=False)

        # 情况3：指定了元素索引，在对应元素中输入
        elif element_index is not None:
            # 将element_index转换为整数
            try:
                element_index = int(element_index)
            except (ValueError, TypeError):
                result = {
                    "status": "invalid_element_index",
                    "udid": udid,
                    "platform": platform,
                    "text": text,
                    "element_index": element_index,
                    "message": f"元素索引 {element_index} 不是有效的整数"
                }
                get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
                return json.dumps(result, ensure_ascii=False)
            
            if element_index >= len(input_elements) or element_index < 0:
                result = {
                    "status": "invalid_element_index",
                    "udid": udid,
                    "platform": platform,
                    "text": text,
                    "element_index": element_index,
                    "input_elements_count": len(input_elements),
                    "message": f"元素索引 {element_index} 无效，有效范围: 0-{len(input_elements)-1}"
                }
                get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
                return json.dumps(result, ensure_ascii=False)

            target_element = input_elements[element_index]
            get_current_task_log_manager().info_tools(f"使用指定的第 {element_index} 个输入元素: {target_element['description']}", "smart_input_text")

            # 执行输入操作
            if platform == 'android':
                success = perform_input_android(driver, target_element, text)
            else:  # iOS
                scale_ratio = device_status.get('scale_ratio', 1.0)
                success = perform_input_ios(driver, target_element, text, scale_ratio)

            if success:
                result = {
                    "status": "success",
                    "udid": udid,
                    "platform": platform,
                    "text": text,
                    "input_elements_count": len(input_elements),
                    "used_element_index": element_index,
                    "used_element": target_element,
                    "message": f"成功在设备 {udid} ({platform}) 的第 {element_index} 个输入元素中输入文本: '{text}'"
                }
                get_current_task_log_manager().info_tools(result["message"], "smart_input_text")
            else:
                result = {
                    "status": "input_failed",
                    "udid": udid,
                    "platform": platform,
                    "text": text,
                    "element_index": element_index,
                    "target_element": target_element,
                    "message": f"在设备 {udid} ({platform}) 的第 {element_index} 个输入元素中输入文本失败"
                }
                get_current_task_log_manager().error_tools(result["message"], "smart_input_text")

            return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        result = {
            "status": "error",
            "udid": udid,
            "text": text,
            "element_index": element_index,
            "error": str(e),
            "message": f"智能文本输入操作时出错: {str(e)}"
        }
        get_current_task_log_manager().error_tools(result["message"], "smart_input_text")
        return json.dumps(result, ensure_ascii=False)


if __name__ == "__main__":
    # 示例用法
    print("测试智能文本输入工具...")
    from _device_driver_manage_tools import DeviceDriverManager
    DeviceDriverManager().create_android_driver("d6f09d4a")

    try:
        # 测试智能输入
        test_udid = "d6f09d4a"
        print(f"测试设备 {test_udid} 智能文本输入...")
        result = smart_input_text(test_udid, "奶茶")
        print(f"输入结果: {result}")

    except Exception as e:
        print(f"智能文本输入测试失败: {e}")
