#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接从XML转换为文本格式工具

跳过JSON中间步骤，直接从Appium XML源码提取有意义的叶子节点
"""

import xml.etree.ElementTree as ET
import re

def extract_android_meaningful_elements(xml_string):
    """
    直接从Android XML中提取有意义的叶子节点
    
    参数:
        xml_string: Appium获取的XML源码
        
    返回:
        有意义元素的列表
    """
    try:
        root = ET.fromstring(xml_string)
        meaningful_elements = []
        
        def traverse_android_xml(element):
            # 检查当前元素是否有意义
            tag = element.tag
            text = element.get('text', '').strip()
            content_desc = element.get('content-desc', '').strip()
            resource_id = element.get('resource-id', '').strip()
            bounds = element.get('bounds', '')
            clickable = element.get('clickable') == 'true'
            scrollable = element.get('scrollable') == 'true'
            long_clickable = element.get('long-clickable') == 'true'
            enabled = element.get('enabled') == 'true'
            displayed = element.get('displayed') == 'true'
            
            # 如果没有子元素，这是叶子节点
            if len(element) == 0:
                # 检查是否有意义
                has_text = bool(text)
                has_desc = bool(content_desc)
                has_id = bool(resource_id)
                is_interactive = clickable or scrollable or long_clickable
                
                # 过滤装饰性元素
                is_decorative = (
                    'shadow' in resource_id.lower() or
                    'divider' in resource_id.lower() or
                    'line' in resource_id.lower() or
                    'bg_' in resource_id.lower() or
                    'background' in resource_id.lower()
                )
                
                # 如果有意义且显示，就添加
                if (has_text or has_desc or is_interactive or has_id) and displayed and not is_decorative:
                    element_info = {
                        'tag': tag.split('.')[-1] if '.' in tag else tag,
                        'text': text,
                        'content_desc': content_desc,
                        'resource_id': resource_id,
                        'bounds': bounds,
                        'clickable': clickable,
                        'scrollable': scrollable,
                        'long_clickable': long_clickable,
                        'enabled': enabled,
                        'displayed': displayed
                    }
                    meaningful_elements.append(element_info)
            else:
                # 递归处理子元素
                for child in element:
                    traverse_android_xml(child)
        
        traverse_android_xml(root)
        return meaningful_elements
        
    except Exception as e:
        return []

def extract_ios_meaningful_elements(xml_string):
    """
    直接从iOS XML中提取有意义的叶子节点
    
    参数:
        xml_string: Appium获取的XML源码
        
    返回:
        有意义元素的列表
    """
    try:
        root = ET.fromstring(xml_string)  
        meaningful_elements = []
        
        def traverse_ios_xml(element):
            # 检查当前元素是否有意义
            tag = element.tag
            name = element.get('name', '').strip()
            label = element.get('label', '').strip()
            value = element.get('value', '').strip()
            x = element.get('x', '')
            y = element.get('y', '')
            width = element.get('width', '')
            height = element.get('height', '')
            enabled = element.get('enabled') == 'true'
            visible = element.get('visible') == 'true'
            accessible = element.get('accessible') == 'true'
            
            # 如果没有子元素，这是叶子节点
            if len(element) == 0:
                # 检查是否有意义
                has_name = bool(name)
                has_label = bool(label)
                has_value = bool(value)
                is_interactive = enabled or accessible
                
                # 过滤无用的XCUIElementTypeOther
                is_useful_element = (
                    tag != 'XCUIElementTypeOther' or 
                    has_name or has_label or has_value
                )
                
                # 如果有意义且可见，就添加
                if (has_name or has_label or has_value or is_interactive) and visible and is_useful_element:
                    element_info = {
                        'tag': tag.replace('XCUIElementType', ''),
                        'name': name,
                        'label': label,
                        'value': value,
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height,
                        'enabled': enabled,
                        'visible': visible,
                        'accessible': accessible
                    }
                    meaningful_elements.append(element_info)
            else:
                # 递归处理子元素
                for child in element:
                    traverse_ios_xml(child)
        
        traverse_ios_xml(root)
        return meaningful_elements
        
    except Exception as e:
        return []

def parse_android_bounds(bounds_str):
    """解析Android bounds字符串，返回中心点坐标"""
    if not bounds_str:
        return None
    match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds_str)
    if match:
        x1, y1, x2, y2 = map(int, match.groups())
        width = x2 - x1
        height = y2 - y1
        # 计算中心点坐标
        center_x = x1 + width // 2
        center_y = y1 + height // 2
        return {
            'x': center_x, 'y': center_y,
            'width': width, 'height': height
        }
    return None

def format_android_element_text(element):
    """格式化Android元素为文本"""
    parts = []
    
    # 元素类型
    parts.append(element['tag'])
    
    # 文本内容
    if element['text']:
        parts.append(f'text="{element["text"]}"')
    
    # 描述信息
    if element['content_desc']:
        parts.append(f'desc="{element["content_desc"]}"')
    
    # 位置信息
    bounds = parse_android_bounds(element['bounds'])
    if bounds:
        parts.append(f'center_pos=({bounds["x"]},{bounds["y"]}) size={bounds["width"]}x{bounds["height"]}')
    
    # 交互状态
    interactions = []
    if element['clickable']:
        interactions.append('clickable')
    if element['scrollable']:
        interactions.append('scrollable')
    if element['long_clickable']:
        interactions.append('long-clickable')
    if interactions:
        parts.append(f'interactive={"|".join(interactions)}')
    
    # Resource ID (简化)
    if element['resource_id']:
        short_id = element['resource_id'].split(':id/')[-1] if ':id/' in element['resource_id'] else element['resource_id']
        if len(short_id) < 50:  # 过滤过长ID
            parts.append(f'id={short_id}')
    
    return ' '.join(parts)

def format_ios_element_text(element):
    """格式化iOS元素为文本"""
    parts = []
    
    # 元素类型
    parts.append(element['tag'])
    
    # 名称和标签
    if element['name']:
        parts.append(f'name="{element["name"]}"')
    if element['label'] and element['label'] != element['name']:
        parts.append(f'label="{element["label"]}"')
    
    # 值
    if element['value']:
        parts.append(f'value="{element["value"]}"')
    
    # 位置信息 - 计算中心点坐标
    if all(element[k] for k in ['x', 'y', 'width', 'height']):
        x = int(element['x'])
        y = int(element['y'])
        width = int(element['width'])
        height = int(element['height'])
        # 计算中心点坐标
        center_x = x + width // 2
        center_y = y + height // 2
        parts.append(f'center_pos=({center_x},{center_y}) size={width}x{height}')
    
    # 交互状态
    interactions = []
    if element['enabled']:
        interactions.append('enabled')
    if element['accessible']:
        interactions.append('accessible')
    if interactions:
        parts.append(f'interactive={"|".join(interactions)}')
    
    return ' '.join(parts)

def xml_to_text(xml_string, platform=None):
    """
    直接将XML转换为文本格式
    
    参数:
        xml_string: Appium XML源码
        platform: 'android' 或 'ios'，如果不指定会自动判断
        
    返回:
        文本格式的页面元素列表
    """
    if not xml_string or not xml_string.strip():
        return "Page Elements:"
    
    # 自动判断平台
    if platform is None:
        if 'android.widget' in xml_string or 'android.view' in xml_string:
            platform = 'android'
        elif 'XCUIElementType' in xml_string:
            platform = 'ios'
        else:
            return "Error: Cannot determine platform from XML"
    
    # 提取有意义的元素
    if platform == 'android':
        elements = extract_android_meaningful_elements(xml_string)
        formatter = format_android_element_text
    else:  # iOS
        elements = extract_ios_meaningful_elements(xml_string)
        formatter = format_ios_element_text
    
    # 生成文本格式
    text_lines = ["Page Elements:"]
    
    for i, element in enumerate(elements, 1):
        formatted = formatter(element)
        text_lines.append(f"{i}. {formatted}")
    
    return '\n'.join(text_lines)

if __name__ == "__main__":
    # 测试函数
    print("XML直接转文本工具已创建")
    print("主要函数: xml_to_text(xml_string, platform=None)")
    print("支持平台: 'android', 'ios' 或自动检测")