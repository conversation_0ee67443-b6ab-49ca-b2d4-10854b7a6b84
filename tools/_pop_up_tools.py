#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
弹窗检测工具，提供函数 `detect_popups` 用于识别图像中的所有弹窗区域。
"""

import cv2
import numpy as np
import logging
from typing import Dict, Tuple, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('popup_detector')

def _detect_all_popup_regions(image_path: str, debug: bool = False) -> List[Dict[str, any]]:
    """
    使用亮度法检测截图中弹窗区域，优先检测顶部横幅，再检测常规弹窗。
    Args:
        image_path: 截图路径
        debug: 是否打印详细调试信息
    Returns:
        list: 检测到的弹窗区域列表（最多1个），每个元素是坐标信息字典。
    """
    try:
        # ===== 可调参数区域 =====
        BOTTOM_CROP_PERCENTAGE = 0.02  # 从底部裁切的百分比 (0.0 to 1.0)
        CENTER_RANGE_MIN = 0.2
        CENTER_RANGE_MAX = 0.8
        MIN_AREA_RATIO = 0.05
        MAX_AREA_RATIO = 0.8
        MIN_ASPECT_RATIO = 0.3
        MAX_ASPECT_RATIO = 2.5
        # ========================

        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法读取图片: {image_path}")
            return []

        # --- 新增：裁切图像底部 ---
        original_height, original_width = image.shape[:2]
        crop_height = int(original_height * BOTTOM_CROP_PERCENTAGE)
        if 0 < crop_height < original_height:
            cropped_image = image[0:original_height - crop_height, :]
            if debug:
                logger.info(f"已从底部裁切 {crop_height} 像素 (原始高度: {original_height}, 裁切后高度: {cropped_image.shape[0]})")
        else:
            cropped_image = image
            if debug:
                logger.info("未进行底部裁切。")
        # --- 结束新增 ---

        # 转换为灰度图 (使用裁切后的图像)
        gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape

        # 计算亮度信息
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        threshold = mean_brightness + std_brightness
        if debug:
            logger.info(f"平均亮度: {mean_brightness:.2f}")
            logger.info(f"亮度标准差: {std_brightness:.2f}")
            logger.info(f"二值化阈值: {threshold:.2f}")

        # 亮区掩码
        bright_mask = gray > threshold

        popup_regions = []

        # 先检测顶部横幅
        top_region_height = int(height * 0.1)
        top_region_mask = bright_mask[:top_region_height, :]
        top_region_detected = False
        if np.any(top_region_mask):
            for y in range(top_region_height):
                row = top_region_mask[y]
                if np.sum(row) > width * 0.8:
                    top_region_detected = True
                    break
        if debug:
            logger.info(f"顶部横幅检测: {'有' if top_region_detected else '无'}")
        if top_region_detected:
            y_coords, x_coords = np.where(top_region_mask)
            if len(x_coords) > 0:
                min_x, max_x = np.min(x_coords), np.max(x_coords)
                min_y, max_y = np.min(y_coords), np.max(y_coords)
                popup_regions.append({
                    'left_top': (int(min_x), int(min_y)),
                    'right_bottom': (int(max_x), int(max_y)),
                    'type': 'banner',
                    'area_ratio': float((max_x-min_x)*(max_y-min_y)/(height*width)),
                    'aspect_ratio': float((max_x-min_x)/(max_y-min_y) if (max_y-min_y)!=0 else 0)
                })
                return popup_regions  # 只返回横幅

        # 没有横幅，检测常规弹窗
        analysis_mask = bright_mask.copy()
        analysis_mask[:top_region_height, :] = False
        y_coords, x_coords = np.where(analysis_mask)
        if len(x_coords) > 0 and len(y_coords) > 0:
            min_x, max_x = np.min(x_coords), np.max(x_coords)
            min_y, max_y = np.min(y_coords), np.max(y_coords)
            width_region = max_x - min_x
            height_region = max_y - min_y
            area = width_region * height_region
            image_area = height * width
            area_ratio = area / image_area if image_area > 0 else 0
            relative_x_center = (min_x + max_x) / (2 * width) if width > 0 else 0
            relative_y_center = (min_y + max_y) / (2 * height) if height > 0 else 0
            is_center = (CENTER_RANGE_MIN <= relative_x_center <= CENTER_RANGE_MAX and
                         CENTER_RANGE_MIN <= relative_y_center <= CENTER_RANGE_MAX)
            is_reasonable_size = (MIN_AREA_RATIO <= area_ratio <= MAX_AREA_RATIO)
            aspect_ratio = width_region / height_region if height_region != 0 else 0
            is_reasonable_shape = (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO)
            if debug:
                logger.info(f"常规弹窗检测: 中心=({relative_x_center:.2f},{relative_y_center:.2f}), "
                            f"面积比={area_ratio:.2%}, 宽高比={aspect_ratio:.2f}")
                logger.info(f"中心判定: {is_center}, 大小判定: {is_reasonable_size}, 形状判定: {is_reasonable_shape}")
            if is_center and is_reasonable_size and is_reasonable_shape:
                popup_regions.append({
                    'left_top': (int(min_x), int(min_y)),
                    'right_bottom': (int(max_x), int(max_y)),
                    'type': 'normal',
                    'area_ratio': float(area_ratio),
                    'aspect_ratio': float(aspect_ratio)
                })
        return popup_regions
    except Exception as e:
        logger.error(f"检测弹窗区域时出错: {e}", exc_info=True)
        return []


def detect_popups(image_path: str) -> Dict[str, Dict[str, Tuple[int, int]]]:
    """
    检测图像中的弹窗（优先横幅），只返回第一个检测到的弹窗。
    Args:
        image_path: 图像的本地文件路径。
    Returns:
        dict: {"popup_1": {"left_top": (x, y), "right_bottom": (x, y)}}
    """
    popup_regions = _detect_all_popup_regions(image_path, debug=False)
    formatted_popups = {}
    if popup_regions:
        region = popup_regions[0]
        logger.info(f"检测到弹窗区域: {region}")
        formatted_popups["popup_1"] = {
            "left_top": region['left_top'],
            "right_bottom": region['right_bottom']
        }
    else:
        logger.info("未检测到弹窗区域。")
    return formatted_popups


def detect_popups_detail(image_path: str) -> Dict[str, Dict[str, Tuple[int, int]]]:
    """
    检测图像中的弹窗（优先横幅），只返回第一个检测到的弹窗，输出详细调试信息。
    Args:
        image_path: 图像的本地文件路径。
    Returns:
        dict: {"popup_1": {"left_top": (x, y), "right_bottom": (x, y)}}
    """
    logger.info(f"开始详细检测弹窗: {image_path}")
    popup_regions = _detect_all_popup_regions(image_path, debug=True)
    formatted_popups = {}
    if popup_regions:
        region = popup_regions[0]
        logger.info(f"详细检测到弹窗区域: {region}")
        formatted_popups["popup_1"] = {
            "left_top": region['left_top'],
            "right_bottom": region['right_bottom']
        }
    else:
        logger.info("未检测到弹窗区域（详细模式）。")
    return formatted_popups

if __name__ == "__main__":
    # 可以在这里测试新的详细模式函数
    test_image_path = "/Users/<USER>/Desktop/小象浮层弹窗.webp"
    print(f"--- 使用 detect_popups ---")
    results = detect_popups(test_image_path)
    print(results)

    print(f"\n--- 使用 detect_popups_detail ---")
    results_detail = detect_popups_detail(test_image_path)
    print(results_detail)
