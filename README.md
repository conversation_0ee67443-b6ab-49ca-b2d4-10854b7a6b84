# LangChain Ollama Agent 项目

基于Ollama和LangChain构建的本地AI Agent系统，专为移动设备UI自动化测试设计，支持并发任务处理和多轮对话。

## 🚀 核心特性

### 🔥 **并发任务处理系统** ⭐ NEW
- **双任务并发**: 支持最多2个任务同时运行，2倍处理效率
- **智能模式切换**: 自动检测并发能力，失败时回退到单例模式
- **任务隔离**: 每个任务独立的日志管理器和执行环境
- **负载均衡**: 智能选择空闲Agent实例，优化资源利用
- **轮次管理**: 每个任务独立的轮次ID和日志目录

### 🧠 **智能AI交互**
- **本地LLM**: 使用Ollama运行本地大语言模型
- **多轮对话**: 支持上下文感知的连续对话  
- **Function Calling**: 支持工具调用和函数执行
- **MLX-VLM集成**: 基于Apple Silicon优化的本地视觉语言模型

### 📱 **移动设备自动化**
- **跨平台支持**: 统一的Android和iOS设备管理
- **24/7设备状态保持**: 智能端口分配和设备状态监控
- **页面布局分析**: 95%+压缩率的XML到文本转换
- **智能交互**: 截图、点击、滑动、文本输入一体化

### 🛠️ **系统优化**
- **Driver保活机制**: 10分钟超时配置，自动连接池优化
- **API OCR识别**: 基于美团内部OCR API的高精度文字识别
- **消息通知**: 集成DX消息和邮件通知功能
- **实时日志分析**: 自动监控和上传测试结果

## 系统要求

### 基础环境
- **Python**: 3.8+
- **Ollama**: 已安装并运行（支持多实例）
- **操作系统**: macOS (推荐，MLX-VLM需要Apple Silicon)

### 并发模式要求
- **CPU**: 推荐12核心以上（最低8核心）
- **内存**: 推荐32GB以上（最低16GB，需运行2个模型实例）
- **存储**: ~20GB空间用于模型存储

### 依赖模型
- **主要模型**: `qwen3-14b-40k` (并发模式)
- **MLX模型**: Qwen2.5-VL-7B-Instruct-4bit (本地视觉分析)

## 🚀 快速开始

### 方法一: 并发模式启动 (推荐) ⭐

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动并发模式
python ollama_simple_manager.py  # 启动双Ollama实例(端口11435,11436)

# 3. 启动API服务器
python api_server.py  # 自动检测并启用并发模式
```

### 方法二: 传统单例模式

```bash
# 1. 启动Ollama服务
ollama serve

# 2. 下载模型
ollama pull qwen3-14b-40k

# 3. 启动API服务器
python api_server.py  # 自动回退到单例模式
```

### 验证系统状态

```bash
# 检查服务器模式
curl http://localhost:5630/
# 返回 "concurrent_mode": true 表示并发模式已启用

# 查看并发状态
curl http://localhost:5630/pool_status

# 检查Ollama实例状态
curl http://localhost:5630/ollama_status
```

## 🎯 核心功能详解

### 📋 并发任务处理 ⭐ NEW

#### 架构概览

项目实现了**双任务并发处理**功能，通过以下组件协同工作：

| 组件 | 文件 | 功能 |
|------|------|------|
| **Ollama实例管理** | `ollama_simple_manager.py` | 管理端口11435和11436上的双实例 |
| **并发任务管理器** | `tools/_concurrent_task_manager.py` | 任务生命周期和状态管理 |
| **并发日志管理器** | `tools/_concurrent_log_manager.py` | 每个任务独立的日志隔离 |
| **并发Agent池** | `concurrent_agent_pool.py` | Agent实例池和负载均衡 |
| **智能任务规划器** | `agent_json_planer.py` | 自然语言到结构化计划转换 |
| **API服务器** | `api_server.py` | 智能模式切换和任务分发 |

#### 关键特性

```bash
# 并发测试示例
# 任务1和任务2将同时执行
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "任务1", "mis_id": "test1"}'

curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "任务2", "mis_id": "test2"}'

# 第3个任务会被拒绝（超出2个任务限制）
```

#### 日志隔离机制

每个并发任务都有独立的日志目录：
```
log/
├── round_000190_20250725_161512/  # 任务1独立日志
│   ├── agent.log
│   ├── tools.log
│   └── unified.log
├── round_000191_20250725_161520/  # 任务2独立日志
│   ├── agent.log
│   ├── tools.log
│   └── unified.log
```

### 📱 页面布局分析优化 ⭐

#### XML到文本转换

实现了**95%+压缩率**的页面布局分析：

| 平台 | 原始大小 | 转换后大小 | 压缩率 | 提取元素 |
|------|----------|------------|--------|----------|
| **Android** | 94,656字符 | 4,657字符 | **95.1%** | 76个元素 |
| **iOS** | 61,124字符 | 3,406字符 | **94.4%** | 44个元素 |

#### 转换效果示例

```
原始XML (冗余信息):
<TextView android:layout_width="wrap_content" android:layout_height="wrap_content" 
          android:text="搜索" android:id="@+id/search_button" ... />

转换后 (核心信息):
1. TextView text="搜索" pos=(970,294) size=179x102 interactive=clickable id=search_button
```

#### MLX-VLM提示优化

针对Apple Silicon优化的视觉分析模型，现在包含：

- **平台特定元素类型详解**: Android (TextView, ImageView) vs iOS (Button, StaticText)
- **属性字段说明**: text/name, desc/label, pos=(x,y), size=宽x高
- **智能查找策略**: 直接文本匹配 → 功能推理 → 标识符匹配 → 位置分析

### 🔄 设备驱动优化 ⭐

#### Driver保活机制

解决设备会话超时问题：

```python
# 关键优化参数
new_command_timeout = 600  # 从5分钟增加到10分钟
keepalive_interval = 10    # 每10秒保活一次（从5秒优化）
keepalive_command = driver.get_window_size()  # 无害保活指令
```

#### 连接池优化

解决urllib3连接池满警告：

```python
# 优化前: 大量警告日志
"Connection pool is full, discarding connection: localhost"

# 优化后: 消除警告，提升性能
- 增加保活间隔到10秒
- 禁用urllib3警告日志
- 自动清理失效连接
```

### 🔍 智能工具系统

#### 工具调用统计

| 工具类别 | 数量 | 主要功能 |
|---------|------|----------|
| **设备管理** | 5个 | 设备发现、连接、状态管理 |
| **UI交互** | 6个 | 截图、点击、滑动、文本输入 |
| **内容分析** | 4个 | OCR识别、页面检查、元素查找 |
| **应用操作** | 3个 | 应用重启、后台切换 |
| **流程记录** | 3个 | 流程总结、问题跟踪 |

#### 统一架构模式

所有工具遵循统一设计模式：

```python
def unified_tool(udid: str, *args) -> str:
    """统一工具接口，自动适配平台"""
    device_status = get_device_status(udid)
    platform = device_status.get('platform', '').lower()
    
    if platform == 'android':
        return tool_android(udid, *args)
    elif platform == 'ios':
        return tool_ios(udid, *args)
    else:
        raise ValueError(f"不支持的平台类型: {platform}")
```


## 📊 API接口文档

### 🌐 RESTful API服务器

API服务器运行在**5630端口**，支持局域网访问和并发任务处理：

```bash
# 服务器地址
📡 API服务: http://localhost:5630
📚 API文档: http://localhost:5630/docs  
🔧 RedDoc文档: http://localhost:5630/redoc
```

### 核心API端点

#### 1. 并发任务提交 - `/submit_task`

```bash
# 提交并发任务（支持同时2个任务）
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "请你按照下面的步骤操作: 1.连接Android手机并初始化; 2.截图并OCR识别; 3.点击搜索框",
    "mis_id": "test_001"
  }'

# 响应示例
{
  "round_id": "round_000190_20250725_161512",
  "message": "任务已提交并分配，轮次: round_000190_20250725_161512 - 已分配给Agent"
}
```

#### 1.1. 智能任务提交 - `/submit_smart_task` ⭐ NEW

```bash
# 智能任务提交（自动转换自然语言为执行计划）
curl -X POST "http://localhost:5630/submit_smart_task" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "点击左上角的地址，校验地址选择页搜索框默认文案为\"搜索城市/区县/地点\"，点击搜索框，输入\"北京\"",
    "mis_id": "smart_test_001"
  }'

# 响应示例（完全兼容原接口）
{
  "round_id": "round_000191_20250801_143022",
  "message": "智能任务已提交并分配，轮次: round_000191_20250801_143022 - 已分配给Agent"
}
```

#### 1.2. 执行计划生成 - `/generate_plan` ⭐ NEW

```bash
# 生成执行计划（仅转换，不执行）
curl -X POST "http://localhost:5630/generate_plan" \
  -H "Content-Type: application/json" \
  -d '{
    "natural_language": "点击搜索框，输入火锅，点击搜索结果第一个商家",
    "platform": "ios"
  }'

# 响应示例
{
  "status": "success",
  "plan": {
    "plan_id": "plan_abc123",
    "platform": "ios",
    "total_steps": 6,
    "steps": [...]
  },
  "instructions": {
    "simple": "简化指令文本",
    "detailed": "详细指令文本"
  }
}
```

#### 2. 系统状态监控

```bash
# 检查API服务器基本信息
curl "http://localhost:5630/"
{
  "message": "Local Agent API Server",
  "version": "2.1.0",
  "concurrent_mode": true,
  "endpoints": {
    "submit_task": "POST /submit_task - 异步任务提交（支持并发）",
    "submit_smart_task": "POST /submit_smart_task - 智能任务提交（自动转换+执行）",
    "generate_plan": "POST /generate_plan - 自然语言转执行计划"
  }
}

# Agent池状态
curl "http://localhost:5630/pool_status"
{
  "concurrent_mode": true,
  "pool_status": {
    "total_agents": 2,
    "idle_agents": 1,
    "busy_agents": 1
  },
  "running_tasks": [...],
  "message": "并发Agent池运行正常，1/2 个Agent空闲"
}

# Ollama实例状态  
curl "http://localhost:5630/ollama_status"
{
  "concurrent_mode": true,
  "instance_status": {
    "11435": {"status": "running", "healthy": true},
    "11436": {"status": "running", "healthy": true}
  },
  "available_instances": [11435, 11436],
  "message": "Ollama实例状态正常，2/2 个实例健康"
}

# 测试状态查询
curl "http://localhost:5630/test_status"
{
  "is_running": true,
  "running_tasks_count": 2,
  "message": "有 2 个测试正在运行"
}
```

#### 3. 任务结果查询 - `/task_result/{task_id}`

```bash
# 查询异步任务结果
curl "http://localhost:5630/task_result/round_000190_20250725_161512"

# 任务完成响应
{
  "status": "completed",
  "result": "✅ 任务执行成功！已完成设备连接、截图识别和搜索框点击",
  "session_info": {
    "round_id": "round_000190_20250725_161512",
    "log_dir": "/Users/<USER>/log/round_000190_20250725_161512"
  }
}
```

### 🔥 并发处理能力

| 场景 | 传统模式 | 并发模式 | 性能提升 |
|------|----------|----------|----------|
| **单任务** | ✅ 正常执行 | ✅ 正常执行 | 1x |
| **双任务** | ❌ 串行等待 | ✅ 并行执行 | **~2x** |
| **智能任务** | ❌ 不支持 | ✅ 自动转换+执行 | **∞** |
| **超负载** | ❌ 直接拒绝 | 🚀 智能排队 | **∞** |

### 局域网发现

支持局域网内自动发现Agent服务器：

```bash
# 服务器确认接口
curl -X POST "http://************:5630/confirm" \
  -H "Content-Type: application/json" \
  -d '{"message": "aitest confirm"}'

# 成功响应
{"status": "confirmed", "message": "confirmed"}
```

### 🛠️ 核心工具集

#### 📱 设备管理工具
- **`find_available_device`**: 智能设备发现，支持Android/iOS自动识别
- **`start_device_test` / `end_device_test`**: 测试环境生命周期管理

#### 🎯 UI交互工具  
- **`take_screenshot`**: 跨平台截图，自动上传生成URL
- **`tap_device`**: 精确像素坐标点击，支持platform自适配
- **`slide_device`**: 比例坐标滑动，自动转换像素坐标
- **`input_text_smart`**: 智能文本输入，自动查找输入元素

#### 🧠 AI分析工具
- **`ocr_text_only`**: 基于美团OCR API的高精度文字识别
- **`check_page_display`**: MLX-VLM页面异常检测
- **`find_element_on_page`**: 基于自然语言的元素定位

#### 📋 应用操作工具
- **`restart_application`**: 智能应用重启
- **`app_background_switch`**: 应用后台切换测试
- **`record_agent_summary`**: 流程记录和问题跟踪

## 📋 使用示例

### 基本对话示例

```bash
# 设备连接和截图
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "连接可用设备并截图", "mis_id": "demo1"}'

# OCR文字识别
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "对当前页面进行OCR识别", "mis_id": "demo2"}'

# 页面异常检测
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "检查页面是否有展示异常", "mis_id": "demo3"}'
```

### 复合任务示例

```bash
# 完整的UI自动化流程
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "请按步骤执行: 1.连接iOS设备; 2.截图并OCR识别; 3.查找搜索框; 4.点击搜索框; 5.输入奶茶进行搜索",
    "mis_id": "automation_demo"
  }'
```

## ⚙️ 配置说明

### 并发模式配置

**Ollama实例配置** (`ollama_simple_manager.py`):
```python
DEFAULT_MODEL = "qwen3-14b-40k"         # 使用的模型
OLLAMA_PORTS = [11435, 11436]           # Ollama实例端口
OLLAMA_DIRS = ["~/.ollama1", "~/.ollama2"]  # 模型目录
```

**任务并发限制**:
```python
MAX_CONCURRENT_TASKS = 2  # 最大并发任务数
```

### MLX-VLM配置

**本地模型路径** (`check_page_detail_tools.py`):
```python
DEFAULT_MLX_MODEL = "/path/to/Qwen2.5-VL-7B-Instruct-4bit"
```

### OCR API配置

**美团内部OCR服务** (`api_ocr_tools.py`):
```python
OCR_API_URL = "http://qaassist.sankuai.com/compass/api/ocr/getUiPageParseResult"
```

### 设备白名单配置

**允许的设备列表** (`check_devices_tools.py`):
```python
# Android设备
allowed_android_devices = {"UQG5T20327008560", "your_device_id"}

# iOS设备  
allowed_ios_devices = {"00008140-000238321401801C", "your_device_id"}
```

## 📁 项目结构

```
langchain_ollama/
├── api_server.py                    # API服务器(支持并发模式)
├── agent_json_planer.py             # 智能任务规划器 ⭐ NEW
├── concurrent_agent.py              # 并发Agent实现
├── concurrent_agent_pool.py         # Agent实例池管理
├── ollama_simple_manager.py         # Ollama多实例管理
├── tools/
│   ├── _concurrent_task_manager.py  # 并发任务管理器
│   ├── _concurrent_log_manager.py   # 并发日志管理器
│   ├── _xml_to_text_tools.py        # XML转换工具(95%压缩率)
│   ├── check_page_detail_tools.py   # MLX-VLM页面分析工具
│   ├── _device_driver_manage_tools.py # Driver保活机制
│   ├── screenshot_tools.py          # 统一截图工具
│   ├── tap_tools.py                 # 统一点击工具
│   ├── slide_tools.py               # 统一滑动工具
│   ├── smart_input_tools.py         # 智能输入工具
│   ├── api_ocr_tools.py             # OCR识别工具
│   └── ...
├── log/                             # 日志目录
│   ├── round_XXXXXX_YYYYMMDD_HHMMSS/ # 轮次日志(并发隔离)
│   ├── smart_task_conversion/       # 智能任务转换日志 ⭐ NEW
│   ├── agent.log                    # Agent日志
│   ├── tools.log                    # 工具日志
│   └── unified.log                  # 统一日志
├── .platform_state.json            # 平台轮询状态(持久化) ⭐ NEW
├── status/                          # 设备状态文件
├── screenshot/                      # 截图文件
├── SMART_TASK_USAGE.md              # 智能任务功能说明 ⭐ NEW
└── requirements.txt                 # 项目依赖
```

### 📊 日志管理

#### 并发模式日志隔离
- **独立日志目录**: 每个任务创建独立的 `round_XXXXXX_YYYYMMDD_HHMMSS/` 目录
- **完全隔离**: 避免并发任务的日志交叉污染
- **自动清理**: 任务完成后自动清理临时资源

#### 日志查看
```bash
# 查看实时系统日志
tail -f log/unified.log

# 查看特定任务日志
tail -f log/round_000190_20250725_161512/agent.log

# 查看并发任务状态
ls -la log/ | grep round_
```

## 🔧 故障排除

### 常见问题

#### 1. 并发模式问题

**症状**: API返回 `"concurrent_mode": false`  
**解决方案**:
```bash
# 检查Ollama进程
ps aux | grep ollama

# 检查端口占用
netstat -an | grep 11435
netstat -an | grep 11436

# 重新启动并发模式
python ollama_simple_manager.py
```

#### 2. 任务提交被拒绝

**症状**: 返回"已达到最大并发任务数"  
**解决方案**:
```bash
# 检查当前任务状态
curl http://localhost:5630/pool_status

# 等待任务完成或重启服务
python api_server.py
```

#### 3. MLX-VLM分析失败

**症状**: 页面检查工具报错  
**解决方案**:
- 确保运行在Apple Silicon Mac上
- 检查MLX模型路径配置
- 确保有足够内存运行模型

#### 4. 设备连接问题

**症状**: 设备检测失败  
**解决方案**:
```bash
# Android设备
adb devices

# iOS设备  
idevice_id -l

# 检查设备白名单配置
```

#### 5. VPN连接问题

**症状**: 连接超时或502错误  
**解决方案**: 临时关闭VPN或代理软件

### 调试命令

```bash
# 系统状态检查
curl http://localhost:5630/

# 详细错误日志
tail -f log/unified.log

# 特定任务日志
tail -f log/round_XXXXXX_YYYYMMDD_HHMMSS/agent.log

# 并发状态监控
watch -n 2 "curl -s http://localhost:5630/pool_status | jq"
```

## 🎉 总结

**LangChain Ollama Agent项目**是一个功能完善的移动设备UI自动化测试平台，具有以下核心优势：

### ✨ 主要亮点

- **🔥 并发处理**: 双任务并发，2倍处理效率
- **🧠 智能任务**: 自然语言自动转换为结构化执行计划 ⭐ NEW
- **🎯 智能分析**: MLX-VLM + OCR双重页面分析能力  
- **📱 跨平台**: 统一的Android/iOS设备管理，支持持久化轮询
- **⚡ 高压缩**: 95%+的页面布局数据压缩率
- **🛡️ 稳定性**: Driver保活机制，10分钟超时保护

### 📊 技术指标

| 指标 | 数值 |
|------|------|
| **并发任务数** | 最多2个 |
| **智能任务转换** | 自然语言 → 结构化计划 ⭐ NEW |
| **平台轮询持久化** | 系统重启后状态恢复 ⭐ NEW |
| **XML压缩率** | 95.1% (Android), 94.4% (iOS) |
| **Driver超时** | 600秒 (10分钟) |
| **API端口** | 5630 |
| **支持平台** | Android + iOS |

### 🚀 快速上手

```bash
# 1. 启动并发模式
python ollama_simple_manager.py

# 2. 启动API服务器  
python api_server.py

# 3. 提交测试任务
curl -X POST "http://localhost:5630/submit_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "连接设备并进行UI自动化测试", "mis_id": "demo"}'

# 或者使用智能任务提交（自动转换自然语言）⭐ NEW
curl -X POST "http://localhost:5630/submit_smart_task" \
  -H "Content-Type: application/json" \
  -d '{"task_description": "点击搜索框，输入火锅，点击第一个搜索结果", "mis_id": "smart_demo"}'
```

### 📞 支持与反馈

- **API文档**: http://localhost:5630/docs
- **智能任务文档**: 查看 `SMART_TASK_USAGE.md` ⭐ NEW
- **项目结构**: 查看 `langchain_ollama/` 目录
- **日志调试**: `tail -f log/unified.log`
- **智能任务日志**: `ls log/smart_task_conversion/` ⭐ NEW
- **状态监控**: `curl http://localhost:5630/pool_status`

---

**注意**: 本项目专为移动设备UI自动化测试设计，请确保在使用时遵守相关法律法规和最佳实践。

## 许可证

MIT License

## 支持

如有问题或建议，请创建Issue或联系维护者。

---

**注意**: 此项目仅用于学习和研究目的，请确保在使用时遵守相关法律法规。