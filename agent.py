import json
import time
import re
import uuid
from typing import List, Dict, Any, Optional
from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from log_manager import log_manager
from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios, get_available_device
from tools.wait_tools import wait_for_seconds
from tools.screenshot_tools import get_screenshot
from tools.tap_tools import tap
from tools.slide_tools import slide
from tools.api_ocr_tools import show_page_ocr_result
from tools.app_operate_tools import restart_app, background_switch
from tools.smart_input_tools import smart_input_text
from tools._device_driver_manage_tools import get_device_driver, close_device_driver
from tools.llm_base_tools import start_test, end_test, get_test_driver, record_summary, record_issue
from tools._test_process_tools import start_new_test_session, end_current_test_session, get_session_status
from tools._device_status_manage_tools import (
    update_device_status, 
    create_device_status, 
    get_device_status,
    get_devices_by_status,
    manager as device_status_manager
)
from tools.check_page_detail_tools import check_page_display_mlx, find_element_detail_mlx

class LocalAgent:
    """本地Agent - 支持多轮对话和Function Calling"""
    
    def __init__(self, 
                 model_name: str = "qwen3-14b-40k",
                 base_url: str = "http://localhost:11434",
                 temperature: float = 0.1,
                 is_smart_task: bool = False):
        self.model_name = model_name
        self.base_url = base_url
        self.temperature = temperature
        self.chat_history = []
        self.is_smart_task = is_smart_task  # 标识是否为智能任务
        
        # 任务上下文管理
        self.current_round_id = None
        
        # 设备状态管理
        self.current_device_udid = None
        
        # 移除会话级驱动管理，统一使用全局驱动管理
        self.task_context = None  # 任务上下文
        
        # 初始化LLM
        self.llm = ChatOllama(
            model=model_name,
            base_url=base_url,
            temperature=temperature,
            # 配置 HTTP 客户端忽略环境变量，解决 VPN 环境下的 502 错误
            client_kwargs={"trust_env": False}
        )
        
        # 定义工具
        self.tools = self._create_tools()
        
        # 绑定工具到LLM
        try:
            self.llm_with_tools = self.llm.bind_tools(self.tools)
            self.supports_tools = True
            log_manager.info_agent(f"模型 {model_name} 支持工具调用")
        except Exception as e:
            log_manager.error_agent(f"绑定工具失败: {e}")
            self.llm_with_tools = self.llm
            self.supports_tools = False
        
        log_manager.info_agent(f"Agent初始化完成，模型: {model_name}")
        log_manager.info_agent(f"可用工具: {[tool.name for tool in self.tools]}")
    
    def _estimate_token_count(self, text: str) -> int:
        """估算文本的token数量 (简单估算：1 token ≈ 4 字符)"""
        # 对于中文和英文混合文本的粗略估算
        # 中文字符通常占用更多token，这里采用保守估算
        char_count = len(text)
        # 经验公式：英文约4字符/token，中文约1.5字符/token，混合取中间值
        estimated_tokens = int(char_count / 3)
        return estimated_tokens
    
    def _calculate_context_usage(self, messages: List) -> Dict[str, Any]:
        """计算当前上下文的使用情况"""
        total_chars = 0
        message_breakdown = []
        
        full_content_list = []
        for i, msg in enumerate(messages):
            content = ""
            if hasattr(msg, 'content'):
                content = str(msg.content)
            else:
                content = str(msg)
            
            chars = len(content)
            tokens = self._estimate_token_count(content)
            total_chars += chars
            full_content_list.append(content)
            
            msg_type = type(msg).__name__
            message_breakdown.append({
                "index": i,
                "type": msg_type,
                "chars": chars,
                "estimated_tokens": tokens,
                "content_preview": content[:100] + "..." if len(content) > 100 else content
            })
        
        # 使用完整内容来计算总token数，而不是preview
        total_tokens = self._estimate_token_count("".join(full_content_list))
        
        # qwen3模型的上下文限制
        context_limit = 40960  # 40k tokens
        usage_percentage = (total_tokens / context_limit) * 100
        
        return {
            "total_messages": len(messages),
            "total_characters": total_chars,
            "estimated_total_tokens": total_tokens,
            "context_limit": context_limit,
            "usage_percentage": round(usage_percentage, 2),
            "remaining_tokens": context_limit - total_tokens,
            "is_approaching_limit": usage_percentage > 80,
            "is_critical": usage_percentage > 95,
            "message_breakdown": message_breakdown
        }
    
    def _mark_device_testing(self, udid: str, device_name: str = "", platform: str = "") -> bool:
        """标记设备为测试中状态（内部方法）
        
        Args:
            udid: 设备UDID
            device_name: 设备名称（如果是新设备则必须提供）
            platform: 设备平台（如果是新设备则必须提供，ios/android）
            
        Returns:
            操作是否成功
        """
        try:
            # 检查设备是否已存在
            current_status = get_device_status(udid)
            
            if not current_status:
                # 设备不存在，需要创建
                if not device_name or not platform:
                    log_manager.error_agent(f"设备 {udid} 不存在，需要提供 device_name 和 platform 参数")
                    return False
                
                # 创建设备状态
                create_result = create_device_status(udid, device_name, platform)
                if not create_result:
                    log_manager.error_agent(f"创建设备状态失败: {udid}")
                    return False
                
                log_manager.info_agent(f"创建新设备状态: {udid} ({device_name}, {platform})")
            
            # 更新设备状态为测试中
            update_device_status(udid, {
                "status": "testing",
                "start_time": time.time()
            })
            
            log_manager.info_agent(f"设备状态更新: {udid} -> testing")
            return True
            
        except Exception as e:
            log_manager.error_agent(f"标记设备测试状态失败: {e}")
            return False
    
    def _mark_device_ready(self, udid: str) -> bool:
        """标记设备为就绪状态（内部方法）
        
        Args:
            udid: 设备UDID
            
        Returns:
            操作是否成功
        """
        try:
            # 检查设备是否存在
            current_status = get_device_status(udid)
            if not current_status:
                log_manager.error_agent(f"设备 {udid} 不存在，无法更新状态")
                return False
            
            # 计算测试时长
            start_time = current_status.get("start_time", 0)
            test_duration = time.time() - start_time if start_time > 0 else 0
            
            # 更新设备状态为就绪
            update_device_status(udid, {
                "status": "ready",
                "test_duration": test_duration,
                "start_time": 0.0  # 重置开始时间
            })
            
            log_manager.info_agent(f"设备状态更新: {udid} -> ready (测试时长: {test_duration:.1f}秒)")
            return True
            
        except Exception as e:
            log_manager.error_agent(f"标记设备就绪状态失败: {e}")
            return False
    
    
    def _cleanup_device_status(self):
        """清理设备状态（内部方法）- 将当前使用的设备恢复为就绪状态"""
        if self.current_device_udid:
            log_manager.info_agent(f"🔄 开始清理设备状态: {self.current_device_udid}")
            success = self._mark_device_ready(self.current_device_udid)
            if success:
                log_manager.info_agent(f"✅ 设备状态已恢复: {self.current_device_udid}")
            else:
                log_manager.error_agent(f"❌ 设备状态恢复失败: {self.current_device_udid}")
            self.current_device_udid = None
        else:
            log_manager.info_agent("📝 没有需要清理的设备状态")
    
    def _cleanup_global_drivers(self):
        """清理全局驱动管理器中的所有驱动（内部方法）"""
        try:
            log_manager.info_agent("🔄 开始清理全局驱动管理器中的驱动")
            
            # 调用全局驱动管理器的清理方法
            from tools._device_driver_manage_tools import close_all_device_drivers
            closed_count = close_all_device_drivers()
            
            if closed_count > 0:
                log_manager.info_agent(f"✅ 成功关闭了 {closed_count} 个全局驱动")
            else:
                log_manager.info_agent("📝 没有需要关闭的全局驱动")
                
        except Exception as e:
            log_manager.error_agent(f"❌ 清理全局驱动时出错: {e}")
    
    def _cleanup_screenshot_folder(self):
        """清理当前设备的截图文件夹（内部方法）"""
        try:
            log_manager.info_agent("🔄 开始执行截图文件夹清理")
            from tools._device_file_manage_tools import cleanup_device_screenshots
            success = cleanup_device_screenshots(self.current_device_udid)
            if success:
                log_manager.info_agent("✅ 截图文件夹清理完成")
            else:
                log_manager.warning_agent("⚠️ 截图文件夹清理失败")
        except Exception as e:
            log_manager.error_agent(f"❌ 调用截图清理工具时出错: {e}")
    
    def _cleanup_layout_logs(self, max_files: int = 20):
        """清理layout日志文件（内部方法）

        Args:
            max_files: 最大保留文件数，默认20
        """
        try:
            log_manager.info_agent("🔄 开始执行layout日志清理")
            from tools._device_file_manage_tools import cleanup_layout_logs
            success = cleanup_layout_logs(max_files)
            if success:
                log_manager.info_agent("✅ layout日志清理完成")
            else:
                log_manager.warning_agent("⚠️ layout日志清理失败")
        except Exception as e:
            log_manager.error_agent(f"❌ 调用layout日志清理工具时出错: {e}")

    def _cleanup_appium_logs(self, max_files: int = 10):
        """清理appium日志文件（内部方法）

        Args:
            max_files: 最大保留文件数，默认10
        """
        try:
            log_manager.info_agent("🔄 开始执行appium日志清理")
            from tools._device_file_manage_tools import cleanup_appium_logs
            success = cleanup_appium_logs(max_files)
            if success:
                log_manager.info_agent("✅ appium日志清理完成")
            else:
                log_manager.warning_agent("⚠️ appium日志清理失败")
        except Exception as e:
            log_manager.error_agent(f"❌ 调用appium日志清理工具时出错: {e}")

    def _cleanup_log_folders(self, max_rounds: int = 20):
        """清理过多的日志文件夹（内部方法）

        Args:
            max_rounds: 最大保留轮次数，默认20
        """
        try:
            log_manager.info_agent("🔄 开始执行日志文件夹清理")
            from tools._device_file_manage_tools import cleanup_log_folders
            success = cleanup_log_folders(max_rounds)
            if success:
                log_manager.info_agent("✅ 日志文件夹清理完成")
            else:
                log_manager.warning_agent("⚠️ 日志文件夹清理失败")
        except Exception as e:
            log_manager.error_agent(f"❌ 调用日志清理工具时出错: {e}")
    
    def _create_tools(self) -> List:
        """创建工具列表"""
        
        @tool
        def find_available_device(platform: str) -> str:
            """查找指定平台的可用设备（已连接且状态为ready）
            
            Args:
                platform: 设备平台，可选值: "ios" 或 "android"
                
            Returns:
                可用设备信息的JSON字符串
            """
            try:
                udid = get_available_device(platform)
                if udid:
                    # 获取设备详细信息
                    device_info = get_device_status(udid)
                    device_name = device_info.get("device_name", "Unknown") if device_info else "Unknown"
                    
                    # 自动标记设备为测试中状态
                    if self._mark_device_testing(udid, device_name, platform):
                        self.current_device_udid = udid
                        log_manager.info_agent(f"📱 自动标记设备为测试中: {udid}")
                        
                        result = {
                            "status": "success",
                            "platform": platform,
                            "udid": udid,
                            "device_name": device_name,
                            "message": f"找到可用的{platform}设备: {udid}，已自动标记为测试中"
                        }
                    else:
                        result = {
                            "status": "error",
                            "platform": platform,
                            "udid": udid,
                            "message": f"找到设备{udid}但标记为测试中状态失败"
                        }
                else:
                    result = {
                        "status": "not_found",
                        "platform": platform,
                        "message": f"没有找到可用的{platform}设备（已连接且状态为ready）"
                    }
                return json.dumps(result, ensure_ascii=False)
            except Exception as e:
                result = {
                    "status": "error",
                    "platform": platform,
                    "error": str(e),
                    "message": f"查找{platform}设备时出错: {str(e)}"
                }
                return json.dumps(result, ensure_ascii=False)
        
        
        @tool
        def wait_seconds(seconds: int = 5) -> str:
            """等待指定的秒数
            
            Args:
                seconds: 等待的秒数，默认为5秒，范围1-60秒
                
            Returns:
                等待完成的信息的JSON字符串
            """
            return wait_for_seconds(seconds)
        
        
        @tool
        def take_screenshot(udid: str, description: str = "") -> str:
            """对指定设备进行截图操作
            
            Args:
                udid: 设备UDID
                description: 截图描述信息（可选）
                
            Returns:
                截图操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始为设备 {udid} 进行截图")
                if description:
                    log_manager.info_agent(f"截图描述: {description}")
                
                # 执行截图操作
                screenshot_result = get_screenshot(udid)
                
                result = {
                    "status": "success",
                    "udid": udid,
                    "local_path": screenshot_result.get("local_path", ""),
                    "image_url": screenshot_result.get("image_url", ""),
                    "description": description,
                    "message": f"成功完成设备 {udid} 的截图操作"
                }
                
                if screenshot_result.get("image_url"):
                    log_manager.info_agent(f"截图已上传，URL: {screenshot_result['image_url']}")
                else:
                    log_manager.warning_agent("截图上传失败，但本地文件已保存")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"截图操作失败: {str(e)}"
                }
                log_manager.error_agent(f"截图操作失败: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        
        @tool
        def start_device_test(udid: str) -> str:
            """开始设备测试 - 为指定设备创建和维护driver
            
            Args:
                udid: 设备UDID
                
            Returns:
                测试启动结果的JSON字符串
            """
            return start_test(udid)
        
        
        @tool
        def end_device_test(udid: str) -> str:
            """结束设备测试 - 关闭指定设备的driver并清理资源
            
            Args:
                udid: 设备UDID
                
            Returns:
                测试结束结果的JSON字符串
            """
            return end_test(udid)
        
        
        @tool
        def tap_device(udid: str, x: int, y: int, description: str = "") -> str:
            """在指定设备上执行点击操作
            
            Args:
                udid: 设备UDID
                x: 点击位置的x坐标
                y: 点击位置的y坐标
                description: 点击操作描述信息（可选）
                
            Returns:
                点击操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始对设备 {udid} 执行点击操作，坐标: ({x}, {y})")
                if description:
                    log_manager.info_agent(f"点击描述: {description}")
                
                # 执行点击操作
                success = tap(udid, x, y)
                
                if success:
                    result = {
                        "status": "success",
                        "udid": udid,
                        "x": x,
                        "y": y,
                        "description": description,
                        "message": f"成功在设备 {udid} 的坐标 ({x}, {y}) 执行点击操作"
                    }
                    log_manager.info_agent(f"点击操作成功完成")
                else:
                    result = {
                        "status": "failed",
                        "udid": udid,
                        "x": x,
                        "y": y,
                        "description": description,
                        "message": f"在设备 {udid} 的坐标 ({x}, {y}) 点击操作失败"
                    }
                    log_manager.warning_agent(f"点击操作失败")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "x": x,
                    "y": y,
                    "error": str(e),
                    "message": f"点击操作出现异常: {str(e)}"
                }
                log_manager.error_agent(f"点击操作异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        
        @tool
        def slide_device(udid: str, from_x: float, from_y: float, to_x: float, to_y: float, duration: float = 0.5, description: str = "") -> str:
            """在指定设备上执行滑动操作
            
            **智能滑动范围选择**：
            如果用户没有提供明确的滑动范围，你可以根据以下上下文信息自主选择合适的滑动范围：
            - 当前屏幕内容（通过OCR识别结果）
            - 测试目标和意图（如向下滚动查看更多内容、向左滑动翻页等）
            - 常见的滑动模式（如页面滚动、抽屉拉出、轮播图切换等）
            
            **常用滑动范围参考**：
            - 向下滚动页面：from_x=0.5, from_y=0.7, to_x=0.5, to_y=0.3
            - 向上滚动页面：from_x=0.5, from_y=0.3, to_x=0.5, to_y=0.7
            
            Args:
                udid: 设备UDID
                from_x: 起始点的x坐标比例（0~1）
                from_y: 起始点的y坐标比例（0~1）
                to_x: 结束点的x坐标比例（0~1）
                to_y: 结束点的y坐标比例（0~1）
                duration: 滑动持续时间，单位为秒，默认0.5秒
                description: 滑动操作描述信息（可选）
                
            Returns:
                滑动操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始对设备 {udid} 执行滑动操作，从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})，持续 {duration}秒")
                if description:
                    log_manager.info_agent(f"滑动描述: {description}")
                
                # 执行滑动操作
                success = slide(udid, from_x, from_y, to_x, to_y, duration)
                
                if success:
                    result = {
                        "status": "success",
                        "udid": udid,
                        "from_x": from_x,
                        "from_y": from_y,
                        "to_x": to_x,
                        "to_y": to_y,
                        "duration": duration,
                        "description": description,
                        "message": f"成功在设备 {udid} 上执行滑动操作：从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})"
                    }
                    log_manager.info_agent(f"滑动操作成功完成")
                else:
                    result = {
                        "status": "failed",
                        "udid": udid,
                        "from_x": from_x,
                        "from_y": from_y,
                        "to_x": to_x,
                        "to_y": to_y,
                        "duration": duration,
                        "description": description,
                        "message": f"在设备 {udid} 上滑动操作失败：从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})"
                    }
                    log_manager.warning_agent(f"滑动操作失败")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "from_x": from_x,
                    "from_y": from_y,
                    "to_x": to_x,
                    "to_y": to_y,
                    "duration": duration,
                    "error": str(e),
                    "message": f"滑动操作出现异常: {str(e)}"
                }
                log_manager.error_agent(f"滑动操作异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def ocr_text_only(udid: str) -> str:
            """对指定设备进行OCR文字识别，返回结构化结果
            
            Args:
                udid: 设备UDID
                
            Returns:
                包含status、udid、text_result、image_url的结构化JSON字符串
            """
            try:
                log_manager.info_agent(f"开始为设备 {udid} 进行OCR文本识别")
                
                # 执行OCR文本识别，获取完整结构化结果（包含截图信息）
                ocr_result = show_page_ocr_result(udid)
                text_result = ocr_result.get("final_text", "")
                image_url = ocr_result.get("image_url", "")
                
                # 构建结构化结果
                result = {
                    "status": "success",
                    "udid": udid,
                    "text_result": text_result,
                    "image_url": image_url
                }
                
                log_manager.info_agent(f"设备 {udid} OCR文本识别完成")
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"OCR文本识别失败: {str(e)}"
                }
                log_manager.error_agent(f"OCR文本识别异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def check_page_display(udid: str, scene_desc: str = "页面检查") -> str:
            """使用MLX-VLM模型检查页面展示是否正常（基于本地Qwen2.5-VL模型）
            
            Args:
                udid: 设备UDID
                scene_desc: 场景描述
                
            Returns:
                包含status、udid、text_result、image_url的结构化JSON字符串
            """
            try:
                log_manager.info_agent(f"开始检查设备 {udid} 的页面展示")
                if scene_desc:
                    log_manager.info_agent(f"场景描述: {scene_desc}")
                
                # 调用MLX-VLM页面检查（返回结构化结果）
                check_result = check_page_display_mlx(udid, scene_desc)
                
                if check_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": check_result["text_result"],
                        "image_url": check_result.get("image_url", ""),
                        "message": f"页面检查失败: {check_result['text_result']}"
                    }
                    log_manager.error_agent(f"页面检查失败: {check_result['text_result']}")
                else:
                    # 判断检查结果
                    text_result = check_result["text_result"]
                    is_normal = "页面展示正常" in text_result
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": check_result.get("image_url", ""),
                        "is_normal": is_normal,
                        "message": f"页面检查完成: {'正常' if is_normal else '异常'}"
                    }
                    log_manager.info_agent(f"页面检查完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"页面检查出现异常: {str(e)}"
                }
                log_manager.error_agent(f"页面检查异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        
        @tool
        def find_element_on_page(udid: str, element: str, scene_desc: str = "元素查找") -> str:
            """使用MLX-VLM模型在页面上查找指定元素（基于本地Qwen2.5-VL模型）
            
            Args:
                udid: 设备UDID
                element: 要查找的元素名称或描述
                scene_desc: 场景描述
                
            Returns:
                包含status、udid、text_result、image_url的结构化JSON字符串
            """
            try:
                log_manager.info_agent(f"开始在设备 {udid} 上查找元素: {element}")
                if scene_desc:
                    log_manager.info_agent(f"场景描述: {scene_desc}")
                
                # 调用MLX-VLM元素查找（返回结构化结果）
                find_result = find_element_detail_mlx(udid, element, scene_desc)
                
                if find_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": find_result["text_result"],
                        "image_url": find_result.get("image_url", ""),
                        "message": f"元素查找失败: {find_result['text_result']}"
                    }
                    log_manager.error_agent(f"元素查找失败: {find_result['text_result']}")
                else:
                    # 判断是否找到元素
                    text_result = find_result["text_result"]
                    found = not text_result.startswith("未找到")
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": find_result.get("image_url", ""),
                        "found": found,
                        "element": element,
                        "message": f"元素查找完成: {'找到' if found else '未找到'}"
                    }
                    log_manager.info_agent(f"元素查找完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"元素查找出现异常: {str(e)}"
                }
                log_manager.error_agent(f"元素查找异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        
        @tool
        def restart_application(udid: str) -> str:
            """重启指定设备上的应用
            
            Args:
                udid: 设备UDID
                
            Returns:
                重启应用操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始重启设备 {udid} 上的应用")
                
                # 执行重启应用操作
                result = restart_app(udid)
                
                if result == "success":
                    response = {
                        "status": "success",
                        "udid": udid,
                        "message": f"成功重启设备 {udid} 上的应用"
                    }
                    log_manager.info_agent(f"应用重启成功完成")
                elif result == "unknown_device":
                    response = {
                        "status": "error",
                        "udid": udid,
                        "error": "unknown_device",
                        "message": f"设备 {udid} 不存在或未配置"
                    }
                    log_manager.error_agent(f"设备 {udid} 不存在")
                elif result == "device_offline":
                    response = {
                        "status": "error", 
                        "udid": udid,
                        "error": "device_offline",
                        "message": f"设备 {udid} 已掉线"
                    }
                    log_manager.error_agent(f"设备 {udid} 掉线")
                else:
                    response = {
                        "status": "failed",
                        "udid": udid,
                        "error": result,
                        "message": f"重启设备 {udid} 应用失败: {result}"
                    }
                    log_manager.warning_agent(f"应用重启失败: {result}")
                
                return json.dumps(response, ensure_ascii=False)
                
            except Exception as e:
                error_response = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"重启应用操作出现异常: {str(e)}"
                }
                log_manager.error_agent(f"重启应用操作异常: {e}")
                return json.dumps(error_response, ensure_ascii=False)
        
        @tool
        def app_background_switch(udid: str) -> str:
            """让美团应用退到后台，等待3秒后再切回前台
            
            Args:
                udid: 设备UDID
                
            Returns:
                后台切换操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始执行应用后台切换操作 - 设备: {udid}")
                
                # 执行后台切换操作
                result = background_switch(udid)
                
                if result == "success":
                    response = {
                        "status": "success",
                        "udid": udid,
                        "message": f"成功完成设备 {udid} 的应用后台切换操作"
                    }
                    log_manager.info_agent(f"应用后台切换操作成功完成")
                elif result == "unknown_device":
                    response = {
                        "status": "error",
                        "udid": udid,
                        "error": "unknown_device",
                        "message": f"设备 {udid} 不存在或未配置"
                    }
                    log_manager.error_agent(f"设备 {udid} 不存在")
                elif result == "device_offline":
                    response = {
                        "status": "error", 
                        "udid": udid,
                        "error": "device_offline",
                        "message": f"设备 {udid} 已掉线"
                    }
                    log_manager.error_agent(f"设备 {udid} 掉线")
                else:
                    response = {
                        "status": "failed",
                        "udid": udid,
                        "error": result,
                        "message": f"设备 {udid} 后台切换操作失败: {result}"
                    }
                    log_manager.warning_agent(f"应用后台切换操作失败: {result}")
                
                return json.dumps(response, ensure_ascii=False)
                
            except Exception as e:
                error_response = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"后台切换操作出现异常: {str(e)}"
                }
                log_manager.error_agent(f"后台切换操作异常: {e}")
                return json.dumps(error_response, ensure_ascii=False)
        
        @tool
        def record_agent_summary(summary_text: str, summary_type: str = "步骤总结") -> str:
            """记录Agent的实时流程总结和想法
            
            Args:
                summary_text: Agent生成的流程总结文本（如：当前步骤结果、测试进展、下一步计划等）
                summary_type: 总结类型（如："步骤总结"、"流程分析"、"计划制定"、"最终结论"等）
                
            Returns:
                记录操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始记录Agent流程总结: {summary_type}")
                
                # 调用底层记录函数
                result = record_summary(summary_text, summary_type)
                
                log_manager.info_agent(f"Agent流程总结记录完成")
                return result
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "summary_text": summary_text,
                    "summary_type": summary_type,
                    "error": str(e),
                    "message": f"记录Agent流程总结失败: {str(e)}"
                }
                log_manager.error_agent(f"记录Agent流程总结异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        
        @tool
        def record_test_issue(issue_text: str, issue_type: str = "测试问题", severity: str = "中等") -> str:
            """记录测试过程中发现的问题
            
            Args:
                issue_text: 发现的具体问题描述（如：页面展示异常、功能无法使用、性能问题等）
                issue_type: 问题类型，可选值：
                           - "功能异常": 功能不可用或行为异常
                           - "UI问题": 界面展示问题、布局异常等
                           - "性能问题": 响应慢、卡顿等性能相关
                           - "兼容性问题": 设备或版本兼容性问题
                           - "数据问题": 数据错误、缺失等
                           - "其他问题": 其他未分类问题
                severity: 问题严重程度，可选值："严重"、"中等"、"轻微"
                
            Returns:
                记录操作结果的JSON字符串
            """
            try:
                log_manager.warning_agent(f"开始记录测试问题: {issue_type}, 严重程度: {severity}")
                
                # 调用底层记录函数
                result = record_issue(issue_text, issue_type, severity)
                
                log_manager.warning_agent(f"测试问题记录完成")
                return result
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "issue_text": issue_text,
                    "issue_type": issue_type,
                    "severity": severity,
                    "error": str(e),
                    "message": f"记录测试问题失败: {str(e)}"
                }
                log_manager.error_agent(f"记录测试问题异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)


        @tool
        def input_text_smart(udid: str, text: str, element_index: int = None) -> str:
            """智能文本输入工具

            功能说明：
            - 自动查找页面中的输入元素
            - 如果只有一个输入元素，直接输入文本
            - 如果有多个输入元素且未指定索引，返回所有元素信息供选择
            - 如果指定了元素索引，在对应元素中输入文本

            重要使用说明：
            - 第一次调用时，不要传递 element_index 参数，让工具自动判断页面中的输入元素
            - 如果工具返回 "multiple_elements_found" 状态，说明页面有多个输入元素
            - 此时需要第二次调用该工具，并从返回的元素列表中选择一个 element_index
            - 只有在第一次调用返回多个元素时，才需要在第二次调用中指定 element_index

            Args:
                udid: 设备UDID
                text: 要输入的文本内容（支持中文）
                element_index: 可选，指定输入元素的索引。仅在第一次调用返回多个元素后的第二次调用时使用

            Returns:
                输入操作结果的JSON字符串
            """
            try:
                log_manager.info_agent(f"开始智能文本输入操作 - 设备: {udid}, 文本: '{text}', 元素索引: {element_index}")

                # 调用智能输入文本函数
                result = smart_input_text(udid, text, element_index)

                log_manager.info_agent(f"设备 {udid} 智能文本输入操作完成")
                return result

            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text": text,
                    "element_index": element_index,
                    "error": str(e),
                    "message": f"智能文本输入失败: {str(e)}"
                }
                log_manager.error_agent(f"智能文本输入异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)


        return [find_available_device, start_device_test, end_device_test, wait_seconds, take_screenshot, tap_device, slide_device, ocr_text_only, check_page_display, find_element_on_page, restart_application, app_background_switch, record_agent_summary, record_test_issue, input_text_smart]
    
    def _create_system_message(self) -> str:
        """创建系统消息"""
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description}")
        
        tools_text = "\n".join(tool_descriptions)
        
        if self.is_smart_task:
            # 智能任务：简化版系统提示词，避免重复规划和规则
            return f"""你是一个专业的移动设备测试执行助手。你的任务是严格按照提供的结构化测试计划执行操作。

可用工具：
{tools_text}

**核心执行原则：**
- 严格按照用户提供的步骤顺序执行，不要跳过或修改任何步骤
- 一次只调用一个工具，等待结果后再进行下一步
- 每个工具调用都要对应用户指令中的具体步骤
- 工具返回JSON格式数据，请解析并简洁回应

**步骤关联性要求：**
- **find_element + tap组合**: 当find_element找到元素后，下一步tap必须使用找到的坐标信息
- **设备一致性**: 确保所有操作都在同一设备上执行，udid保持一致
- **结果传递**: 上一步的执行结果可能会影响下一步的参数，请仔细阅读工具返回结果
- **坐标精确性**: tap_device时，优先使用find_element_on_page返回的坐标，而不是预设坐标

**特别注意：**
- 用户提供的是已经经过规划的结构化测试指令，请直接执行，无需额外规划
- 如果工具调用失败，尝试1-2次后继续下一步
- 完成所有步骤后调用record_agent_summary记录总结

请用中文回答，专注执行而非规划，特别注意步骤间的关联性。"""
        else:
            # 普通任务：完整版系统提示词，包含完整的规划和规则
            return f"""你是一个专业的移动设备测试助手。你可以使用以下工具来帮助用户：

工具说明：
{tools_text}

**重要的测试工作流程：**
1. **查找设备**: 使用 find_available_device 工具查找可用设备，每次会返回对应设备的 udid
2. **开始测试**: 获取到设备的 udid 后必须要使用 start_device_test 工具为选定的设备启动测试环境（不管是 ios 还是 Android），这一步是必须要做的，否则后续的工具调用就会出现问题
3. **执行操作**: 使用 take_screenshot、wait_seconds 等工具执行具体测试操作，具体使用什么工具，请根据用户的要求来决定
4. **记录问题**: 使用 record_test_issue 工具记录测试过程中发现的问题
5. **结束测试**: 当用户明确表示"完成测试"、"结束测试"或类似指示时，使用 end_device_test 工具结束测试
6. **记录结论**: 完成一轮测试后，要使用 record_agent_summary 工具记录之前步骤的操作流程，并做出一个测试总结，涵盖测试流程以及总体的结论

**关键执行规则：**
- **必须逐步执行**: 用户会给出包含多个步骤的操作指令，必须按顺序逐一执行每个步骤，不能跳过或提前结束
- **单一工具调用**: 一般来说我给你的指令都会比较复杂，需要多次调用工具才能完成，但是我希望你一次只调用一个工具，然后我会将其执行的结果告诉你，你再根据结果来决定下一步做什么，尽量不要一次调用多个工具
- **结束条件**: 当用户的步骤中明确包含"结束测试"、"完成测试"、"测试结束"等明确结束指示时，你要先调用 end_device_test 工具结束测试，然后调用 record_agent_summary 工具记录总结结论，
- **持续执行**: 在没有明确结束指示的情况下，按照用户最开始指令中的所有步骤，依次进行
- **错误情况**: 如果工具调用失败，尝试重复 1-2 次，仍然失败则结束测试

**工具使用规则：**
- 在使用 take_screenshot 之前，必须先调用 start_device_test 启动测试环境
- 只有在用户明确表示测试完成时，才能调用 end_device_test 结束测试
- 每个设备在一次会话中只需要调用一次 start_device_test 和 end_device_test
- 工具返回JSON格式的结构化数据，请解析并用友好的语言回答

**任务执行策略：**
1. 你要先完整理解用户的所有需求和步骤，然后有一个大致的任务执行规划
2. 一般来说所有的任务一开始都需要先查找可用设备，然后为选定设备启动测试环境
3. 一次只能执行一个工具，然后我会将其执行的结果告诉你，你再根据结果来决定下一步做什么
4. 当且仅当用户的步骤中明确表示"结束测试"或者类似语句时，才结束本次测试的流程，你要尽可能的按照用户指定的操作路径来完成一步步的操作（工具调用）
5. 一般来说用户的指令会是"1.做A; 2.做B; 3.做C; 4.结束测试"，你必须依次执行A、B、C，然后才能结束

**注意事项：**
- 测试会话的启动和结束将由系统自动管理
- 设备状态管理由系统自动处理
- 重要：每次开始新的测试会话时，系统会自动重启美团应用，确保测试从美团App首页开始
- 重要：每次调用工具前都重新查看用户要求，思考下一步工具调用是什么
- 阶段性总结不代表测试结束: 完成OCR识别、截图等中间操作后，可以进行阶段性总结，调用 record_agent_summary 来记录截断性的结论，但是你依然要思考下一个步骤是什么，来接着完成用户最开始的指令
- 遇到不确定的问题不要来询问用户: 你要时刻想着按照用户原初的指令来执行对应流程，遇到不确定的问题，请先思考，然后调用工具来解决，不要来询问用户下一步怎么做，直至你完成了全部流程

请用中文回答用户问题，并严格按照上述工作流程执行。"""
    
    def chat(self, user_input: str, mis_id: Optional[str] = None) -> str:
        """处理用户输入并生成响应 - 简化版本，不自动管理会话"""
        try:
            # 记录用户输入
            log_manager.info_agent(f"收到用户输入: {user_input}")
            
            # 添加用户消息到历史
            self.chat_history.append(HumanMessage(content=user_input))
            
            # 执行对话逻辑
            if self.supports_tools:
                response = self._chat_with_tools(user_input)
            else:
                response = self._chat_without_tools(user_input)
            
            # 清理设备状态
            self._cleanup_device_status()
            
            # 清理各种文件
            self._cleanup_screenshot_folder()
            self._cleanup_layout_logs()
            self._cleanup_appium_logs()
            self._cleanup_log_folders()
            
            return response
                
        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            log_manager.error_agent(error_msg)
            
            # 清理设备状态（错误情况下）
            self._cleanup_device_status()
            
            # 清理各种文件（错误时）
            self._cleanup_screenshot_folder()
            self._cleanup_layout_logs()
            self._cleanup_appium_logs()
            self._cleanup_log_folders()
            
            # 如果有进行中的任务，结束任务日志记录（错误状态）
            if log_manager.current_task_id:
                log_manager.end_task(status="错误")
            
            return error_msg
    
    def _chat_with_tools(self, user_input: str) -> str:
        """使用工具调用模式（支持多轮工具调用）"""
        # 开始任务结构化日志记录（会话管理已在上层处理）
        task_id = log_manager.start_task(user_input)
        
        # 构建完整的消息列表
        messages = [
            SystemMessage(content=self._create_system_message()),
            *self.chat_history
        ]
        
        # 计算并记录上下文使用情况
        context_usage = self._calculate_context_usage(messages)
        
        # 记录LLM详细输入
        log_manager.info_llm(f"发送消息到模型，消息数量: {len(messages)}", self.model_name)
        log_manager.info_llm(f"用户输入: {user_input}", self.model_name)
        
        # 记录上下文使用统计
        log_manager.info_agent(f"📊 上下文使用统计:")
        log_manager.info_agent(f"  总消息数: {context_usage['total_messages']}")
        log_manager.info_agent(f"  总字符数: {context_usage['total_characters']:,}")
        log_manager.info_agent(f"  估算token数: {context_usage['estimated_total_tokens']:,}")
        log_manager.info_agent(f"  使用率: {context_usage['usage_percentage']:.1f}% ({context_usage['estimated_total_tokens']}/{context_usage['context_limit']})")
        log_manager.info_agent(f"  剩余token: {context_usage['remaining_tokens']:,}")
        
        if context_usage['is_critical']:
            log_manager.error_agent("🚨 上下文使用率超过95%，可能导致模型遗忘早期信息!")
        elif context_usage['is_approaching_limit']:
            log_manager.warning_agent("⚠️ 上下文使用率超过80%，建议注意context长度")
        
        log_manager.log_llm_input(messages, self.model_name)
        
        # 支持多轮工具调用的循环
        max_rounds = 20  # 防止无限循环
        max_format_corrections = 3  # 最大格式纠正次数
        round_count = 0
        format_correction_count = 0
        consecutive_failures = 0
        last_error_type = None
        
        while round_count < max_rounds:
            round_count += 1
            
            # 开始新轮次的结构化日志记录
            current_round = log_manager.start_round()
            log_manager.info_agent(f"━━━ 开始第 {current_round} 轮执行 ━━━")
            
            # 调用LLM（可能包含工具调用）
            log_manager.info_agent(f"正在询问模型下一步操作...")
            response = self.llm_with_tools.invoke(messages)
            
            # 记录LLM详细输出
            log_manager.log_llm_output(response, self.model_name)
            
            # 增强的工具调用检测和解析逻辑
            parsed_alternative_tools = []
            detected_format = None
            
            # 1. 首先检查标准工具调用
            if hasattr(response, 'tool_calls') and response.tool_calls:
                log_manager.info_agent(f"✅ 检测到标准工具调用: {[tc['name'] for tc in response.tool_calls]}")
                log_manager.log_model_decision(tool_calls=response.tool_calls, direct_reply=False)
            
            # 2. 如果没有标准调用，尝试解析非标准格式
            elif hasattr(response, 'content') and response.content:
                content = response.content
                log_manager.info_agent("🔍 尝试解析非标准工具调用格式...")
                
                # 检测各种非标准格式
                if '```tool_call' in content:
                    detected_format = "gemma3_tool_format"
                    log_manager.info_agent("🔍 检测到 gemma3 工具调用格式")
                elif '{"name":' in content and ('"parameters":' in content or '"args":' in content):
                    detected_format = "text_with_tool_pattern"
                    log_manager.info_agent("🔍 检测到文本中的工具调用模式")
                
                # 尝试解析非标准格式
                if detected_format:
                    parsed_alternative_tools = self._parse_alternative_tool_formats(content)
                    
                    if parsed_alternative_tools:
                        # 成功解析，直接使用
                        response.tool_calls = parsed_alternative_tools
                        log_manager.info_agent(f"✅ 成功解析并转换非标准工具调用格式")
                        log_manager.log_model_decision(tool_calls=parsed_alternative_tools, direct_reply=False)
                    else:
                        # 检测到但解析失败，考虑格式纠正
                        if format_correction_count < max_format_corrections:
                            format_correction_count += 1
                            log_manager.warning_agent(f"⚠️ 检测到非标准格式但解析失败，进行第 {format_correction_count} 次格式纠正")
                            
                            # 创建格式纠正提示
                            correction_prompt = self._create_format_correction_prompt(detected_format, content)
                            
                            # 添加格式纠正提示到历史
                            self.chat_history.append(response)
                            correction_message = SystemMessage(content=correction_prompt)
                            self.chat_history.append(correction_message)
                            
                            # 更新消息列表，让模型重新生成
                            messages = [
                                SystemMessage(content=self._create_system_message()),
                                *self.chat_history
                            ]
                            
                            log_manager.warning_agent(f"🔄 发送格式纠正提示，要求模型重新生成...")
                            # 继续下一轮，让模型重新生成
                            continue
                        else:
                            # 格式纠正次数超限，记录错误并尝试结束
                            log_manager.error_agent(f"❌ 格式纠正次数已达上限 ({max_format_corrections})，无法解析工具调用")
                            consecutive_failures += 1
                            
                            # 直接当作文本回复处理
                            log_manager.info_agent(f"📝 将响应当作文本回复处理")
                            log_manager.log_model_decision(direct_reply=True)
                else:
                    # 确实没有检测到任何工具调用模式
                    log_manager.info_agent(f"📝 确认为纯文本回复，没有检测到工具调用模式")
                    log_manager.log_model_decision(direct_reply=True)
            
            # 3. 既没有标准调用，也没有内容
            else:
                log_manager.warning_agent(f"⚠️ 模型响应为空或无内容")
                log_manager.log_model_decision(direct_reply=True)
            
            # 检查是否有工具调用
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # 每轮只执行第一个工具调用，确保真正的分步执行
                tool_call = response.tool_calls[0]
                tool_name = tool_call.get('name', 'unknown')
                
                if len(response.tool_calls) > 1:
                    log_manager.info_agent(f"⚠️  模型请求了 {len(response.tool_calls)} 个工具，但本轮只执行第一个: '{tool_name}'")
                else:
                    log_manager.info_agent(f"🔧 准备执行工具: '{tool_name}'")
                
                # 添加AI响应到历史
                self.chat_history.append(response)
                
                # 执行单个工具调用
                log_manager.info_agent(f"⏳ 正在执行工具 '{tool_name}'...")
                
                # 记录工具执行开始时间
                tool_start_time = time.time()
                tool_result = self._execute_tool_call(tool_call)
                tool_duration = time.time() - tool_start_time
                
                # 检查工具执行结果
                tool_success = False
                try:
                    import json
                    result_data = json.loads(tool_result)
                    if 'message' in result_data:
                        result_summary = result_data['message']
                    else:
                        result_summary = tool_result[:100] + "..." if len(tool_result) > 100 else tool_result
                    
                    # 判断工具是否执行成功
                    if result_data.get('status') in ['success', 'completed']:
                        tool_success = True
                        consecutive_failures = 0  # 重置连续失败计数
                        last_error_type = None
                    elif result_data.get('status') in ['error', 'failed']:
                        consecutive_failures += 1
                        last_error_type = "tool_execution_failed"
                        log_manager.warning_agent(f"⚠️ 工具执行失败，连续失败次数: {consecutive_failures}")
                    else:
                        # 状态不明确，暂时算作成功
                        tool_success = True
                        consecutive_failures = 0
                        
                except:
                    # JSON解析失败，根据内容判断
                    result_summary = tool_result[:100] + "..." if len(tool_result) > 100 else tool_result
                    if 'error' in tool_result.lower() or 'failed' in tool_result.lower():
                        consecutive_failures += 1
                        last_error_type = "tool_execution_failed"
                        log_manager.warning_agent(f"⚠️ 工具执行可能失败，连续失败次数: {consecutive_failures}")
                    else:
                        tool_success = True
                        consecutive_failures = 0
                
                # 记录结构化工具执行日志
                log_manager.log_tool_execution(
                    tool_name=tool_name,
                    tool_args=tool_call.get('args', {}),
                    tool_result=tool_result,
                    duration=tool_duration
                )
                
                if tool_success:
                    log_manager.info_agent(f"✅ 工具 '{tool_name}' 执行成功")
                else:
                    log_manager.warning_agent(f"⚠️ 工具 '{tool_name}' 执行可能失败")
                    
                log_manager.info_agent(f"📝 工具返回结果: {result_summary}")
                
                # 记录工具执行结果
                log_manager.log_llm_tool_result(tool_name, tool_result, self.model_name)
                self.chat_history.append(
                    ToolMessage(
                        content=tool_result,
                        tool_call_id=tool_call.get('id', 'unknown')
                    )
                )
                
                # 检查是否需要提前终止
                if consecutive_failures >= 3:
                    log_manager.error_agent(f"❌ 连续失败次数过多 ({consecutive_failures})，可能存在系统性问题")
                    log_manager.error_agent(f"最后错误类型: {last_error_type}")
                    log_manager.error_agent("🛑 为避免浪费资源，提前终止执行")
                    break
                
                # 更新消息列表，准备下一轮
                messages = [
                    SystemMessage(content=self._create_system_message()),
                    *self.chat_history
                ]
                
                # 记录轮次结束时的上下文使用情况
                updated_context_usage = self._calculate_context_usage(messages)
                log_manager.info_agent(f"🔄 第 {round_count} 轮完成，当前上下文使用: {updated_context_usage['usage_percentage']:.1f}% ({updated_context_usage['estimated_total_tokens']}/{updated_context_usage['context_limit']})")
                
                if updated_context_usage['is_critical']:
                    log_manager.error_agent("🚨 上下文接近饱和，模型可能开始遗忘早期信息!")
                    log_manager.error_agent("🛑 为避免上下文溢出，考虑提前终止")
                    break
                
                log_manager.info_agent(f"询问模型是否需要继续执行更多步骤...")
                # 继续下一轮循环，让模型决定是否需要更多工具调用
                continue
            else:
                # 没有工具调用，检查任务完成情况
                if self.is_smart_task:
                    # 智能任务：直接认为任务完成，无需硬编码的"结束测试"判断
                    self.chat_history.append(response)
                    
                    # 记录最终回复到结构化日志
                    log_manager.log_final_reply(response.content)
                    
                    log_manager.info_agent(f"🎉 智能任务执行完成！")
                    log_manager.info_agent(f"📊 总执行轮数: {round_count} 轮")
                    
                    # 结束任务结构化日志记录
                    log_manager.end_task(status="成功")
                    
                    return response.content
                else:
                    # 普通任务：保持原有的"结束测试"关键词判断逻辑
                    # 检查用户原始输入是否包含"结束测试"等明确结束指示
                    end_keywords = ["结束测试", "完成测试", "测试结束", "最后结束测试"]
                    user_wants_end = any(keyword in user_input for keyword in end_keywords)
                    
                    if user_wants_end:
                        # 用户明确要求结束测试，允许结束
                        self.chat_history.append(response)
                        
                        # 记录最终回复到结构化日志
                        log_manager.log_final_reply(response.content)
                        
                        log_manager.info_agent(f"🎉 用户明确要求结束测试，任务完成！")
                        log_manager.info_agent(f"📊 总执行轮数: {round_count} 轮")
                        
                        # 结束任务结构化日志记录
                        log_manager.end_task(status="成功")
                        
                        return response.content
                    else:
                        # 用户没有明确要求结束，但模型停止了工具调用，这可能是错误
                        log_manager.warning_agent(f"⚠️ 模型在第 {round_count} 轮停止工具调用，但用户未明确要求结束测试")
                        log_manager.warning_agent(f"原始用户输入: {user_input}")
                        log_manager.warning_agent(f"模型回复: {response.content}")
                        
                        # 添加提示信息，要求模型继续执行
                        reminder_msg = SystemMessage(content="""
警告：你似乎提前结束了任务执行。请检查用户的原始指令，确保所有步骤都已完成。
只有当用户明确说出"结束测试"、"完成测试"等结束指示时，才能停止工具调用。
如果还有未完成的步骤，请继续执行相应的工具。
""")
                        
                        # 将模型回复和提醒添加到历史
                        self.chat_history.append(response)
                        self.chat_history.append(reminder_msg)
                        
                        # 更新消息列表，强制模型重新思考
                        messages = [
                            SystemMessage(content=self._create_system_message()),
                            *self.chat_history
                        ]
                        
                        log_manager.info_agent(f"🔄 发送提醒信息，要求模型继续执行...")
                        # 继续下一轮，让模型重新执行
                        continue
        
        # 达到最大轮数限制或其他终止条件
        if round_count >= max_rounds:
            log_manager.warning_agent(f"达到最大工具调用轮数限制 ({max_rounds})，强制结束")
            termination_reason = "超时"
        elif consecutive_failures >= 3:
            log_manager.error_agent(f"连续失败次数过多，强制结束")
            termination_reason = "连续失败"
        else:
            termination_reason = "其他原因"
            
        # 输出执行总结
        log_manager.info_agent(f"📊 执行总结:")
        log_manager.info_agent(f"   总轮次: {round_count}")
        log_manager.info_agent(f"   格式纠正次数: {format_correction_count}")
        log_manager.info_agent(f"   连续失败次数: {consecutive_failures}")
        log_manager.info_agent(f"   最后错误类型: {last_error_type or '无'}")
        log_manager.info_agent(f"   终止原因: {termination_reason}")
        
        # 结束任务结构化日志记录
        log_manager.end_task(status=termination_reason)
        
        # 仍然返回最后的响应
        if hasattr(response, 'content'):
            self.chat_history.append(response)
            return response.content
        else:
            return f"任务执行终止 ({termination_reason})，请重新开始"
    
    def _chat_without_tools(self, user_input: str) -> str:
        """不使用工具的对话模式"""
        messages = [
            SystemMessage(content="你是一个友好的AI助手，请用中文回答问题。"),
            *self.chat_history
        ]
        
        # 记录LLM详细输入
        log_manager.info_llm(f"无工具模式，用户输入: {user_input}", self.model_name)
        log_manager.log_llm_input(messages, self.model_name)
        
        response = self.llm.invoke(messages)
        self.chat_history.append(response)
        
        # 记录LLM详细输出
        log_manager.log_llm_output(response, self.model_name)
        log_manager.info_llm(f"无工具模式回复: {response.content[:100]}...", self.model_name)
        
        return response.content
    
    def _is_gemma3_model(self) -> bool:
        """判断当前模型是否是 gemma3 系列模型"""
        return 'gemma3' in self.model_name.lower()
    
    def _parse_gemma3_tool_calls(self, content: str) -> list:
        """解析 gemma3 模型的 tool_call 格式
        
        gemma3 模型输出格式示例:
        ```tool_call
        {"name": "find_available_device", "parameters": {"platform": "ios"}}
        ```
        
        Args:
            content: 模型输出的文本内容
            
        Returns:
            解析后的工具调用列表，格式与 LangChain 标准格式兼容
        """
        tool_calls = []
        
        # 匹配 ```tool_call 代码块格式
        pattern = r'```tool_call\s*\n({.*?})\s*\n```'
        matches = re.findall(pattern, content, re.DOTALL)
        
        for match in matches:
            try:
                # 解析 JSON 数据
                tool_data = json.loads(match)
                
                # 转换为 LangChain 兼容格式
                tool_call = {
                    'name': tool_data.get('name'),
                    'args': tool_data.get('parameters', {}),
                    'id': str(uuid.uuid4())
                }
                tool_calls.append(tool_call)
                
                log_manager.info_agent(f"🔄 检测到 gemma3 工具调用: {tool_call['name']}")
                
            except json.JSONDecodeError as e:
                log_manager.warning_agent(f"⚠️ 解析 gemma3 工具调用 JSON 失败: {e}")
                continue
            except Exception as e:
                log_manager.warning_agent(f"⚠️ 处理 gemma3 工具调用时出错: {e}")
                continue
        
        if tool_calls:
            log_manager.info_agent(f"✅ 成功解析 {len(tool_calls)} 个 gemma3 工具调用")
        
        return tool_calls
    
    def _parse_alternative_tool_formats(self, content: str) -> list:
        """解析各种非标准工具调用格式
        
        Args:
            content: 模型输出的文本内容
            
        Returns:
            解析后的工具调用列表
        """
        tool_calls = []
        
        # 1. 解析 gemma3 格式: ```tool_call
        if '```tool_call' in content:
            pattern = r'```tool_call\s*\n({.*?})\s*\n```'
            matches = re.findall(pattern, content, re.DOTALL)
            
            for match in matches:
                try:
                    tool_data = json.loads(match)
                    tool_call = {
                        'name': tool_data.get('name'),
                        'args': tool_data.get('parameters', {}),
                        'id': str(uuid.uuid4())
                    }
                    tool_calls.append(tool_call)
                    log_manager.info_agent(f"🔄 检测到 gemma3 工具调用: {tool_call['name']}")
                except json.JSONDecodeError as e:
                    log_manager.warning_agent(f"⚠️ 解析 gemma3 工具调用 JSON 失败: {e}")
                    continue
        
        # 2. 解析文本中的纯JSON工具调用格式
        elif '{"name":' in content and ('"parameters":' in content or '"args":' in content):
            # 匹配 {"name": "tool_name", "parameters": {...}} 格式
            pattern1 = r'{"name":\s*"([^"]+)"[^}]*"parameters":\s*({[^}]+})\s*}'
            matches1 = re.findall(pattern1, content)
            
            for name, params_str in matches1:
                try:
                    params = json.loads(params_str)
                    tool_call = {
                        'name': name,
                        'args': params,
                        'id': str(uuid.uuid4())
                    }
                    tool_calls.append(tool_call)
                    log_manager.info_agent(f"🔄 检测到 JSON 工具调用: {tool_call['name']}")
                except json.JSONDecodeError as e:
                    log_manager.warning_agent(f"⚠️ 解析 JSON 工具调用失败: {e}")
                    continue
            
            # 匹配 {"name": "tool_name", "args": {...}} 格式
            pattern2 = r'{"name":\s*"([^"]+)"[^}]*"args":\s*({[^}]+})\s*}'
            matches2 = re.findall(pattern2, content)
            
            for name, args_str in matches2:
                try:
                    args = json.loads(args_str)
                    tool_call = {
                        'name': name,
                        'args': args,
                        'id': str(uuid.uuid4())
                    }
                    tool_calls.append(tool_call)
                    log_manager.info_agent(f"🔄 检测到 JSON 工具调用: {tool_call['name']}")
                except json.JSONDecodeError as e:
                    log_manager.warning_agent(f"⚠️ 解析 JSON 工具调用失败: {e}")
                    continue
        
        if tool_calls:
            log_manager.info_agent(f"✅ 成功解析 {len(tool_calls)} 个非标准工具调用")
        
        return tool_calls
    
    def _normalize_tool_args(self, tool_name: str, args: dict) -> dict:
        """标准化工具参数类型
        
        Args:
            tool_name: 工具名称
            args: 工具参数
            
        Returns:
            标准化后的参数字典
        """
        # 定义参数类型映射
        type_mappings = {
            'tap_device': {'x': int, 'y': int},
            'slide_device': {'from_x': float, 'from_y': float, 'to_x': float, 'to_y': float, 'duration': float},
            'wait_seconds': {'seconds': int}
        }
        
        if tool_name in type_mappings:
            for param, expected_type in type_mappings[tool_name].items():
                if param in args:
                    try:
                        args[param] = expected_type(args[param])
                        log_manager.info_agent(f"🔄 标准化参数 {param}: {args[param]} ({expected_type.__name__})")
                    except (ValueError, TypeError) as e:
                        log_manager.warning_agent(f"⚠️ 参数 {param} 类型转换失败: {e}")
        
        return args
    
    def _create_format_correction_prompt(self, detected_format: str, original_content: str) -> str:
        """创建格式纠正提示
        
        Args:
            detected_format: 检测到的格式类型
            original_content: 原始内容
            
        Returns:
            格式纠正提示消息
        """
        if detected_format == "gemma3_tool_format":
            return f"""
⚠️ 检测到你使用了 ```tool_call 格式，但系统需要标准的工具调用格式。

你的输出: {original_content[:200]}...

请重新使用标准的工具调用格式。系统会自动处理工具调用，你只需要正确指定工具名称和参数即可。
请重新思考并直接调用所需的工具。
"""
        elif detected_format == "text_with_tool_pattern":
            return f"""
⚠️ 检测到你在文本中包含了工具调用指令，但系统需要标准的工具调用格式。

你的输出: {original_content[:200]}...

请重新使用标准的工具调用格式。系统会自动处理工具调用，你只需要正确指定工具名称和参数即可。
请重新思考并直接调用所需的工具。
"""
        else:
            return f"""
⚠️ 检测到非标准的工具调用格式。

你的输出: {original_content[:200]}...

请使用标准的工具调用格式。系统会自动处理工具调用，你只需要正确指定工具名称和参数即可。
请重新思考并直接调用所需的工具。
"""
    
    def _execute_tool_call(self, tool_call: Dict[str, Any]) -> str:
        """执行工具调用"""
        try:
            tool_name = tool_call['name']
            tool_args = tool_call.get('args', {})
            
            # 标准化参数类型
            tool_args = self._normalize_tool_args(tool_name, tool_args)
            
            log_manager.info_agent(f"开始执行工具: {tool_name}, 参数: {tool_args}")
            
            # 查找并执行工具
            for tool in self.tools:
                if tool.name == tool_name:
                    result = tool.func(**tool_args)
                    log_manager.info_agent(f"工具 {tool_name} 执行成功")
                    return result
            
            error_msg = f"未找到工具: {tool_name}"
            log_manager.error_agent(error_msg)
            return json.dumps({
                "status": "error",
                "message": error_msg
            }, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"执行工具 {tool_call.get('name', 'unknown')} 时出错: {str(e)}"
            log_manager.error_agent(error_msg)
            return json.dumps({
                "status": "error",
                "error": str(e),
                "message": error_msg
            }, ensure_ascii=False)
    
    def get_chat_history(self) -> List:
        """获取聊天历史"""
        return self.chat_history
    
    def clear_history(self):
        """清除聊天历史"""
        self.chat_history.clear()
        log_manager.info_agent("聊天历史已清除")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools]
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "supports_tools": self.supports_tools
        }
    
    def get_context_usage_info(self) -> Dict[str, Any]:
        """获取当前上下文使用情况"""
        if not self.chat_history:
            return {
                "status": "empty",
                "message": "当前聊天历史为空"
            }
        
        # 构建当前的完整消息列表（包括系统消息）
        messages = [
            SystemMessage(content=self._create_system_message()),
            *self.chat_history
        ]
        
        context_usage = self._calculate_context_usage(messages)
        
        # 添加状态描述
        if context_usage['is_critical']:
            status = "critical"
            status_desc = "危险：上下文接近饱和"
        elif context_usage['is_approaching_limit']:
            status = "warning"
            status_desc = "警告：上下文使用率较高"
        else:
            status = "normal"
            status_desc = "正常：上下文使用率健康"
        
        context_usage.update({
            "status": status,
            "status_description": status_desc
        })
        
        return context_usage
    
    def get_last_session_info(self) -> Dict[str, Any]:
        """获取最后一次会话的信息"""
        try:
            # 获取状态管理器中的最新轮次信息
            current_round = self.session_manager.status_manager.get_current_round()
            if current_round > 0:
                round_id = f"round_{current_round:04d}"
                return self.session_manager.status_manager.get_round_log_info(round_id) or {}
            return {}
        except Exception as e:
            log_manager.error_agent(f"获取会话信息失败: {e}")
            return {}
    
    def get_session_status(self, round_id: str = None) -> Dict[str, Any]:
        """获取会话状态信息"""
        try:
            if round_id:
                return self.session_manager.status_manager.get_task_info(round_id) or {}
            else:
                return {
                    "current_round": self.session_manager.status_manager.get_current_round(),
                    "all_tasks": self.session_manager.status_manager.get_all_tasks(),
                    "running_tasks": self.session_manager.status_manager.get_running_tasks()
                }
        except Exception as e:
            log_manager.error_agent(f"获取会话状态失败: {e}")
            return {}
    
    def set_task_context(self, task_id: str, round_id: str, port: int):
        """设置任务上下文，用于标识和隔离"""
        try:
            self.task_context = {
                'task_id': task_id,
                'round_id': round_id,
                'port': port,
                'agent_prefix': f"[Agent-{port}]"
            }
            log_manager.info_agent(f"Agent-{port} 开始处理任务 {task_id} (轮次: {round_id})")
        except Exception as e:
            log_manager.error_agent(f"设置任务上下文失败: {e}")
    
    def reset_device_connections(self):
        """重置设备连接状态，避免任务间冲突"""
        try:
            # 清除当前设备状态
            self.current_device_udid = None
            log_manager.info_agent("已重置设备连接状态")
        except Exception as e:
            log_manager.error_agent(f"重置设备连接失败: {e}")
    
    def get_task_prefix(self):
        """获取任务前缀用于日志标识"""
        if hasattr(self, 'task_context') and self.task_context:
            return self.task_context.get('agent_prefix', '[Agent]')
        return '[Agent]'


def test_tool_calling():
    """测试工具调用功能"""
    print("🔧 测试工具调用功能...")
    
    try:
        agent = LocalAgent()
        model_info = agent.get_model_info()
        log_manager.info_agent("工具调用测试开始")
        print(f"✅ Agent初始化成功")
        print(f"🤖 模型: {model_info['model_name']}")
        print(f"🔧 工具支持: {'是' if model_info['supports_tools'] else '否'}")
        
        if model_info['supports_tools']:
            print("\n📱 测试设备检查...")
            response = agent.chat("检查一下我有哪些Android设备")
            print(f"🤖 回答: {response}")
            log_manager.info_agent("工具调用测试完成")
        else:
            print("❌ 模型不支持工具调用")
            log_manager.warning_agent("模型不支持工具调用")
            
    except Exception as e:
        error_msg = f"测试失败: {e}"
        log_manager.error_agent(error_msg)
        print(f"❌ {error_msg}")

    def set_task_context(self, task_id: str, round_id: str, port: int):
        """设置任务上下文，用于标识和隔离"""
        try:
            self.task_context = {
                'task_id': task_id,
                'round_id': round_id,
                'port': port,
                'agent_prefix': f"[Agent-{port}]"
            }
            log_manager.info_agent(f"Agent-{port} 开始处理任务 {task_id} (轮次: {round_id})")
        except Exception as e:
            log_manager.error_agent(f"设置任务上下文失败: {e}")
    
    def reset_device_connections(self):
        """重置设备连接状态，避免任务间冲突"""
        try:
            # 清除当前设备状态
            self.current_device_udid = None
            log_manager.info_agent("已重置设备连接状态")
        except Exception as e:
            log_manager.error_agent(f"重置设备连接失败: {e}")
    
    def get_task_prefix(self):
        """获取任务前缀用于日志标识"""
        if hasattr(self, 'task_context') and self.task_context:
            return self.task_context.get('agent_prefix', '[Agent]')
        return '[Agent]'

def main():
    """主函数 - 命令行界面"""
    print("🚀 本地Agent已启动!")
    print("✨ 支持多轮对话和Function Calling")
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'clear' 清除对话历史")
    print("输入 'tools' 查看可用工具")
    print("输入 'history' 查看对话历史")
    print("输入 'info' 查看模型信息")
    print("输入 'context' 查看上下文使用情况")
    print("输入 'test' 测试工具调用")
    print("-" * 50)
    
    agent = LocalAgent()
    log_manager.info_agent("命令行界面已启动")
    
    # 显示模型信息
    model_info = agent.get_model_info()
    print(f"🤖 模型: {model_info['model_name']}")
    print(f"🔧 工具支持: {'是' if model_info['supports_tools'] else '否'}")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                log_manager.info_agent("用户退出程序")
                print("👋 再见!")
                break
            
            if user_input.lower() in ['clear', '清除']:
                agent.clear_history()
                print("✅ 对话历史已清除")
                continue
            
            if user_input.lower() in ['tools', '工具']:
                print("🔧 可用工具:")
                for tool in agent.get_available_tools():
                    print(f"  - {tool}")
                continue
            
            if user_input.lower() in ['history', '历史']:
                history = agent.get_chat_history()
                print(f"📜 对话历史 (共 {len(history)} 条消息)")
                for i, msg in enumerate(history[-6:], 1):  # 显示最近6条
                    msg_type = type(msg).__name__
                    content = getattr(msg, 'content', str(msg))[:100]
                    print(f"  {i}. {msg_type}: {content}...")
                continue
            
            if user_input.lower() in ['info', '信息']:
                info = agent.get_model_info()
                print("🔍 模型信息:")
                for key, value in info.items():
                    print(f"  {key}: {value}")
                continue
            
            if user_input.lower() in ['context', '上下文']:
                context_info = agent.get_context_usage_info()
                if context_info.get('status') == 'empty':
                    print("📊 上下文状态: 空")
                    print("  当前聊天历史为空")
                else:
                    print("📊 上下文使用情况:")
                    print(f"  状态: {context_info['status_description']}")
                    print(f"  总消息数: {context_info['total_messages']}")
                    print(f"  总字符数: {context_info['total_characters']:,}")
                    print(f"  估算token数: {context_info['estimated_total_tokens']:,}")
                    print(f"  使用率: {context_info['usage_percentage']:.1f}%")
                    print(f"  剩余token: {context_info['remaining_tokens']:,}")
                    print(f"  上下文限制: {context_info['context_limit']:,}")
                continue
            
            if user_input.lower() in ['test', '测试']:
                test_tool_calling()
                continue
            
            if not user_input:
                continue
            
            print("🤖 Agent: ", end="")
            response = agent.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            log_manager.info_agent("用户中断程序")
            print("\n👋 再见!")
            break
        except Exception as e:
            error_msg = f"程序运行错误: {str(e)}"
            log_manager.error_agent(error_msg)
            print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    main()