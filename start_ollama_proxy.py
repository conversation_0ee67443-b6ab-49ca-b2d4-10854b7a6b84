#!/usr/bin/env python3
"""
Ollama Local API Server 快速启动脚本

在项目根目录运行此脚本即可启动 Ollama 代理服务器
"""

import sys
import os
from pathlib import Path

# 添加 common_api_server 目录到 Python 路径
server_dir = Path(__file__).parent / "common_api_server"
sys.path.insert(0, str(server_dir))

# 切换到服务器目录
os.chdir(server_dir)

# 导入并运行启动脚本
try:
    from start_server import main
    import asyncio
    
    print("🚀 启动 Ollama Local API Server...")
    asyncio.run(main())
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖都已安装:")
    print("pip install fastapi uvicorn httpx psutil")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)