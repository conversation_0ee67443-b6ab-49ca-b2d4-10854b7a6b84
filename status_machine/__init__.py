#!/usr/bin/env python3
"""
StatusMachine模块 - 基于状态机的移动端测试Agent系统

这个模块提供了一个完整的状态机驱动的移动端应用测试解决方案，
解决了传统Agent在长步骤测试中的"计划遗忘"问题。

主要组件：
- StatusAgent: 状态机驱动的测试Agent主控制器
- PlanConverter: 智能测试计划转换器
- ToolRegistry: 工具注册中心和元数据管理
- BlackboardMemory: 上下文记忆管理系统
"""

from .status_agent import StatusAgent, ExecutionStatus, ActionResult
from .tool_registry import (
    get_tool_registry, 
    register_new_tool, 
    auto_register_tool,
    ToolRegistry,
    ToolCategory,
    StateTransitionType,
    ToolMetadata
)
from .plan_converter import (
    PlanConverter,
    convert_human_plan,
    StateIdentificationStrategy
)

__version__ = "1.0.0"
__author__ = "Claude Code Assistant"

__all__ = [
    # 核心类
    'StatusAgent',
    'PlanConverter', 
    'ToolRegistry',
    
    # 枚举类型
    'ExecutionStatus',
    'ActionResult',
    'ToolCategory',
    'StateTransitionType',
    'StateIdentificationStrategy',
    
    # 数据类
    'ToolMetadata',
    
    # 便捷函数
    'get_tool_registry',
    'register_new_tool', 
    'auto_register_tool',
    'convert_human_plan',
]