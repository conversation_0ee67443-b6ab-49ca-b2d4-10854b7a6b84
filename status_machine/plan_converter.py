#!/usr/bin/env python3
"""
智能测试计划转换器 - 基于工具注册中心的可扩展架构
将human_plan.json转换为StatusAgent可执行的状态机配置
"""

import json
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from .tool_registry import get_tool_registry, ToolRegistry, ToolCategory, StateTransitionType, ToolMetadata


class StateIdentificationStrategy(Enum):
    """状态识别策略"""
    NAVIGATION_BASED = "navigation_based"    # 基于导航工具分割
    SEMANTIC_BASED = "semantic_based"        # 基于语义分析分割
    MIXED_STRATEGY = "mixed_strategy"        # 混合策略


@dataclass
class ConversionRule:
    """转换规则"""
    name: str
    description: str
    condition: Dict[str, Any]  # 匹配条件
    action: str               # 执行动作
    priority: int = 1


class StateIdentifier:
    """状态识别器 - 负责将线性步骤序列识别为状态"""
    
    def __init__(self, registry: ToolRegistry):
        self.registry = registry
        self.page_keywords = self._load_page_keywords()
    
    def _load_page_keywords(self) -> Dict[str, List[str]]:
        """加载页面关键词映射"""
        return {
            "首页": ["首页", "主页", "home", "main", "启动", "打开"],
            "地址选择页": ["地址选择", "选择地址", "地址页", "address", "location"],
            "搜索页": ["搜索", "search", "输入", "查找"],
            "结果页": ["结果", "result", "确认", "选择结果"],
            "设置页": ["设置", "setting", "配置", "选项"],
            "详情页": ["详情", "detail", "信息", "info"]
        }
    
    def identify_states(self, steps: List[Dict[str, Any]], 
                       strategy: StateIdentificationStrategy = StateIdentificationStrategy.MIXED_STRATEGY) -> List[Dict[str, Any]]:
        """识别状态分割点"""
        if strategy == StateIdentificationStrategy.NAVIGATION_BASED:
            return self._navigation_based_identification(steps)
        elif strategy == StateIdentificationStrategy.SEMANTIC_BASED:
            return self._semantic_based_identification(steps)
        else:
            return self._mixed_strategy_identification(steps)
    
    def _navigation_based_identification(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于导航工具的状态识别"""
        states = []
        current_state_steps = []
        state_counter = 0
        
        for i, step in enumerate(steps):
            current_state_steps.append(step)
            
            # 检查是否为导航工具
            tool_name = step.get('action', '')
            if self.registry.is_navigation_tool(tool_name):
                # 这是一个状态的结束点
                state_name = self._generate_state_name(current_state_steps, state_counter)
                states.append({
                    'state_name': state_name,
                    'description': self._generate_state_description(current_state_steps),
                    'steps': current_state_steps.copy(),
                    'transition_tool': tool_name,
                    'is_final': i == len(steps) - 1
                })
                
                current_state_steps = []
                state_counter += 1
        
        # 处理剩余步骤
        if current_state_steps:
            state_name = self._generate_state_name(current_state_steps, state_counter)
            states.append({
                'state_name': state_name,
                'description': self._generate_state_description(current_state_steps),
                'steps': current_state_steps,
                'transition_tool': None,
                'is_final': True
            })
        
        return states
    
    def _semantic_based_identification(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于语义分析的状态识别"""
        states = []
        current_state_steps = []
        current_page_context = "初始状态"
        state_counter = 0
        
        for step in steps:
            description = step.get('description', '').lower()
            
            # 检查是否提到了新的页面
            detected_page = self._detect_page_from_text(description)
            
            if detected_page and detected_page != current_page_context:
                # 页面切换，创建新状态
                if current_state_steps:
                    state_name = self._generate_semantic_state_name(current_page_context, state_counter)
                    states.append({
                        'state_name': state_name,
                        'description': f"{current_page_context}相关操作",
                        'steps': current_state_steps.copy(),
                        'page_context': current_page_context,
                        'transition_tool': step.get('action'),
                        'is_final': False
                    })
                    state_counter += 1
                
                current_state_steps = []
                current_page_context = detected_page
            
            current_state_steps.append(step)
        
        # 处理最后一个状态
        if current_state_steps:
            state_name = self._generate_semantic_state_name(current_page_context, state_counter)
            states.append({
                'state_name': state_name,
                'description': f"{current_page_context}相关操作",
                'steps': current_state_steps,
                'page_context': current_page_context,
                'transition_tool': None,
                'is_final': True
            })
        
        return states
    
    def _mixed_strategy_identification(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """混合策略状态识别"""
        # 首先使用语义分析
        semantic_states = self._semantic_based_identification(steps)
        
        # 然后使用导航分析进行优化
        optimized_states = []
        
        for state in semantic_states:
            state_steps = state['steps']
            
            # 检查状态内部是否有导航工具，需要进一步分割
            sub_states = self._split_state_by_navigation(state_steps, state['state_name'])
            optimized_states.extend(sub_states)
        
        return optimized_states
    
    def _split_state_by_navigation(self, steps: List[Dict[str, Any]], base_state_name: str) -> List[Dict[str, Any]]:
        """根据导航工具分割状态"""
        if len(steps) <= 3:  # 短状态不分割
            return [{
                'state_name': base_state_name,
                'description': self._generate_state_description(steps),
                'steps': steps,
                'transition_tool': self._find_transition_tool(steps),
                'is_final': False
            }]
        
        sub_states = []
        current_sub_steps = []
        sub_counter = 0
        
        for step in steps:
            current_sub_steps.append(step)
            
            if self.registry.is_navigation_tool(step.get('action', '')):
                # 创建子状态
                sub_state_name = f"{base_state_name}_Part{sub_counter + 1}"
                sub_states.append({
                    'state_name': sub_state_name,
                    'description': self._generate_state_description(current_sub_steps),
                    'steps': current_sub_steps.copy(),
                    'transition_tool': step.get('action'),
                    'is_final': False
                })
                
                current_sub_steps = []
                sub_counter += 1
        
        # 处理剩余步骤
        if current_sub_steps:
            sub_state_name = f"{base_state_name}_Part{sub_counter + 1}"
            sub_states.append({
                'state_name': sub_state_name,
                'description': self._generate_state_description(current_sub_steps),
                'steps': current_sub_steps,
                'transition_tool': None,
                'is_final': True
            })
        
        return sub_states if sub_states else [{
            'state_name': base_state_name,
            'description': self._generate_state_description(steps),
            'steps': steps,
            'transition_tool': None,
            'is_final': False
        }]
    
    def _detect_page_from_text(self, text: str) -> Optional[str]:
        """从文本中检测页面类型"""
        text_lower = text.lower()
        
        for page_name, keywords in self.page_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return page_name
        
        return None
    
    def _generate_state_name(self, steps: List[Dict[str, Any]], counter: int) -> str:
        """生成状态名称"""
        if not steps:
            return f"State_{counter}"
        
        # 尝试从步骤描述中提取关键信息
        first_step = steps[0]
        description = first_step.get('description', '')
        
        # 检测页面关键词
        detected_page = self._detect_page_from_text(description)
        if detected_page:
            return f"{detected_page}State"
        
        # 基于动作类型生成
        action = first_step.get('action', '')
        if action in ['find_available_device', 'start_device_test']:
            return "InitState"
        elif action in ['end_device_test']:
            return "FinalState"
        else:
            return f"ExecutionState_{counter}"
    
    def _generate_semantic_state_name(self, page_context: str, counter: int) -> str:
        """基于语义生成状态名称"""
        if page_context == "初始状态":
            return "InitState"
        else:
            # 移除空格并转换为驼峰命名
            clean_name = re.sub(r'[^\w]', '', page_context)
            return f"{clean_name}State"
    
    def _generate_state_description(self, steps: List[Dict[str, Any]]) -> str:
        """生成状态描述"""
        if not steps:
            return "空状态"
        
        descriptions = [step.get('description', '') for step in steps if step.get('description')]
        if descriptions:
            return f"执行{len(steps)}个操作: " + "; ".join(descriptions[:2]) + ("..." if len(descriptions) > 2 else "")
        else:
            actions = [step.get('action', '') for step in steps]
            return f"执行工具序列: {', '.join(actions[:3])}" + ("..." if len(actions) > 3 else "")
    
    def _find_transition_tool(self, steps: List[Dict[str, Any]]) -> Optional[str]:
        """查找状态转换工具"""
        for step in reversed(steps):
            tool_name = step.get('action', '')
            if self.registry.is_navigation_tool(tool_name):
                return tool_name
        return None


class ParameterResolver:
    """参数解析器 - 处理步骤间的参数传递"""
    
    def __init__(self, registry: ToolRegistry):
        self.registry = registry
        self.variable_patterns = {
            'device_udid': r'\{device_udid\}|\{udid\}',
            'element_x': r'\{element_x\}|\{x\}',
            'element_y': r'\{element_y\}|\{y\}',
            'element_coordinates': r'\{element_.*\}'
        }
    
    def resolve_step_parameters(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """解析步骤参数"""
        resolved_params = {}
        original_params = step.get('parameters', {})
        tool_name = step.get('action', '')
        
        # 获取工具元数据
        tool_metadata = self.registry.get_tool_metadata(tool_name)
        if not tool_metadata:
            return original_params
        
        # 解析每个参数
        for param_name, param_value in original_params.items():
            resolved_value = self._resolve_parameter_value(
                param_name, param_value, tool_metadata, context
            )
            resolved_params[param_name] = resolved_value
        
        # 添加缺失的必需参数
        self._add_required_parameters(resolved_params, tool_metadata, context)
        
        return resolved_params
    
    def _resolve_parameter_value(self, param_name: str, param_value: Any, 
                                tool_metadata: ToolMetadata, context: Dict[str, Any]) -> Any:
        """解析单个参数值"""
        if not isinstance(param_value, str):
            return param_value
        
        # 检查是否为变量引用
        if param_value.startswith('{') and param_value.endswith('}'):
            var_name = param_value[1:-1]
            
            # 从上下文获取值
            if var_name in context:
                return context[var_name]
            
            # 根据工具元数据推断数据源
            param_info = tool_metadata.parameters.get(param_name, {})
            if 'source' in param_info:
                source = param_info['source']
                # 解析source格式，如 "find_element_on_page.x"
                if '.' in source:
                    tool_source, field_source = source.split('.', 1)
                    return f"{{{tool_source}_{field_source}}}"
            
            # 保持变量格式，运行时解析
            return param_value
        
        return param_value
    
    def _add_required_parameters(self, resolved_params: Dict[str, Any], 
                               tool_metadata: ToolMetadata, context: Dict[str, Any]) -> None:
        """添加缺失的必需参数"""
        for param_name, param_info in tool_metadata.parameters.items():
            if param_info.get('required', False) and param_name not in resolved_params:
                # 尝试从context或默认值获取
                if param_name == 'udid' and 'device_udid' in context:
                    resolved_params[param_name] = "{device_udid}"
                elif 'default' in param_info:
                    resolved_params[param_name] = param_info['default']
                elif 'source' in param_info:
                    source = param_info['source']
                    resolved_params[param_name] = f"{{{source}}}"


class PlanConverter:
    """智能测试计划转换器主类"""
    
    def __init__(self, registry: Optional[ToolRegistry] = None):
        self.registry = registry or get_tool_registry()
        self.state_identifier = StateIdentifier(self.registry)
        self.parameter_resolver = ParameterResolver(self.registry)
        self.conversion_rules = self._load_conversion_rules()
    
    def convert_plan_to_state_machine(self, plan_data: Dict[str, Any],
                                    strategy: StateIdentificationStrategy = StateIdentificationStrategy.MIXED_STRATEGY,
                                    optimize: bool = True) -> Dict[str, Any]:
        """将测试计划转换为状态机配置"""
        
        print(f"🔄 开始转换测试计划: {plan_data.get('plan_id', 'unknown')}")
        print(f"📊 原始步骤数量: {plan_data.get('total_steps', len(plan_data.get('steps', [])))}")
        
        # 1. 验证和预处理步骤
        steps = self._preprocess_steps(plan_data.get('steps', []))
        
        # 2. 识别状态分割
        states_info = self.state_identifier.identify_states(steps, strategy)
        print(f"🎯 识别状态数量: {len(states_info)}")
        
        # 3. 生成状态机配置
        state_machine_config = self._generate_state_machine_config(states_info, plan_data)
        
        # 4. 优化配置
        if optimize:
            state_machine_config = self._optimize_configuration(state_machine_config)
        
        # 5. 验证配置
        validation_result = self._validate_configuration(state_machine_config)
        if validation_result['errors']:
            print(f"⚠️ 配置验证发现问题: {validation_result['errors']}")
        
        print(f"✅ 状态机配置生成完成")
        return state_machine_config
    
    def _preprocess_steps(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理步骤"""
        processed_steps = []
        
        for step in steps:
            # 标准化步骤格式
            processed_step = {
                'step_id': step.get('step_id', len(processed_steps) + 1),
                'action': step.get('action', ''),
                'description': step.get('description', ''),
                'parameters': step.get('parameters', {}),
                'expected_result': step.get('expected_result', ''),
                'parameter_types': step.get('parameter_types', {}),
                'parameter_sources': step.get('parameter_sources', {})
            }
            
            # 验证工具存在性
            tool_name = processed_step['action']
            if not self.registry.get_tool_metadata(tool_name):
                print(f"⚠️ 未知工具: {tool_name}, 步骤: {processed_step['step_id']}")
            
            processed_steps.append(processed_step)
        
        return processed_steps
    
    def _generate_state_machine_config(self, states_info: List[Dict[str, Any]], 
                                     plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成状态机配置"""
        states = {}
        
        for i, state_info in enumerate(states_info):
            state_name = state_info['state_name']
            
            # 生成状态的动作配置
            actions = []
            for step in state_info['steps']:
                action_config = self._generate_action_config(step)
                actions.append(action_config)
            
            # 确定状态转换
            transitions = self._determine_state_transitions(state_info, states_info, i)
            
            # 创建状态配置
            states[state_name] = {
                'description': state_info['description'],
                'actions': actions,
                'transitions': transitions,
                'timeout': 300  # 默认5分钟超时
            }
        
        # 添加完成状态
        if "CompletedState" not in states:
            states["CompletedState"] = {
                'description': '测试完成状态',
                'actions': [],
                'transitions': {}
            }
        
        # 创建完整配置
        initial_state = states_info[0]['state_name'] if states_info else "CompletedState"
        
        return {
            'initial_state': initial_state,
            'states': states,
            'global_config': {
                'max_execution_time': 1800,  # 30分钟
                'auto_screenshot': True,
                'error_recovery': True,
                'source_plan': {
                    'plan_id': plan_data.get('plan_id', ''),
                    'platform': plan_data.get('platform', ''),
                    'original_steps': plan_data.get('total_steps', 0)
                }
            }
        }
    
    def _generate_action_config(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """生成动作配置"""
        tool_name = step['action']
        tool_metadata = self.registry.get_tool_metadata(tool_name)
        
        # 解析参数
        resolved_parameters = self.parameter_resolver.resolve_step_parameters(
            step, {'device_udid': '{device_udid}'}
        )
        
        # 基本动作配置
        action_config = {
            'tool': tool_name,
            'parameters': resolved_parameters,
            'expected_result': step.get('expected_result', 'success')
        }
        
        # 添加重试配置
        if tool_metadata and tool_metadata.retry_config:
            action_config['retry_config'] = tool_metadata.retry_config
        
        # 添加超时配置
        action_config['timeout'] = 60  # 默认1分钟
        
        return action_config
    
    def _determine_state_transitions(self, current_state: Dict[str, Any], 
                                   all_states: List[Dict[str, Any]], 
                                   current_index: int) -> Dict[str, str]:
        """确定状态转换"""
        transitions = {}
        
        # 如果是最后一个状态
        if current_index >= len(all_states) - 1:
            # 根据最后一个动作确定转换
            last_step = current_state['steps'][-1] if current_state['steps'] else None
            if last_step and last_step.get('action') == 'end_device_test':
                transitions['test_ended'] = 'CompletedState'
            else:
                transitions['success'] = 'CompletedState'
        else:
            # 转换到下一个状态
            next_state = all_states[current_index + 1]
            
            # 基于转换工具确定转换条件
            transition_tool = current_state.get('transition_tool')
            if transition_tool:
                tool_metadata = self.registry.get_tool_metadata(transition_tool)
                if tool_metadata and tool_metadata.transition_type == StateTransitionType.PAGE_NAVIGATION:
                    transitions['tap_success'] = next_state['state_name']
                    transitions['navigation_success'] = next_state['state_name']
                else:
                    transitions['success'] = next_state['state_name']
            else:
                transitions['success'] = next_state['state_name']
        
        return transitions
    
    def _optimize_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """优化配置"""
        optimized_config = config.copy()
        
        # 1. 合并相似的短状态
        optimized_config = self._merge_similar_states(optimized_config)
        
        # 2. 添加错误恢复路径
        optimized_config = self._add_error_recovery(optimized_config)
        
        # 3. 优化等待时间
        optimized_config = self._optimize_wait_times(optimized_config)
        
        return optimized_config
    
    def _merge_similar_states(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """合并相似的短状态"""
        # 简化实现：合并只有1-2个动作的相邻状态
        states = config['states']
        merged_states = {}
        
        state_names = list(states.keys())
        skip_states = set()
        
        for i, state_name in enumerate(state_names):
            if state_name in skip_states or state_name == 'CompletedState':
                continue
            
            current_state = states[state_name]
            actions = current_state.get('actions', [])
            
            # 如果当前状态动作少于3个，尝试与下一个状态合并
            if len(actions) < 3 and i + 1 < len(state_names):
                next_state_name = state_names[i + 1]
                next_state = states.get(next_state_name, {})
                next_actions = next_state.get('actions', [])
                
                # 合并条件：总动作数不超过5个
                if len(actions) + len(next_actions) <= 5:
                    merged_actions = actions + next_actions
                    merged_description = f"{current_state['description']}; {next_state.get('description', '')}"
                    
                    # 创建合并状态
                    merged_states[state_name] = {
                        'description': merged_description,
                        'actions': merged_actions,
                        'transitions': next_state.get('transitions', current_state.get('transitions', {})),
                        'timeout': max(current_state.get('timeout', 300), next_state.get('timeout', 300))
                    }
                    
                    skip_states.add(next_state_name)
                    continue
            
            # 不合并，保持原状态
            merged_states[state_name] = current_state
        
        # 添加CompletedState
        if 'CompletedState' in states:
            merged_states['CompletedState'] = states['CompletedState']
        
        config['states'] = merged_states
        return config
    
    def _add_error_recovery(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """添加错误恢复路径"""
        # 为每个状态添加错误处理转换
        states = config['states']
        
        for state_name, state_config in states.items():
            if state_name != 'CompletedState':
                transitions = state_config.get('transitions', {})
                
                # 添加通用错误处理
                if 'error' not in transitions:
                    transitions['error'] = 'CompletedState'  # 简化：错误时直接结束
                if 'timeout' not in transitions:
                    transitions['timeout'] = 'CompletedState'
                
                state_config['transitions'] = transitions
        
        return config
    
    def _optimize_wait_times(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """优化等待时间"""
        states = config['states']
        
        for state_name, state_config in states.items():
            actions = state_config.get('actions', [])
            
            for action in actions:
                if action.get('tool') == 'wait_seconds':
                    # 根据前后动作类型调整等待时间
                    current_wait = action.get('parameters', {}).get('seconds', 3)
                    
                    # 如果等待时间过长，适当减少
                    if current_wait > 5:
                        action['parameters']['seconds'] = 3
                        print(f"⚡ 优化等待时间: {state_name} 从 {current_wait}s 减少到 3s")
        
        return config
    
    def _validate_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置"""
        errors = []
        warnings = []
        
        # 1. 检查必需字段
        if 'initial_state' not in config:
            errors.append("缺少initial_state字段")
        
        if 'states' not in config:
            errors.append("缺少states字段")
            return {'errors': errors, 'warnings': warnings}
        
        states = config['states']
        initial_state = config.get('initial_state')
        
        # 2. 检查初始状态存在性
        if initial_state not in states:
            errors.append(f"初始状态 {initial_state} 不存在")
        
        # 3. 检查状态转换的完整性
        for state_name, state_config in states.items():
            transitions = state_config.get('transitions', {})
            
            for target_state in transitions.values():
                if target_state not in states:
                    errors.append(f"状态 {state_name} 转换到不存在的状态 {target_state}")
        
        # 4. 检查工具有效性
        for state_name, state_config in states.items():
            actions = state_config.get('actions', [])
            
            for action in actions:
                tool_name = action.get('tool', '')
                if not self.registry.get_tool_metadata(tool_name):
                    warnings.append(f"状态 {state_name} 使用了未知工具: {tool_name}")
        
        # 5. 检查死锁状态
        reachable_states = self._find_reachable_states(states, initial_state)
        for state_name in states:
            if state_name not in reachable_states and state_name != 'CompletedState':
                warnings.append(f"状态 {state_name} 无法访问")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _find_reachable_states(self, states: Dict[str, Any], initial_state: str) -> set:
        """查找可访问状态"""
        reachable = set()
        to_visit = [initial_state]
        
        while to_visit:
            current = to_visit.pop()
            if current in reachable:
                continue
            
            reachable.add(current)
            
            if current in states:
                transitions = states[current].get('transitions', {})
                for target in transitions.values():
                    if target not in reachable:
                        to_visit.append(target)
        
        return reachable
    
    def _load_conversion_rules(self) -> List[ConversionRule]:
        """加载转换规则"""
        return [
            ConversionRule(
                name="merge_short_states",
                description="合并短状态",
                condition={"action_count": {"<": 3}},
                action="merge_with_next",
                priority=1
            ),
            ConversionRule(
                name="split_long_states", 
                description="分割长状态",
                condition={"action_count": {">": 8}},
                action="split_by_navigation",
                priority=2
            )
        ]
    
    def export_conversion_report(self, original_plan: Dict[str, Any], 
                               converted_config: Dict[str, Any],
                               output_path: str) -> None:
        """导出转换报告"""
        report = {
            'conversion_summary': {
                'original_steps': len(original_plan.get('steps', [])),
                'generated_states': len(converted_config.get('states', {})),
                'conversion_strategy': 'mixed_strategy',
                'optimization_applied': True
            },
            'state_breakdown': [],
            'tool_usage': {},
            'validation_result': self._validate_configuration(converted_config)
        }
        
        # 分析状态分解
        for state_name, state_config in converted_config.get('states', {}).items():
            actions = state_config.get('actions', [])
            report['state_breakdown'].append({
                'state_name': state_name,
                'description': state_config.get('description', ''),
                'action_count': len(actions),
                'tools_used': [action.get('tool', '') for action in actions]
            })
        
        # 统计工具使用
        for state_info in report['state_breakdown']:
            for tool in state_info['tools_used']:
                report['tool_usage'][tool] = report['tool_usage'].get(tool, 0) + 1
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📊 转换报告已保存: {output_path}")


def convert_human_plan(plan_file_path: str, output_file_path: str, 
                      report_file_path: Optional[str] = None) -> Dict[str, Any]:
    """便捷函数：转换human_plan.json文件"""
    
    print(f"📥 读取测试计划: {plan_file_path}")
    
    # 读取原始计划
    with open(plan_file_path, 'r', encoding='utf-8') as f:
        plan_data = json.load(f)
    
    # 创建转换器并转换
    converter = PlanConverter()
    config = converter.convert_plan_to_state_machine(plan_data)
    
    # 保存状态机配置
    with open(output_file_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"💾 状态机配置已保存: {output_file_path}")
    
    # 生成报告
    if report_file_path:
        converter.export_conversion_report(plan_data, config, report_file_path)
    
    return config


if __name__ == "__main__":
    # 测试转换器
    print("🧪 测试PlanConverter")
    
    # 模拟测试计划
    test_plan = {
        "plan_id": "test_conversion",
        "platform": "ios",
        "total_steps": 6,
        "steps": [
            {
                "step_id": 1,
                "action": "find_available_device",
                "description": "查找iOS设备",
                "parameters": {"platform": "ios"},
                "expected_result": "device_found"
            },
            {
                "step_id": 2,
                "action": "start_device_test",
                "description": "启动测试",
                "parameters": {"udid": "{device_udid}"},
                "expected_result": "test_started"
            },
            {
                "step_id": 3,
                "action": "find_element_on_page",
                "description": "查找地址元素",
                "parameters": {"udid": "{device_udid}", "element": "左上角地址"},
                "expected_result": "element_found"
            },
            {
                "step_id": 4,
                "action": "tap_device",
                "description": "点击地址元素",
                "parameters": {"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"},
                "expected_result": "tap_success"
            },
            {
                "step_id": 5,
                "action": "ocr_text_validation",
                "description": "验证页面文本",
                "parameters": {"udid": "{device_udid}", "target_text": "搜索城市"},
                "expected_result": "text_validated"
            },
            {
                "step_id": 6,
                "action": "end_device_test",
                "description": "结束测试",
                "parameters": {"udid": "{device_udid}"},
                "expected_result": "test_ended"
            }
        ]
    }
    
    # 转换
    converter = PlanConverter()
    result = converter.convert_plan_to_state_machine(test_plan)
    
    print(f"✅ 转换完成，生成 {len(result['states'])} 个状态:")
    for state_name in result['states']:
        print(f"  - {state_name}")
    
    print("🎉 PlanConverter测试完成")