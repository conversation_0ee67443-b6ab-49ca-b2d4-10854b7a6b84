{"initial_state": "搜索页State", "states": {"搜索页State": {"description": "执行1个操作: 查找左上角地址元素; 执行2个操作: 校验返回首页地址展示北京; 结束测试会话", "actions": [{"tool": "find_element", "parameters": {"udid": "{device_udid}", "element": "左上角地址"}, "expected_result": "成功找到左上角地址元素", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}, {"tool": "ocr_validate_text", "parameters": {"udid": "{device_udid}", "target_text": "北京"}, "expected_result": "成功校验返回首页地址展示北京", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}, {"tool": "end_test", "parameters": {"udid": "{device_udid}"}, "expected_result": "成功结束测试会话", "timeout": 60}], "transitions": {"success": "CompletedState", "error": "CompletedState", "timeout": "CompletedState"}, "timeout": 300}, "地址选择页State": {"description": "执行3个操作: 点击左上角地址进入地址选择页; 等待页面加载完成...", "actions": [{"tool": "tap", "parameters": {"udid": "{device_udid}", "x": "{find_element_x}", "y": "{find_element_y}"}, "expected_result": "成功点击左上角地址进入地址选择页", "timeout": 60}, {"tool": "wait", "parameters": {"seconds": 3}, "expected_result": "页面加载完成", "timeout": 60}, {"tool": "ocr_validate_text", "parameters": {"udid": "{device_udid}", "target_text": "搜索城市/区县/地点"}, "expected_result": "成功校验地址选择页搜索框默认文案", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}], "transitions": {"tap_success": "搜索页State_Part1", "navigation_success": "搜索页State_Part1", "error": "CompletedState", "timeout": "CompletedState"}, "timeout": 300}, "搜索页State_Part1": {"description": "执行2个操作: 查找当前页面搜索框元素; 点击搜索框进入地址搜索页", "actions": [{"tool": "find_element", "parameters": {"udid": "{device_udid}", "element": "搜索框"}, "expected_result": "成功找到当前页面搜索框元素", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}, {"tool": "tap", "parameters": {"udid": "{device_udid}", "x": "{find_element_x}", "y": "{find_element_y}"}, "expected_result": "成功点击搜索框进入地址搜索页", "timeout": 60}], "transitions": {"tap_success": "搜索页State_Part2", "navigation_success": "搜索页State_Part2", "error": "CompletedState", "timeout": "CompletedState"}, "timeout": 300}, "搜索页State_Part2": {"description": "执行4个操作: 等待页面加载完成; 校验地址搜索页搜索框默认文案为“搜索城市/区县/地点”...", "actions": [{"tool": "wait", "parameters": {"seconds": 3}, "expected_result": "页面加载完成", "timeout": 60}, {"tool": "ocr_validate_text", "parameters": {"udid": "{device_udid}", "target_text": "搜索城市/区县/地点"}, "expected_result": "成功校验地址搜索页搜索框默认文案", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}, {"tool": "find_element", "parameters": {"udid": "{device_udid}", "element": "输入框"}, "expected_result": "成功找到输入框元素", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}, {"tool": "tap", "parameters": {"udid": "{device_udid}", "x": "{find_element_x}", "y": "{find_element_y}"}, "expected_result": "成功点击输入框激活输入状态", "timeout": 60}], "transitions": {"tap_success": "搜索页State_Part3", "navigation_success": "搜索页State_Part3", "error": "CompletedState", "timeout": "CompletedState"}, "timeout": 300}, "搜索页State_Part3": {"description": "执行4个操作: 在搜索框中输入文字“北京”; 等待页面加载完成...", "actions": [{"tool": "input_text", "parameters": {"udid": "{device_udid}", "text": "北京"}, "expected_result": "成功输入文字“北京”", "timeout": 60}, {"tool": "wait", "parameters": {"seconds": 3}, "expected_result": "页面加载完成", "timeout": 60}, {"tool": "find_element", "parameters": {"udid": "{device_udid}", "element": "北京市"}, "expected_result": "成功找到搜索结果“北京市”元素", "retry_config": {"max_retries": 2, "wait_seconds": 3}, "timeout": 60}, {"tool": "tap", "parameters": {"udid": "{device_udid}", "x": "{find_element_x}", "y": "{find_element_y}"}, "expected_result": "成功点击搜索结果“北京市”", "timeout": 60}], "transitions": {"tap_success": "搜索页State_Part4", "navigation_success": "搜索页State_Part4", "error": "CompletedState", "timeout": "CompletedState"}, "timeout": 300}, "搜索页State_Part4": {"description": "执行1个操作: 等待页面加载完成; 测试完成状态", "actions": [{"tool": "wait", "parameters": {"seconds": 3}, "expected_result": "页面加载完成", "timeout": 60}], "transitions": {"error": "CompletedState", "timeout": "CompletedState"}, "timeout": 300}, "CompletedState": {"description": "测试完成状态", "actions": [], "transitions": {"error": "CompletedState", "timeout": "CompletedState"}}}, "global_config": {"max_execution_time": 1800, "auto_screenshot": true, "error_recovery": true, "source_plan": {"plan_id": "plan_12345", "platform": "ios", "original_steps": 14}}}