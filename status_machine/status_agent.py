#!/usr/bin/env python3
"""
StatusAgent - 基于状态机的移动端测试Agent
解决长步骤测试中的"计划遗忘"问题
"""

import json
import time
import uuid
import traceback
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from dataclasses import dataclass, asdict

from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

# 导入日志管理器
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from tools._concurrent_log_manager import LogManager, get_global_log_manager

# 导入现有工具模块 - 重用现有的工具基础设施
from tools.check_devices_tools import get_available_device
from tools.wait_tools import wait_for_seconds
from tools.screenshot_tools import get_screenshot
from tools.tap_tools import tap
from tools.slide_tools import slide
from tools.api_ocr_tools import show_page_ocr_result, locate_element_from_ocr
from tools.app_operate_tools import restart_app, background_switch
from tools.smart_input_tools import smart_input_text
from tools.llm_base_tools import start_test, end_test, record_summary, record_issue
from tools.check_page_detail_tools import check_ui_bugs, locate_element_from_layout, analysis_now_page


class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    BLOCKED = "blocked"
    COMPLETED = "completed"


class ActionResult(Enum):
    """动作执行结果枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"
    SKIP = "skip"
    ABORT = "abort"


@dataclass
class ActionConfig:
    """动作配置"""
    tool: str
    parameters: Dict[str, Any]
    expected_result: str
    retry_config: Optional[Dict[str, Any]] = None
    timeout: int = 60


@dataclass  
class StateConfig:
    """状态配置"""
    description: str
    actions: List[ActionConfig]
    transitions: Dict[str, str]
    timeout: int = 300


@dataclass
class StateMachineConfig:
    """状态机配置"""
    initial_state: str
    states: Dict[str, StateConfig]
    global_config: Dict[str, Any]


class BlackboardMemory:
    """黑板记忆管理器 - 负责上下文信息的存储和管理"""
    
    def __init__(self, log_manager: LogManager):
        self.log_manager = log_manager
        self.data = {
            'device_info': {},
            'current_state': '',
            'current_action_index': 0,
            'page_info': {},
            'elements': {},
            'execution_log': [],
            'global_variables': {},
            'error_history': []
        }
    
    def get(self, key: str, default=None) -> Any:
        """获取数据"""
        return self.data.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置数据"""
        self.data[key] = value
        self.log_manager.debug_agent(f"Blackboard更新: {key} = {str(value)[:100]}")
    
    def update(self, updates: Dict[str, Any]) -> None:
        """批量更新数据"""
        self.data.update(updates)
        self.log_manager.info_agent(f"Blackboard批量更新: {list(updates.keys())}")
    
    def get_device_udid(self) -> str:
        """获取设备UDID"""
        return self.data.get('device_info', {}).get('udid', '')
    
    def add_execution_log(self, entry: Dict[str, Any]) -> None:
        """添加执行日志"""
        entry['timestamp'] = time.time()
        self.data['execution_log'].append(entry)
        
        # 只保留最近20条日志，避免内存过度使用
        if len(self.data['execution_log']) > 20:
            self.data['execution_log'] = self.data['execution_log'][-20:]
    
    def state_transition_cleanup(self, from_state: str, to_state: str) -> None:
        """状态转换时的数据清理"""
        self.log_manager.info_agent(f"🧹 状态转换清理: {from_state} -> {to_state}")
        
        # 保留全局信息
        persistent_data = {
            'device_info': self.data.get('device_info', {}),
            'global_variables': self.data.get('global_variables', {}),
            'execution_log': self.data.get('execution_log', [])[-5:],  # 只保留最近5条
            'elements': self._filter_global_elements()
        }
        
        # 清除状态特定信息
        state_specific_keys = ['page_info', 'current_action_index']
        for key in state_specific_keys:
            if key in self.data:
                del self.data[key]
        
        # 更新状态信息
        self.data.update({
            'current_state': to_state,
            'current_action_index': 0,
            'page_info': {},
        })
        
        # 重新加载持久化数据
        self.data.update(persistent_data)
        
        self.log_manager.info_agent(f"✅ 状态转换清理完成，保留了 {len(persistent_data)} 项持久化数据")
    
    def _filter_global_elements(self) -> Dict[str, Any]:
        """过滤出全局有用的元素"""
        elements = self.data.get('elements', {})
        global_elements = {}
        
        # 保留一些可能在多个状态中使用的元素
        global_element_keywords = ['设备', 'device', '首页', 'main', '返回', 'back']
        
        for element_name, element_data in elements.items():
            if any(keyword in element_name.lower() for keyword in global_element_keywords):
                global_elements[element_name] = element_data
        
        return global_elements
    
    def get_context_for_llm(self, max_tokens: int = 2000) -> str:
        """为LLM生成精简的上下文信息"""
        context_parts = []
        
        # 设备信息
        device_info = self.data.get('device_info', {})
        if device_info:
            context_parts.append(f"设备: {device_info.get('device_name', 'Unknown')} ({device_info.get('udid', 'Unknown')})")
        
        # 当前状态
        current_state = self.data.get('current_state', 'Unknown')
        context_parts.append(f"当前状态: {current_state}")
        
        # 页面信息
        page_info = self.data.get('page_info', {})
        if page_info:
            page_name = page_info.get('current_page_name', '')
            page_analysis = page_info.get('page_analysis', '')
            if page_name:
                context_parts.append(f"当前页面: {page_name}")
            if page_analysis:
                context_parts.append(f"页面分析: {page_analysis[:200]}...")
        
        # 最近执行历史
        recent_logs = self.data.get('execution_log', [])[-3:]
        if recent_logs:
            context_parts.append("最近执行:")
            for log in recent_logs:
                action = log.get('action', 'unknown')
                result = log.get('result', 'unknown')
                context_parts.append(f"  - {action}: {result}")
        
        # 合并上下文并控制长度
        full_context = "\n".join(context_parts)
        if len(full_context) > max_tokens * 3:  # 粗略估算token长度
            full_context = full_context[:max_tokens * 3] + "..."
        
        return full_context


class RuleEngine:
    """规则引擎 - 处理简单的异常情况"""
    
    def __init__(self, log_manager: LogManager):
        self.log_manager = log_manager
        self.rules = self._load_default_rules()
    
    def _load_default_rules(self) -> List[Dict[str, Any]]:
        """加载默认规则"""
        return [
            {
                "name": "element_not_found_retry",
                "trigger": "element_not_found",
                "condition": {"tool": "find_element_on_page"},
                "action": "retry",
                "parameters": {"wait_seconds": 3, "max_retries": 2},
                "priority": 1
            },
            {
                "name": "tap_no_response_retry",
                "trigger": "tap_no_response", 
                "condition": {"tool": "tap_device"},
                "action": "wait_and_retry",
                "parameters": {"wait_seconds": 5, "max_retries": 1},
                "priority": 1
            },
            {
                "name": "page_load_timeout",
                "trigger": "timeout",
                "condition": {"consecutive_failures": 3},
                "action": "call_llm",
                "parameters": {"reason": "连续失败，需要智能分析"},
                "priority": 2
            }
        ]
    
    def match_rule(self, trigger: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """匹配规则"""
        self.log_manager.info_agent(f"🔍 规则匹配: trigger={trigger}, context={list(context.keys())}")
        
        matched_rules = []
        for rule in self.rules:
            if rule["trigger"] == trigger:
                # 检查条件匹配
                condition = rule.get("condition", {})
                if self._check_condition(condition, context):
                    matched_rules.append(rule)
        
        # 按优先级排序，返回最高优先级的规则
        if matched_rules:
            best_rule = max(matched_rules, key=lambda r: r.get("priority", 0))
            self.log_manager.info_agent(f"✅ 匹配规则: {best_rule['name']}")
            return best_rule
        
        self.log_manager.info_agent("❌ 无匹配规则")
        return None
    
    def _check_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查条件是否满足"""
        for key, expected_value in condition.items():
            if key not in context or context[key] != expected_value:
                return False
        return True


class StatusAgent:
    """基于状态机的移动端测试Agent主控制器"""
    
    def __init__(self, 
                 model_name: str = "hf.co/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Qwen3-30B-A3B-Instruct-2507-UD-Q8_K_XL.gguf",
                 base_url: str = "http://localhost:11435",
                 temperature: float = 0.1,
                 task_id: Optional[str] = None,
                 log_manager: Optional[LogManager] = None):
        
        self.model_name = model_name
        self.base_url = base_url
        self.temperature = temperature
        self.task_id = task_id or str(uuid.uuid4())
        self.log_manager = log_manager if log_manager else get_global_log_manager()
        
        # 初始化组件
        self.blackboard = BlackboardMemory(self.log_manager)
        self.rule_engine = RuleEngine(self.log_manager)
        
        # 初始化LLM
        self.llm = ChatOllama(
            model=model_name,
            base_url=base_url,
            temperature=temperature,
            client_kwargs={"trust_env": False}
        )
        
        # 状态机配置和运行时状态
        self.state_machine_config: Optional[StateMachineConfig] = None
        self.current_state: str = ""
        self.execution_status = ExecutionStatus.PENDING
        
        # 工具映射
        self.tool_mapping = self._create_tool_mapping()
        
        self.log_manager.info_agent(f"🤖 StatusAgent初始化完成 - 任务ID: {self.task_id}")
        self.log_manager.info_agent(f"📋 可用工具: {list(self.tool_mapping.keys())}")
    
    def _create_tool_mapping(self) -> Dict[str, callable]:
        """创建工具映射"""
        return {
            'find_available_device': self._tool_find_device,
            'start_device_test': self._tool_start_test,
            'end_device_test': self._tool_end_test,
            'wait_seconds': self._tool_wait,
            'take_screenshot': self._tool_screenshot,
            'tap_device': self._tool_tap,
            'slide_device': self._tool_slide,
            'ocr_text_only': self._tool_ocr_text,
            'ocr_text_validation': self._tool_ocr_validation,
            'find_element_on_page': self._tool_find_element,
            'analyze_meituan_page': self._tool_analyze_page,
            'input_text_smart': self._tool_input_text,
            'restart_application': self._tool_restart_app,
            'app_background_switch': self._tool_background_switch,
            'record_agent_summary': self._tool_record_summary,
            'record_test_issue': self._tool_record_issue,
        }
    
    def load_state_machine_config(self, config_data: Dict[str, Any]) -> bool:
        """加载状态机配置"""
        try:
            self.log_manager.info_agent("📥 开始加载状态机配置...")
            
            # 解析配置
            initial_state = config_data['initial_state']
            states_data = config_data['states']
            global_config = config_data.get('global_config', {})
            
            # 构建状态配置
            states = {}
            for state_name, state_data in states_data.items():
                actions = []
                for action_data in state_data['actions']:
                    action = ActionConfig(
                        tool=action_data['tool'],
                        parameters=action_data['parameters'],
                        expected_result=action_data['expected_result'],
                        retry_config=action_data.get('retry_config'),
                        timeout=action_data.get('timeout', 60)
                    )
                    actions.append(action)
                
                state = StateConfig(
                    description=state_data['description'],
                    actions=actions,
                    transitions=state_data['transitions'],
                    timeout=state_data.get('timeout', 300)
                )
                states[state_name] = state
            
            # 创建状态机配置
            self.state_machine_config = StateMachineConfig(
                initial_state=initial_state,
                states=states,
                global_config=global_config
            )
            
            self.current_state = initial_state
            self.blackboard.set('current_state', initial_state)
            
            self.log_manager.info_agent(f"✅ 状态机配置加载成功")
            self.log_manager.info_agent(f"📊 配置包含 {len(states)} 个状态，起始状态: {initial_state}")
            
            return True
            
        except Exception as e:
            self.log_manager.error_agent(f"❌ 加载状态机配置失败: {e}")
            self.log_manager.error_agent(f"详细错误: {traceback.format_exc()}")
            return False
    
    def execute_test_plan(self) -> str:
        """执行测试计划"""
        if not self.state_machine_config:
            return "❌ 未加载状态机配置，无法执行测试"
        
        self.log_manager.info_agent("🚀 开始执行状态机驱动的测试计划")
        self.execution_status = ExecutionStatus.RUNNING
        
        try:
            # 开始任务日志记录
            task_id = self.log_manager.start_task("StatusAgent测试执行")
            
            # 状态机执行循环
            max_iterations = 50  # 防止无限循环
            iteration_count = 0
            
            while (self.current_state != "CompletedState" and 
                   self.execution_status == ExecutionStatus.RUNNING and 
                   iteration_count < max_iterations):
                
                iteration_count += 1
                self.log_manager.info_agent(f"🔄 执行第 {iteration_count} 次状态机循环")
                
                # 执行当前状态
                state_result = self._execute_current_state()
                
                if state_result == ActionResult.SUCCESS:
                    # 状态执行成功，检查是否需要转换
                    next_state = self._get_next_state()
                    if next_state and next_state != self.current_state:
                        self._transition_to_state(next_state)
                    elif next_state == self.current_state:
                        self.log_manager.info_agent("⏸️  状态机在当前状态等待...")
                        time.sleep(1)  # 防止busy loop
                    else:
                        self.log_manager.warning_agent("⚠️ 无法确定下一个状态，可能配置有误")
                        break
                
                elif state_result == ActionResult.FAILED:
                    self.log_manager.error_agent("❌ 状态执行失败")
                    self.execution_status = ExecutionStatus.FAILED
                    break
                
                elif state_result == ActionResult.ABORT:
                    self.log_manager.error_agent("🛑 测试被中止")
                    self.execution_status = ExecutionStatus.FAILED
                    break
            
            # 检查完成状态
            if self.current_state == "CompletedState":
                self.execution_status = ExecutionStatus.COMPLETED
                result_message = "✅ 测试计划执行完成"
            elif iteration_count >= max_iterations:
                self.execution_status = ExecutionStatus.FAILED
                result_message = f"❌ 达到最大迭代次数 ({max_iterations})，强制停止"
            else:
                result_message = f"❌ 测试执行异常停止，状态: {self.execution_status.value}"
            
            # 结束任务日志记录
            self.log_manager.end_task(status=self.execution_status.value)
            
            self.log_manager.info_agent(f"🏁 测试执行结束: {result_message}")
            return result_message
            
        except Exception as e:
            self.execution_status = ExecutionStatus.FAILED
            error_message = f"❌ 执行测试计划时发生异常: {e}"
            self.log_manager.error_agent(error_message)
            self.log_manager.error_agent(f"详细错误: {traceback.format_exc()}")
            
            # 确保任务日志正确结束
            try:
                self.log_manager.end_task(status="异常")
            except:
                pass
                
            return error_message
    
    def _execute_current_state(self) -> ActionResult:
        """执行当前状态"""
        if not self.state_machine_config or self.current_state not in self.state_machine_config.states:
            self.log_manager.error_agent(f"❌ 无效状态: {self.current_state}")
            return ActionResult.ABORT
        
        current_state_config = self.state_machine_config.states[self.current_state]
        self.log_manager.info_agent(f"🎯 执行状态: {self.current_state} - {current_state_config.description}")
        
        # 获取当前动作索引
        action_index = self.blackboard.get('current_action_index', 0)
        actions = current_state_config.actions
        
        if action_index >= len(actions):
            # 当前状态的所有动作都已执行完成
            self.log_manager.info_agent(f"✅ 状态 {self.current_state} 的所有动作执行完成")
            return ActionResult.SUCCESS
        
        # 执行当前动作
        action = actions[action_index]
        self.log_manager.info_agent(f"🔧 执行动作 [{action_index + 1}/{len(actions)}]: {action.tool}")
        
        # 开始轮次记录
        round_id = self.log_manager.start_round()
        
        try:
            # 解析动作参数
            resolved_params = self._resolve_parameters(action.parameters)
            
            # 执行工具
            tool_start_time = time.time()
            tool_result = self._execute_tool(action.tool, resolved_params)
            tool_duration = time.time() - tool_start_time
            
            # 记录工具执行
            self.log_manager.log_tool_execution(
                tool_name=action.tool,
                tool_args=resolved_params,
                tool_result=tool_result,
                duration=tool_duration
            )
            
            # 分析结果
            result_analysis = self._analyze_tool_result(tool_result, action.expected_result)
            
            # 添加执行日志到黑板
            self.blackboard.add_execution_log({
                'state': self.current_state,
                'action_index': action_index,
                'action': action.tool,
                'parameters': resolved_params,
                'result': result_analysis['status'],
                'duration': tool_duration
            })
            
            if result_analysis['status'] == 'success':
                # 动作执行成功，移动到下一个动作
                self.blackboard.set('current_action_index', action_index + 1)
                self.log_manager.info_agent(f"✅ 动作执行成功，准备执行下一个动作")
                return ActionResult.SUCCESS
                
            else:
                # 动作执行失败，尝试异常处理
                self.log_manager.warning_agent(f"⚠️ 动作执行失败: {result_analysis['reason']}")
                return self._handle_action_failure(action, result_analysis)
        
        except Exception as e:
            self.log_manager.error_agent(f"❌ 执行动作时发生异常: {e}")
            self.log_manager.error_agent(f"详细错误: {traceback.format_exc()}")
            
            # 尝试异常处理
            failure_info = {
                'status': 'error',
                'reason': f'执行异常: {str(e)}',
                'details': str(e)
            }
            return self._handle_action_failure(action, failure_info)
    
    def _handle_action_failure(self, action: ActionConfig, failure_info: Dict[str, Any]) -> ActionResult:
        """处理动作失败"""
        self.log_manager.info_agent(f"🔧 开始处理动作失败: {action.tool}")
        
        # 尝试规则引擎处理
        context = {
            'tool': action.tool,
            'failure_reason': failure_info.get('reason', ''),
            'current_state': self.current_state,
            'blackboard_data': self.blackboard.data
        }
        
        rule = self.rule_engine.match_rule('action_failed', context)
        
        if rule:
            self.log_manager.info_agent(f"📋 使用规则处理: {rule['name']}")
            return self._execute_rule_action(rule, action, failure_info)
        else:
            self.log_manager.info_agent("🤖 转交LLM处理复杂异常")
            return self._llm_decision_making(action, failure_info)
    
    def _execute_rule_action(self, rule: Dict[str, Any], action: ActionConfig, failure_info: Dict[str, Any]) -> ActionResult:
        """执行规则动作"""
        rule_action = rule['action']
        rule_params = rule.get('parameters', {})
        
        if rule_action == 'retry':
            max_retries = rule_params.get('max_retries', 1)
            wait_seconds = rule_params.get('wait_seconds', 0)
            
            self.log_manager.info_agent(f"🔄 规则重试: 等待{wait_seconds}秒后重试，最大重试{max_retries}次")
            
            if wait_seconds > 0:
                time.sleep(wait_seconds)
            
            # 简单的重试逻辑，这里可以扩展为更复杂的重试机制
            return ActionResult.RETRY
            
        elif rule_action == 'skip':
            self.log_manager.info_agent("⏭️  规则决定跳过当前动作")
            current_index = self.blackboard.get('current_action_index', 0)
            self.blackboard.set('current_action_index', current_index + 1)
            return ActionResult.SUCCESS
            
        elif rule_action == 'call_llm':
            self.log_manager.info_agent("🤖 规则要求调用LLM决策")
            return self._llm_decision_making(action, failure_info)
            
        else:
            self.log_manager.warning_agent(f"⚠️ 未知规则动作: {rule_action}")
            return ActionResult.FAILED
    
    def _llm_decision_making(self, action: ActionConfig, failure_info: Dict[str, Any]) -> ActionResult:
        """LLM决策处理"""
        self.log_manager.info_agent("🧠 LLM智能决策处理开始")
        
        try:
            # 构建决策提示词
            context = self.blackboard.get_context_for_llm()
            
            prompt = f"""
你是一个智能的移动端测试助手。当前测试过程中遇到了异常，需要你的决策。

【当前上下文】
{context}

【失败动作】
工具: {action.tool}
参数: {action.parameters}
预期结果: {action.expected_result}

【失败信息】
失败原因: {failure_info.get('reason', '未知')}
详细信息: {failure_info.get('details', '无')}

【可选决策】
1. retry - 重试当前动作
2. skip - 跳过当前动作，继续下一个
3. wait_retry - 等待5秒后重试
4. abort - 中止测试

请分析情况并返回JSON格式的决策：
{{
  "decision": "决策选项(retry/skip/wait_retry/abort)",
  "reason": "决策理由",
  "wait_seconds": 等待秒数(如果选择wait_retry),
  "additional_info": "额外信息"
}}
"""
            
            # 调用LLM
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # 解析LLM响应
            llm_decision = self._parse_llm_decision(response.content)
            
            self.log_manager.info_agent(f"🤖 LLM决策: {llm_decision['decision']} - {llm_decision['reason']}")
            
            # 执行LLM决策
            return self._execute_llm_decision(llm_decision)
            
        except Exception as e:
            self.log_manager.error_agent(f"❌ LLM决策处理失败: {e}")
            return ActionResult.ABORT
    
    def _parse_llm_decision(self, llm_response: str) -> Dict[str, Any]:
        """解析LLM决策响应"""
        try:
            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'\{.*?\}', llm_response, re.DOTALL)
            if json_match:
                decision_data = json.loads(json_match.group())
                return decision_data
            else:
                # 如果没有JSON，使用关键词分析
                if 'retry' in llm_response.lower():
                    return {'decision': 'retry', 'reason': 'LLM建议重试'}
                elif 'skip' in llm_response.lower():
                    return {'decision': 'skip', 'reason': 'LLM建议跳过'}
                elif 'abort' in llm_response.lower():
                    return {'decision': 'abort', 'reason': 'LLM建议中止'}
                else:
                    return {'decision': 'retry', 'reason': 'LLM响应不明确，默认重试'}
        except:
            return {'decision': 'retry', 'reason': '解析LLM响应失败，默认重试'}
    
    def _execute_llm_decision(self, decision: Dict[str, Any]) -> ActionResult:
        """执行LLM决策"""
        decision_action = decision.get('decision', 'retry')
        
        if decision_action == 'retry':
            return ActionResult.RETRY
        elif decision_action == 'skip':
            current_index = self.blackboard.get('current_action_index', 0)
            self.blackboard.set('current_action_index', current_index + 1)
            return ActionResult.SUCCESS
        elif decision_action == 'wait_retry':
            wait_seconds = decision.get('wait_seconds', 5)
            self.log_manager.info_agent(f"⏳ LLM决策等待 {wait_seconds} 秒后重试")
            time.sleep(wait_seconds)
            return ActionResult.RETRY
        elif decision_action == 'abort':
            return ActionResult.ABORT
        else:
            self.log_manager.warning_agent(f"⚠️ 未知LLM决策: {decision_action}")
            return ActionResult.RETRY
    
    def _get_next_state(self) -> Optional[str]:
        """获取下一个状态"""
        current_state_config = self.state_machine_config.states[self.current_state]
        action_index = self.blackboard.get('current_action_index', 0)
        
        if action_index >= len(current_state_config.actions):
            # 当前状态所有动作完成，查找转换目标
            # 这里简化处理，实际应该基于最后一个动作的结果
            transitions = current_state_config.transitions
            if transitions:
                # 取第一个转换目标（实际应该更智能）
                return list(transitions.values())[0]
        
        return None
    
    def _transition_to_state(self, next_state: str) -> None:
        """状态转换"""
        old_state = self.current_state
        self.log_manager.info_agent(f"🔀 状态转换: {old_state} -> {next_state}")
        
        # 黑板记忆清理
        self.blackboard.state_transition_cleanup(old_state, next_state)
        
        # 更新状态
        self.current_state = next_state
        self.blackboard.set('current_state', next_state)
        
        self.log_manager.info_agent(f"✅ 状态转换完成，当前状态: {next_state}")
    
    def _resolve_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """解析参数中的变量"""
        resolved = {}
        
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith('{') and value.endswith('}'):
                # 变量替换
                var_name = value[1:-1]
                if var_name == 'device_udid':
                    resolved[key] = self.blackboard.get_device_udid()
                elif var_name.startswith('element_'):
                    element_key = var_name.replace('element_', '')
                    elements = self.blackboard.get('elements', {})
                    if element_key in elements:
                        resolved[key] = elements[element_key]
                    else:
                        resolved[key] = value  # 保持原值
                else:
                    # 从global_variables中查找
                    global_vars = self.blackboard.get('global_variables', {})
                    resolved[key] = global_vars.get(var_name, value)
            else:
                resolved[key] = value
        
        return resolved
    
    def _execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """执行工具"""
        if tool_name not in self.tool_mapping:
            return json.dumps({
                "status": "error",
                "message": f"未知工具: {tool_name}"
            }, ensure_ascii=False)
        
        tool_func = self.tool_mapping[tool_name]
        return tool_func(parameters)
    
    def _analyze_tool_result(self, tool_result: str, expected_result: str) -> Dict[str, Any]:
        """分析工具执行结果"""
        try:
            result_data = json.loads(tool_result)
            status = result_data.get('status', 'unknown')
            
            if status == 'success':
                return {
                    'status': 'success',
                    'reason': '工具执行成功',
                    'data': result_data
                }
            else:
                return {
                    'status': 'failed',
                    'reason': result_data.get('message', '工具执行失败'),
                    'data': result_data
                }
        except:
            # 非JSON响应，简单分析
            if 'success' in tool_result.lower() or '成功' in tool_result:
                return {
                    'status': 'success',
                    'reason': '工具执行成功',
                    'data': {'raw_result': tool_result}
                }
            else:
                return {
                    'status': 'failed', 
                    'reason': '工具执行可能失败',
                    'data': {'raw_result': tool_result}
                }
    
    # 工具封装方法 - 复用现有的工具基础设施
    def _tool_find_device(self, params: Dict[str, Any]) -> str:
        """查找设备工具"""
        try:
            platform = params.get('platform', 'ios')
            udid = get_available_device(platform)
            
            if udid:
                # 更新黑板记忆
                device_info = {
                    'udid': udid,
                    'platform': platform,
                    'device_name': f'{platform.upper()} Device'
                }
                self.blackboard.set('device_info', device_info)
                
                return json.dumps({
                    "status": "success",
                    "udid": udid,
                    "platform": platform,
                    "message": f"找到可用的{platform}设备"
                }, ensure_ascii=False)
            else:
                return json.dumps({
                    "status": "error",
                    "message": f"未找到可用的{platform}设备"
                }, ensure_ascii=False)
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"查找设备失败: {str(e)}"
            }, ensure_ascii=False)
    
    def _tool_start_test(self, params: Dict[str, Any]) -> str:
        """启动测试工具"""
        udid = params.get('udid', '')
        return start_test(udid)
    
    def _tool_end_test(self, params: Dict[str, Any]) -> str:
        """结束测试工具"""
        udid = params.get('udid', '')
        return end_test(udid)
    
    def _tool_wait(self, params: Dict[str, Any]) -> str:
        """等待工具"""
        seconds = params.get('seconds', 3)
        return wait_for_seconds(seconds)
    
    def _tool_screenshot(self, params: Dict[str, Any]) -> str:
        """截图工具"""
        udid = params.get('udid', '')
        return json.dumps(get_screenshot(udid), ensure_ascii=False)
    
    def _tool_tap(self, params: Dict[str, Any]) -> str:
        """点击工具"""
        udid = params.get('udid', '')
        x = params.get('x', 0)
        y = params.get('y', 0)
        
        success = tap(udid, x, y)
        return json.dumps({
            "status": "success" if success else "failed",
            "message": f"{'成功' if success else '失败'}点击坐标 ({x}, {y})"
        }, ensure_ascii=False)
    
    def _tool_slide(self, params: Dict[str, Any]) -> str:
        """滑动工具"""
        udid = params.get('udid', '')
        from_x = params.get('from_x', 0.5)
        from_y = params.get('from_y', 0.5)
        to_x = params.get('to_x', 0.5)
        to_y = params.get('to_y', 0.8)
        duration = params.get('duration', 0.5)
        
        success = slide(udid, from_x, from_y, to_x, to_y, duration)
        return json.dumps({
            "status": "success" if success else "failed",
            "message": f"{'成功' if success else '失败'}执行滑动操作"
        }, ensure_ascii=False)
    
    def _tool_ocr_text(self, params: Dict[str, Any]) -> str:
        """OCR文本识别工具"""
        udid = params.get('udid', '')
        result = show_page_ocr_result(udid)
        
        # 更新页面信息到黑板
        page_info = {
            'ocr_text': result.get('final_text', ''),
            'image_url': result.get('image_url', '')
        }
        self.blackboard.update({'page_info': page_info})
        
        return json.dumps(result, ensure_ascii=False)
    
    def _tool_ocr_validation(self, params: Dict[str, Any]) -> str:
        """OCR文本校验工具"""
        udid = params.get('udid', '')
        target_text = params.get('target_text', '')
        
        result = locate_element_from_ocr(udid, target_text)
        return json.dumps(result, ensure_ascii=False)
    
    def _tool_find_element(self, params: Dict[str, Any]) -> str:
        """查找元素工具"""
        udid = params.get('udid', '')
        element = params.get('element', '')
        scene_desc = params.get('scene_desc', '元素查找')
        
        result = locate_element_from_layout(udid, element, scene_desc)
        
        # 如果找到元素，更新到黑板记忆
        if result.get('status') == 'success' and '找到' in result.get('text_result', ''):
            # 尝试解析坐标（这里简化处理）
            text_result = result.get('text_result', '')
            # 假设结果包含坐标信息，实际需要根据具体返回格式解析
            elements = self.blackboard.get('elements', {})
            elements[element] = {
                'found': True,
                'result': text_result,
                'timestamp': time.time()
            }
            self.blackboard.set('elements', elements)
        
        return json.dumps(result, ensure_ascii=False)
    
    def _tool_analyze_page(self, params: Dict[str, Any]) -> str:
        """分析页面工具"""
        udid = params.get('udid', '')
        action_description = params.get('action_description', '')
        model = params.get('model', 'qwen2.5vl:3b')
        
        result = analysis_now_page(udid, action_description, model)
        
        # 更新页面信息到黑板
        if result.get('status') == 'success':
            page_info = self.blackboard.get('page_info', {})
            page_info.update({
                'page_analysis': result.get('text_result', ''),
                'image_url': result.get('image_url', ''),
                'action_description': action_description
            })
            self.blackboard.set('page_info', page_info)
        
        return json.dumps(result, ensure_ascii=False)
    
    def _tool_input_text(self, params: Dict[str, Any]) -> str:
        """输入文本工具"""
        udid = params.get('udid', '')
        text = params.get('text', '')
        element_index = params.get('element_index')
        
        return smart_input_text(udid, text, element_index)
    
    def _tool_restart_app(self, params: Dict[str, Any]) -> str:
        """重启应用工具"""
        udid = params.get('udid', '')
        result = restart_app(udid)
        return json.dumps({"status": result, "message": f"重启应用: {result}"}, ensure_ascii=False)
    
    def _tool_background_switch(self, params: Dict[str, Any]) -> str:
        """后台切换工具"""
        udid = params.get('udid', '')
        result = background_switch(udid)
        return json.dumps({"status": result, "message": f"后台切换: {result}"}, ensure_ascii=False)
    
    def _tool_record_summary(self, params: Dict[str, Any]) -> str:
        """记录总结工具"""
        summary_text = params.get('summary_text', '')
        summary_type = params.get('summary_type', '状态执行总结')
        return record_summary(summary_text, summary_type)
    
    def _tool_record_issue(self, params: Dict[str, Any]) -> str:
        """记录问题工具"""
        issue_text = params.get('issue_text', '')
        issue_type = params.get('issue_type', '状态执行问题')
        severity = params.get('severity', '中等')
        return record_issue(issue_text, issue_type, severity)
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态信息"""
        return {
            'task_id': self.task_id,
            'current_state': self.current_state,
            'execution_status': self.execution_status.value,
            'current_action_index': self.blackboard.get('current_action_index', 0),
            'device_info': self.blackboard.get('device_info', {}),
            'total_states': len(self.state_machine_config.states) if self.state_machine_config else 0
        }


if __name__ == "__main__":
    # 测试StatusAgent
    print("🧪 测试StatusAgent")
    
    agent = StatusAgent(task_id="status_test_001")
    
    # 简单测试配置
    test_config = {
        "initial_state": "TestState",
        "states": {
            "TestState": {
                "description": "测试状态",
                "actions": [
                    {
                        "tool": "wait_seconds",
                        "parameters": {"seconds": 1},
                        "expected_result": "wait_complete"
                    }
                ],
                "transitions": {
                    "wait_complete": "CompletedState"
                }
            },
            "CompletedState": {
                "description": "完成状态",
                "actions": [],
                "transitions": {}
            }
        },
        "global_config": {}
    }
    
    # 加载配置并执行
    if agent.load_state_machine_config(test_config):
        result = agent.execute_test_plan()
        print(f"✅ 测试结果: {result}")
        print(f"📊 最终状态: {agent.get_status()}")
    else:
        print("❌ 配置加载失败")