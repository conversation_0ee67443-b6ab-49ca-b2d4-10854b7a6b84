#!/usr/bin/env python3
"""
实际转换human_plan.json为StatusAgent可执行的状态机配置
"""

import json
import os
import sys

# 添加父目录到路径，以便作为独立脚本运行
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    # 尝试相对导入（作为模块使用时）
    from .plan_converter import convert_human_plan, PlanConverter, StateIdentificationStrategy
except ImportError:
    # 回退到绝对导入（作为独立脚本使用时）
    from status_machine.plan_converter import convert_human_plan, PlanConverter, StateIdentificationStrategy


def main():
    """主函数：转换real plan"""
    
    # 文件路径 - 自动检测是否在status_machine目录下运行
    if os.path.basename(os.getcwd()) == 'status_machine':
        input_file = "../human_plan.json"
        output_file = "../state_machine_config.json"
        report_file = "../conversion_report.json"
    else:
        input_file = "human_plan.json"
        output_file = "state_machine_config.json" 
        report_file = "conversion_report.json"
    
    print("🚀 开始转换human_plan.json到StatusAgent状态机配置")
    print("=" * 60)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return
    
    try:
        # 执行转换
        config = convert_human_plan(input_file, output_file, report_file)
        
        print("=" * 60)
        print("🎉 转换成功完成！")
        print(f"📊 转换结果统计:")
        
        # 统计信息
        with open(input_file, 'r', encoding='utf-8') as f:
            original_plan = json.load(f)
        
        original_steps = len(original_plan.get('steps', []))
        generated_states = len(config.get('states', {}))
        
        print(f"  原始步骤数量: {original_steps}")
        print(f"  生成状态数量: {generated_states}")
        print(f"  压缩比例: {((original_steps - generated_states) / original_steps * 100):.1f}%")
        
        # 显示状态信息
        print(f"\n📋 生成的状态列表:")
        for i, (state_name, state_config) in enumerate(config['states'].items(), 1):
            action_count = len(state_config.get('actions', []))
            description = state_config.get('description', '')
            print(f"  {i}. {state_name}")
            print(f"     📝 {description}")
            print(f"     🔧 包含 {action_count} 个动作")
        
        # 显示文件位置
        print(f"\n📁 输出文件:")
        print(f"  状态机配置: {os.path.abspath(output_file)}")
        print(f"  转换报告: {os.path.abspath(report_file)}")
        
        # 验证配置
        print(f"\n🔍 配置验证:")
        converter = PlanConverter()
        validation = converter._validate_configuration(config)
        
        if validation['errors']:
            print(f"  ❌ 发现 {len(validation['errors'])} 个错误:")
            for error in validation['errors']:
                print(f"    - {error}")
        else:
            print(f"  ✅ 配置验证通过，无错误")
        
        if validation['warnings']:
            print(f"  ⚠️  发现 {len(validation['warnings'])} 个警告:")
            for warning in validation['warnings']:
                print(f"    - {warning}")
        
        # 使用建议
        print(f"\n💡 使用建议:")
        print(f"  1. 使用以下代码加载和执行状态机:")
        print(f"     ```python")
        print(f"     from status_agent import StatusAgent")
        print(f"     ")
        print(f"     agent = StatusAgent()")
        print(f"     with open('{output_file}', 'r') as f:")
        print(f"         config = json.load(f)")
        print(f"     agent.load_state_machine_config(config)")
        print(f"     result = agent.execute_test_plan()")
        print(f"     ```")
        print(f"  ")
        print(f"  2. 检查转换报告了解详细信息: {report_file}")
        print(f"  3. 如需调整状态分割策略，可修改StateIdentificationStrategy")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")


def analyze_original_plan():
    """分析原始计划的复杂性"""
    if os.path.basename(os.getcwd()) == 'status_machine':
        input_file = "../human_plan.json"
    else:
        input_file = "human_plan.json"
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    with open(input_file, 'r', encoding='utf-8') as f:
        plan_data = json.load(f)
    
    steps = plan_data.get('steps', [])
    
    print("🔍 原始计划分析:")
    print("=" * 40)
    print(f"测试计划ID: {plan_data.get('plan_id', 'unknown')}")
    print(f"目标平台: {plan_data.get('platform', 'unknown')}")
    print(f"总步骤数: {len(steps)}")
    
    # 分析工具使用
    tool_usage = {}
    for step in steps:
        tool = step.get('action', 'unknown')
        tool_usage[tool] = tool_usage.get(tool, 0) + 1
    
    print(f"\n🔧 工具使用统计:")
    for tool, count in sorted(tool_usage.items(), key=lambda x: x[1], reverse=True):
        print(f"  {tool}: {count} 次")
    
    # 分析参数依赖
    print(f"\n🔗 参数依赖分析:")
    dependencies = set()
    for step in steps:
        params = step.get('parameters', {})
        for param_value in params.values():
            if isinstance(param_value, str) and param_value.startswith('{') and param_value.endswith('}'):
                dependencies.add(param_value)
    
    if dependencies:
        print(f"  发现的变量依赖: {list(dependencies)}")
    else:
        print(f"  无变量依赖")
    
    # 分析预期的状态转换点
    print(f"\n🔄 潜在状态转换点:")
    navigation_steps = []
    for i, step in enumerate(steps):
        if step.get('action') in ['tap_device', 'restart_application']:
            navigation_steps.append((i + 1, step.get('description', '')))
    
    for step_id, desc in navigation_steps:
        print(f"  步骤 {step_id}: {desc}")


if __name__ == "__main__":
    # 先分析原始计划
    print("🔍 第一步：分析原始测试计划")
    analyze_original_plan()
    
    print("\n" + "=" * 60)
    
    # 执行转换
    print("🔄 第二步：执行转换")
    main()