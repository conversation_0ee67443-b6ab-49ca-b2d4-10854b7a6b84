#!/usr/bin/env python3
"""
StatusMachine模块使用示例
演示如何使用状态机Agent执行测试计划
"""

import json
import sys
import os

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from status_machine import StatusAgent, PlanConverter, convert_human_plan


def example_1_direct_usage():
    """示例1: 直接使用StatusAgent执行预定义的配置"""
    print("🔥 示例1: 直接使用StatusAgent")
    print("=" * 50)
    
    # 简单的测试配置
    test_config = {
        "initial_state": "InitState",
        "states": {
            "InitState": {
                "description": "初始化设备",
                "actions": [
                    {
                        "tool": "find_available_device",
                        "parameters": {"platform": "ios"},
                        "expected_result": "device_found"
                    },
                    {
                        "tool": "start_device_test",
                        "parameters": {"udid": "{device_udid}"},
                        "expected_result": "test_started"
                    }
                ],
                "transitions": {
                    "test_started": "TestState"
                }
            },
            "TestState": {
                "description": "执行测试",
                "actions": [
                    {
                        "tool": "wait_seconds",
                        "parameters": {"seconds": 2},
                        "expected_result": "wait_complete"
                    },
                    {
                        "tool": "end_device_test",
                        "parameters": {"udid": "{device_udid}"},
                        "expected_result": "test_ended"
                    }
                ],
                "transitions": {
                    "test_ended": "CompletedState"
                }
            },
            "CompletedState": {
                "description": "测试完成",
                "actions": [],
                "transitions": {}
            }
        },
        "global_config": {
            "max_execution_time": 600,
            "auto_screenshot": True
        }
    }
    
    # 创建StatusAgent并执行
    agent = StatusAgent(task_id="example_direct")
    
    if agent.load_state_machine_config(test_config):
        print("✅ 状态机配置加载成功")
        print(f"📊 当前状态: {agent.get_status()}")
        
        # 注意：这里只是演示配置加载，实际执行需要真实的设备环境
        # result = agent.execute_test_plan()
        # print(f"🎉 执行结果: {result}")
    else:
        print("❌ 状态机配置加载失败")


def example_2_convert_and_run():
    """示例2: 从human_plan.json转换并执行"""
    print("\n🔥 示例2: 转换human_plan.json并执行")
    print("=" * 50)
    
    # 检查human_plan.json是否存在  
    # 使用项目根目录路径
    project_root = os.path.dirname(os.path.dirname(__file__))
    plan_file = os.path.join(project_root, "human_plan.json")
    if not os.path.exists(plan_file):
        print(f"❌ 测试计划文件不存在: {plan_file}")
        return
    
    try:
        # 转换测试计划
        print("🔄 转换测试计划...")
        status_machine_dir = os.path.dirname(__file__)
        config = convert_human_plan(
            plan_file, 
            os.path.join(status_machine_dir, "state_machine_config_example.json"),
            os.path.join(status_machine_dir, "conversion_report_example.json")
        )
        
        print("✅ 转换完成")
        print(f"📊 生成状态数量: {len(config.get('states', {}))}")
        
        # 创建StatusAgent并加载配置
        agent = StatusAgent(task_id="example_convert")
        
        if agent.load_state_machine_config(config):
            print("✅ 状态机配置加载成功")
            
            status = agent.get_status()
            print(f"📋 初始状态: {status['current_state']}")
            print(f"🎯 总状态数: {status['total_states']}")
            
            # 注意：实际执行需要真实设备环境
            # result = agent.execute_test_plan()
            # print(f"🎉 执行结果: {result}")
        else:
            print("❌ 状态机配置加载失败")
            
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")


def example_3_custom_converter():
    """示例3: 使用自定义转换器"""
    print("\n🔥 示例3: 自定义转换器使用")
    print("=" * 50)
    
    # 模拟测试计划
    custom_plan = {
        "plan_id": "custom_test",
        "platform": "ios",
        "steps": [
            {
                "step_id": 1,
                "action": "find_available_device",
                "description": "查找设备",
                "parameters": {"platform": "ios"},
                "expected_result": "device_found"
            },
            {
                "step_id": 2,
                "action": "start_device_test",
                "description": "启动测试",
                "parameters": {"udid": "{device_udid}"},
                "expected_result": "test_started"
            },
            {
                "step_id": 3,
                "action": "wait_seconds",
                "description": "等待",
                "parameters": {"seconds": 5},
                "expected_result": "wait_complete"
            },
            {
                "step_id": 4,
                "action": "end_device_test",
                "description": "结束测试",
                "parameters": {"udid": "{device_udid}"},
                "expected_result": "test_ended"
            }
        ]
    }
    
    # 使用PlanConverter进行转换
    converter = PlanConverter()
    config = converter.convert_plan_to_state_machine(custom_plan)
    
    print(f"✅ 自定义计划转换完成")
    print(f"📊 生成状态: {list(config['states'].keys())}")
    
    # 验证配置
    validation = converter._validate_configuration(config)
    if validation['errors']:
        print(f"❌ 验证错误: {validation['errors']}")
    else:
        print("✅ 配置验证通过")


def example_4_tool_registry():
    """示例4: 工具注册中心使用"""
    print("\n🔥 示例4: 工具注册中心")
    print("=" * 50)
    
    from status_machine import get_tool_registry, ToolCategory, register_new_tool, StateTransitionType
    
    # 获取工具注册中心
    registry = get_tool_registry()
    
    print(f"📋 当前注册工具数量: {len(registry.get_all_tools())}")
    
    # 查看UI交互工具
    ui_tools = registry.get_tools_by_category(ToolCategory.UI_INTERACTION)
    print(f"🖱️  UI交互工具: {ui_tools}")
    
    # 注册新工具示例
    register_new_tool(
        name="custom_validation",
        category=ToolCategory.VALIDATION,
        description="自定义验证工具",
        parameters={
            "udid": {"type": "string", "required": True},
            "validation_rule": {"type": "string", "required": True}
        },
        return_format="{'status': 'success/failed', 'result': '...'}",
        transition_type=StateTransitionType.NO_TRANSITION
    )
    
    print(f"✅ 新工具注册完成，总数量: {len(registry.get_all_tools())}")
    
    # 验证工具序列
    test_sequence = ["find_available_device", "start_device_test", "custom_validation", "end_device_test"]
    warnings = registry.validate_tool_sequence(test_sequence)
    suggestions = registry.suggest_missing_tools(test_sequence)
    
    print(f"⚠️ 序列验证警告: {warnings}")
    print(f"💡 建议添加工具: {suggestions}")


def main():
    """主函数"""
    print("🚀 StatusMachine模块使用示例")
    print("=" * 60)
    
    try:
        # 运行所有示例
        example_1_direct_usage()
        example_2_convert_and_run()
        example_3_custom_converter()
        example_4_tool_registry()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例演示完成!")
        print("\n💡 使用提示:")
        print("1. 确保已安装所需依赖: langchain_ollama")
        print("2. 实际执行需要配置设备环境和工具函数")
        print("3. 可以根据需要扩展工具注册中心")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()