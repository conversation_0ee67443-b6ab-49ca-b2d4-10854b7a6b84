#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动提交任务脚本
每隔15分钟自动提交Android和iOS测试任务
"""

import requests
import time
import logging
from datetime import datetime

# 配置日志
import os
os.makedirs('log', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log/auto_submit_tasks.log'),
        logging.StreamHandler()
    ]
)

# 配置参数
BASE_URL = "http://qaassist.sankuai.com"
SEND_MESSAGE_ENDPOINT = "/compass/api/ai-test-record/send-message"
TARGET_IP = "************"
INTERVAL_MINUTES = 15

# 基础任务提示词模板（通用开头，会自动拼接设备类型）
BASE_PROMPT_TEMPLATE = "请你按照下面的步骤来一步步进行操作: 1.连接我的 {} 手机并完成初始化;"

# 具体任务提示词列表（每个任务的具体步骤）
TASK_PROMPTS = [
    "2.查找当前页面中展示地址的位置;3.点击地址;4.通过 ocr 工具校验是否存在文案\"搜索城市/区县/地点\";5.查找当前页面的搜索框;6.点击搜索框;7.输入\"北京\";8.通过 ocr 工具查找\"北京市\"位置;9.点击\"北京市\";10.检查新页面中地址选择器信息是否变为了北京;11.结束测试",
    "2.搜索当前页面搜索框的位置; 3.点击当前页面的搜索框; 4.在当前页面搜索框中输入\"奶茶\"; 5.通过 ocr 找到\"搜索\"按 钮的坐标; 6.点击 \"搜索\" 按钮; 7.告诉我当前页面有什么; 8.结束测试",
    "2.通过 ocr 工具找到导航区\"外卖\"图标;3.点击\"外卖\"图标，进入外卖首页;4.通过 ocr 工具找到外卖店铺;5.点击任一店铺;6.在店铺页面从下往上滑动，展示出更多商品信息;7.通过 ocr 工具识别商家页面;8.选取可以下单的商品，直到店铺页右下角出现“去结算”按钮;9.通过 ocr 工具找到\"去结算\";10.点击\"去结算\";11.弹出下单弹框后使用 ocr 工具检查是否有金额信息;12.结束测试",
    "2.通过 ocr 工具检查是否存在\"资质与规则\";3.从下往上滑动当前页面;4.等待 2 秒;5.通过 ocr 工具检查是否存在\"资质与规则\";6.点击\"资质与规则\";7.通过 ocr 工具检查是否存在\"美团规则\";8.查找当前页面的返回按钮位置并点击;9.通过 ocr 工具检查是否存在\"资质与规则\";10.结束测试",
    "2.搜索当前页面中搜索框的位置;3.点击搜索框;4.通过 ocr 工具检查是否存在\"历史搜索\"并记录内容;5.通过 ocr 工具找到\"搜索\"按钮的坐标;6.点击 \"搜索\" 按钮;7.查找当前页面的返回按钮位置并点击;8.检查当前页面中\"历史搜索\"内容是否有更新;9.结束测试",
    "2.通过 ocr 工具找到导航区\"团购\"图标;3.点击\"团购\"图标，进入团购首页;4.通过 ocr 工具找到团购商品;5.点击任一商品;6.通过 ocr 工具找到\"立即秒杀\"按钮;7.点击\"立即秒杀\"按钮;8.选取可以下单的商品，直到店铺页右下角出现“去结算”按钮;9.弹出下单弹框后使用 ocr 工具检查是否有金额信息;10.结束测试",
    "2.搜索当前页面中底 tab 里\"消息\"的位置;3.点击\"消息\"图标，进入消息页面;4.通过 ocr 工具检查是否存在\"订单动态\"和\"服务提醒\";5.点击\"订单动态\";6.查找当前页面的返回按钮位置并点击;7.校验一下是否回到了消息页面;8.结束测试",
    "2.搜索当前页面中底 tab 里\"我的\"的位置;3.点击\"我的\"图标，进入我的页面;4.通过 ocr 工具找到\"设置\";5.点击\"设置\";6.在设置页面从下往上滑动，展示出更多设置信息;7.点击\"切换账号\";8.点击\"添加账号\";9.通过 ocr 工具检查是否存在\"密码登录\";10.如果存在，点击\"密码登录\";11.在当前页面输入账号密码，账号为 14000382711，密码为 mttest123456 ;12.点击\"我已阅读并同意\";13.点击\"登录\";14.使用 ocr 工具分析当前页面;15.结束测试",
]

class TaskSubmitter:
    def __init__(self):
        self.current_prompt_index = 0
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AutoTaskSubmitter/1.0'
        })
    
    def send_message(self, device_type: str, prompt: str):
        """
        发送消息到后端接口
        
        Args:
            device_type: 设备类型 ('android' 或 'ios')
            prompt: 任务提示词
            
        Returns:
            是否成功提交
        """
        url = f"{BASE_URL}{SEND_MESSAGE_ENDPOINT}"
        
        # 根据后端接口规范构造请求数据
        payload = {
            "targetIp": TARGET_IP,
            "message": prompt,
            "misId": "cuijie12"  # 使用指定的MIS ID
        }
        
        try:
            logging.info(f"正在提交{device_type}任务...")
            response = self.session.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                logging.info(f"成功提交{device_type}任务")
                return True
            else:
                logging.error(f"提交{device_type}任务失败: HTTP {response.status_code}")
                logging.error(f"响应内容: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logging.error(f"提交{device_type}任务时发生网络错误: {e}")
            return False
    
    def submit_tasks_for_current_prompt(self):
        """
        使用当前prompt提交Android和iOS任务
        """
        current_task_prompt = TASK_PROMPTS[self.current_prompt_index]
        logging.info(f"使用第{self.current_prompt_index + 1}个任务prompt提交任务")
        logging.info(f"当前任务步骤: {current_task_prompt[:100]}...")
        
        # 为Android设备生成完整prompt
        android_prompt = BASE_PROMPT_TEMPLATE.format("android") + " " + current_task_prompt
        android_success = self.send_message("android", android_prompt)
        
        # 等待一小段时间再提交iOS任务
        time.sleep(2)
        
        # 为iOS设备生成完整prompt
        ios_prompt = BASE_PROMPT_TEMPLATE.format("ios") + " " + current_task_prompt
        ios_success = self.send_message("ios", ios_prompt)
        
        if android_success and ios_success:
            logging.info("本轮任务提交完成")
        else:
            logging.warning("本轮任务提交存在失败")
    
    def next_prompt(self):
        """
        切换到下一个prompt
        """
        self.current_prompt_index = (self.current_prompt_index + 1) % len(TASK_PROMPTS)
        logging.info(f"切换到第{self.current_prompt_index + 1}个prompt")
    
    def run(self):
        """
        主运行循环
        """
        logging.info("自动任务提交脚本启动")
        logging.info(f"目标服务器: {BASE_URL}")
        logging.info(f"目标IP: {TARGET_IP}")
        logging.info(f"提交间隔: {INTERVAL_MINUTES}分钟")
        logging.info(f"总共有{len(TASK_PROMPTS)}个任务prompt")
        
        try:
            while True:
                start_time = datetime.now()
                logging.info(f"开始新一轮任务提交 - {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 提交当前prompt的任务
                self.submit_tasks_for_current_prompt()
                
                # 切换到下一个prompt
                self.next_prompt()
                
                # 等待15分钟
                logging.info(f"等待{INTERVAL_MINUTES}分钟后进行下一轮提交...")
                time.sleep(INTERVAL_MINUTES * 60)
                
        except KeyboardInterrupt:
            logging.info("收到中断信号，正在停止...")
        except Exception as e:
            logging.error(f"运行过程中发生错误: {e}")
            raise

def main():
    """
    主函数
    """
    if not TASK_PROMPTS or len(TASK_PROMPTS[0]) == 0:
        logging.error("请先在TASK_PROMPTS列表中添加任务步骤")
        return
    
    submitter = TaskSubmitter()
    submitter.run()

if __name__ == "__main__":
    main()