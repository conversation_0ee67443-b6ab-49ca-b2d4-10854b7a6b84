import json
import os
import time
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class TaskInfo:
    round_id: str
    task_description: str
    status: str
    start_time: str
    mis_id: Optional[str] = None
    end_time: Optional[str] = None
    result: Optional[str] = None
    error: Optional[str] = None
    ollama_port: Optional[int] = None  # 新增：记录使用的 Ollama 端口

class StatusManager:
    def __init__(self, status_dir: str = "status"):
        self.status_dir = status_dir
        self.status_file = os.path.join(status_dir, "agent_status.json")
        self.ensure_status_dir()
        self.load_status()
    
    def ensure_status_dir(self):
        """确保status目录存在"""
        if not os.path.exists(self.status_dir):
            os.makedirs(self.status_dir)
    
    def load_status(self):
        """加载状态文件"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.current_round = data.get('current_round', 0)
                    self.tasks = data.get('tasks', {})
            except (json.JSONDecodeError, FileNotFoundError):
                self.current_round = 0
                self.tasks = {}
        else:
            self.current_round = 0
            self.tasks = {}
    
    def save_status(self):
        """保存状态到文件"""
        data = {
            'current_round': self.current_round,
            'tasks': self.tasks
        }
        with open(self.status_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def create_new_task(self, task_description: str, mis_id: Optional[str] = None) -> str:
        """创建新任务，返回轮次号"""
        self.current_round += 1
        round_id = f"round_{self.current_round:06d}"
        
        task_info = TaskInfo(
            round_id=round_id,
            task_description=task_description,
            status=TaskStatus.PENDING.value,
            start_time=datetime.now().isoformat(),
            mis_id=mis_id
        )
        
        self.tasks[round_id] = asdict(task_info)
        self.save_status()
        
        return round_id
    
    def get_round_log_info(self, round_id: str) -> Optional[Dict]:
        """获取轮次的日志信息"""
        if round_id in self.tasks:
            task_info = self.tasks[round_id]
            return {
                "round_id": round_id,
                "task_description": task_info["task_description"],
                "status": task_info["status"],
                "start_time": task_info["start_time"],
                "end_time": task_info.get("end_time"),
                "log_dir": f"log/{round_id}_{task_info['start_time'].replace(':', '').replace('-', '').replace('T', '_').split('.')[0]}"
            }
        return None
    
    def update_task_status(self, round_id: str, status: TaskStatus, result: Optional[str] = None, error: Optional[str] = None, ollama_port: Optional[int] = None):
        """更新任务状态"""
        if round_id in self.tasks:
            self.tasks[round_id]['status'] = status.value
            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                self.tasks[round_id]['end_time'] = datetime.now().isoformat()
            if result:
                self.tasks[round_id]['result'] = result
            if error:
                self.tasks[round_id]['error'] = error
            if ollama_port:
                self.tasks[round_id]['ollama_port'] = ollama_port
            self.save_status()
    
    def get_task_info(self, round_id: str) -> Optional[Dict]:
        """获取任务信息"""
        return self.tasks.get(round_id)
    
    def get_all_tasks(self) -> Dict:
        """获取所有任务信息"""
        return self.tasks
    
    def get_current_round(self) -> int:
        """获取当前轮次号"""
        return self.current_round
    
    def get_running_tasks(self) -> List[Dict]:
        """获取正在运行的任务"""
        return [task for task in self.tasks.values() if task['status'] == TaskStatus.RUNNING.value]
    
    def get_running_tasks_count(self) -> int:
        """获取正在运行的任务数量"""
        return len(self.get_running_tasks())
    
    def can_accept_new_task(self, max_concurrent_tasks: int = 2) -> bool:
        """检查是否可以接受新任务（基于最大并发任务数）"""
        return self.get_running_tasks_count() < max_concurrent_tasks