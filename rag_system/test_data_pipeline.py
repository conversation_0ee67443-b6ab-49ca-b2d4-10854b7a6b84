#!/usr/bin/env python3
"""
测试完整的RAG数据管道
验证知识提取器、数据库管理器和向量存储的端到端功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from rag_system.data_models import DatabaseConfig
from rag_system.database_manager import DatabaseManager
from rag_system.knowledge_extractor import KnowledgeExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataPipelineTester:
    """数据管道测试器"""
    
    def __init__(self, log_dir: str = "log"):
        self.log_dir = Path(log_dir)
        self.config = DatabaseConfig()
        self.db_manager = None
        self.knowledge_extractor = None
    
    def setup_database(self) -> bool:
        """初始化数据库管理器"""
        try:
            logger.info("🔧 初始化数据库管理器...")
            self.db_manager = DatabaseManager(self.config)
            logger.info("✅ 数据库管理器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库管理器初始化失败: {e}")
            return False
    
    def setup_extractor(self) -> bool:
        """初始化知识提取器"""
        try:
            logger.info("🔧 初始化知识提取器...")
            self.knowledge_extractor = KnowledgeExtractor(
                db_manager=self.db_manager,
                log_dir=str(self.log_dir)
            )
            logger.info("✅ 知识提取器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 知识提取器初始化失败: {e}")
            return False
    
    def test_embedding_service(self) -> bool:
        """测试embedding服务"""
        try:
            logger.info("🧪 测试embedding服务...")
            
            test_text = "测试embedding服务连接"
            embedding = self.db_manager.embedding_service.get_embedding(test_text)
            
            if embedding and len(embedding) == self.config.vector_dimension:
                logger.info(f"✅ Embedding服务正常，向量维度: {len(embedding)}")
                return True
            else:
                logger.error("❌ Embedding服务异常")
                return False
                
        except Exception as e:
            logger.error(f"❌ Embedding服务测试失败: {e}")
            return False
    
    def scan_log_folders(self) -> list:
        """扫描日志文件夹"""
        try:
            if not self.log_dir.exists():
                logger.error(f"❌ 日志目录不存在: {self.log_dir}")
                return []
            
            round_folders = [
                f for f in self.log_dir.iterdir() 
                if f.is_dir() and f.name.startswith("round_")
            ]
            
            round_folders.sort(key=lambda x: x.name)
            logger.info(f"📁 发现 {len(round_folders)} 个轮次文件夹")
            
            return round_folders
            
        except Exception as e:
            logger.error(f"❌ 扫描日志文件夹失败: {e}")
            return []
    
    def test_single_extraction(self, round_folder: Path) -> bool:
        """测试单个轮次的提取"""
        try:
            logger.info(f"🔍 测试提取: {round_folder.name}")
            
            # 测试提取执行信息
            execution = self.knowledge_extractor.extract_execution_from_log(round_folder)
            if not execution:
                logger.warning(f"⚠️  无法提取执行信息: {round_folder.name}")
                return False
            
            logger.info(f"📊 提取到执行信息:")
            logger.info(f"   - 执行ID: {execution.execution_id}")
            logger.info(f"   - 轮次: {execution.round_number}")
            logger.info(f"   - 指令: {execution.original_instruction[:50]}...")
            logger.info(f"   - 状态: {execution.execution_status.value}")
            logger.info(f"   - 平台: {execution.platform.value}")
            logger.info(f"   - 步骤数: {execution.total_steps}")
            
            # 测试提取步骤信息
            steps = self.knowledge_extractor.extract_steps_from_log(
                round_folder, execution.execution_id
            )
            logger.info(f"📋 提取到 {len(steps)} 个步骤")
            
            # 测试保存到数据库
            if self.db_manager.save_task_execution(execution):
                logger.info("✅ 执行信息保存成功")
                
                # 保存步骤信息
                saved_steps = 0
                for step in steps:
                    if self.db_manager.save_task_step(step):
                        saved_steps += 1
                
                logger.info(f"✅ 保存了 {saved_steps}/{len(steps)} 个步骤")
                return True
            else:
                logger.error("❌ 执行信息保存失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 单个提取测试失败 {round_folder.name}: {e}")
            return False
    
    def test_batch_processing(self, limit: int = 3) -> bool:
        """测试批量处理"""
        try:
            logger.info(f"🚀 测试批量处理（限制 {limit} 个）...")
            
            result = self.knowledge_extractor.batch_process_logs(limit=limit)
            
            if "error" in result:
                logger.error(f"❌ 批量处理失败: {result['error']}")
                return False
            
            logger.info("📊 批量处理结果:")
            logger.info(f"   - 总处理数: {result['total_processed']}")
            logger.info(f"   - 成功数: {result['successful']}")
            logger.info(f"   - 失败数: {result['failed']}")
            logger.info(f"   - 成功率: {result['success_rate']:.2%}")
            
            return result['successful'] > 0
            
        except Exception as e:
            logger.error(f"❌ 批量处理测试失败: {e}")
            return False
    
    def test_vector_search(self) -> bool:
        """测试向量搜索功能"""
        try:
            logger.info("🔍 测试向量搜索功能...")
            
            # 测试指令搜索
            search_query = "点击地址"
            results = self.db_manager.search_similar_instructions(
                query_text=search_query,
                top_k=3
            )
            
            logger.info(f"📝 指令搜索结果 ('{search_query}'):")
            for i, result in enumerate(results, 1):
                score = result.get('score', 0)
                payload = result.get('payload', {})
                text_content = payload.get('text_content', '')[:50]
                logger.info(f"   {i}. 相似度: {score:.4f}, 内容: {text_content}...")
            
            # 测试工具结果搜索
            tool_query = "截图成功"
            tool_results = self.db_manager.search_similar_tool_results(
                query_text=tool_query,
                top_k=3
            )
            
            logger.info(f"🔧 工具结果搜索 ('{tool_query}'):")
            for i, result in enumerate(tool_results, 1):
                score = result.get('score', 0)
                payload = result.get('payload', {})
                text_content = payload.get('text_content', '')[:50]
                logger.info(f"   {i}. 相似度: {score:.4f}, 内容: {text_content}...")
            
            return len(results) > 0 or len(tool_results) > 0
            
        except Exception as e:
            logger.error(f"❌ 向量搜索测试失败: {e}")
            return False
    
    def test_database_stats(self) -> bool:
        """测试数据库统计功能"""
        try:
            logger.info("📊 获取数据库统计信息...")
            
            stats = self.db_manager.get_database_stats()
            
            logger.info("📈 数据库统计:")
            logger.info(f"   - 总执行数: {stats.get('total_executions', 0)}")
            logger.info(f"   - 总步骤数: {stats.get('total_steps', 0)}")
            logger.info(f"   - 总向量数: {stats.get('total_vectors', 0)}")
            
            # 显示平台分布
            platform_stats = stats.get('executions_by_platform', {})
            if platform_stats:
                logger.info("   - 平台分布:")
                for platform, count in platform_stats.items():
                    logger.info(f"     * {platform}: {count}")
            
            # 显示向量集合统计
            vector_stats = stats.get('vector_collections', {})
            if vector_stats:
                logger.info("   - 向量集合:")
                for collection, count in vector_stats.items():
                    logger.info(f"     * {collection}: {count}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库统计测试失败: {e}")
            return False
    
    def run_comprehensive_test(self) -> bool:
        """运行综合测试"""
        logger.info("🚀 启动RAG数据管道综合测试")
        logger.info("=" * 60)
        
        test_results = []
        
        # 1. 初始化测试
        logger.info("\n📋 阶段1: 初始化测试")
        test_results.append(("数据库初始化", self.setup_database()))
        if not test_results[-1][1]:
            logger.error("❌ 数据库初始化失败，停止测试")
            return False
        
        test_results.append(("知识提取器初始化", self.setup_extractor()))
        if not test_results[-1][1]:
            logger.error("❌ 知识提取器初始化失败，停止测试") 
            return False
        
        test_results.append(("Embedding服务测试", self.test_embedding_service()))
        
        # 2. 数据提取测试
        logger.info("\n📋 阶段2: 数据提取测试")
        round_folders = self.scan_log_folders()
        
        if round_folders:
            # 测试单个提取
            test_folder = round_folders[-1]  # 选择最新的文件夹
            test_results.append(("单个轮次提取", self.test_single_extraction(test_folder)))
            
            # 测试批量处理
            test_results.append(("批量处理", self.test_batch_processing(limit=2)))
        else:
            logger.warning("⚠️  没有找到日志文件夹，跳过提取测试")
            test_results.append(("日志扫描", False))
        
        # 3. 向量搜索测试
        logger.info("\n📋 阶段3: 向量功能测试")
        test_results.append(("向量搜索", self.test_vector_search()))
        test_results.append(("数据库统计", self.test_database_stats()))
        
        # 4. 汇总结果
        logger.info("\n📊 测试结果汇总")
        logger.info("=" * 60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name:20} : {status}")
            if result:
                passed += 1
        
        success_rate = passed / total
        logger.info(f"\n🎯 总体结果: {passed}/{total} 通过 ({success_rate:.1%})")
        
        if success_rate >= 0.8:
            logger.info("🎉 RAG数据管道测试成功！系统已准备就绪")
            return True
        else:
            logger.error("❌ RAG数据管道测试未完全通过，请检查问题")
            return False

def main():
    """主函数"""
    tester = DataPipelineTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n✅ RAG数据管道测试完成，可以继续后续开发")
    else:
        print("\n❌ RAG数据管道测试失败，请修复问题后重试")
    
    return success

if __name__ == "__main__":
    main()