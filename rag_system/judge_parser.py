#!/usr/bin/env python3
"""
Judge报告解析器 - 解析process_judge.py生成的评价报告
专门处理log/round_xxx/judge_report.log文件
"""

import re
import json
import logging
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from .data_models import JudgeReport, ExecutionStatus
from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)


class JudgeReportParser:
    """Judge报告解析器 - 专门处理轮次文件夹内的judge_report.log"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def parse_judge_report_file(self, report_file_path: Path) -> Optional[JudgeReport]:
        """解析judge报告文件"""
        try:
            if not report_file_path.exists():
                logger.error(f"Judge报告文件不存在: {report_file_path}")
                return None
            
            with open(report_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.parse_judge_report_content(content, str(report_file_path))
            
        except Exception as e:
            logger.error(f"解析judge报告文件失败 {report_file_path}: {e}")
            return None
    
    def parse_judge_report_content(self, content: str, source_path: str = "") -> Optional[JudgeReport]:
        """解析judge报告内容 - 核心解析逻辑"""
        try:
            # 第一步：提取基本信息
            basic_info = self._extract_basic_info(content)
            if not basic_info:
                logger.error("无法提取judge报告基本信息")
                return None
            
            # 第二步：分离思考过程和主体分析内容
            thinking_process, main_analysis = self._separate_thinking_and_analysis(content)
            
            # 第三步：从主体分析中提取结构化信息
            evaluation_results = self._extract_evaluation_results(main_analysis)
            detailed_analysis = self._extract_detailed_analysis(main_analysis)
            issues_and_suggestions = self._extract_issues_and_suggestions(main_analysis)
            
            # 第四步：构建JudgeReport对象
            report = JudgeReport(
                report_id="",  # 将在__post_init__中生成
                execution_id=self._resolve_execution_id(basic_info),
                round_id=basic_info.get("round_id", "unknown"),
                judge_model=basic_info.get("judge_model", "unknown"),
                judge_timestamp=basic_info.get("judge_timestamp", datetime.now()),
                judge_version="1.0",
                
                # 总体评价结果
                overall_success=evaluation_results.get("overall_success", False),
                success_score=evaluation_results.get("success_score", 0.0),
                confidence_score=evaluation_results.get("confidence_score", 0.8),
                
                # 细分评价维度
                test_quality_score=evaluation_results.get("test_quality_score", 0.0),
                plan_quality_score=evaluation_results.get("plan_quality_score", 0.0),
                execution_quality_score=evaluation_results.get("execution_quality_score", 0.0),
                target_achievement_score=evaluation_results.get("target_achievement_score", 0.0),
                
                # 详细分析内容
                detailed_analysis=detailed_analysis,
                key_issues=issues_and_suggestions.get("key_issues", []),
                improvement_suggestions=issues_and_suggestions.get("improvement_suggestions", []),
                best_practices=issues_and_suggestions.get("best_practices", []),
                
                # 错误和失败分析
                failure_points=issues_and_suggestions.get("failure_points", []),
                error_categories=issues_and_suggestions.get("error_categories", []),
                root_cause_analysis=issues_and_suggestions.get("root_cause_analysis"),
                
                # 人工修正（初始为未修改）
                human_modified=False,
                human_override_success=None,
                human_override_score=None,
                human_notes=None,
                human_modifier=None,
                human_modified_at=None,
                
                # 原始数据
                raw_judge_output=content,
                judge_thinking_process=thinking_process,
                
                # 时间信息
                created_at=datetime.now(),
                updated_at=datetime.now(),
                
                # 元数据
                metadata={
                    "source_path": source_path,
                    "content_length": len(content),
                    "parsing_timestamp": datetime.now().isoformat(),
                    "has_thinking_process": bool(thinking_process),
                    "analysis_sections_found": len([s for s in ["1.", "2.", "3.", "4."] if s in main_analysis])
                }
            )
            
            logger.info(f"成功解析judge报告: {report.round_id}")
            return report
            
        except Exception as e:
            logger.error(f"解析judge报告内容失败: {e}")
            return None
    
    def _extract_basic_info(self, content: str) -> Optional[Dict[str, Any]]:
        """提取基本信息"""
        try:
            info = {}
            
            # 提取测试轮次
            round_patterns = [
                r'📁 测试轮次[：:]\s*(round_\d+_\d{8}_\d{6})',
                r'测试轮次[：:]\s*(round_\d+_\d{8}_\d{6})',
                r'(round_\d+_\d{8}_\d{6})'
            ]
            
            for pattern in round_patterns:
                match = re.search(pattern, content)
                if match:
                    info["round_id"] = match.group(1)
                    break
            
            # 提取轮次编号
            if "round_id" in info:
                round_num_match = re.search(r'round_(\d+)_', info["round_id"])
                if round_num_match:
                    info["round_number"] = int(round_num_match.group(1))
            
            # 提取分析时间
            time_patterns = [
                r'⏰ 分析时间[：:]\s*([\d\-: ]+)',
                r'分析时间[：:]\s*([\d\-: ]+)'
            ]
            
            for pattern in time_patterns:
                match = re.search(pattern, content)
                if match:
                    try:
                        info["judge_timestamp"] = datetime.strptime(match.group(1).strip(), "%Y-%m-%d %H:%M:%S")
                        break
                    except:
                        continue
            
            if "judge_timestamp" not in info:
                info["judge_timestamp"] = datetime.now()
            
            # 提取分析模型
            model_patterns = [
                r'🤖 分析模型[：:]\s*(.+?)(?:\n|$)',
                r'分析模型[：:]\s*(.+?)(?:\n|$)'
            ]
            
            for pattern in model_patterns:
                match = re.search(pattern, content)
                if match:
                    info["judge_model"] = match.group(1).strip()
                    break
            
            if "judge_model" not in info:
                info["judge_model"] = "unknown"
            
            return info if info else None
            
        except Exception as e:
            logger.error(f"提取基本信息失败: {e}")
            return None
    
    def _resolve_execution_id(self, basic_info: Dict[str, Any]) -> str:
        """根据基本信息解析execution_id"""
        try:
            # 方法1：通过round_id查询数据库
            round_id = basic_info.get("round_id")
            if round_id:
                round_num_match = re.search(r'round_(\d+)_', round_id)
                if round_num_match:
                    round_number = int(round_num_match.group(1))
                    executions = self.db_manager.get_executions_by_round(round_number)
                    if executions:
                        return executions[0].execution_id
            
            # 方法2：直接使用round_id作为fallback
            if round_id:
                return f"exec_{round_id}"
            
            return "unknown"
            
        except Exception as e:
            logger.error(f"解析execution_id失败: {e}")
            return "unknown"
    
    def _separate_thinking_and_analysis(self, content: str) -> Tuple[Optional[str], str]:
        """分离思考过程和主体分析内容"""
        try:
            thinking_process = None
            main_analysis = content
            
            # 查找分析思考过程部分
            thinking_patterns = [
                r'分析思考过程\s*={40,}\s*(.*?)={40,}',
                r'思考过程\s*={40,}\s*(.*?)={40,}'
            ]
            
            for pattern in thinking_patterns:
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    thinking_process = match.group(1).strip()
                    # 从主内容中移除思考过程部分
                    main_analysis = re.sub(pattern, '', content, flags=re.DOTALL)
                    break
            
            # 查找专业分析评价部分作为主体内容
            analysis_patterns = [
                r'专业分析评价\s*={40,}\s*(.*?)(?=\n={40,}|$)',
                r'AI智能分析结果\s*={40,}\s*(.*?)(?=\n={40,}|$)',
                r'分析结果\s*={40,}\s*(.*?)(?=\n={40,}|$)'
            ]
            
            for pattern in analysis_patterns:
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    main_analysis = match.group(1).strip()
                    break
            
            return thinking_process, main_analysis
            
        except Exception as e:
            logger.error(f"分离思考过程和分析内容失败: {e}")
            return None, content
    
    def _extract_evaluation_results(self, analysis_content: str) -> Dict[str, Any]:
        """从分析内容中提取评价结果"""
        results = {
            "overall_success": False,
            "success_score": 0.0,
            "confidence_score": 0.8,
            "test_quality_score": 0.0,
            "plan_quality_score": 0.0,
            "execution_quality_score": 0.0,
            "target_achievement_score": 0.0
        }
        
        try:
            # 提取最终成功状态
            success_patterns = [
                r'\*\*最终成功状态\*\*[：:]\s*(成功|失败)',
                r'最终状态[：:]\s*(成功|失败)',
                r'整体测试[是否]*成功[：:]?\s*(是|否|成功|失败)',
                r'测试.*?(成功|失败)',
                r'任务.*?(完成|成功|失败)'
            ]
            
            for pattern in success_patterns:
                match = re.search(pattern, analysis_content, re.IGNORECASE)
                if match:
                    status_text = match.group(1).strip()
                    if status_text in ['成功', '是', '完成']:
                        results["overall_success"] = True
                        results["success_score"] = 0.8  # 默认成功评分
                    break
            
            # 提取具体评分
            score_patterns = {
                "success_score": [
                    r'\*\*成功评分\*\*[：:]\s*([\d.]+)',
                    r'成功评分[：:]\s*([\d.]+)',
                    r'总体评分[：:]\s*([\d.]+)'
                ],
                "confidence_score": [
                    r'\*\*置信度\*\*[：:]\s*([\d.]+)',
                    r'置信度[：:]\s*([\d.]+)'
                ],
                "plan_quality_score": [
                    r'\*\*测试计划质量评分\*\*[：:]\s*([\d.]+)',
                    r'测试计划质量评分[：:]\s*([\d.]+)',
                    r'计划质量[：:]\s*([\d.]+)'
                ],
                "execution_quality_score": [
                    r'\*\*执行质量评分\*\*[：:]\s*([\d.]+)',
                    r'执行质量评分[：:]\s*([\d.]+)',
                    r'执行质量[：:]\s*([\d.]+)'
                ],
                "target_achievement_score": [
                    r'\*\*目标达成度评分\*\*[：:]\s*([\d.]+)',
                    r'目标达成度评分[：:]\s*([\d.]+)',
                    r'目标达成[：:]\s*([\d.]+)'
                ]
            }
            
            for score_key, patterns in score_patterns.items():
                for pattern in patterns:
                    match = re.search(pattern, analysis_content, re.IGNORECASE)
                    if match:
                        try:
                            score = float(match.group(1))
                            if 0 <= score <= 1:
                                results[score_key] = score
                            elif 0 <= score <= 100:
                                results[score_key] = score / 100.0
                            break
                        except:
                            continue
            
            # 如果没有找到具体的评分，基于文本内容推断
            if results["plan_quality_score"] == 0.0:
                if any(word in analysis_content for word in ['计划.*优秀', '计划.*良好', '设计.*合理']):
                    results["plan_quality_score"] = 0.8
                elif any(word in analysis_content for word in ['计划.*问题', '设计.*缺陷', '理解.*错误']):
                    results["plan_quality_score"] = 0.3
                else:
                    results["plan_quality_score"] = 0.5
            
            if results["execution_quality_score"] == 0.0:
                if any(word in analysis_content for word in ['执行.*准确', '执行.*正确', '操作.*准确']):
                    results["execution_quality_score"] = 0.8
                elif any(word in analysis_content for word in ['执行.*偏差', '操作.*错误', '步骤.*失败']):
                    results["execution_quality_score"] = 0.3
                else:
                    results["execution_quality_score"] = 0.5
            
            # 设置test_quality_score为其他维度的平均值
            results["test_quality_score"] = (
                results["plan_quality_score"] + results["execution_quality_score"]
            ) / 2
            
            # 设置target_achievement_score
            if results["target_achievement_score"] == 0.0:
                results["target_achievement_score"] = results["success_score"]
            
            return results
            
        except Exception as e:
            logger.error(f"提取评价结果失败: {e}")
            return results
    
    def _extract_detailed_analysis(self, analysis_content: str) -> str:
        """提取详细分析内容"""
        try:
            # 移除评分部分，保留分析文本
            cleaned_content = analysis_content
            
            # 移除评分行
            score_patterns = [
                r'\*\*[^*]+评分\*\*[：:]\s*[\d.]+\s*',
                r'\*\*最终成功状态\*\*[：:]\s*[成功失败]\s*',
                r'\*\*置信度\*\*[：:]\s*[\d.]+\s*'
            ]
            
            for pattern in score_patterns:
                cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.MULTILINE)
            
            # 移除过多的空行
            cleaned_content = re.sub(r'\n\s*\n', '\n\n', cleaned_content)
            
            # 移除特殊字符标记
            cleaned_content = re.sub(r'[📁🔢⏰🤖📋📊✅❌⚠️💡🔧📝]', '', cleaned_content)
            
            return cleaned_content.strip()
            
        except Exception as e:
            logger.error(f"提取详细分析失败: {e}")
            return "解析详细分析时出错"
    
    def _extract_issues_and_suggestions(self, analysis_content: str) -> Dict[str, List]:
        """提取问题和建议"""
        result = {
            "key_issues": [],
            "improvement_suggestions": [],
            "best_practices": [],
            "failure_points": [],
            "error_categories": [],
            "root_cause_analysis": None
        }
        
        try:
            # 查找四个维度的分析内容
            sections = {
                "1": "测试计划质量分析",
                "2": "执行符合度分析", 
                "3": "执行路径追踪分析",
                "4": "综合评价与建议"
            }
            
            for section_num, section_name in sections.items():
                # 查找该节的内容
                section_patterns = [
                    rf'##\s*{section_num}[.\s]*{section_name}(.*?)(?=##\s*[234]|$)',
                    rf'{section_num}[.\s]*\*\*{section_name}\*\*(.*?)(?=[234]\.|$)',
                    rf'{section_name}(.*?)(?=\n[234]\.|$)'
                ]
                
                section_content = ""
                for pattern in section_patterns:
                    match = re.search(pattern, analysis_content, re.DOTALL | re.IGNORECASE)
                    if match:
                        section_content = match.group(1).strip()
                        break
                
                if section_content:
                    # 提取关键问题
                    issues = self._extract_section_items(section_content, ["关键问题", "主要问题", "核心问题", "问题"])
                    result["key_issues"].extend(issues)
                    
                    # 提取改进建议
                    suggestions = self._extract_section_items(section_content, ["改进建议", "建议", "优化建议"])
                    result["improvement_suggestions"].extend(suggestions)
                    
                    # 提取最佳实践
                    practices = self._extract_section_items(section_content, ["最佳实践", "良好做法"])
                    result["best_practices"].extend(practices)
                    
                    # 提取根本问题
                    root_causes = self._extract_section_items(section_content, ["根本问题", "根因"])
                    if root_causes and not result["root_cause_analysis"]:
                        result["root_cause_analysis"] = "; ".join(root_causes)
            
            # 去重和清理
            for key in ["key_issues", "improvement_suggestions", "best_practices"]:
                result[key] = list(set([item for item in result[key] if len(item) > 10]))[:5]  # 限制数量
            
            return result
            
        except Exception as e:
            logger.error(f"提取问题和建议失败: {e}")
            return result
    
    def _extract_section_items(self, section_content: str, keywords: List[str]) -> List[str]:
        """从章节内容中提取特定类型的条目"""
        items = []
        
        try:
            for keyword in keywords:
                # 查找关键词后的内容
                patterns = [
                    rf'-\s*\*\*{keyword}\*\*[：:]\s*(.+?)(?=\n-|\n\*\*|\n##|$)',
                    rf'\*\*{keyword}\*\*[：:]\s*(.+?)(?=\n\*\*|\n##|\n-|$)',
                    rf'{keyword}[：:]\s*(.+?)(?=\n[A-Za-z\u4e00-\u9fa5]*[：:]|\n-|\n\*\*|$)'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, section_content, re.DOTALL | re.IGNORECASE)
                    for match in matches:
                        # 清理并分割成多个条目
                        cleaned = match.strip()
                        if cleaned:
                            # 按照列表标记分割
                            sub_items = re.split(r'\n\s*[-•]\s*', cleaned)
                            for item in sub_items:
                                item = item.strip()
                                if len(item) > 10:  # 过滤过短的条目
                                    items.append(item)
        
        except Exception as e:
            logger.error(f"提取章节条目失败: {e}")
        
        return items
    
    def process_round_folders_batch(self, log_dir: Path, limit: Optional[int] = None) -> Dict[str, Any]:
        """批量处理轮次文件夹中的judge报告"""
        try:
            # 查找所有轮次目录中的judge_report.log文件
            judge_files = []
            
            for round_dir in log_dir.iterdir():
                if round_dir.is_dir() and round_dir.name.startswith("round_"):
                    judge_file = round_dir / "judge_report.log"
                    if judge_file.exists():
                        judge_files.append(judge_file)
            
            # 按文件名排序
            judge_files.sort(key=lambda x: x.parent.name)
            
            if limit:
                judge_files = judge_files[-limit:]  # 处理最新的N个
            
            logger.info(f"开始批量处理 {len(judge_files)} 个judge报告")
            
            # 处理统计
            processed_count = 0
            successful_count = 0
            failed_count = 0
            skipped_count = 0
            
            for judge_file in judge_files:
                processed_count += 1
                
                try:
                    # 检查是否已经处理过
                    round_id = judge_file.parent.name
                    
                    # 检查数据库中是否已存在
                    existing_reports = []
                    try:
                        with sqlite3.connect(self.db_manager.db_path) as conn:
                            cursor = conn.cursor()
                            cursor.execute("SELECT report_id FROM judge_reports WHERE round_id = ?", (round_id,))
                            existing = cursor.fetchone()
                            if existing:
                                logger.info(f"Judge报告已存在，跳过: {round_id}")
                                skipped_count += 1
                                continue
                    except Exception as db_error:
                        logger.warning(f"检查数据库时出错: {db_error}")
                        # 继续处理，不因为检查错误而跳过
                    
                    # 解析并保存报告
                    report = self.parse_judge_report_file(judge_file)
                    if report and self.db_manager.save_judge_report(report):
                        successful_count += 1
                        logger.info(f"处理成功 ({successful_count}/{processed_count}): {round_id}")
                    else:
                        failed_count += 1
                        logger.error(f"处理失败 ({failed_count}/{processed_count}): {round_id}")
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"处理异常 ({failed_count}/{processed_count}) {judge_file}: {e}")
            
            result = {
                "total_processed": processed_count,
                "successful": successful_count,
                "failed": failed_count,
                "skipped": skipped_count,
                "success_rate": successful_count / processed_count if processed_count > 0 else 0
            }
            
            logger.info(f"批量处理judge报告完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"批量处理judge报告失败: {e}")
            return {"error": str(e)}


def create_judge_parser(db_manager: DatabaseManager) -> JudgeReportParser:
    """创建Judge报告解析器的工厂函数"""
    return JudgeReportParser(db_manager)