#!/usr/bin/env python3
"""
RAG系统数据库管理器
负责管理Qdrant向量数据库的连接、集合管理和数据操作
"""

import json
import time
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import requests
import logging
from datetime import datetime

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition

from .data_models import (
    TaskExecution, TaskStep, PlanImprovement, KnowledgeEntry, VectorRecord,
    InstructionMapping, JudgeReport, DatabaseConfig, SearchParams, Collections, 
    ExecutionStatus, PlatformType, TaskType
)

logger = logging.getLogger(__name__)

class EmbeddingService:
    """Embedding服务 - 负责文本向量化"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.base_url = f"http://{config.ollama_host}"
        self.model_name = config.embedding_model
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取文本的向量表示"""
        try:
            url = f"{self.base_url}/api/embeddings"
            payload = {
                "model": self.model_name,
                "prompt": text
            }
            
            # 绕过代理，直接连接本地Ollama服务
            proxies = {
                'http': None,
                'https': None
            }
            
            response = requests.post(url, json=payload, timeout=30, proxies=proxies)
            
            if response.status_code == 200:
                result = response.json()
                embedding = result.get("embedding", [])
                
                if len(embedding) != self.config.vector_dimension:
                    logger.warning(f"向量维度不匹配: 期望{self.config.vector_dimension}, 实际{len(embedding)}")
                
                return embedding
            else:
                logger.error(f"获取向量失败: HTTP {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"获取向量异常: {e}")
            return None
    
    def get_batch_embeddings(self, texts: List[str]) -> Dict[str, Optional[List[float]]]:
        """批量获取向量表示"""
        result = {}
        for text in texts:
            result[text] = self.get_embedding(text)
            time.sleep(0.1)  # 避免请求过快
        return result

class DatabaseManager:
    """数据库管理器 - 管理向量数据库和关系数据库"""
    
    def __init__(self, config: DatabaseConfig, db_path: str = "rag_data/rag_metadata.db"):
        self.config = config
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化服务
        self.embedding_service = EmbeddingService(config)
        
        # 配置Qdrant客户端，绕过代理
        import os
        old_http_proxy = os.environ.get('HTTP_PROXY')
        old_https_proxy = os.environ.get('HTTPS_PROXY')
        
        # 临时移除代理环境变量
        if 'HTTP_PROXY' in os.environ:
            del os.environ['HTTP_PROXY']
        if 'HTTPS_PROXY' in os.environ:
            del os.environ['HTTPS_PROXY']
        
        try:
            self.qdrant_client = QdrantClient(
                host=config.qdrant_host,
                port=config.qdrant_port,
                timeout=30
            )
        finally:
            # 恢复代理环境变量
            if old_http_proxy:
                os.environ['HTTP_PROXY'] = old_http_proxy
            if old_https_proxy:
                os.environ['HTTPS_PROXY'] = old_https_proxy
        
        # 初始化数据库
        self._init_sqlite_db()
        self._init_qdrant_collections()
    
    def _init_sqlite_db(self):
        """初始化SQLite数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 任务执行表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS task_executions (
                    execution_id TEXT PRIMARY KEY,
                    round_id TEXT UNIQUE,
                    round_number INTEGER,
                    original_instruction TEXT NOT NULL,
                    structured_plan TEXT,
                    task_type TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    mis_id TEXT NOT NULL,
                    device_udid TEXT,
                    device_name TEXT,
                    execution_status TEXT NOT NULL,
                    final_result TEXT,
                    execution_error TEXT,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    ended_at TEXT,
                    total_duration REAL,
                    total_steps INTEGER DEFAULT 0,
                    successful_steps INTEGER DEFAULT 0,
                    failed_steps INTEGER DEFAULT 0,
                    log_folder_path TEXT,
                    metadata TEXT
                )
            """)
            
            # 任务步骤表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS task_steps (
                    step_id TEXT PRIMARY KEY,
                    execution_id TEXT NOT NULL,
                    step_number INTEGER NOT NULL,
                    tool_name TEXT NOT NULL,
                    tool_args TEXT NOT NULL,
                    tool_result TEXT NOT NULL,
                    status TEXT NOT NULL,
                    error_message TEXT,
                    duration REAL NOT NULL,
                    executed_at TEXT NOT NULL,
                    is_successful BOOLEAN NOT NULL,
                    image_url TEXT,
                    text_result TEXT,
                    metadata TEXT,
                    FOREIGN KEY (execution_id) REFERENCES task_executions (execution_id)
                )
            """)
            
            # 计划改进表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS plan_improvements (
                    improvement_id TEXT PRIMARY KEY,
                    original_execution_id TEXT NOT NULL,
                    original_plan TEXT NOT NULL,
                    improved_plan TEXT NOT NULL,
                    improvement_reason TEXT NOT NULL,
                    improvement_type TEXT NOT NULL,
                    effectiveness_score REAL,
                    validation_execution_id TEXT,
                    human_reviewed BOOLEAN DEFAULT FALSE,
                    reviewer_id TEXT,
                    review_notes TEXT,
                    created_at TEXT NOT NULL,
                    reviewed_at TEXT,
                    metadata TEXT,
                    FOREIGN KEY (original_execution_id) REFERENCES task_executions (execution_id)
                )
            """)
            
            # 知识库表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_entries (
                    knowledge_id TEXT PRIMARY KEY,
                    knowledge_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    instruction_pattern TEXT NOT NULL,
                    platform TEXT,
                    tool_sequence TEXT NOT NULL,
                    success_conditions TEXT NOT NULL,
                    common_failures TEXT NOT NULL,
                    solutions TEXT NOT NULL,
                    usage_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 0.0,
                    source_execution_ids TEXT NOT NULL,
                    created_by TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    tags TEXT,
                    metadata TEXT
                )
            """)
            
            # 指令映射表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS instruction_mappings (
                    mapping_id TEXT PRIMARY KEY,
                    original_instruction TEXT NOT NULL,
                    structured_plan TEXT NOT NULL,
                    conversion_timestamp TEXT NOT NULL,
                    planner_model TEXT NOT NULL,
                    conversion_context TEXT,
                    conversion_quality_score REAL DEFAULT 0.0,
                    plan_complexity_score REAL DEFAULT 0.0,
                    reuse_count INTEGER DEFAULT 0,
                    success_reuse_count INTEGER DEFAULT 0,
                    effectiveness_rating REAL DEFAULT 0.0,
                    source_execution_ids TEXT,
                    platform TEXT,
                    human_reviewed BOOLEAN DEFAULT FALSE,
                    human_quality_score REAL,
                    human_notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    tags TEXT,
                    metadata TEXT
                )
            """)
            
            # AI评价报告表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS judge_reports (
                    report_id TEXT PRIMARY KEY,
                    execution_id TEXT NOT NULL,
                    round_id TEXT NOT NULL,
                    judge_model TEXT NOT NULL,
                    judge_timestamp TEXT NOT NULL,
                    judge_version TEXT NOT NULL,
                    overall_success BOOLEAN NOT NULL,
                    success_score REAL NOT NULL,
                    confidence_score REAL NOT NULL,
                    test_quality_score REAL NOT NULL,
                    plan_quality_score REAL NOT NULL,
                    execution_quality_score REAL NOT NULL,
                    target_achievement_score REAL NOT NULL,
                    detailed_analysis TEXT NOT NULL,
                    key_issues TEXT,
                    improvement_suggestions TEXT,
                    best_practices TEXT,
                    failure_points TEXT,
                    error_categories TEXT,
                    root_cause_analysis TEXT,
                    human_modified BOOLEAN DEFAULT FALSE,
                    human_override_success BOOLEAN,
                    human_override_score REAL,
                    human_notes TEXT,
                    human_modifier TEXT,
                    human_modified_at TEXT,
                    raw_judge_output TEXT NOT NULL,
                    judge_thinking_process TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    metadata TEXT,
                    FOREIGN KEY (execution_id) REFERENCES task_executions (execution_id)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_executions_round ON task_executions (round_number)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_executions_status ON task_executions (execution_status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_executions_platform ON task_executions (platform)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_steps_execution ON task_steps (execution_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_steps_tool ON task_steps (tool_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_knowledge_type ON knowledge_entries (knowledge_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_knowledge_platform ON knowledge_entries (platform)")
            
            # 新表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mappings_instruction ON instruction_mappings (original_instruction)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mappings_platform ON instruction_mappings (platform)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mappings_reuse ON instruction_mappings (reuse_count)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_judge_execution ON judge_reports (execution_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_judge_round ON judge_reports (round_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_judge_success ON judge_reports (overall_success)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_judge_score ON judge_reports (success_score)")
            
            conn.commit()
            logger.info("SQLite数据库初始化完成")
    
    def _init_qdrant_collections(self):
        """初始化Qdrant集合"""
        vector_config = VectorParams(
            size=self.config.vector_dimension,
            distance=Distance.COSINE
        )
        
        collections_to_create = [
            Collections.INSTRUCTIONS,
            Collections.TOOL_RESULTS,
            Collections.ERROR_PATTERNS,
            Collections.KNOWLEDGE_BASE,
            Collections.IMPROVEMENTS,
            Collections.INSTRUCTION_MAPPINGS,
            Collections.JUDGE_INSIGHTS
        ]
        
        for collection_name in collections_to_create:
            try:
                # 检查集合是否存在
                collections = self.qdrant_client.get_collections().collections
                existing_names = [c.name for c in collections]
                
                if collection_name not in existing_names:
                    self.qdrant_client.create_collection(
                        collection_name=collection_name,
                        vectors_config=vector_config
                    )
                    logger.info(f"创建Qdrant集合: {collection_name}")
                else:
                    logger.info(f"Qdrant集合已存在: {collection_name}")
                    
            except Exception as e:
                logger.error(f"创建Qdrant集合失败 {collection_name}: {e}")
    
    # === 任务执行相关操作 ===
    
    def save_task_execution(self, execution: TaskExecution) -> bool:
        """保存任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 准备数据
                data = execution.to_dict()
                structured_plan_json = json.dumps(data['structured_plan']) if data['structured_plan'] else None
                metadata_json = json.dumps(data['metadata'])
                
                cursor.execute("""
                    INSERT OR REPLACE INTO task_executions (
                        execution_id, round_id, round_number, original_instruction, structured_plan,
                        task_type, platform, mis_id, device_udid, device_name, execution_status,
                        final_result, execution_error, created_at, started_at, ended_at,
                        total_duration, total_steps, successful_steps, failed_steps,
                        log_folder_path, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['execution_id'], data['round_id'], data['round_number'],
                    data['original_instruction'], structured_plan_json, data['task_type'],
                    data['platform'], data['mis_id'], data['device_udid'], data['device_name'],
                    data['execution_status'], data['final_result'], data['execution_error'],
                    data['created_at'], data['started_at'], data['ended_at'],
                    data['total_duration'], data['total_steps'], data['successful_steps'],
                    data['failed_steps'], data['log_folder_path'], metadata_json
                ))
                
                conn.commit()
                logger.info(f"保存任务执行记录: {execution.execution_id}")
                
                # 创建向量记录
                self._create_execution_vectors(execution)
                
                return True
                
        except Exception as e:
            logger.error(f"保存任务执行记录失败: {e}")
            return False
    
    def get_execution_final_success_status(self, execution_id: str) -> Dict[str, Any]:
        """获取执行的最终成功状态（优先使用AI评价）"""
        try:
            # 首先查找AI评价报告
            judge_report = self.get_judge_report_by_execution(execution_id)
            if judge_report:
                return {
                    "success": judge_report.get_final_success(),
                    "score": judge_report.get_final_score(),
                    "source": "ai_judge",
                    "confidence": judge_report.confidence_score,
                    "human_modified": judge_report.human_modified,
                    "judge_timestamp": judge_report.judge_timestamp
                }
            
            # 如果没有AI评价，使用传统的execution_status
            execution = self.get_task_execution(execution_id)
            if execution:
                success = execution.execution_status.value == "completed"
                score = 1.0 if success else 0.0
                
                return {
                    "success": success,
                    "score": score,
                    "source": "execution_status",
                    "confidence": 0.5,  # 传统方法的置信度较低
                    "human_modified": False,
                    "judge_timestamp": execution.ended_at or execution.created_at
                }
            
            return {
                "success": False,
                "score": 0.0,
                "source": "unknown",
                "confidence": 0.0,
                "human_modified": False,
                "judge_timestamp": None
            }
            
        except Exception as e:
            logger.error(f"获取最终成功状态失败: {e}")
            return {
                "success": False,
                "score": 0.0,
                "source": "error",
                "confidence": 0.0,
                "human_modified": False,
                "judge_timestamp": None,
                "error": str(e)
            }
    
    def get_executions_with_ai_evaluation(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取带有AI评价的执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT 
                        e.execution_id, e.round_id, e.round_number, e.original_instruction,
                        e.execution_status, e.total_steps, e.successful_steps, e.failed_steps,
                        e.created_at, e.ended_at,
                        j.overall_success, j.success_score, j.confidence_score, 
                        j.human_modified, j.human_override_success, j.human_override_score,
                        j.judge_timestamp
                    FROM task_executions e
                    LEFT JOIN judge_reports j ON e.execution_id = j.execution_id
                    ORDER BY e.round_number DESC
                    LIMIT ?
                """, (limit,))
                
                results = []
                for row in cursor.fetchall():
                    execution_data = {
                        "execution_id": row[0],
                        "round_id": row[1],
                        "round_number": row[2],
                        "original_instruction": row[3],
                        "traditional_status": row[4],
                        "total_steps": row[5],
                        "successful_steps": row[6],
                        "failed_steps": row[7],
                        "created_at": row[8],
                        "ended_at": row[9],
                        "has_ai_evaluation": row[10] is not None,
                        "ai_success": row[10],
                        "ai_score": row[11],
                        "ai_confidence": row[12],
                        "human_modified": row[13],
                        "final_success": row[14] if row[14] is not None else row[10],
                        "final_score": row[15] if row[15] is not None else row[11],
                        "judge_timestamp": row[16]
                    }
                    
                    # 计算传统成功率
                    if execution_data["total_steps"] > 0:
                        execution_data["traditional_success_rate"] = execution_data["successful_steps"] / execution_data["total_steps"]
                    else:
                        execution_data["traditional_success_rate"] = 0.0
                    
                    results.append(execution_data)
                
                return results
                
        except Exception as e:
            logger.error(f"获取带AI评价的执行记录失败: {e}")
            return []
    
    def update_instruction_mapping_effectiveness(self, mapping_id: str, execution_success: bool) -> bool:
        """根据执行结果更新指令映射的有效性"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取当前统计
                cursor.execute("""
                    SELECT reuse_count, success_reuse_count, effectiveness_rating
                    FROM instruction_mappings WHERE mapping_id = ?
                """, (mapping_id,))
                
                row = cursor.fetchone()
                if not row:
                    return False
                
                current_reuse = row[0]
                current_success = row[1]
                current_rating = row[2]
                
                # 更新统计
                new_reuse = current_reuse + 1
                new_success = current_success + (1 if execution_success else 0)
                new_rating = new_success / new_reuse if new_reuse > 0 else 0.0
                
                cursor.execute("""
                    UPDATE instruction_mappings SET
                        reuse_count = ?,
                        success_reuse_count = ?,
                        effectiveness_rating = ?,
                        updated_at = ?
                    WHERE mapping_id = ?
                """, (new_reuse, new_success, new_rating, datetime.now().isoformat(), mapping_id))
                
                conn.commit()
                logger.info(f"更新指令映射有效性: {mapping_id}, 新评级: {new_rating:.2f}")
                return True
                
        except Exception as e:
            logger.error(f"更新指令映射有效性失败: {e}")
            return False
    
    def get_task_execution(self, execution_id: str) -> Optional[TaskExecution]:
        """获取任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM task_executions WHERE execution_id = ?", (execution_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_execution(row)
                return None
                
        except Exception as e:
            logger.error(f"获取任务执行记录失败: {e}")
            return None
    
    def get_executions_by_round(self, round_number: int) -> List[TaskExecution]:
        """根据轮次编号获取执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM task_executions WHERE round_number = ?", (round_number,))
                rows = cursor.fetchall()
                
                return [self._row_to_execution(row) for row in rows]
                
        except Exception as e:
            logger.error(f"根据轮次获取执行记录失败: {e}")
            return []
    
    def _row_to_execution(self, row) -> TaskExecution:
        """将数据库行转换为TaskExecution对象"""
        columns = [
            'execution_id', 'round_id', 'round_number', 'original_instruction', 'structured_plan',
            'task_type', 'platform', 'mis_id', 'device_udid', 'device_name', 'execution_status',
            'final_result', 'execution_error', 'created_at', 'started_at', 'ended_at',
            'total_duration', 'total_steps', 'successful_steps', 'failed_steps',
            'log_folder_path', 'metadata'
        ]
        
        data = dict(zip(columns, row))
        
        # 处理JSON字段
        if data['structured_plan']:
            data['structured_plan'] = json.loads(data['structured_plan'])
        if data['metadata']:
            data['metadata'] = json.loads(data['metadata'])
        else:
            data['metadata'] = {}
        
        return TaskExecution.from_dict(data)
    
    # === 任务步骤相关操作 ===
    
    def save_task_step(self, step: TaskStep) -> bool:
        """保存任务步骤记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                data = step.to_dict()
                tool_args_json = json.dumps(data['tool_args'])
                tool_result_json = json.dumps(data['tool_result'])
                metadata_json = json.dumps(data['metadata'])
                
                cursor.execute("""
                    INSERT OR REPLACE INTO task_steps (
                        step_id, execution_id, step_number, tool_name, tool_args, tool_result,
                        status, error_message, duration, executed_at, is_successful,
                        image_url, text_result, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['step_id'], data['execution_id'], data['step_number'],
                    data['tool_name'], tool_args_json, tool_result_json,
                    data['status'], data['error_message'], data['duration'],
                    data['executed_at'], data['is_successful'],
                    data['image_url'], data['text_result'], metadata_json
                ))
                
                conn.commit()
                logger.info(f"保存任务步骤记录: {step.step_id}")
                
                # 创建向量记录
                self._create_step_vectors(step)
                
                return True
                
        except Exception as e:
            logger.error(f"保存任务步骤记录失败: {e}")
            return False
    
    def get_steps_by_execution(self, execution_id: str) -> List[TaskStep]:
        """获取指定执行的所有步骤"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM task_steps WHERE execution_id = ? ORDER BY step_number",
                    (execution_id,)
                )
                rows = cursor.fetchall()
                
                return [self._row_to_step(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取执行步骤失败: {e}")
            return []
    
    def _row_to_step(self, row) -> TaskStep:
        """将数据库行转换为TaskStep对象"""
        columns = [
            'step_id', 'execution_id', 'step_number', 'tool_name', 'tool_args', 'tool_result',
            'status', 'error_message', 'duration', 'executed_at', 'is_successful',
            'image_url', 'text_result', 'metadata'
        ]
        
        data = dict(zip(columns, row))
        
        # 处理JSON字段
        data['tool_args'] = json.loads(data['tool_args'])
        data['tool_result'] = json.loads(data['tool_result'])
        data['metadata'] = json.loads(data['metadata']) if data['metadata'] else {}
        
        return TaskStep.from_dict(data)
    
    # === 向量操作 ===
    
    def _create_execution_vectors(self, execution: TaskExecution):
        """为任务执行创建向量记录"""
        try:
            # 为原始指令创建向量
            instruction_vector = self.embedding_service.get_embedding(execution.original_instruction)
            if instruction_vector:
                vector_record = VectorRecord(
                    source_type="execution",
                    source_id=execution.execution_id,
                    text_content=execution.original_instruction,
                    text_type="original_instruction",
                    vector_dimension=self.config.vector_dimension,
                    embedding_model=self.config.embedding_model,
                    execution_id=execution.execution_id,
                    platform=execution.platform,
                    metadata={
                        "task_type": execution.task_type.value,
                        "execution_status": execution.execution_status.value,
                        "round_number": execution.round_number,
                        "total_steps": execution.total_steps
                    }
                )
                
                point = PointStruct(
                    id=vector_record.vector_id,
                    vector=instruction_vector,
                    payload=vector_record.to_dict()
                )
                
                self.qdrant_client.upsert(
                    collection_name=Collections.INSTRUCTIONS,
                    points=[point]
                )
                
                logger.info(f"创建指令向量: {execution.execution_id}")
            
            # 如果有结构化计划，也创建向量
            if execution.structured_plan and execution.final_result:
                result_vector = self.embedding_service.get_embedding(execution.final_result)
                if result_vector:
                    vector_record = VectorRecord(
                        source_type="execution",
                        source_id=execution.execution_id,
                        text_content=execution.final_result,
                        text_type="final_result",
                        vector_dimension=self.config.vector_dimension,
                        embedding_model=self.config.embedding_model,
                        execution_id=execution.execution_id,
                        platform=execution.platform,
                        metadata={
                            "task_type": execution.task_type.value,
                            "execution_status": execution.execution_status.value,
                            "has_structured_plan": True
                        }
                    )
                    
                    point = PointStruct(
                        id=vector_record.vector_id,
                        vector=result_vector,
                        payload=vector_record.to_dict()
                    )
                    
                    self.qdrant_client.upsert(
                        collection_name=Collections.TOOL_RESULTS,
                        points=[point]
                    )
            
        except Exception as e:
            logger.error(f"创建执行向量失败: {e}")
    
    def _create_step_vectors(self, step: TaskStep):
        """为任务步骤创建向量记录"""
        try:
            # 为工具结果创建向量
            if step.text_result:
                result_text = f"工具:{step.tool_name} 结果:{step.text_result}"
                result_vector = self.embedding_service.get_embedding(result_text)
                
                if result_vector:
                    vector_record = VectorRecord(
                        source_type="step",
                        source_id=step.step_id,
                        text_content=result_text,
                        text_type="tool_result",
                        vector_dimension=self.config.vector_dimension,
                        embedding_model=self.config.embedding_model,
                        execution_id=step.execution_id,
                        metadata={
                            "tool_name": step.tool_name,
                            "status": step.status,
                            "is_successful": step.is_successful,
                            "duration": step.duration
                        }
                    )
                    
                    point = PointStruct(
                        id=vector_record.vector_id,
                        vector=result_vector,
                        payload=vector_record.to_dict()
                    )
                    
                    self.qdrant_client.upsert(
                        collection_name=Collections.TOOL_RESULTS,
                        points=[point]
                    )
            
            # 为错误信息创建向量  
            if step.error_message:
                error_text = f"工具:{step.tool_name} 错误:{step.error_message}"
                error_vector = self.embedding_service.get_embedding(error_text)
                
                if error_vector:
                    vector_record = VectorRecord(
                        source_type="step",
                        source_id=step.step_id,
                        text_content=error_text,
                        text_type="error_message",
                        vector_dimension=self.config.vector_dimension,
                        embedding_model=self.config.embedding_model,
                        execution_id=step.execution_id,
                        metadata={
                            "tool_name": step.tool_name,
                            "status": step.status,
                            "is_successful": step.is_successful
                        }
                    )
                    
                    point = PointStruct(
                        id=vector_record.vector_id,
                        vector=error_vector,
                        payload=vector_record.to_dict()
                    )
                    
                    self.qdrant_client.upsert(
                        collection_name=Collections.ERROR_PATTERNS,
                        points=[point]
                    )
            
        except Exception as e:
            logger.error(f"创建步骤向量失败: {e}")
    
    # === 向量搜索 ===
    
    def search_similar_instructions(self, query_text: str, top_k: int = 5, 
                                   platform: Optional[PlatformType] = None) -> List[Dict[str, Any]]:
        """搜索相似的指令"""
        return self._vector_search(
            query_text=query_text,
            collection_name=Collections.INSTRUCTIONS,
            top_k=top_k,
            filter_conditions={"platform": platform.value} if platform else None
        )
    
    def search_similar_tool_results(self, query_text: str, tool_name: Optional[str] = None,
                                   top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似的工具结果"""
        filter_conditions = {}
        if tool_name:
            filter_conditions["tool_name"] = tool_name
            
        return self._vector_search(
            query_text=query_text,
            collection_name=Collections.TOOL_RESULTS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
    
    def search_error_patterns(self, error_text: str, tool_name: Optional[str] = None,
                             top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似的错误模式"""
        filter_conditions = {}
        if tool_name:
            filter_conditions["tool_name"] = tool_name
            
        return self._vector_search(
            query_text=error_text,
            collection_name=Collections.ERROR_PATTERNS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
    
    def search_judge_insights(self, query_text: str, insight_type: Optional[str] = None,
                            top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索评价洞察"""
        filter_conditions = {}
        if insight_type:
            filter_conditions["text_type"] = insight_type  # detailed_analysis 或 improvement_suggestions
            
        return self._vector_search(
            query_text=query_text,
            collection_name=Collections.JUDGE_INSIGHTS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
    
    # === 多维度搜索 ===
    
    def search_execution_level(self, query_text: str, platform: Optional[PlatformType] = None,
                             success_only: bool = False, top_k: int = 5) -> List[Dict[str, Any]]:
        """轮次级别搜索 - 查找相似的整体测试任务"""
        filter_conditions = {}
        if platform:
            filter_conditions["platform"] = platform.value
        if success_only:
            filter_conditions["overall_success"] = True
            
        # 搜索指令映射
        instruction_results = self._vector_search(
            query_text=query_text,
            collection_name=Collections.INSTRUCTION_MAPPINGS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
        
        # 搜索指令向量
        instruction_vector_results = self._vector_search(
            query_text=query_text,
            collection_name=Collections.INSTRUCTIONS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
        
        # 合并并按相似度排序
        all_results = instruction_results + instruction_vector_results
        all_results.sort(key=lambda x: x["score"], reverse=True)
        
        return all_results[:top_k]
    
    def search_step_level(self, query_text: str, tool_name: Optional[str] = None,
                         success_only: bool = False, top_k: int = 5) -> List[Dict[str, Any]]:
        """步骤级别搜索 - 查找特定工具调用的历史经验"""
        filter_conditions = {}
        if tool_name:
            filter_conditions["tool_name"] = tool_name
        if success_only:
            filter_conditions["is_successful"] = True
            
        return self._vector_search(
            query_text=query_text,
            collection_name=Collections.TOOL_RESULTS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
    
    def search_problem_patterns(self, query_text: str, problem_type: Optional[str] = None,
                              top_k: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """问题模式搜索 - 基于错误类型查找解决方案"""
        results = {
            "error_patterns": [],
            "improvement_suggestions": [],
            "similar_failures": []
        }
        
        try:
            # 搜索错误模式
            results["error_patterns"] = self.search_error_patterns(query_text, top_k=top_k)
            
            # 搜索改进建议
            results["improvement_suggestions"] = self.search_judge_insights(
                query_text, insight_type="improvement_suggestions", top_k=top_k
            )
            
            # 搜索相似失败案例
            failed_executions = []
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT j.round_id, j.detailed_analysis, j.key_issues, j.success_score
                        FROM judge_reports j 
                        WHERE j.overall_success = 0 AND j.detailed_analysis LIKE ?
                        ORDER BY j.created_at DESC LIMIT ?
                    """, (f"%{query_text}%", top_k))
                    
                    for row in cursor.fetchall():
                        failed_executions.append({
                            "round_id": row[0],
                            "analysis": row[1],
                            "issues": json.loads(row[2]) if row[2] else [],
                            "score": row[3]
                        })
                        
                results["similar_failures"] = failed_executions
                
            except Exception as e:
                logger.error(f"搜索相似失败案例失败: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"问题模式搜索失败: {e}")
            return results
    
    def get_success_patterns_for_instruction(self, instruction: str, platform: Optional[PlatformType] = None,
                                           top_k: int = 3) -> Dict[str, Any]:
        """获取指定指令的成功模式"""
        try:
            result = {
                "similar_successful_instructions": [],
                "successful_execution_examples": [],
                "best_practices": [],
                "average_success_rate": 0.0
            }
            
            # 搜索相似的成功指令映射
            similar_mappings = self.search_similar_instruction_mappings(instruction, top_k=top_k, platform=platform)
            
            for mapping_result in similar_mappings:
                mapping_id = mapping_result.get("payload", {}).get("mapping_id")
                if mapping_id:
                    mapping = self.get_instruction_mapping(mapping_id)
                    if mapping and mapping.effectiveness_rating > 0.6:
                        result["similar_successful_instructions"].append({
                            "original_instruction": mapping.original_instruction,
                            "structured_plan": mapping.structured_plan,
                            "effectiveness_rating": mapping.effectiveness_rating,
                            "reuse_count": mapping.reuse_count,
                            "success_reuse_count": mapping.success_reuse_count
                        })
            
            # 搜索成功的执行示例
            successful_executions = self.search_execution_level(instruction, platform=platform, success_only=True, top_k=top_k)
            
            for exec_result in successful_executions:
                execution_id = exec_result.get("payload", {}).get("execution_id")
                if execution_id:
                    execution = self.get_task_execution(execution_id)
                    if execution and execution.execution_status.value == "completed":
                        result["successful_execution_examples"].append({
                            "execution_id": execution.execution_id,
                            "round_id": execution.round_id,
                            "structured_plan": execution.structured_plan,
                            "total_steps": execution.total_steps,
                            "successful_steps": execution.successful_steps,
                            "success_rate": execution.successful_steps / execution.total_steps if execution.total_steps > 0 else 0
                        })
            
            # 计算平均成功率
            if result["successful_execution_examples"]:
                total_success_rate = sum(ex["success_rate"] for ex in result["successful_execution_examples"])
                result["average_success_rate"] = total_success_rate / len(result["successful_execution_examples"])
            
            # 提取最佳实践
            best_practices_search = self.search_judge_insights(instruction, top_k=top_k)
            for insight_result in best_practices_search:
                payload = insight_result.get("payload", {})
                if payload.get("text_type") == "improvement_suggestions":
                    result["best_practices"].append(payload.get("text_content", ""))
            
            return result
            
        except Exception as e:
            logger.error(f"获取成功模式失败: {e}")
            return {"error": str(e)}
    
    def _vector_search(self, query_text: str, collection_name: str, top_k: int = 5,
                      filter_conditions: Optional[Dict] = None, 
                      score_threshold: float = 0.3) -> List[Dict[str, Any]]:
        """通用向量搜索"""
        try:
            # 获取查询向量
            query_vector = self.embedding_service.get_embedding(query_text)
            if not query_vector:
                logger.error("获取查询向量失败")
                return []
            
            # 构建过滤条件
            search_filter = None
            if filter_conditions:
                conditions = []
                for key, value in filter_conditions.items():
                    if value is not None:
                        conditions.append(
                            FieldCondition(key=key, match=models.MatchValue(value=value))
                        )
                if conditions:
                    search_filter = Filter(must=conditions)
            
            # 执行搜索
            search_result = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                query_filter=search_filter,
                limit=top_k,
                score_threshold=score_threshold
            )
            
            # 处理结果
            results = []
            for scored_point in search_result:
                result = {
                    "id": scored_point.id,
                    "score": scored_point.score,
                    "payload": scored_point.payload
                }
                results.append(result)
            
            logger.info(f"向量搜索完成: 集合={collection_name}, 查询='{query_text[:50]}...', 结果数={len(results)}")
            return results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    # === 指令映射相关操作 ===
    
    def save_instruction_mapping(self, mapping: InstructionMapping) -> bool:
        """保存指令映射记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                data = mapping.to_dict()
                structured_plan_json = json.dumps(data['structured_plan'])
                conversion_context_json = json.dumps(data.get('conversion_context', {}))
                source_execution_ids_json = json.dumps(data.get('source_execution_ids', []))
                tags_json = json.dumps(data.get('tags', []))
                metadata_json = json.dumps(data.get('metadata', {}))
                
                cursor.execute("""
                    INSERT OR REPLACE INTO instruction_mappings (
                        mapping_id, original_instruction, structured_plan, conversion_timestamp,
                        planner_model, conversion_context, conversion_quality_score, plan_complexity_score,
                        reuse_count, success_reuse_count, effectiveness_rating, source_execution_ids,
                        platform, human_reviewed, human_quality_score, human_notes,
                        created_at, updated_at, tags, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['mapping_id'], data['original_instruction'], structured_plan_json,
                    data['conversion_timestamp'], data['planner_model'], conversion_context_json,
                    data['conversion_quality_score'], data['plan_complexity_score'],
                    data['reuse_count'], data['success_reuse_count'], data['effectiveness_rating'],
                    source_execution_ids_json, data.get('platform'), data['human_reviewed'],
                    data.get('human_quality_score'), data.get('human_notes'),
                    data['created_at'], data['updated_at'], tags_json, metadata_json
                ))
                
                conn.commit()
                logger.info(f"保存指令映射记录: {mapping.mapping_id}")
                
                # 创建向量记录
                self._create_mapping_vectors(mapping)
                
                return True
                
        except Exception as e:
            logger.error(f"保存指令映射记录失败: {e}")
            return False
    
    def get_instruction_mapping(self, mapping_id: str) -> Optional[InstructionMapping]:
        """获取指令映射记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM instruction_mappings WHERE mapping_id = ?", (mapping_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_mapping(row)
                return None
                
        except Exception as e:
            logger.error(f"获取指令映射记录失败: {e}")
            return None
    
    def search_similar_instruction_mappings(self, instruction: str, top_k: int = 5, 
                                         platform: Optional[PlatformType] = None) -> List[Dict[str, Any]]:
        """搜索相似的指令映射"""
        filter_conditions = {}
        if platform:
            filter_conditions["platform"] = platform.value
            
        return self._vector_search(
            query_text=instruction,
            collection_name=Collections.INSTRUCTION_MAPPINGS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )
    
    def _row_to_mapping(self, row) -> InstructionMapping:
        """将数据库行转换为InstructionMapping对象"""
        columns = [
            'mapping_id', 'original_instruction', 'structured_plan', 'conversion_timestamp',
            'planner_model', 'conversion_context', 'conversion_quality_score', 'plan_complexity_score',
            'reuse_count', 'success_reuse_count', 'effectiveness_rating', 'source_execution_ids',
            'platform', 'human_reviewed', 'human_quality_score', 'human_notes',
            'created_at', 'updated_at', 'tags', 'metadata'
        ]
        
        data = dict(zip(columns, row))
        
        # 处理JSON字段
        data['structured_plan'] = json.loads(data['structured_plan'])
        data['conversion_context'] = json.loads(data['conversion_context']) if data['conversion_context'] else {}
        data['source_execution_ids'] = json.loads(data['source_execution_ids']) if data['source_execution_ids'] else []
        data['tags'] = json.loads(data['tags']) if data['tags'] else []
        data['metadata'] = json.loads(data['metadata']) if data['metadata'] else {}
        
        return InstructionMapping.from_dict(data)
    
    def _create_mapping_vectors(self, mapping: InstructionMapping):
        """为指令映射创建向量记录"""
        try:
            # 为原始指令创建向量
            instruction_vector = self.embedding_service.get_embedding(mapping.original_instruction)
            if instruction_vector:
                vector_record = VectorRecord(
                    source_type="instruction_mapping",
                    source_id=mapping.mapping_id,
                    text_content=mapping.original_instruction,
                    text_type="original_instruction",
                    vector_dimension=self.config.vector_dimension,
                    embedding_model=self.config.embedding_model,
                    platform=mapping.platform,
                    metadata={
                        "mapping_id": mapping.mapping_id,
                        "planner_model": mapping.planner_model,
                        "conversion_quality_score": mapping.conversion_quality_score,
                        "reuse_count": mapping.reuse_count,
                        "effectiveness_rating": mapping.effectiveness_rating,
                        "human_reviewed": mapping.human_reviewed
                    }
                )
                
                point = PointStruct(
                    id=vector_record.vector_id,
                    vector=instruction_vector,
                    payload=vector_record.to_dict()
                )
                
                self.qdrant_client.upsert(
                    collection_name=Collections.INSTRUCTION_MAPPINGS,
                    points=[point]
                )
                
                logger.info(f"创建指令映射向量: {mapping.mapping_id}")
                
        except Exception as e:
            logger.error(f"创建指令映射向量失败: {e}")
    
    # === AI评价报告相关操作 ===
    
    def save_judge_report(self, report: JudgeReport) -> bool:
        """保存AI评价报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                data = report.to_dict()
                key_issues_json = json.dumps(data.get('key_issues', []))
                improvement_suggestions_json = json.dumps(data.get('improvement_suggestions', []))
                best_practices_json = json.dumps(data.get('best_practices', []))
                failure_points_json = json.dumps(data.get('failure_points', []))
                error_categories_json = json.dumps(data.get('error_categories', []))
                metadata_json = json.dumps(data.get('metadata', {}))
                
                cursor.execute("""
                    INSERT OR REPLACE INTO judge_reports (
                        report_id, execution_id, round_id, judge_model, judge_timestamp, judge_version,
                        overall_success, success_score, confidence_score, test_quality_score,
                        plan_quality_score, execution_quality_score, target_achievement_score,
                        detailed_analysis, key_issues, improvement_suggestions, best_practices,
                        failure_points, error_categories, root_cause_analysis, human_modified,
                        human_override_success, human_override_score, human_notes, human_modifier,
                        human_modified_at, raw_judge_output, judge_thinking_process,
                        created_at, updated_at, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['report_id'], data['execution_id'], data['round_id'], data['judge_model'],
                    data['judge_timestamp'], data['judge_version'], data['overall_success'],
                    data['success_score'], data['confidence_score'], data['test_quality_score'],
                    data['plan_quality_score'], data['execution_quality_score'], data['target_achievement_score'],
                    data['detailed_analysis'], key_issues_json, improvement_suggestions_json, best_practices_json,
                    failure_points_json, error_categories_json, data.get('root_cause_analysis'),
                    data['human_modified'], data.get('human_override_success'), data.get('human_override_score'),
                    data.get('human_notes'), data.get('human_modifier'), data.get('human_modified_at'),
                    data['raw_judge_output'], data.get('judge_thinking_process'),
                    data['created_at'], data['updated_at'], metadata_json
                ))
                
                conn.commit()
                logger.info(f"保存AI评价报告: {report.report_id}")
                
                # 创建向量记录
                self._create_judge_vectors(report)
                
                return True
                
        except Exception as e:
            logger.error(f"保存AI评价报告失败: {e}")
            return False
    
    def get_judge_report(self, report_id: str) -> Optional[JudgeReport]:
        """获取AI评价报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM judge_reports WHERE report_id = ?", (report_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_judge_report(row)
                return None
                
        except Exception as e:
            logger.error(f"获取AI评价报告失败: {e}")
            return None
    
    def get_judge_report_by_execution(self, execution_id: str) -> Optional[JudgeReport]:
        """根据执行ID获取AI评价报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM judge_reports WHERE execution_id = ? ORDER BY created_at DESC LIMIT 1", (execution_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_judge_report(row)
                return None
                
        except Exception as e:
            logger.error(f"根据执行ID获取AI评价报告失败: {e}")
            return None
    
    def update_judge_report_human_override(self, report_id: str, success: bool, score: float, 
                                         notes: str, modifier: str) -> bool:
        """更新AI评价报告的人工覆盖"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE judge_reports SET 
                        human_modified = TRUE,
                        human_override_success = ?,
                        human_override_score = ?,
                        human_notes = ?,
                        human_modifier = ?,
                        human_modified_at = ?,
                        updated_at = ?
                    WHERE report_id = ?
                """, (success, score, notes, modifier, datetime.now().isoformat(), 
                      datetime.now().isoformat(), report_id))
                
                conn.commit()
                logger.info(f"更新AI评价报告人工覆盖: {report_id}")
                return True
                
        except Exception as e:
            logger.error(f"更新AI评价报告人工覆盖失败: {e}")
            return False
    
    def _row_to_judge_report(self, row) -> JudgeReport:
        """将数据库行转换为JudgeReport对象"""
        columns = [
            'report_id', 'execution_id', 'round_id', 'judge_model', 'judge_timestamp', 'judge_version',
            'overall_success', 'success_score', 'confidence_score', 'test_quality_score',
            'plan_quality_score', 'execution_quality_score', 'target_achievement_score',
            'detailed_analysis', 'key_issues', 'improvement_suggestions', 'best_practices',
            'failure_points', 'error_categories', 'root_cause_analysis', 'human_modified',
            'human_override_success', 'human_override_score', 'human_notes', 'human_modifier',
            'human_modified_at', 'raw_judge_output', 'judge_thinking_process',
            'created_at', 'updated_at', 'metadata'
        ]
        
        data = dict(zip(columns, row))
        
        # 处理JSON字段
        data['key_issues'] = json.loads(data['key_issues']) if data['key_issues'] else []
        data['improvement_suggestions'] = json.loads(data['improvement_suggestions']) if data['improvement_suggestions'] else []
        data['best_practices'] = json.loads(data['best_practices']) if data['best_practices'] else []
        data['failure_points'] = json.loads(data['failure_points']) if data['failure_points'] else []
        data['error_categories'] = json.loads(data['error_categories']) if data['error_categories'] else []
        data['metadata'] = json.loads(data['metadata']) if data['metadata'] else {}
        
        return JudgeReport.from_dict(data)
    
    def _create_judge_vectors(self, report: JudgeReport):
        """为AI评价报告创建向量记录"""
        try:
            # 为详细分析创建向量
            if report.detailed_analysis:
                analysis_vector = self.embedding_service.get_embedding(report.detailed_analysis)
                if analysis_vector:
                    vector_record = VectorRecord(
                        source_type="judge_report",
                        source_id=report.report_id,
                        text_content=report.detailed_analysis,
                        text_type="detailed_analysis",
                        vector_dimension=self.config.vector_dimension,
                        embedding_model=self.config.embedding_model,
                        execution_id=report.execution_id,
                        metadata={
                            "report_id": report.report_id,
                            "round_id": report.round_id,
                            "overall_success": report.overall_success,
                            "success_score": report.success_score,
                            "judge_model": report.judge_model,
                            "human_modified": report.human_modified
                        }
                    )
                    
                    point = PointStruct(
                        id=vector_record.vector_id,
                        vector=analysis_vector,
                        payload=vector_record.to_dict()
                    )
                    
                    self.qdrant_client.upsert(
                        collection_name=Collections.JUDGE_INSIGHTS,
                        points=[point]
                    )
            
            # 为改进建议创建向量
            if report.improvement_suggestions:
                suggestions_text = " ".join(report.improvement_suggestions)
                suggestions_vector = self.embedding_service.get_embedding(suggestions_text)
                if suggestions_vector:
                    vector_record = VectorRecord(
                        source_type="judge_report",
                        source_id=report.report_id,
                        text_content=suggestions_text,
                        text_type="improvement_suggestions",
                        vector_dimension=self.config.vector_dimension,
                        embedding_model=self.config.embedding_model,
                        execution_id=report.execution_id,
                        metadata={
                            "report_id": report.report_id,
                            "round_id": report.round_id,
                            "suggestion_count": len(report.improvement_suggestions)
                        }
                    )
                    
                    point = PointStruct(
                        id=vector_record.vector_id,
                        vector=suggestions_vector,
                        payload=vector_record.to_dict()
                    )
                    
                    self.qdrant_client.upsert(
                        collection_name=Collections.JUDGE_INSIGHTS,
                        points=[point]
                    )
                
                logger.info(f"创建评价报告向量: {report.report_id}")
                
        except Exception as e:
            logger.error(f"创建评价报告向量失败: {e}")
    
    # === AI评价驱动的成功判断 ===
    
    def get_execution_success_status(self, execution_id: str) -> Dict[str, Any]:
        """获取执行的最终成功状态 - 优先使用AI评价"""
        try:
            execution = self.get_task_execution(execution_id)
            if not execution:
                return {"success": False, "source": "not_found", "score": 0.0}
            
            # 1. 优先使用AI评价报告的结果
            judge_report = self.get_judge_report_by_execution(execution_id)
            if judge_report:
                final_success = judge_report.get_final_success()
                final_score = judge_report.get_final_score()
                source = "human_override" if judge_report.human_modified else "ai_judge"
                
                return {
                    "success": final_success,
                    "score": final_score,
                    "source": source,
                    "confidence": judge_report.confidence_score,
                    "judge_model": judge_report.judge_model,
                    "human_modified": judge_report.human_modified
                }
            
            # 2. 备用方案：使用传统的执行状态
            traditional_success = execution.execution_status == ExecutionStatus.COMPLETED
            return {
                "success": traditional_success,
                "score": 0.8 if traditional_success else 0.2,
                "source": "execution_status",
                "confidence": 0.5,  # 低置信度，因为是简单判断
                "judge_model": None,
                "human_modified": False
            }
            
        except Exception as e:
            logger.error(f"获取执行成功状态失败: {e}")
            return {"success": False, "source": "error", "score": 0.0}
    
    def update_execution_success_criteria(self, execution_id: str, new_success: bool, 
                                        new_score: float, modifier: str, notes: str = "") -> bool:
        """更新执行的成功标准"""
        try:
            judge_report = self.get_judge_report_by_execution(execution_id)
            if judge_report:
                # 如果有AI评价报告，更新评价报告
                return self.update_judge_report_human_override(
                    judge_report.report_id, new_success, new_score, notes, modifier
                )
            else:
                # 如果没有AI评价报告，创建一个基础报告
                execution = self.get_task_execution(execution_id)
                if not execution:
                    return False
                
                basic_report = JudgeReport(
                    report_id="",
                    execution_id=execution_id,
                    round_id=execution.round_id,
                    judge_model="human_override",
                    judge_timestamp=datetime.now(),
                    judge_version="1.0",
                    overall_success=new_success,
                    success_score=new_score,
                    confidence_score=1.0,  # 人工判断置信度高
                    test_quality_score=new_score,
                    plan_quality_score=new_score,
                    execution_quality_score=new_score,
                    target_achievement_score=new_score,
                    detailed_analysis=f"人工修正评价：{notes}",
                    key_issues=[],
                    improvement_suggestions=[],
                    best_practices=[],
                    failure_points=[],
                    error_categories=[],
                    root_cause_analysis=None,
                    human_modified=True,
                    human_override_success=new_success,
                    human_override_score=new_score,
                    human_notes=notes,
                    human_modifier=modifier,
                    human_modified_at=datetime.now(),
                    raw_judge_output="人工创建的评价报告",
                    judge_thinking_process=None,
                    metadata={"created_by": "human_override"}
                )
                
                return self.save_judge_report(basic_report)
                
        except Exception as e:
            logger.error(f"更新执行成功标准失败: {e}")
            return False
    
    def batch_update_success_criteria(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量更新成功标准"""
        try:
            successful_updates = 0
            failed_updates = 0
            results = []
            
            for update in updates:
                execution_id = update.get("execution_id")
                new_success = update.get("success")
                new_score = update.get("score", 0.5)
                modifier = update.get("modifier", "system")
                notes = update.get("notes", "")
                
                if self.update_execution_success_criteria(execution_id, new_success, new_score, modifier, notes):
                    successful_updates += 1
                    results.append({"execution_id": execution_id, "status": "success"})
                else:
                    failed_updates += 1
                    results.append({"execution_id": execution_id, "status": "failed"})
            
            return {
                "total": len(updates),
                "successful": successful_updates,
                "failed": failed_updates,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"批量更新成功标准失败: {e}")
            return {"error": str(e)}
    
    def get_success_statistics(self) -> Dict[str, Any]:
        """获取成功率统计 - 基于AI评价"""
        try:
            stats = {}
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总体统计
                cursor.execute("SELECT COUNT(*) FROM task_executions")
                total_executions = cursor.fetchone()[0]
                
                # AI评价成功统计
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_judged,
                        SUM(CASE WHEN 
                            COALESCE(human_override_success, overall_success) = 1 
                            THEN 1 ELSE 0 END) as ai_success_count,
                        AVG(CASE WHEN 
                            human_override_score IS NOT NULL 
                            THEN human_override_score 
                            ELSE success_score END) as avg_score
                    FROM judge_reports
                """)
                
                judge_stats = cursor.fetchone()
                
                # 传统执行状态统计
                cursor.execute("""
                    SELECT execution_status, COUNT(*) 
                    FROM task_executions 
                    GROUP BY execution_status
                """)
                status_counts = dict(cursor.fetchall())
                
                stats = {
                    "total_executions": total_executions,
                    "judged_executions": judge_stats[0] if judge_stats else 0,
                    "ai_success_count": judge_stats[1] if judge_stats else 0,
                    "ai_success_rate": (judge_stats[1] / judge_stats[0]) if judge_stats and judge_stats[0] > 0 else 0,
                    "average_success_score": judge_stats[2] if judge_stats else 0,
                    "traditional_success_count": status_counts.get("completed", 0),
                    "traditional_success_rate": status_counts.get("completed", 0) / total_executions if total_executions > 0 else 0,
                    "status_breakdown": status_counts,
                    "judge_coverage": (judge_stats[0] / total_executions) if total_executions > 0 else 0
                }
                
                return stats
                
        except Exception as e:
            logger.error(f"获取成功率统计失败: {e}")
            return {"error": str(e)}
    
    # === 统计和管理 ===
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {}
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # SQLite统计
                cursor.execute("SELECT COUNT(*) FROM task_executions")
                stats["total_executions"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM task_steps")
                stats["total_steps"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM plan_improvements")
                stats["total_improvements"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM knowledge_entries")
                stats["total_knowledge_entries"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM instruction_mappings")
                stats["total_instruction_mappings"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM judge_reports")
                stats["total_judge_reports"] = cursor.fetchone()[0]
                
                # 按平台统计
                cursor.execute("SELECT platform, COUNT(*) FROM task_executions GROUP BY platform")
                stats["executions_by_platform"] = dict(cursor.fetchall())
                
                # 按状态统计
                cursor.execute("SELECT execution_status, COUNT(*) FROM task_executions GROUP BY execution_status")
                stats["executions_by_status"] = dict(cursor.fetchall())
            
            # Qdrant统计
            qdrant_stats = {}
            for collection_name in [Collections.INSTRUCTIONS, Collections.TOOL_RESULTS, 
                                   Collections.ERROR_PATTERNS, Collections.KNOWLEDGE_BASE,
                                   Collections.IMPROVEMENTS, Collections.INSTRUCTION_MAPPINGS,
                                   Collections.JUDGE_INSIGHTS]:
                try:
                    collection_info = self.qdrant_client.get_collection(collection_name)
                    qdrant_stats[collection_name] = collection_info.points_count
                except:
                    qdrant_stats[collection_name] = 0
            
            stats["vector_collections"] = qdrant_stats
            stats["total_vectors"] = sum(qdrant_stats.values())
            
            return stats
            
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {}
    
    def cleanup_old_data(self, days: int = 30):
        """清理过期数据"""
        try:
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
            cutoff_str = cutoff_date.isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取要删除的执行ID
                cursor.execute(
                    "SELECT execution_id FROM task_executions WHERE created_at < ?",
                    (cutoff_str,)
                )
                old_execution_ids = [row[0] for row in cursor.fetchall()]
                
                if old_execution_ids:
                    # 删除相关的步骤记录
                    placeholders = ','.join(['?' for _ in old_execution_ids])
                    cursor.execute(
                        f"DELETE FROM task_steps WHERE execution_id IN ({placeholders})",
                        old_execution_ids
                    )
                    
                    # 删除执行记录
                    cursor.execute(
                        f"DELETE FROM task_executions WHERE execution_id IN ({placeholders})",
                        old_execution_ids
                    )
                    
                    conn.commit()
                    logger.info(f"清理了 {len(old_execution_ids)} 条过期记录")
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
    
    def get_judge_report_by_id(self, report_id: str) -> Optional[JudgeReport]:
        """根据报告ID获取Judge报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM judge_reports WHERE report_id = ?", (report_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_judge_report(row)
                return None
                
        except Exception as e:
            logger.error(f"获取Judge报告失败: {e}")
            return None
    
    def get_executions_by_round(self, round_number: int) -> List[Any]:
        """根据轮次编号获取执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM task_executions 
                    WHERE round_number = ?
                    ORDER BY created_at DESC
                """, (round_number,))
                rows = cursor.fetchall()
                
                # 简单返回字典列表，实际项目中可能需要转换为对象
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"根据轮次获取执行记录失败: {e}")
            return []
    
    def search_judge_insights(self, query_text: str, top_k: int = 5, 
                            confidence_threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """搜索Judge评价洞察"""
        filter_conditions = {}
        if confidence_threshold:
            filter_conditions["confidence_score"] = {"$gte": confidence_threshold}
            
        return self._vector_search(
            query_text=query_text,
            collection_name=Collections.JUDGE_INSIGHTS,
            top_k=top_k,
            filter_conditions=filter_conditions if filter_conditions else None
        )

def create_database_manager(config: Optional[DatabaseConfig] = None) -> DatabaseManager:
    """创建数据库管理器的工厂函数"""
    if config is None:
        config = DatabaseConfig()
    return DatabaseManager(config)