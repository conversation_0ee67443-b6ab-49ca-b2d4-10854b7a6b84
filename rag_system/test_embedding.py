#!/usr/bin/env python3
"""
测试embedding模型的调用和向量化功能
"""

import requests
import json
import numpy as np
import time
from typing import List, Dict, Any

class EmbeddingModelTester:
    """Embedding模型测试器"""
    
    def __init__(self, ollama_host: str = "127.0.0.1:11437"):
        self.ollama_host = ollama_host
        self.base_url = f"http://{ollama_host}"
        self.model_name = "hf.co/Qwen/Qwen3-Embedding-4B-GGUF:Qwen3-Embedding-4B-Q4_K_M.gguf"
        
    def get_embedding(self, text: str) -> Dict[str, Any]:
        """获取文本的向量表示"""
        try:
            url = f"{self.base_url}/api/embeddings"
            payload = {
                "model": self.model_name,
                "prompt": text
            }
            
            # 绕过代理，直接连接本地Ollama服务
            proxies = {
                'http': None,
                'https': None
            }
            
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=30, proxies=proxies)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                embedding = result.get("embedding", [])
                
                return {
                    "success": True,
                    "embedding": embedding,
                    "dimension": len(embedding),
                    "duration": duration,
                    "text_length": len(text)
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "duration": duration
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "duration": 0
            }
    
    def test_basic_embedding(self):
        """测试基本的embedding功能"""
        print("🧪 测试基本Embedding功能")
        print("=" * 50)
        
        test_texts = [
            "点击左上角的地址",
            "在iOS设备上打开美团App",
            "检查页面展示是否正常",
            "执行截图操作并保存",
            "find_available_device工具调用成功"
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n📝 测试文本 {i}: {text}")
            
            result = self.get_embedding(text)
            
            if result["success"]:
                print(f"✅ 成功获取向量")
                print(f"📊 向量维度: {result['dimension']}")
                print(f"⏱️  处理时间: {result['duration']:.3f}秒")
                print(f"📏 文本长度: {result['text_length']} 字符")
                
                # 显示向量的前几个值
                embedding = result["embedding"]
                if len(embedding) >= 5:
                    preview = [f"{x:.4f}" for x in embedding[:5]]
                    print(f"🔢 向量预览: [{', '.join(preview)}, ...]")
            else:
                print(f"❌ 获取向量失败: {result['error']}")
    
    def test_similarity_calculation(self):
        """测试相似性计算"""
        print("\n\n🔍 测试相似性计算")
        print("=" * 50)
        
        # 准备相似和不相似的文本对
        similar_pairs = [
            ("点击左上角的地址", "点击页面左上角地址选择器"),
            ("截图操作成功", "成功完成截图"),
            ("find_available_device", "查找可用设备")
        ]
        
        different_pairs = [
            ("点击左上角的地址", "重启应用程序"),
            ("截图操作", "输入文本内容"),
            ("iOS设备", "Android系统")
        ]
        
        def cosine_similarity(a, b):
            """计算余弦相似度"""
            a = np.array(a)
            b = np.array(b)
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        
        print("\n🔗 相似文本对:")
        for text1, text2 in similar_pairs:
            emb1 = self.get_embedding(text1)
            emb2 = self.get_embedding(text2)
            
            if emb1["success"] and emb2["success"]:
                similarity = cosine_similarity(emb1["embedding"], emb2["embedding"])
                print(f"📊 '{text1}' vs '{text2}': {similarity:.4f}")
            else:
                print(f"❌ 无法计算相似度")
        
        print("\n🔀 不同文本对:")
        for text1, text2 in different_pairs:
            emb1 = self.get_embedding(text1)
            emb2 = self.get_embedding(text2)
            
            if emb1["success"] and emb2["success"]:
                similarity = cosine_similarity(emb1["embedding"], emb2["embedding"])
                print(f"📊 '{text1}' vs '{text2}': {similarity:.4f}")
            else:
                print(f"❌ 无法计算相似度")
    
    def test_batch_embedding(self):
        """测试批量向量化"""
        print("\n\n⚡ 测试批量向量化性能")
        print("=" * 50)
        
        # 生成一批测试文本
        batch_texts = [
            f"测试文本 {i}: 这是第{i}个测试用例，用于验证batch embedding功能"
            for i in range(1, 11)
        ]
        
        # 单个处理
        print("🔄 单个处理模式:")
        start_time = time.time()
        single_results = []
        for text in batch_texts:
            result = self.get_embedding(text)
            if result["success"]:
                single_results.append(result["embedding"])
        single_duration = time.time() - start_time
        
        print(f"✅ 处理了 {len(single_results)} 个文本")
        print(f"⏱️  总耗时: {single_duration:.3f}秒")
        print(f"📊 平均耗时: {single_duration/len(batch_texts):.3f}秒/文本")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 启动Embedding模型测试")
        print("=" * 60)
        
        # 检查模型是否可用
        test_text = "测试连接"
        result = self.get_embedding(test_text)
        
        if not result["success"]:
            print(f"❌ 模型连接失败: {result['error']}")
            return False
        
        print(f"✅ 模型连接成功")
        print(f"🤖 模型: {self.model_name}")
        print(f"🌐 服务器: {self.base_url}")
        print(f"📏 向量维度: {result['dimension']}")
        
        # 运行所有测试
        self.test_basic_embedding()
        self.test_similarity_calculation()
        self.test_batch_embedding()
        
        print("\n\n🎉 所有测试完成！")
        return True

def main():
    """主函数"""
    tester = EmbeddingModelTester()
    success = tester.run_all_tests()
    
    if success:
        print("✅ Embedding模型测试通过，可以用于RAG系统")
    else:
        print("❌ Embedding模型测试失败，请检查配置")

if __name__ == "__main__":
    main()