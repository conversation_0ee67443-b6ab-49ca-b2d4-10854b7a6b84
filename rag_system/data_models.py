#!/usr/bin/env python3
"""
RAG系统数据模型定义
设计用于存储和检索agent执行历史、知识和改进建议
"""

from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum
import json
import uuid

class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

class PlatformType(Enum):
    """平台类型枚举"""
    IOS = "ios"
    ANDROID = "android"
    UNKNOWN = "unknown"

class TaskType(Enum):
    """任务类型枚举"""
    NORMAL = "normal"      # 普通任务
    SMART = "smart"        # 智能任务（经过agent_json_planer处理）

@dataclass
class TaskExecution:
    """任务执行记录 - 对应一次完整的测试执行"""
    
    # 基本标识信息
    execution_id: str                    # 执行ID，唯一标识
    round_id: str                       # 轮次ID，如 round_000370_20250801_143640
    round_number: int                   # 轮次编号，如 370
    
    # 输入信息
    original_instruction: str           # 原始自然语言指令
    structured_plan: Optional[Dict]     # agent_json_planer生成的结构化计划
    task_type: TaskType                 # 任务类型
    platform: PlatformType             # 目标平台
    
    # 执行信息
    mis_id: str                        # 提交者ID
    device_udid: Optional[str]         # 执行设备UDID
    device_name: Optional[str]         # 设备名称
    
    # 状态和结果
    execution_status: ExecutionStatus   # 执行状态
    final_result: Optional[str]        # 最终执行结果
    execution_error: Optional[str]     # 错误信息
    
    # 时间信息
    created_at: datetime               # 创建时间
    started_at: Optional[datetime]     # 开始执行时间
    ended_at: Optional[datetime]       # 结束时间
    total_duration: Optional[float]    # 总耗时（秒）
    
    # 统计信息
    total_steps: int                   # 总步骤数
    successful_steps: int             # 成功步骤数
    failed_steps: int                 # 失败步骤数
    
    # 元数据
    log_folder_path: str              # 日志文件夹路径
    metadata: Dict[str, Any]          # 额外元数据
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.execution_id:
            self.execution_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理枚举和datetime
        data['execution_status'] = self.execution_status.value
        data['platform'] = self.platform.value
        data['task_type'] = self.task_type.value
        data['created_at'] = self.created_at.isoformat() if self.created_at else None
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['ended_at'] = self.ended_at.isoformat() if self.ended_at else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskExecution':
        """从字典创建实例"""
        # 处理枚举
        if 'execution_status' in data:
            data['execution_status'] = ExecutionStatus(data['execution_status'])
        if 'platform' in data:
            data['platform'] = PlatformType(data['platform'])
        if 'task_type' in data:
            data['task_type'] = TaskType(data['task_type'])
        
        # 处理datetime
        for field in ['created_at', 'started_at', 'ended_at']:
            if field in data and data[field]:
                data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)

@dataclass 
class TaskStep:
    """任务步骤记录 - 对应一次工具调用"""
    
    # 基本标识
    step_id: str                       # 步骤ID
    execution_id: str                  # 所属执行ID
    step_number: int                   # 步骤序号
    
    # 工具调用信息
    tool_name: str                     # 工具名称
    tool_args: Dict[str, Any]         # 工具参数
    tool_result: Dict[str, Any]       # 工具返回结果
    
    # 执行信息
    status: str                       # 步骤状态 (success/failed/error)
    error_message: Optional[str]      # 错误信息
    duration: float                   # 执行耗时（秒）
    
    # 时间信息
    executed_at: datetime             # 执行时间
    
    # 结果分析
    is_successful: bool               # 是否成功
    image_url: Optional[str]          # 相关图片URL
    text_result: Optional[str]        # 文本结果
    
    # 元数据
    metadata: Dict[str, Any]          # 额外元数据
    
    def __post_init__(self):
        if not self.step_id:
            self.step_id = str(uuid.uuid4())
        if not self.executed_at:
            self.executed_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['executed_at'] = self.executed_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskStep':
        if 'executed_at' in data and data['executed_at']:
            data['executed_at'] = datetime.fromisoformat(data['executed_at'])
        return cls(**data)

@dataclass
class PlanImprovement:
    """计划改进记录 - 存储对结构化计划的改进建议"""
    
    # 基本标识
    improvement_id: str               # 改进ID
    original_execution_id: str        # 原始执行ID
    
    # 改进内容
    original_plan: Dict[str, Any]     # 原始计划
    improved_plan: Dict[str, Any]     # 改进后计划
    improvement_reason: str           # 改进原因
    improvement_type: str             # 改进类型 (tool_selection/parameter_tuning/step_order/error_fix)
    
    # 效果评估
    effectiveness_score: Optional[float]  # 改进效果评分 (0-1)
    validation_execution_id: Optional[str]  # 验证执行ID
    
    # 审核信息
    human_reviewed: bool              # 是否经过人工审核
    reviewer_id: Optional[str]        # 审核者ID
    review_notes: Optional[str]       # 审核备注
    
    # 时间信息
    created_at: datetime
    reviewed_at: Optional[datetime]
    
    # 元数据
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        if not self.improvement_id:
            self.improvement_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['reviewed_at'] = self.reviewed_at.isoformat() if self.reviewed_at else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PlanImprovement':
        for field in ['created_at', 'reviewed_at']:
            if field in data and data[field]:
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)

@dataclass
class InstructionMapping:
    """指令映射记录 - 存储原始指令到结构化指令的转换"""
    
    # 基本标识
    mapping_id: str                   # 映射ID
    original_instruction: str         # 原始自然语言指令
    structured_plan: Dict[str, Any]   # 结构化测试计划
    
    # 转换信息
    conversion_timestamp: datetime    # 转换时间
    planner_model: str               # 使用的计划生成模型
    conversion_context: Dict[str, Any] # 转换时的上下文信息
    
    # 质量评估
    conversion_quality_score: float   # 转换质量评分 (0-1)
    plan_complexity_score: float     # 计划复杂度评分 (0-1) 
    
    # 使用统计
    reuse_count: int                 # 被复用次数
    success_reuse_count: int         # 成功复用次数
    effectiveness_rating: float      # 有效性评级 (0-1)
    
    # 关联信息
    source_execution_ids: List[str]   # 使用此映射的执行ID列表
    platform: Optional[PlatformType] # 适用平台
    
    # 人工标注
    human_reviewed: bool             # 是否经过人工审核
    human_quality_score: Optional[float] # 人工质量评分
    human_notes: Optional[str]       # 人工备注
    
    # 时间信息
    created_at: datetime
    updated_at: datetime
    
    # 元数据
    tags: List[str]                  # 标签 
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        if not self.mapping_id:
            self.mapping_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now()
        if not self.updated_at:
            self.updated_at = datetime.now()
        if not hasattr(self, 'tags') or self.tags is None:
            self.tags = []
        if not hasattr(self, 'metadata') or self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['platform'] = self.platform.value if self.platform else None
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['conversion_timestamp'] = self.conversion_timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InstructionMapping':
        if 'platform' in data and data['platform']:
            data['platform'] = PlatformType(data['platform'])
        for field in ['created_at', 'updated_at', 'conversion_timestamp']:
            if field in data and data[field]:
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)

@dataclass
class JudgeReport:
    """AI评价报告 - 存储process_judge.py生成的智能评价"""
    
    # 基本标识
    report_id: str                   # 报告ID
    execution_id: str                # 关联的执行ID
    round_id: str                    # 轮次ID
    
    # 评价模型信息
    judge_model: str                 # 评价模型名称
    judge_timestamp: datetime        # 评价时间
    judge_version: str               # 评价逻辑版本
    
    # 总体评价结果
    overall_success: bool            # 总体是否成功
    success_score: float             # 成功评分 (0-1)
    confidence_score: float          # 评价置信度 (0-1)
    
    # 细分评价维度
    test_quality_score: float        # 测试质量评分 (0-1)
    plan_quality_score: float        # 计划质量评分 (0-1)
    execution_quality_score: float   # 执行质量评分 (0-1)
    target_achievement_score: float  # 目标达成度评分 (0-1)
    
    # 详细分析内容
    detailed_analysis: str           # 详细分析文本
    key_issues: List[str]           # 关键问题列表
    improvement_suggestions: List[str] # 改进建议列表 
    best_practices: List[str]        # 最佳实践提取
    
    # 错误和失败分析
    failure_points: List[Dict[str, Any]] # 失败点分析
    error_categories: List[str]      # 错误分类
    root_cause_analysis: Optional[str] # 根因分析
    
    # 人工修正
    human_modified: bool             # 是否经过人工修改
    human_override_success: Optional[bool] # 人工覆盖的成功判断
    human_override_score: Optional[float] # 人工覆盖的评分
    human_notes: Optional[str]       # 人工修改备注
    human_modifier: Optional[str]    # 修改人ID
    human_modified_at: Optional[datetime] # 人工修改时间
    
    # 原始数据
    raw_judge_output: str           # 原始judge输出
    judge_thinking_process: Optional[str] # AI思考过程
    
    # 时间信息
    created_at: datetime
    updated_at: datetime
    
    # 元数据
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        if not self.report_id:
            self.report_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now()
        if not self.updated_at:
            self.updated_at = datetime.now()
        if not hasattr(self, 'metadata') or self.metadata is None:
            self.metadata = {}
    
    def get_final_success(self) -> bool:
        """获取最终的成功判断（优先使用人工覆盖）"""
        return self.human_override_success if self.human_override_success is not None else self.overall_success
    
    def get_final_score(self) -> float:
        """获取最终的评分（优先使用人工覆盖）"""
        return self.human_override_score if self.human_override_score is not None else self.success_score
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['judge_timestamp'] = self.judge_timestamp.isoformat()
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.human_modified_at:
            data['human_modified_at'] = self.human_modified_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'JudgeReport':
        for field in ['judge_timestamp', 'created_at', 'updated_at', 'human_modified_at']:
            if field in data and data[field]:
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)

@dataclass
class KnowledgeEntry:
    """知识库条目 - 存储最佳实践和解决方案"""
    
    # 基本标识
    knowledge_id: str                 # 知识ID
    knowledge_type: str               # 知识类型 (best_practice/failure_solution/tool_pattern)
    
    # 知识内容
    title: str                        # 知识标题
    description: str                  # 详细描述
    instruction_pattern: str          # 指令模式
    
    # 适用条件
    platform: Optional[PlatformType]  # 适用平台
    tool_sequence: List[str]          # 工具序列
    success_conditions: List[str]     # 成功条件
    
    # 问题和解决方案
    common_failures: List[str]        # 常见失败模式
    solutions: List[str]              # 解决方案
    
    # 使用统计
    usage_count: int                  # 使用次数
    success_rate: float               # 成功率
    
    # 来源信息
    source_execution_ids: List[str]   # 来源执行ID列表
    created_by: str                   # 创建者
    
    # 时间信息
    created_at: datetime
    updated_at: datetime
    
    # 元数据
    tags: List[str]                   # 标签
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        if not self.knowledge_id:
            self.knowledge_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now()
        if not self.updated_at:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['platform'] = self.platform.value if self.platform else None
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod 
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeEntry':
        if 'platform' in data and data['platform']:
            data['platform'] = PlatformType(data['platform'])
        for field in ['created_at', 'updated_at']:
            if field in data and data[field]:
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)

@dataclass
class VectorRecord:
    """向量记录 - 用于向量数据库存储"""
    
    # 基本标识
    vector_id: str = ""               # 向量ID
    source_type: str = ""             # 来源类型 (instruction/step/knowledge)
    source_id: str = ""               # 来源ID
    
    # 文本内容
    text_content: str = ""            # 向量化的文本内容
    text_type: str = ""               # 文本类型 (original_instruction/tool_result/error_message等)
    
    # 向量信息
    vector_dimension: int = 2560      # 向量维度
    embedding_model: str = ""         # 使用的embedding模型
    
    # 关联信息
    execution_id: Optional[str] = None       # 关联的执行ID
    platform: Optional[PlatformType] = None # 平台
    
    # 元数据
    metadata: Dict[str, Any] = None   # 额外元数据用于过滤
    
    # 时间信息
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.vector_id:
            self.vector_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为Qdrant存储格式"""
        data = asdict(self)
        data['platform'] = self.platform.value if self.platform else None
        data['created_at'] = self.created_at.isoformat()
        return data
    
    def get_qdrant_point(self, vector: List[float]) -> Dict[str, Any]:
        """获取Qdrant Point格式"""
        return {
            "id": self.vector_id,
            "vector": vector,
            "payload": {
                "source_type": self.source_type,
                "source_id": self.source_id,
                "text_content": self.text_content,
                "text_type": self.text_type,
                "vector_dimension": self.vector_dimension,
                "embedding_model": self.embedding_model,
                "execution_id": self.execution_id,
                "platform": self.platform.value if self.platform else None,
                "created_at": self.created_at.isoformat(),
                "metadata": self.metadata
            }
        }

# 数据库集合名称常量
class Collections:
    """Qdrant集合名称"""
    INSTRUCTIONS = "instructions"          # 指令向量
    TOOL_RESULTS = "tool_results"         # 工具结果向量  
    ERROR_PATTERNS = "error_patterns"     # 错误模式向量
    KNOWLEDGE_BASE = "knowledge_base"     # 知识库向量
    IMPROVEMENTS = "improvements"         # 改进建议向量
    INSTRUCTION_MAPPINGS = "instruction_mappings"  # 指令映射向量
    JUDGE_INSIGHTS = "judge_insights"     # 评价洞察向量

# 向量搜索参数
@dataclass
class SearchParams:
    """向量搜索参数"""
    query_text: str                   # 查询文本
    collection_name: str              # 集合名称
    top_k: int = 5                   # 返回数量
    score_threshold: float = 0.7     # 相似度阈值
    filter_conditions: Optional[Dict] = None  # 过滤条件
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

# 数据库配置
@dataclass
class DatabaseConfig:
    """数据库配置"""
    qdrant_host: str = "127.0.0.1"
    qdrant_port: int = 6333
    vector_dimension: int = 2560       # Qwen3-Embedding-4B向量维度
    embedding_model: str = "hf.co/Qwen/Qwen3-Embedding-4B-GGUF:Qwen3-Embedding-4B-Q4_K_M.gguf"
    ollama_host: str = "127.0.0.1:11437"
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)