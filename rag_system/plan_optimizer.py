#!/usr/bin/env python3
"""
计划优化器 - 基于RAG系统优化执行计划
通过历史执行记录和错误模式来改进计划生成质量
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from .database_manager import DatabaseManager
from .data_models import (
    TaskExecution, TaskStep, PlanImprovement, KnowledgeEntry,
    SearchParams, Collections, ExecutionStatus, PlatformType, TaskType
)

logger = logging.getLogger(__name__)

class ErrorPattern:
    """错误模式定义"""
    def __init__(self, pattern_id: str, pattern_name: str, 
                 description: str, trigger_conditions: List[str],
                 suggested_fixes: List[str]):
        self.pattern_id = pattern_id
        self.pattern_name = pattern_name
        self.description = description
        self.trigger_conditions = trigger_conditions
        self.suggested_fixes = suggested_fixes

class PlanOptimizer:
    """计划优化器 - 基于RAG系统优化执行计划"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.error_patterns = self._initialize_error_patterns()
        
    def _initialize_error_patterns(self) -> List[ErrorPattern]:
        """初始化错误模式库"""
        patterns = [
            ErrorPattern(
                pattern_id="input_without_click",
                pattern_name="输入前未点击",
                description="直接调用input_text工具而没有先点击输入元素",
                trigger_conditions=[
                    "has_input_text_tool",
                    "missing_tap_before_input"
                ],
                suggested_fixes=[
                    "在input_text之前添加tap操作",
                    "确保先通过find_element找到输入元素",
                    "点击输入元素激活输入状态后再输入文本"
                ]
            ),
            ErrorPattern(
                pattern_id="missing_element_search",
                pattern_name="缺少元素查找",
                description="直接点击坐标而没有先查找元素位置",
                trigger_conditions=[
                    "has_tap_with_hardcoded_coords",
                    "missing_find_element_before_tap"
                ],
                suggested_fixes=[
                    "在tap之前添加find_element操作",
                    "使用find_element返回的坐标进行点击",
                    "避免使用硬编码的坐标值"
                ]
            ),
            ErrorPattern(
                pattern_id="missing_page_wait",
                pattern_name="缺少页面等待",
                description="连续操作没有等待页面加载完成",
                trigger_conditions=[
                    "consecutive_operations",
                    "no_wait_between_actions"
                ],
                suggested_fixes=[
                    "在页面跳转后添加等待时间",
                    "在关键操作后使用check_page验证页面状态",
                    "添加适当的wait_seconds操作"
                ]
            ),
            ErrorPattern(
                pattern_id="platform_specific_issues",
                pattern_name="平台特定问题",
                description="针对特定平台的操作方式不当",
                trigger_conditions=[
                    "platform_mismatch",
                    "wrong_coordinate_system"
                ],
                suggested_fixes=[
                    "根据平台调整操作方式",
                    "iOS和Android使用不同的坐标系统",
                    "考虑平台特定的UI元素特征"
                ]
            )
        ]
        return patterns
    
    def optimize_plan(self, original_plan: Dict[str, Any], 
                     original_instruction: str) -> Dict[str, Any]:
        """优化执行计划"""
        try:
            logger.info(f"开始优化计划: {original_plan.get('plan_id', 'unknown')}")
            
            # 1. 搜索相似的历史执行记录
            similar_executions = self._search_similar_executions(
                original_instruction, 
                original_plan.get('platform', 'unknown')
            )
            
            # 2. 分析工具调用序列
            tool_sequence = self._extract_tool_sequence(original_plan)
            
            # 3. 检测错误模式
            detected_patterns = self._detect_error_patterns(original_plan, tool_sequence)
            
            # 4. 基于历史记录和错误模式生成改进建议
            improvements = self._generate_improvements(
                original_plan, similar_executions, detected_patterns
            )
            
            # 5. 应用改进建议生成优化后的计划
            optimized_plan = self._apply_improvements(original_plan, improvements)
            
            # 6. 记录优化过程
            optimization_metadata = {
                "similar_executions_count": len(similar_executions),
                "detected_patterns": [p.pattern_id for p in detected_patterns],
                "improvements_applied": len(improvements),
                "optimization_timestamp": datetime.now().isoformat()
            }
            
            result = {
                "status": "success",
                "original_plan": original_plan,
                "optimized_plan": optimized_plan,
                "improvements": improvements,
                "similar_executions": similar_executions[:3],  # 只返回前3个最相似的
                "detected_patterns": [
                    {
                        "pattern_id": p.pattern_id,
                        "pattern_name": p.pattern_name,
                        "description": p.description
                    } for p in detected_patterns
                ],
                "metadata": optimization_metadata
            }
            
            logger.info(f"计划优化完成: 检测到{len(detected_patterns)}个模式，应用{len(improvements)}个改进")
            return result
            
        except Exception as e:
            logger.error(f"计划优化失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "original_plan": original_plan
            }
    
    def _search_similar_executions(self, instruction: str, platform: str) -> List[Dict[str, Any]]:
        """搜索相似的历史执行记录"""
        try:
            # 搜索相似的指令
            similar_instructions = self.db_manager.search_similar_instructions(
                query_text=instruction,
                top_k=10,
                platform=PlatformType(platform) if platform in ["ios", "android"] else None
            )
            
            # 获取对应的执行记录详情
            executions = []
            for result in similar_instructions:
                payload = result.get("payload", {})
                execution_id = payload.get("execution_id")
                if execution_id:
                    execution = self.db_manager.get_task_execution(execution_id)
                    if execution:
                        # 添加相似度分数
                        execution_dict = execution.to_dict()
                        execution_dict["similarity_score"] = result.get("score", 0.0)
                        executions.append(execution_dict)
            
            # 按相似度排序
            executions.sort(key=lambda x: x.get("similarity_score", 0), reverse=True)
            
            logger.info(f"找到{len(executions)}个相似的历史执行记录")
            return executions
            
        except Exception as e:
            logger.error(f"搜索相似执行记录失败: {e}")
            return []
    
    def _extract_tool_sequence(self, plan: Dict[str, Any]) -> List[str]:
        """提取计划中的工具调用序列"""
        try:
            steps = plan.get("steps", [])
            sequence = []
            for step in steps:
                action = step.get("action", "")
                if action:
                    sequence.append(action)
            return sequence
        except Exception as e:
            logger.error(f"提取工具序列失败: {e}")
            return []
    
    def _detect_error_patterns(self, plan: Dict[str, Any], 
                              tool_sequence: List[str]) -> List[ErrorPattern]:
        """检测计划中的错误模式"""
        detected_patterns = []
        
        try:
            steps = plan.get("steps", [])
            
            for pattern in self.error_patterns:
                if self._matches_error_pattern(pattern, steps, tool_sequence):
                    detected_patterns.append(pattern)
                    logger.info(f"检测到错误模式: {pattern.pattern_name}")
            
            return detected_patterns
            
        except Exception as e:
            logger.error(f"错误模式检测失败: {e}")
            return []
    
    def _matches_error_pattern(self, pattern: ErrorPattern, 
                              steps: List[Dict], tool_sequence: List[str]) -> bool:
        """判断是否匹配特定错误模式"""
        try:
            if pattern.pattern_id == "input_without_click":
                return self._check_input_without_click(steps, tool_sequence)
            elif pattern.pattern_id == "missing_element_search":
                return self._check_missing_element_search(steps, tool_sequence)
            elif pattern.pattern_id == "missing_page_wait":
                return self._check_missing_page_wait(steps, tool_sequence)
            elif pattern.pattern_id == "platform_specific_issues":
                return self._check_platform_specific_issues(steps)
            
            return False
            
        except Exception as e:
            logger.error(f"错误模式匹配检查失败: {e}")
            return False
    
    def _check_input_without_click(self, steps: List[Dict], tool_sequence: List[str]) -> bool:
        """检查是否存在未点击就输入的问题"""
        for i, step in enumerate(steps):
            if step.get("action") == "input_text":
                # 检查前面是否有对应的点击操作
                found_tap = False
                found_find = False
                
                # 向前查找最近的几个步骤
                for j in range(max(0, i-3), i):
                    prev_step = steps[j]
                    prev_action = prev_step.get("action", "")
                    
                    if prev_action == "tap":
                        found_tap = True
                    elif prev_action == "find_element":
                        found_find = True
                
                # 如果有输入操作但缺少点击操作，则匹配此模式
                if not found_tap:
                    return True
        
        return False
    
    def _check_missing_element_search(self, steps: List[Dict], tool_sequence: List[str]) -> bool:
        """检查是否缺少元素查找"""
        for i, step in enumerate(steps):
            if step.get("action") == "tap":
                # 检查是否使用了硬编码坐标且没有先查找元素
                params = step.get("parameters", {})
                if "x" in params and "y" in params:
                    # 检查前面是否有find_element
                    found_find = False
                    for j in range(max(0, i-2), i):
                        if steps[j].get("action") == "find_element":
                            found_find = True
                            break
                    
                    if not found_find:
                        return True
        
        return False
    
    def _check_missing_page_wait(self, steps: List[Dict], tool_sequence: List[str]) -> bool:
        """检查是否缺少页面等待"""
        # 查找连续的操作，特别是tap后立即进行其他操作
        for i, step in enumerate(steps):
            if step.get("action") == "tap" and i < len(steps) - 1:
                next_step = steps[i + 1]
                next_action = next_step.get("action", "")
                
                # tap后立即进行其他交互操作，可能需要等待
                if next_action in ["input_text", "find_element", "check_page"]:
                    # 检查是否有等待操作
                    if next_action != "wait":
                        return True
        
        return False
    
    def _check_platform_specific_issues(self, steps: List[Dict]) -> bool:
        """检查平台特定问题"""
        # 这里可以添加更多平台特定的检查逻辑
        # 目前返回False，后续可以扩展
        return False
    
    def _generate_improvements(self, original_plan: Dict[str, Any], 
                              similar_executions: List[Dict], 
                              detected_patterns: List[ErrorPattern]) -> List[Dict[str, Any]]:
        """基于历史记录和错误模式生成改进建议"""
        improvements = []
        
        try:
            # 1. 基于错误模式生成改进建议
            for pattern in detected_patterns:
                improvement = {
                    "type": "error_pattern_fix",
                    "pattern_id": pattern.pattern_id,
                    "pattern_name": pattern.pattern_name,
                    "description": f"修复错误模式: {pattern.pattern_name}",
                    "suggestions": pattern.suggested_fixes,
                    "priority": "high"
                }
                improvements.append(improvement)
            
            # 2. 基于成功执行记录生成改进建议
            successful_executions = [
                ex for ex in similar_executions 
                if ex.get("execution_status") == "completed"
            ]
            
            if successful_executions:
                # 分析成功案例的工具序列模式
                success_patterns = self._analyze_success_patterns(successful_executions)
                for pattern in success_patterns:
                    improvement = {
                        "type": "success_pattern",
                        "description": f"采用成功案例的模式: {pattern['description']}",
                        "suggestions": pattern["suggestions"],
                        "priority": "medium",
                        "success_rate": pattern.get("success_rate", 0.0)
                    }
                    improvements.append(improvement)
            
            # 3. 基于失败执行记录生成预防建议
            failed_executions = [
                ex for ex in similar_executions 
                if ex.get("execution_status") in ["failed", "timeout"]
            ]
            
            if failed_executions:
                failure_patterns = self._analyze_failure_patterns(failed_executions)
                for pattern in failure_patterns:
                    improvement = {
                        "type": "failure_prevention",
                        "description": f"避免常见失败: {pattern['description']}",
                        "suggestions": pattern["suggestions"],
                        "priority": "medium"
                    }
                    improvements.append(improvement)
            
            logger.info(f"生成了{len(improvements)}个改进建议")
            return improvements
            
        except Exception as e:
            logger.error(f"生成改进建议失败: {e}")
            return []
    
    def _analyze_success_patterns(self, successful_executions: List[Dict]) -> List[Dict]:
        """分析成功执行的模式"""
        patterns = []
        
        try:
            # 1. 分析工具序列模式
            if len(successful_executions) >= 2:
                common_sequences = self._find_common_tool_sequences(successful_executions)
                for seq in common_sequences:
                    pattern = {
                        "description": f"成功案例中的工具序列: {' -> '.join(seq)}",
                        "suggestions": [f"推荐使用成功工具序列: {' -> '.join(seq)}"],
                        "success_rate": self._calculate_sequence_success_rate(seq, successful_executions),
                        "pattern_type": "tool_sequence"
                    }
                    patterns.append(pattern)
            
            # 2. 分析成功的等待时间模式
            wait_patterns = self._analyze_wait_time_patterns(successful_executions)
            patterns.extend(wait_patterns)
            
            # 3. 分析成功的错误处理模式
            error_handling_patterns = self._analyze_error_handling_patterns(successful_executions)
            patterns.extend(error_handling_patterns)
            
            # 4. 分析平台特定的成功模式
            platform_patterns = self._analyze_platform_specific_patterns(successful_executions)
            patterns.extend(platform_patterns)
            
            return patterns
            
        except Exception as e:
            logger.error(f"分析成功模式失败: {e}")
            return []
    
    def _calculate_sequence_success_rate(self, sequence: List[str], executions: List[Dict]) -> float:
        """计算工具序列的成功率"""
        try:
            total_with_sequence = 0
            successful_with_sequence = 0
            
            for execution in executions:
                if execution.get("structured_plan") and isinstance(execution["structured_plan"], dict):
                    plan = execution["structured_plan"]
                    if "steps" in plan:
                        exec_sequence = [step.get("action", "") for step in plan["steps"]]
                        if self._contains_subsequence(exec_sequence, sequence):
                            total_with_sequence += 1
                            if execution.get("execution_status") == "completed":
                                successful_with_sequence += 1
            
            return successful_with_sequence / total_with_sequence if total_with_sequence > 0 else 0.0
            
        except Exception as e:
            logger.error(f"计算序列成功率失败: {e}")
            return 0.0
    
    def _contains_subsequence(self, main_sequence: List[str], sub_sequence: List[str]) -> bool:
        """检查主序列是否包含子序列"""
        if len(sub_sequence) > len(main_sequence):
            return False
        
        for i in range(len(main_sequence) - len(sub_sequence) + 1):
            if main_sequence[i:i+len(sub_sequence)] == sub_sequence:
                return True
        return False
    
    def _analyze_wait_time_patterns(self, successful_executions: List[Dict]) -> List[Dict]:
        """分析成功案例中的等待时间模式"""
        patterns = []
        
        try:
            wait_times = []
            wait_contexts = []
            
            for execution in successful_executions:
                if execution.get("structured_plan") and isinstance(execution["structured_plan"], dict):
                    plan = execution["structured_plan"]
                    steps = plan.get("steps", [])
                    
                    for i, step in enumerate(steps):
                        if step.get("action") == "wait":
                            wait_time = step.get("parameters", {}).get("seconds", 0)
                            if wait_time > 0:
                                wait_times.append(wait_time)
                                
                                # 分析等待前后的操作
                                prev_action = steps[i-1].get("action", "") if i > 0 else ""
                                next_action = steps[i+1].get("action", "") if i < len(steps)-1 else ""
                                wait_contexts.append((prev_action, next_action, wait_time))
            
            if wait_times:
                avg_wait_time = sum(wait_times) / len(wait_times)
                common_contexts = self._find_common_wait_contexts(wait_contexts)
                
                for context, times in common_contexts.items():
                    prev_action, next_action = context
                    avg_time = sum(times) / len(times)
                    
                    pattern = {
                        "description": f"成功案例在{prev_action}后{next_action}前的等待模式",
                        "suggestions": [
                            f"在{prev_action}操作后建议等待{avg_time:.1f}秒",
                            f"特别是在执行{next_action}之前"
                        ],
                        "success_rate": 1.0,
                        "pattern_type": "wait_timing",
                        "recommended_wait_time": avg_time
                    }
                    patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.error(f"分析等待时间模式失败: {e}")
            return []
    
    def _find_common_wait_contexts(self, wait_contexts: List[Tuple]) -> Dict[Tuple, List[float]]:
        """查找常见的等待上下文"""
        context_times = {}
        
        for prev_action, next_action, wait_time in wait_contexts:
            context = (prev_action, next_action)
            if context not in context_times:
                context_times[context] = []
            context_times[context].append(wait_time)
        
        # 只返回出现次数大于1的上下文
        return {k: v for k, v in context_times.items() if len(v) > 1}
    
    def _analyze_error_handling_patterns(self, successful_executions: List[Dict]) -> List[Dict]:
        """分析成功案例中的错误处理模式"""
        patterns = []
        
        try:
            recovery_patterns = []
            
            for execution in successful_executions:
                if execution.get("structured_plan") and isinstance(execution["structured_plan"], dict):
                    plan = execution["structured_plan"]
                    steps = plan.get("steps", [])
                    
                    # 查找可能的错误恢复模式
                    for i, step in enumerate(steps):
                        action = step.get("action", "")
                        
                        # 查找重试模式 (连续相同操作)
                        if i < len(steps) - 1:
                            next_action = steps[i+1].get("action", "")
                            if action == next_action and action in ["find_element", "tap", "input_text"]:
                                recovery_patterns.append(f"重试{action}操作")
                        
                        # 查找检查-重试模式
                        if (action == "check_page" and i < len(steps) - 1 and 
                            steps[i+1].get("action") in ["find_element", "tap"]):
                            recovery_patterns.append("检查页面后重试操作")
            
            if recovery_patterns:
                common_recoveries = {}
                for pattern in recovery_patterns:
                    common_recoveries[pattern] = common_recoveries.get(pattern, 0) + 1
                
                for pattern, count in common_recoveries.items():
                    if count > 1:  # 出现多次的模式
                        success_pattern = {
                            "description": f"成功案例中的错误恢复模式: {pattern}",
                            "suggestions": [
                                f"当操作失败时，考虑{pattern}",
                                "在关键操作前添加页面检查"
                            ],
                            "success_rate": 1.0,
                            "pattern_type": "error_recovery",
                            "occurrence_count": count
                        }
                        patterns.append(success_pattern)
            
            return patterns
            
        except Exception as e:
            logger.error(f"分析错误处理模式失败: {e}")
            return []
    
    def _analyze_platform_specific_patterns(self, successful_executions: List[Dict]) -> List[Dict]:
        """分析平台特定的成功模式"""
        patterns = []
        
        try:
            platform_patterns = {"ios": [], "android": []}
            
            for execution in successful_executions:
                platform = execution.get("platform", "").lower()
                if platform in platform_patterns:
                    if execution.get("structured_plan") and isinstance(execution["structured_plan"], dict):
                        plan = execution["structured_plan"]
                        steps = plan.get("steps", [])
                        
                        # 分析平台特定的操作序列
                        action_sequence = [step.get("action", "") for step in steps]
                        platform_patterns[platform].append(action_sequence)
            
            for platform, sequences in platform_patterns.items():
                if len(sequences) >= 2:
                    common_seqs = self._find_common_subsequences(sequences, 2)
                    for seq in common_seqs:
                        pattern = {
                            "description": f"{platform.upper()}平台成功模式: {' -> '.join(seq)}",
                            "suggestions": [
                                f"在{platform.upper()}平台推荐使用: {' -> '.join(seq)}",
                                f"该模式在{platform.upper()}平台成功率较高"
                            ],
                            "success_rate": 1.0,
                            "pattern_type": "platform_specific",
                            "platform": platform
                        }
                        patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.error(f"分析平台特定模式失败: {e}")
            return []
    
    def _analyze_failure_patterns(self, failed_executions: List[Dict]) -> List[Dict]:
        """分析失败执行的模式"""
        patterns = []
        
        try:
            # 分析失败原因（基于错误信息关键词）
            common_errors = {}
            for execution in failed_executions:
                error = execution.get("execution_error", "") or ""
                if error and ("输入元素" in error or "input_elements" in error):
                    common_errors.setdefault("input_element_not_found", []).append(execution)
                elif error and "元素" in error and ("找不到" in error or "not found" in error):
                    common_errors.setdefault("element_not_found", []).append(execution)
            
            for error_type, executions in common_errors.items():
                if error_type == "input_element_not_found":
                    pattern = {
                        "description": "输入元素未找到",
                        "suggestions": [
                            "在输入文本前先点击输入框",
                            "确保页面已完全加载",
                            "验证输入元素是否可见和可交互"
                        ]
                    }
                    patterns.append(pattern)
                elif error_type == "element_not_found":
                    pattern = {
                        "description": "页面元素未找到",
                        "suggestions": [
                            "添加页面等待时间",
                            "检查页面是否正确跳转",
                            "使用更准确的元素描述"
                        ]
                    }
                    patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.error(f"分析失败模式失败: {e}")
            return []
    
    def _find_common_tool_sequences(self, executions: List[Dict]) -> List[List[str]]:
        """查找执行记录中的共同工具序列"""
        # 简化实现，实际可以使用更复杂的序列匹配算法
        sequences = []
        
        try:
            # 提取所有执行的工具序列
            all_sequences = []
            for execution in executions:
                if execution.get("structured_plan"):
                    plan = execution["structured_plan"]
                    if isinstance(plan, dict) and "steps" in plan:
                        seq = [step.get("action", "") for step in plan["steps"]]
                        all_sequences.append(seq)
            
            # 查找长度为2-3的共同子序列
            if len(all_sequences) >= 2:
                for length in [2, 3]:
                    common_subseqs = self._find_common_subsequences(all_sequences, length)
                    sequences.extend(common_subseqs)
            
            return sequences[:5]  # 返回最多5个序列
            
        except Exception as e:
            logger.error(f"查找共同工具序列失败: {e}")
            return []
    
    def _find_common_subsequences(self, sequences: List[List[str]], length: int) -> List[List[str]]:
        """查找指定长度的共同子序列"""
        subsequence_count = {}
        
        for seq in sequences:
            for i in range(len(seq) - length + 1):
                subseq = tuple(seq[i:i+length])
                subsequence_count[subseq] = subsequence_count.get(subseq, 0) + 1
        
        # 返回出现次数超过一半的子序列
        threshold = len(sequences) // 2
        common_subseqs = [
            list(subseq) for subseq, count in subsequence_count.items() 
            if count > threshold
        ]
        
        return common_subseqs
    
    def _apply_improvements(self, original_plan: Dict[str, Any], 
                           improvements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """应用改进建议生成优化后的计划"""
        try:
            optimized_plan = json.loads(json.dumps(original_plan))  # 深拷贝
            
            # 记录必要的系统步骤
            essential_steps = ["find_device", "start_test", "end_test"]
            original_essential = {}
            for step in original_plan.get("steps", []):
                action = step.get("action")
                if action in essential_steps:
                    original_essential[action] = step.copy()
            
            # 应用高优先级的改进
            high_priority_improvements = [
                imp for imp in improvements 
                if imp.get("priority") == "high"
            ]
            
            for improvement in high_priority_improvements:
                if improvement.get("type") == "error_pattern_fix":
                    pattern_id = improvement.get("pattern_id")
                    if pattern_id == "input_without_click":
                        optimized_plan = self._fix_input_without_click(optimized_plan)
                    elif pattern_id == "missing_element_search":
                        optimized_plan = self._fix_missing_element_search(optimized_plan)
                    elif pattern_id == "missing_page_wait":
                        optimized_plan = self._fix_missing_page_wait(optimized_plan)
            
            # 确保必要步骤存在
            current_actions = [step.get("action") for step in optimized_plan.get("steps", [])]
            
            # 检查并恢复缺失的必要步骤
            for essential_action in essential_steps:
                if essential_action not in current_actions and essential_action in original_essential:
                    missing_step = original_essential[essential_action].copy()
                    
                    if essential_action == "find_device":
                        # find_device应该在开头
                        optimized_plan["steps"].insert(0, missing_step)
                        logger.info(f"恢复缺失的开始步骤: {essential_action}")
                    elif essential_action == "start_test":
                        # start_test应该在find_device之后
                        insert_index = 1 if "find_device" in current_actions else 0
                        optimized_plan["steps"].insert(insert_index, missing_step)
                        logger.info(f"恢复缺失的启动步骤: {essential_action}")
                    elif essential_action == "end_test":
                        # end_test应该在结尾
                        optimized_plan["steps"].append(missing_step)
                        logger.info(f"恢复缺失的结束步骤: {essential_action}")
            
            # 更新总步骤数
            if "steps" in optimized_plan:
                optimized_plan["total_steps"] = len(optimized_plan["steps"])
            
            # 重新分配步骤ID
            for i, step in enumerate(optimized_plan.get("steps", []), 1):
                step["step_id"] = i
            
            logger.info(f"应用改进后，计划步骤数从{original_plan.get('total_steps', 0)}变为{optimized_plan.get('total_steps', 0)}")
            return optimized_plan
            
        except Exception as e:
            logger.error(f"应用改进建议失败: {e}")
            return original_plan
    
    def _fix_input_without_click(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """修复输入前未点击的问题"""
        try:
            steps = plan.get("steps", [])
            new_steps = []
            
            for i, step in enumerate(steps):
                new_steps.append(step)
                
                # 如果当前步骤是find_element，下一步是input_text，则插入tap操作
                if (step.get("action") == "find_element" and 
                    i < len(steps) - 1 and 
                    steps[i + 1].get("action") == "input_text"):
                    
                    # 插入tap操作
                    tap_step = {
                        "step_id": len(new_steps) + 1,
                        "action": "tap",
                        "description": f"点击{step.get('parameters', {}).get('element', '元素')}",
                        "parameters": {
                            "udid": step.get("parameters", {}).get("udid", "{device_udid}"),
                            "x": 100,  # 占位坐标，实际应该使用find_element的结果
                            "y": 100,
                            "description": f"点击{step.get('parameters', {}).get('element', '元素')}"
                        },
                        "expected_result": f"成功点击{step.get('parameters', {}).get('element', '元素')}"
                    }
                    new_steps.append(tap_step)
            
            plan["steps"] = new_steps
            return plan
            
        except Exception as e:
            logger.error(f"修复输入前未点击问题失败: {e}")
            return plan
    
    def _fix_missing_element_search(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """修复缺少元素查找的问题"""
        try:
            steps = plan.get("steps", [])
            new_steps = []
            
            for i, step in enumerate(steps):
                # 如果是tap操作且使用硬编码坐标，先添加find_element
                if (step.get("action") == "tap" and 
                    "x" in step.get("parameters", {}) and
                    "y" in step.get("parameters", {})):
                    
                    # 检查前面是否已有find_element
                    has_find_element = False
                    for j in range(max(0, i-2), i):
                        if len(new_steps) > j and new_steps[j].get("action") == "find_element":
                            has_find_element = True
                            break
                    
                    if not has_find_element:
                        # 插入find_element操作
                        element_name = step.get("parameters", {}).get("description", "元素")
                        find_step = {
                            "step_id": len(new_steps) + 1,
                            "action": "find_element", 
                            "description": f"查找{element_name}",
                            "parameters": {
                                "udid": step.get("parameters", {}).get("udid", "{device_udid}"),
                                "element": element_name,
                                "scene_desc": "当前页面"
                            },
                            "expected_result": f"成功找到{element_name}"
                        }
                        new_steps.append(find_step)
                
                new_steps.append(step)
            
            plan["steps"] = new_steps
            return plan
            
        except Exception as e:
            logger.error(f"修复缺少元素查找问题失败: {e}")
            return plan
    
    def _fix_missing_page_wait(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """修复缺少页面等待的问题"""
        try:
            steps = plan.get("steps", [])
            new_steps = []
            
            for i, step in enumerate(steps):
                new_steps.append(step)
                
                # 在tap操作后，如果下一步是重要操作，添加等待
                if (step.get("action") == "tap" and 
                    i < len(steps) - 1):
                    
                    next_step = steps[i + 1]
                    next_action = next_step.get("action", "")
                    
                    if next_action in ["input_text", "find_element", "check_page"]:
                        # 添加等待步骤
                        wait_step = {
                            "step_id": len(new_steps) + 1,
                            "action": "wait",
                            "description": "等待页面加载",
                            "parameters": {"seconds": 2},
                            "expected_result": "页面加载完成"
                        }
                        new_steps.append(wait_step)
            
            plan["steps"] = new_steps
            return plan
            
        except Exception as e:
            logger.error(f"修复缺少页面等待问题失败: {e}")
            return plan
    
    def save_execution_feedback(self, execution: TaskExecution, 
                               optimization_result: Optional[Dict] = None) -> bool:
        """保存执行反馈到RAG系统"""
        try:
            # 保存执行记录到数据库
            success = self.db_manager.save_task_execution(execution)
            if not success:
                logger.error("保存执行记录失败")
                return False
            
            # 如果有优化结果，创建改进记录
            if optimization_result and optimization_result.get("improvements"):
                improvement = PlanImprovement(
                    improvement_id="",
                    original_execution_id=execution.execution_id,
                    original_plan=optimization_result.get("original_plan", {}),
                    improved_plan=optimization_result.get("optimized_plan", {}),
                    improvement_reason="基于RAG系统的自动优化",
                    improvement_type="automatic_optimization",
                    effectiveness_score=None,  # 需要后续评估
                    validation_execution_id=None,
                    human_reviewed=False,
                    reviewer_id=None,
                    review_notes=None,
                    created_at=datetime.now(),
                    reviewed_at=None,
                    metadata={
                        "detected_patterns": optimization_result.get("detected_patterns", []),
                        "similar_executions_count": len(optimization_result.get("similar_executions", [])),
                        "optimization_metadata": optimization_result.get("metadata", {})
                    }
                )
                
                # 这里可以保存到改进记录表，目前数据库管理器还没有相关方法
                # 留待后续扩展
                logger.info(f"创建改进记录: {improvement.improvement_id}")
            
            logger.info(f"成功保存执行反馈: {execution.execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存执行反馈失败: {e}")
            return False
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        try:
            stats = self.db_manager.get_database_stats()
            
            # 添加优化相关的统计
            optimization_stats = {
                "error_patterns_count": len(self.error_patterns),
                "error_patterns": [
                    {
                        "pattern_id": p.pattern_id,
                        "pattern_name": p.pattern_name,
                        "description": p.description
                    } for p in self.error_patterns
                ],
                "database_stats": stats
            }
            
            return optimization_stats
            
        except Exception as e:
            logger.error(f"获取优化统计失败: {e}")
            return {"error": str(e)}


def create_plan_optimizer(db_manager: DatabaseManager) -> PlanOptimizer:
    """创建计划优化器的工厂函数"""
    return PlanOptimizer(db_manager)