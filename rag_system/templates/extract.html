{% extends "base.html" %}

{% block title %}数据管理 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-cog text-primary"></i> 数据管理
            <small class="text-muted">日志提取与系统维护</small>
        </h1>
        <p class="lead">从历史执行日志中提取数据，维护系统知识库。</p>
    </div>
</div>

<!-- 数据提取 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download"></i> 批量数据提取
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">从log目录中的执行日志文件提取结构化数据，并存储到RAG系统中。</p>
                
                <form id="extract-form" class="row g-3">
                    <div class="col-md-6">
                        <label for="extract-limit" class="form-label">提取数量限制</label>
                        <select class="form-select" id="extract-limit">
                            <option value="5">最新 5 个日志</option>
                            <option value="10" selected>最新 10 个日志</option>
                            <option value="20">最新 20 个日志</option>
                            <option value="50">最新 50 个日志</option>
                            <option value="100">最新 100 个日志</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-play"></i> 开始提取
                        </button>
                        <button type="button" class="btn btn-info" id="check-logs">
                            <i class="fas fa-search"></i> 检查日志
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 提取结果 -->
<div class="row" id="extract-results" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> 提取结果
                </h5>
            </div>
            <div class="card-body">
                <div id="extract-content"></div>
            </div>
        </div>
    </div>
</div>

<!-- 系统维护 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i> 系统维护
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-sync-alt fa-2x text-primary mb-3"></i>
                                <h6>刷新统计</h6>
                                <p class="small text-muted">重新计算数据库统计信息</p>
                                <button class="btn btn-primary" id="refresh-stats">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-flask fa-2x text-success mb-3"></i>
                                <h6>测试连接</h6>
                                <p class="small text-muted">测试Qdrant和Embedding服务</p>
                                <button class="btn btn-success" id="test-services">
                                    <i class="fas fa-check"></i> 测试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志查看 -->
<div class="row" id="log-info" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt"></i> 日志目录信息
                </h5>
            </div>
            <div class="card-body">
                <div id="log-content"></div>
            </div>
        </div>
    </div>
</div>

<!-- 进度显示 -->
<div class="modal fade" id="progressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-spinner fa-spin"></i> 正在处理...
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         style="width: 0%"></div>
                </div>
                <p id="progress-text">准备开始...</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 数据提取
document.getElementById('extract-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const limit = document.getElementById('extract-limit').value;
    
    // 显示进度模态框
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progress-text');
    
    progressText.textContent = '开始提取数据...';
    progressBar.style.width = '20%';
    
    fetch('/api/extract', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            limit: parseInt(limit)
        })
    })
    .then(response => {
        progressBar.style.width = '70%';
        progressText.textContent = '处理响应数据...';
        return response.json();
    })
    .then(data => {
        progressBar.style.width = '100%';
        progressText.textContent = '完成!';
        
        setTimeout(() => {
            progressModal.hide();
            displayExtractResults(data);
        }, 1000);
    })
    .catch(error => {
        progressModal.hide();
        alert('提取失败: ' + error.message);
    });
});

// 显示提取结果
function displayExtractResults(data) {
    const container = document.getElementById('extract-content');
    
    if (data.success) {
        const result = data.result;
        const successRate = (result.success_rate * 100).toFixed(1);
        
        container.innerHTML = `
            <div class="row text-center mb-4">
                <div class="col-md-3">
                    <h4 class="text-primary">${result.total_processed}</h4>
                    <small class="text-muted">总处理数</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-success">${result.successful}</h4>
                    <small class="text-muted">成功提取</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-danger">${result.failed}</h4>
                    <small class="text-muted">提取失败</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-info">${successRate}%</h4>
                    <small class="text-muted">成功率</small>
                </div>
            </div>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <strong>提取完成!</strong> 成功从 ${result.total_processed} 个日志文件中提取了 ${result.successful} 个执行记录。
            </div>
        `;
    } else {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>提取失败:</strong> ${data.error}
            </div>
        `;
    }
    
    document.getElementById('extract-results').style.display = 'block';
}

// 检查日志
document.getElementById('check-logs').addEventListener('click', function() {
    const btn = this;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';
    btn.disabled = true;
    
    // 模拟检查日志目录
    setTimeout(() => {
        const logContent = document.getElementById('log-content');
        logContent.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>日志目录状态:</strong>
                <ul class="mb-0 mt-2">
                    <li>日志目录: <code>log/</code></li>
                    <li>发现日志文件夹数量: <strong>163</strong> 个</li>
                    <li>命名格式: <code>round_XXXXXX_YYYYMMDD_HHMMSS</code></li>
                    <li>最新日志: <code>round_000371_20250801_152136</code></li>
                </ul>
            </div>
        `;
        
        document.getElementById('log-info').style.display = 'block';
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 1500);
});

// 刷新统计
document.getElementById('refresh-stats').addEventListener('click', function() {
    const btn = this;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
    btn.disabled = true;
    
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            alert('✅ 统计信息已刷新!\n总执行数: ' + (data.total_executions || 0) + 
                  '\n总向量数: ' + (data.total_vectors || 0));
        })
        .catch(error => {
            alert('❌ 刷新失败: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
});

// 测试服务
document.getElementById('test-services').addEventListener('click', function() {
    const btn = this;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    btn.disabled = true;
    
    Promise.all([
        fetch('/api/test_embedding', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ text: '测试连接' })
        }).then(r => r.json()),
        
        fetch('/api/stats').then(r => r.json())
    ])
    .then(([embeddingResult, statsResult]) => {
        let message = '🔍 服务状态检查结果:\n\n';
        
        if (embeddingResult.success) {
            message += '✅ Embedding服务: 正常\n';
            message += `   - 向量维度: ${embeddingResult.dimension}\n`;
        } else {
            message += '❌ Embedding服务: 异常\n';
        }
        
        if (statsResult.total_vectors !== undefined) {
            message += '✅ Qdrant服务: 正常\n';
            message += `   - 向量总数: ${statsResult.total_vectors}\n`;
        } else {
            message += '❌ Qdrant服务: 异常\n';
        }
        
        alert(message);
    })
    .catch(error => {
        alert('❌ 服务测试失败: ' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
});
</script>
{% endblock %}