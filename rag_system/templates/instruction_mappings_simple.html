<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指令映射管理 - RAG系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-database"></i> RAG系统
            </a>
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('executions') }}">任务执行</a>
                <a class="nav-link" href="{{ url_for('judge_reports') }}">Judge报告</a>
                <a class="nav-link active" href="{{ url_for('instruction_mappings') }}">指令映射</a>
                <a class="nav-link" href="{{ url_for('search') }}">向量搜索</a>
                <a class="nav-link" href="{{ url_for('database_viewer') }}">数据库</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-exchange-alt"></i> 指令映射管理</h2>
                </div>

                <!-- 统计信息 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>总映射数</h6>
                                        <h4>{{ mappings|length }}</h4>
                                    </div>
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>iOS平台</h6>
                                        <h4>0</h4>
                                    </div>
                                    <i class="fab fa-apple fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>Android平台</h6>
                                        <h4>0</h4>
                                    </div>
                                    <i class="fab fa-android fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>平均质量评分</h6>
                                        <h4>0.00</h4>
                                    </div>
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 指令映射列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">指令映射列表</h5>
                    </div>
                    <div class="card-body">
                        {% if mappings %}
                            <p>找到 {{ mappings|length }} 条记录</p>
                            <!-- 这里可以添加表格 -->
                        {% else %}
                            <p class="text-muted">暂无指令映射数据</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>