<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Judge报告详情 - RAG系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .score-display {
            font-size: 1.2em;
            font-weight: bold;
        }
        .success-status { color: #28a745; }
        .failure-status { color: #dc3545; }
        .modified-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.25rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .analysis-content {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .thinking-process {
            background-color: #e9ecef;
            border-left: 4px solid #6c757d;
            padding: 1rem;
            margin: 1rem 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }
        .metadata-table th {
            width: 200px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-database"></i> RAG系统
            </a>
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('executions') }}">任务执行</a>
                <a class="nav-link active" href="{{ url_for('judge_reports') }}">Judge报告</a>
                <a class="nav-link" href="{{ url_for('instruction_mappings') }}">指令映射</a>
                <a class="nav-link" href="{{ url_for('search') }}">向量搜索</a>
                <a class="nav-link" href="{{ url_for('database_viewer') }}">数据库</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <!-- 页面标题和操作按钮 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-gavel"></i> Judge报告详情</h2>
                    <div>
                        <a href="{{ url_for('judge_report_edit', report_id=report.report_id) }}" 
                           class="btn btn-warning">
                            <i class="fas fa-edit"></i> 编辑评价
                        </a>
                        <a href="{{ url_for('judge_reports') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>

                <!-- 基本信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> 基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm metadata-table">
                                    <tr>
                                        <th>报告ID</th>
                                        <td><code>{{ report.report_id }}</code></td>
                                    </tr>
                                    <tr>
                                        <th>执行ID</th>
                                        <td><code>{{ report.execution_id }}</code></td>
                                    </tr>
                                    <tr>
                                        <th>轮次ID</th>
                                        <td><code>{{ report.round_id }}</code></td>
                                    </tr>
                                    <tr>
                                        <th>评价模型</th>
                                        <td>{{ report.judge_model }}</td>
                                    </tr>
                                    <tr>
                                        <th>评价时间</th>
                                        <td>{{ report.judge_timestamp|datetime }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm metadata-table">
                                    <tr>
                                        <th>创建时间</th>
                                        <td>{{ report.created_at|datetime }}</td>
                                    </tr>
                                    <tr>
                                        <th>更新时间</th>
                                        <td>{{ report.updated_at|datetime }}</td>
                                    </tr>
                                    <tr>
                                        <th>版本</th>
                                        <td>{{ report.judge_version }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI评价结果 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-robot"></i> AI评价结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>总体成功状态</h6>
                                        <div class="score-display {{ 'success-status' if report.overall_success else 'failure-status' }}">
                                            {{ '成功' if report.overall_success else '失败' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>成功评分</h6>
                                        <div class="score-display text-primary">
                                            {{ "%.2f"|format(report.success_score) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>置信度</h6>
                                        <div class="score-display text-info">
                                            {{ "%.2f"|format(report.confidence_score) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>测试质量评分</h6>
                                        <div class="score-display text-success">
                                            {{ "%.2f"|format(report.test_quality_score) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 详细评分 -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>计划质量评分</h6>
                                        <div class="score-display text-warning">
                                            {{ "%.2f"|format(report.plan_quality_score) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>执行质量评分</h6>
                                        <div class="score-display text-danger">
                                            {{ "%.2f"|format(report.execution_quality_score) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>目标达成评分</h6>
                                        <div class="score-display text-secondary">
                                            {{ "%.2f"|format(report.target_achievement_score) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人工修正信息 -->
                {% if report.human_modified %}
                <div class="card mb-4">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0"><i class="fas fa-user-edit"></i> 人工修正信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="modified-section">
                            <div class="row">
                                <div class="col-md-6">
                                    {% if report.human_override_success is not none %}
                                    <p><strong>人工判断成功状态:</strong> 
                                        <span class="{{ 'success-status' if report.human_override_success else 'failure-status' }}">
                                            {{ '成功' if report.human_override_success else '失败' }}
                                        </span>
                                    </p>
                                    {% endif %}
                                    {% if report.human_override_score is not none %}
                                    <p><strong>人工修正评分:</strong> 
                                        <span class="text-primary">{{ "%.2f"|format(report.human_override_score) }}</span>
                                    </p>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <p><strong>修改者:</strong> {{ report.human_modifier or '未知' }}</p>
                                    <p><strong>修改时间:</strong> {{ report.human_modified_at|datetime if report.human_modified_at else '未知' }}</p>
                                </div>
                            </div>
                            {% if report.human_notes %}
                            <div class="mt-3">
                                <strong>人工备注:</strong>
                                <div class="mt-2 p-2 bg-white border rounded">
                                    {{ report.human_notes }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 详细分析 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-search"></i> 详细分析内容</h5>
                    </div>
                    <div class="card-body">
                        <div class="analysis-content">{{ report.detailed_analysis or '暂无详细分析内容' }}</div>
                    </div>
                </div>

                <!-- 问题和建议 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6><i class="fas fa-exclamation-triangle"></i> 关键问题</h6>
                            </div>
                            <div class="card-body">
                                {% if report.key_issues %}
                                    <ul class="list-group list-group-flush">
                                        {% for issue in report.key_issues %}
                                        <li class="list-group-item border-0 px-0">
                                            <i class="fas fa-dot-circle text-danger me-2"></i>{{ issue }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <p class="text-muted">暂无关键问题记录</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6><i class="fas fa-lightbulb"></i> 改进建议</h6>
                            </div>
                            <div class="card-body">
                                {% if report.improvement_suggestions %}
                                    <ul class="list-group list-group-flush">
                                        {% for suggestion in report.improvement_suggestions %}
                                        <li class="list-group-item border-0 px-0">
                                            <i class="fas fa-dot-circle text-success me-2"></i>{{ suggestion }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <p class="text-muted">暂无改进建议</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 思考过程 -->
                {% if report.judge_thinking_process %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-brain"></i> AI思考过程</h5>
                    </div>
                    <div class="card-body">
                        <div class="thinking-process">{{ report.judge_thinking_process }}</div>
                    </div>
                </div>
                {% endif %}

                <!-- 原始数据 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>
                            <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#rawDataCollapse">
                                <i class="fas fa-code"></i> 原始评价输出
                            </button>
                        </h5>
                    </div>
                    <div class="collapse" id="rawDataCollapse">
                        <div class="card-body">
                            <pre class="bg-dark text-light p-3 rounded" style="font-size: 0.8em; max-height: 500px; overflow-y: auto;">{{ report.raw_judge_output or '暂无原始输出' }}</pre>
                        </div>
                    </div>
                </div>

                <!-- 元数据 -->
                <div class="card">
                    <div class="card-header">
                        <h5>
                            <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#metadataCollapse">
                                <i class="fas fa-tags"></i> 元数据信息
                            </button>
                        </h5>
                    </div>
                    <div class="collapse" id="metadataCollapse">
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded">{{ report.metadata|tojson_unicode if report.metadata else '暂无元数据' }}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>