<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RAG知识检索系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <style>
        .sidebar {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding-top: 20px;
        }
        .content {
            padding: 20px;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .card-stats {
            transition: transform 0.2s;
        }
        .card-stats:hover {
            transform: translateY(-5px);
        }
        .search-results {
            max-height: 500px;
            overflow-y: auto;
        }
        .similarity-score {
            background: linear-gradient(45deg, #007bff, #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
            border-radius: 5px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-brain"></i> RAG知识检索系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search"></i> 智能搜索
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('executions') }}">
                            <i class="fas fa-tasks"></i> 执行记录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('judge_reports') }}">
                            <i class="fas fa-gavel"></i> Judge报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('instruction_mappings') }}">
                            <i class="fas fa-exchange-alt"></i> 指令映射
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('knowledge') }}">
                            <i class="fas fa-book"></i> 知识库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('extract') }}">
                            <i class="fas fa-download"></i> 数据提取
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('database_viewer') }}">
                            <i class="fas fa-database"></i> 数据库查看
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('index') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'index' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt"></i> 系统概览
                    </a>
                    <a href="{{ url_for('search') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'search' %}active{% endif %}">
                        <i class="fas fa-search"></i> 向量搜索
                    </a>
                    <a href="{{ url_for('executions') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'executions' %}active{% endif %}">
                        <i class="fas fa-list"></i> 执行历史
                    </a>
                    <a href="{{ url_for('judge_reports') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'judge_reports' %}active{% endif %}">
                        <i class="fas fa-gavel"></i> Judge报告
                    </a>
                    <a href="{{ url_for('instruction_mappings') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'instruction_mappings' %}active{% endif %}">
                        <i class="fas fa-exchange-alt"></i> 指令映射
                    </a>
                    <a href="{{ url_for('knowledge') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'knowledge' %}active{% endif %}">
                        <i class="fas fa-database"></i> 知识条目
                    </a>
                    <a href="{{ url_for('extract') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'extract' %}active{% endif %}">
                        <i class="fas fa-cog"></i> 数据管理
                    </a>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-10 content">
                <!-- 闪现消息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>