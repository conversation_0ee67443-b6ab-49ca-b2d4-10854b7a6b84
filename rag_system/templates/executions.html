{% extends "base.html" %}

{% block title %}执行记录 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-tasks text-primary"></i> 执行记录
            <small class="text-muted">历史任务执行记录</small>
        </h1>
        <p class="lead">浏览所有任务执行历史，了解系统运行情况和执行结果。</p>
    </div>
</div>

<!-- 执行记录表格 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 执行历史
                </h5>
                <span class="badge bg-primary">共 {{ pagination.total }} 条记录</span>
            </div>
            <div class="card-body">
                {% if executions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>轮次</th>
                                <th>平台</th>
                                <th>指令摘要</th>
                                <th>状态</th>
                                <th>步骤统计</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for execution in executions %}
                            <tr>
                                <td>
                                    <span class="badge bg-secondary">
                                        #{{ execution.round_number }}
                                    </span>
                                    <br>
                                    <small class="text-muted">{{ execution.round_id }}</small>
                                </td>
                                <td>
                                    <i class="{{ execution.platform | platform_icon }} me-1"></i>
                                    {{ execution.platform.upper() }}
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;" 
                                         title="{{ execution.original_instruction }}">
                                        {{ execution.original_instruction }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ execution.execution_status | status_badge }}">
                                        {{ execution.execution_status | title }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <small>
                                            <i class="fas fa-tasks text-primary"></i>
                                            总数: {{ execution.total_steps }}
                                        </small>
                                        <small>
                                            <i class="fas fa-check text-success"></i>
                                            成功: {{ execution.successful_steps }}
                                        </small>
                                        <small>
                                            <i class="fas fa-times text-danger"></i>
                                            失败: {{ execution.failed_steps }}
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span title="{{ execution.created_at }}">
                                        {{ execution.created_at | datetime }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('execution_detail', execution_id=execution.execution_id) }}" 
                                           class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="copyExecutionId('{{ execution.execution_id }}')" 
                                                title="复制ID">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="d-flex justify-content-center mt-4">
                    {{ pagination.links }}
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>暂无执行记录</p>
                    <a href="{{ url_for('extract') }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> 提取历史数据
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ executions | selectattr('execution_status', 'equalto', 'completed') | list | length }}</h4>
                <small class="text-muted">成功执行</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-danger">{{ executions | selectattr('execution_status', 'equalto', 'failed') | list | length }}</h4>
                <small class="text-muted">执行失败</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">{{ executions | selectattr('platform', 'equalto', 'ios') | list | length }}</h4>
                <small class="text-muted">iOS任务</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">{{ executions | selectattr('platform', 'equalto', 'android') | list | length }}</h4>
                <small class="text-muted">Android任务</small>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function copyExecutionId(executionId) {
    navigator.clipboard.writeText(executionId).then(function() {
        // 创建临时提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 1050;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check"></i> 执行ID已复制到剪贴板
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }).catch(function(err) {
        alert('复制失败: ' + err);
    });
}

// 表格行点击事件
document.querySelectorAll('tbody tr').forEach(row => {
    row.style.cursor = 'pointer';
    row.addEventListener('click', function(e) {
        // 如果点击的是按钮，不触发行点击事件
        if (e.target.closest('.btn-group')) {
            return;
        }
        
        // 获取查看详情链接并跳转
        const detailLink = this.querySelector('a[href*="/execution/"]');
        if (detailLink) {
            window.location.href = detailLink.href;
        }
    });
});
</script>
{% endblock %}