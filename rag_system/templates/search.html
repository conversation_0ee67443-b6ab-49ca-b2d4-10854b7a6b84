{% extends "base.html" %}

{% block title %}智能搜索 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-search text-primary"></i> 智能搜索
            <small class="text-muted">基于向量相似度的语义搜索</small>
        </h1>
        <p class="lead">输入自然语言描述，系统将从历史执行记录中找到最相关的内容。</p>
    </div>
</div>

<!-- 搜索表单 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i> 搜索配置
                </h5>
            </div>
            <div class="card-body">
                <form id="search-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="query" class="form-label">搜索内容 *</label>
                                <textarea class="form-control" id="query" rows="3" 
                                         placeholder="例如：点击地址选择器、截图操作失败、iOS设备连接问题等..."
                                         required></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="search-type" class="form-label">搜索类型</label>
                                <select class="form-select" id="search-type">
                                    <option value="instructions">指令搜索</option>
                                    <option value="instruction_mappings">指令映射</option>
                                    <option value="judge_insights">Judge评价洞察</option>
                                    <option value="tool_results">工具结果</option>
                                    <option value="error_patterns">错误模式</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="top-k" class="form-label">返回数量</label>
                                <select class="form-select" id="top-k">
                                    <option value="5">5条</option>
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 高级选项 -->
                    <div class="row" id="advanced-options">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="platform-filter" class="form-label">平台过滤</label>
                                <select class="form-select" id="platform-filter">
                                    <option value="">所有平台</option>
                                    <option value="ios">iOS</option>
                                    <option value="android">Android</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6" id="tool-name-group" style="display: none;">
                            <div class="mb-3">
                                <label for="tool-name" class="form-label">工具名称</label>
                                <input type="text" class="form-control" id="tool-name" 
                                       placeholder="例如：screenshot、find_element等">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-search"></i> 开始搜索
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg ms-2" id="test-embedding">
                        <i class="fas fa-flask"></i> 测试连接
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 搜索结果 -->
<div class="row" id="results-container" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 搜索结果
                </h5>
                <span id="results-count" class="badge bg-primary"></span>
            </div>
            <div class="card-body">
                <div id="search-results" class="search-results"></div>
            </div>
        </div>
    </div>
</div>

<!-- 加载动画 -->
<div class="row" id="loading-container" style="display: none;">
    <div class="col-md-12 text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">搜索中...</span>
        </div>
        <p class="mt-2">正在进行向量搜索，请稍候...</p>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.result-item {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.result-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.similarity-score {
    font-size: 1.2em;
    font-weight: bold;
}

.metadata-tag {
    font-size: 0.75em;
    margin: 2px;
}

.search-type-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 0.8em;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 搜索类型变化时显示/隐藏相关选项
document.getElementById('search-type').addEventListener('change', function() {
    const searchType = this.value;
    const toolNameGroup = document.getElementById('tool-name-group');
    const platformFilter = document.getElementById('platform-filter');
    
    if (searchType === 'tool_results' || searchType === 'error_patterns') {
        toolNameGroup.style.display = 'block';
    } else {
        toolNameGroup.style.display = 'none';
    }
    
    // 指令搜索显示平台过滤，其他隐藏
    if (searchType === 'instructions') {
        platformFilter.closest('.mb-3').style.display = 'block';
    } else {
        platformFilter.closest('.mb-3').style.display = 'none';
    }
});

// 测试embedding连接
document.getElementById('test-embedding').addEventListener('click', function() {
    const btn = this;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    btn.disabled = true;
    
    fetch('/api/test_embedding', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            text: '测试embedding服务连接'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`✅ Embedding服务正常！\n向量维度: ${data.dimension}\n示例值: [${data.sample.join(', ')}...]`);
        } else {
            alert(`❌ Embedding服务异常: ${data.error}`);
        }
    })
    .catch(error => {
        alert(`❌ 连接失败: ${error.message}`);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
});

// 执行搜索
document.getElementById('search-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const query = document.getElementById('query').value.trim();
    if (!query) {
        alert('请输入搜索内容');
        return;
    }
    
    const searchData = {
        query: query,
        type: document.getElementById('search-type').value,
        top_k: document.getElementById('top-k').value,
        platform: document.getElementById('platform-filter').value,
        tool_name: document.getElementById('tool-name').value
    };
    
    // 显示加载动画
    document.getElementById('loading-container').style.display = 'block';
    document.getElementById('results-container').style.display = 'none';
    
    fetch('/api/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchData)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loading-container').style.display = 'none';
        
        if (data.success) {
            displayResults(data.results, data.total);
        } else {
            alert('搜索失败: ' + data.error);
        }
    })
    .catch(error => {
        document.getElementById('loading-container').style.display = 'none';
        alert('搜索请求失败: ' + error.message);
    });
});

function displayResults(results, total) {
    const container = document.getElementById('search-results');
    const countBadge = document.getElementById('results-count');
    
    countBadge.textContent = `${total} 条结果`;
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>没有找到相关结果，请尝试使用不同的关键词。</p>
            </div>
        `;
    } else {
        const resultsHtml = results.map((result, index) => {
            const payload = result.payload || {};
            const score = (result.score * 100).toFixed(1);
            const scoreColor = result.score > 0.8 ? 'success' : result.score > 0.6 ? 'warning' : 'secondary';
            
            return `
                <div class="result-item p-3 mb-3 rounded position-relative">
                    <div class="search-type-indicator">
                        <span class="badge bg-info">${payload.text_type || 'unknown'}</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">结果 #${index + 1}</h6>
                        <span class="similarity-score text-${scoreColor}">${score}%</span>
                    </div>
                    
                    <p class="text-dark mb-2">${payload.text_content || '内容不可用'}</p>
                    
                    <div class="metadata-tags">
                        ${payload.execution_id ? `<span class="badge bg-primary metadata-tag">执行ID: ${payload.execution_id.substring(0, 8)}...</span>` : ''}
                        ${payload.platform ? `<span class="badge bg-secondary metadata-tag">平台: ${payload.platform.toUpperCase()}</span>` : ''}
                        ${payload.metadata && payload.metadata.tool_name ? `<span class="badge bg-success metadata-tag">工具: ${payload.metadata.tool_name}</span>` : ''}
                        ${payload.created_at ? `<span class="badge bg-info metadata-tag">时间: ${new Date(payload.created_at).toLocaleDateString()}</span>` : ''}
                    </div>
                    
                    ${payload.execution_id ? `
                        <div class="mt-2">
                            <a href="/execution/${payload.execution_id}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
        
        container.innerHTML = resultsHtml;
    }
    
    document.getElementById('results-container').style.display = 'block';
}

// 页面加载时设置默认值
document.addEventListener('DOMContentLoaded', function() {
    // 触发搜索类型变化事件以设置初始状态
    document.getElementById('search-type').dispatchEvent(new Event('change'));
});
</script>
{% endblock %}