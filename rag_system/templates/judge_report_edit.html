<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑Judge报告 - RAG系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .current-value {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .success-status { color: #28a745; }
        .failure-status { color: #dc3545; }
        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-database"></i> RAG系统
            </a>
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('executions') }}">任务执行</a>
                <a class="nav-link active" href="{{ url_for('judge_reports') }}">Judge报告</a>
                <a class="nav-link" href="{{ url_for('instruction_mappings') }}">指令映射</a>
                <a class="nav-link" href="{{ url_for('search') }}">向量搜索</a>
                <a class="nav-link" href="{{ url_for('database_viewer') }}">数据库</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <!-- 页面标题和操作按钮 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-edit"></i> 编辑Judge评价</h2>
                    <div>
                        <a href="{{ url_for('judge_report_detail', report_id=report.report_id) }}" 
                           class="btn btn-info">
                            <i class="fas fa-eye"></i> 查看详情
                        </a>
                        <a href="{{ url_for('judge_reports') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>

                <!-- 基本信息显示 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> 报告信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>轮次ID:</strong> <code>{{ report.round_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>评价时间:</strong> {{ report.judge_timestamp|datetime }}
                            </div>
                            <div class="col-md-4">
                                <strong>评价模型:</strong> {{ report.judge_model }}
                            </div>
                        </div>
                    </div>
                </div>

                <form id="editForm">
                    <input type="hidden" id="reportId" value="{{ report.report_id }}">
                    
                    <!-- AI评价结果展示 -->
                    <div class="form-section">
                        <h5><i class="fas fa-robot"></i> AI原始评价结果</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">AI判断成功状态</label>
                                    <div class="current-value">
                                        <span class="{{ 'success-status' if report.overall_success else 'failure-status' }}">
                                            <i class="fas {{ 'fa-check-circle' if report.overall_success else 'fa-times-circle' }}"></i>
                                            {{ '成功' if report.overall_success else '失败' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">AI成功评分</label>
                                    <div class="current-value">
                                        <span class="text-primary fw-bold">{{ "%.2f"|format(report.success_score) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">置信度</label>
                                    <div class="current-value">{{ "%.2f"|format(report.confidence_score) }}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">计划质量评分</label>
                                    <div class="current-value">{{ "%.2f"|format(report.plan_quality_score) }}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">执行质量评分</label>
                                    <div class="current-value">{{ "%.2f"|format(report.execution_quality_score) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 人工修正区域 -->
                    <div class="form-section bg-warning bg-opacity-10">
                        <h5><i class="fas fa-user-edit"></i> 人工修正</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>说明:</strong> 人工修正将覆盖AI的评价结果，用于改进系统判断准确性。留空表示接受AI原始判断。
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="humanOverrideSuccess" class="form-label">人工判断成功状态</label>
                                    <select class="form-select" id="humanOverrideSuccess">
                                        <option value="">保持AI判断</option>
                                        <option value="true" {{ 'selected' if report.human_override_success == true }}>成功</option>
                                        <option value="false" {{ 'selected' if report.human_override_success == false }}>失败</option>
                                    </select>
                                    <div class="form-text">选择人工判断的成功状态，留空表示采用AI判断</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="humanOverrideScore" class="form-label">人工修正评分</label>
                                    <input type="number" class="form-control" id="humanOverrideScore" 
                                           min="0" max="1" step="0.01" 
                                           value="{{ report.human_override_score if report.human_override_score is not none else '' }}"
                                           placeholder="0.00-1.00，留空采用AI评分">
                                    <div class="form-text">输入0.00-1.00之间的评分，留空表示采用AI评分</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="humanNotes" class="form-label">人工备注</label>
                            <textarea class="form-control" id="humanNotes" rows="4" 
                                      placeholder="请输入修正原因、发现的问题或改进建议...">{{ report.human_notes or '' }}</textarea>
                            <div class="form-text">详细说明人工修正的原因和依据</div>
                        </div>

                        <div class="mb-3">
                            <label for="humanModifier" class="form-label">修改者</label>
                            <input type="text" class="form-control" id="humanModifier" 
                                   value="{{ report.human_modifier or '' }}" 
                                   placeholder="请输入修改者姓名或标识">
                            <div class="form-text">标识进行修正的人员</div>
                        </div>
                    </div>

                    <!-- 当前人工修正状态（如果有） -->
                    {% if report.human_modified %}
                    <div class="form-section bg-success bg-opacity-10">
                        <h6><i class="fas fa-history"></i> 当前修正状态</h6>
                        <div class="row">
                            <div class="col-md-6">
                                {% if report.human_override_success is not none %}
                                <p><strong>当前人工判断:</strong> 
                                    <span class="{{ 'success-status' if report.human_override_success else 'failure-status' }}">
                                        {{ '成功' if report.human_override_success else '失败' }}
                                    </span>
                                </p>
                                {% endif %}
                                {% if report.human_override_score is not none %}
                                <p><strong>当前人工评分:</strong> 
                                    <span class="text-primary fw-bold">{{ "%.2f"|format(report.human_override_score) }}</span>
                                </p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <p><strong>上次修改者:</strong> {{ report.human_modifier or '未知' }}</p>
                                <p><strong>上次修改时间:</strong> {{ report.human_modified_at|datetime if report.human_modified_at else '未知' }}</p>
                            </div>
                        </div>
                        {% if report.human_notes %}
                        <p><strong>当前备注:</strong></p>
                        <div class="bg-white p-2 border rounded">{{ report.human_notes }}</div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- 提交按钮 -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg me-3">
                            <i class="fas fa-save"></i> 保存修正
                        </button>
                        <button type="button" class="btn btn-warning me-3" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置表单
                        </button>
                        <a href="{{ url_for('judge_report_detail', report_id=report.report_id) }}" 
                           class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 成功/错误提示模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 保存原始值用于重置
        const originalValues = {
            humanOverrideSuccess: document.getElementById('humanOverrideSuccess').value,
            humanOverrideScore: document.getElementById('humanOverrideScore').value,
            humanNotes: document.getElementById('humanNotes').value,
            humanModifier: document.getElementById('humanModifier').value
        };

        function showMessage(title, message, isError = false) {
            document.getElementById('messageModalTitle').textContent = title;
            document.getElementById('messageModalBody').innerHTML = message;
            const modal = document.getElementById('messageModal');
            modal.className = 'modal fade';
            if (isError) {
                modal.className += ' text-danger';
            }
            new bootstrap.Modal(modal).show();
        }

        function resetForm() {
            document.getElementById('humanOverrideSuccess').value = originalValues.humanOverrideSuccess;
            document.getElementById('humanOverrideScore').value = originalValues.humanOverrideScore;
            document.getElementById('humanNotes').value = originalValues.humanNotes;
            document.getElementById('humanModifier').value = originalValues.humanModifier;
        }

        document.getElementById('editForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const reportId = document.getElementById('reportId').value;
            const humanOverrideSuccess = document.getElementById('humanOverrideSuccess').value;
            const humanOverrideScore = document.getElementById('humanOverrideScore').value;
            const humanNotes = document.getElementById('humanNotes').value.trim();
            const humanModifier = document.getElementById('humanModifier').value.trim();
            
            // 数据验证
            if (humanOverrideScore && (isNaN(humanOverrideScore) || humanOverrideScore < 0 || humanOverrideScore > 1)) {
                showMessage('错误', '人工评分必须是0.00-1.00之间的数值', true);
                return;
            }
            
            if (!humanModifier && (humanOverrideSuccess || humanOverrideScore || humanNotes)) {
                showMessage('错误', '进行人工修正时必须填写修改者信息', true);
                return;
            }
            
            // 构建提交数据
            const submitData = {
                human_override_success: humanOverrideSuccess === 'true' ? true : (humanOverrideSuccess === 'false' ? false : null),
                human_override_score: humanOverrideScore ? parseFloat(humanOverrideScore) : null,
                human_notes: humanNotes,
                human_modifier: humanModifier
            };
            
            // 提交数据
            fetch(`/api/judge_report/${reportId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('成功', '人工修正已保存成功！将跳转到详情页面...');
                    setTimeout(() => {
                        window.location.href = `{{ url_for('judge_report_detail', report_id=report.report_id) }}`;
                    }, 1500);
                } else {
                    showMessage('错误', data.error || '保存失败', true);
                }
            })
            .catch(error => {
                showMessage('错误', '网络请求失败: ' + error.message, true);
            });
        });

        // 自动填充修改者为当前用户（如果为空）
        document.addEventListener('DOMContentLoaded', function() {
            const modifierInput = document.getElementById('humanModifier');
            if (!modifierInput.value.trim()) {
                modifierInput.value = 'web_user';
            }
        });
    </script>
</body>
</html>