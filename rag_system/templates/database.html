{% extends "base.html" %}

{% block title %}数据库查看 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-database text-primary"></i> 数据库查看
            <small class="text-muted">原始数据查看与分析</small>
        </h1>
        <p class="lead">查看SQLite数据库表和Qdrant向量集合的原始数据，了解系统存储结构。</p>
    </div>
</div>

<!-- 数据库概览 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table"></i> SQLite数据库表
                </h5>
            </div>
            <div class="card-body" id="sqlite-tables">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载表信息...</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-vector-square"></i> Qdrant向量集合
                </h5>
            </div>
            <div class="card-body" id="qdrant-collections">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载集合信息...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据查看器 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search"></i> 数据查看器
                </h5>
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="data-type" id="sqlite-radio" checked>
                    <label class="btn btn-outline-primary" for="sqlite-radio">SQLite表</label>
                    
                    <input type="radio" class="btn-check" name="data-type" id="qdrant-radio">
                    <label class="btn btn-outline-primary" for="qdrant-radio">Qdrant集合</label>
                </div>
            </div>
            <div class="card-body">
                <!-- SQLite查看器 -->
                <div id="sqlite-viewer">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="table-select" class="form-label">选择表</label>
                            <select class="form-select" id="table-select">
                                <option value="">请选择表...</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="limit-select" class="form-label">显示条数</label>
                            <select class="form-select" id="limit-select">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-primary" id="query-btn">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary" id="prev-btn" disabled>
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </button>
                                <button class="btn btn-outline-secondary" id="next-btn" disabled>
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Qdrant查看器 -->
                <div id="qdrant-viewer" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="collection-select" class="form-label">选择集合</label>
                            <select class="form-select" id="collection-select">
                                <option value="">请选择集合...</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="vector-limit-select" class="form-label">显示条数</label>
                            <select class="form-select" id="vector-limit-select">
                                <option value="3">3条</option>
                                <option value="5" selected>5条</option>
                                <option value="10">10条</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-primary" id="vector-query-btn">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 结果显示区域 -->
                <div id="results-container">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>请选择表或集合进行查看</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表结构模态框 -->
<div class="modal fade" id="schemaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">表结构详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="schema-content">
                <!-- 表结构内容 -->
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.table-schema {
    font-size: 0.9em;
}

.json-viewer {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.json-key {
    color: #0066cc;
    font-weight: bold;
}

.json-string {
    color: #009900;
}

.json-number {
    color: #cc6600;
}

.json-boolean {
    color: #990099;
}

.json-null {
    color: #999999;
}

.data-cell {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-cell.expandable {
    cursor: pointer;
}

.data-cell.expandable:hover {
    background-color: #f8f9fa;
}

.card-stats {
    transition: transform 0.2s;
}

.card-stats:hover {
    transform: translateY(-2px);
}

.collection-info {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
    padding: 1rem;
    margin-bottom: 1rem;
}

.vector-payload {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentOffset = 0;
let currentLimit = 10;
let currentTable = '';
let currentTotal = 0;

// 页面加载时获取数据库信息
document.addEventListener('DOMContentLoaded', function() {
    loadDatabaseInfo();
    
    // 绑定事件
    document.getElementById('query-btn').addEventListener('click', queryTable);
    document.getElementById('vector-query-btn').addEventListener('click', queryCollection);
    document.getElementById('prev-btn').addEventListener('click', prevPage);
    document.getElementById('next-btn').addEventListener('click', nextPage);
    
    // 数据类型切换
    document.querySelectorAll('input[name="data-type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.id === 'sqlite-radio') {
                document.getElementById('sqlite-viewer').style.display = 'block';
                document.getElementById('qdrant-viewer').style.display = 'none';
            } else {
                document.getElementById('sqlite-viewer').style.display = 'none';
                document.getElementById('qdrant-viewer').style.display = 'block';
            }
            clearResults();
        });
    });
});

function loadDatabaseInfo() {
    fetch('/api/database/tables')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySQLiteTables(data.sqlite_tables);
                displayQdrantCollections(data.qdrant_collections);
                populateSelectors(data.sqlite_tables, data.qdrant_collections);
            } else {
                showError('获取数据库信息失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('请求失败: ' + error.message);
        });
}

function displaySQLiteTables(tables) {
    const container = document.getElementById('sqlite-tables');
    
    if (Object.keys(tables).length === 0) {
        container.innerHTML = '<p class="text-muted">无可用表</p>';
        return;
    }
    
    let html = '';
    for (const [tableName, info] of Object.entries(tables)) {
        const iconMap = {
            'task_executions': 'fas fa-tasks',
            'task_steps': 'fas fa-list-ol',
            'plan_improvements': 'fas fa-lightbulb',
            'knowledge_entries': 'fas fa-brain'
        };
        
        const icon = iconMap[tableName] || 'fas fa-table';
        const description = getTableDescription(tableName);
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <i class="${icon}"></i>
                    <strong class="ms-2">${tableName}</strong>
                    <br>
                    <small class="text-muted ms-3">${description}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary">${info.count}</span>
                    <br>
                    <button class="btn btn-sm btn-outline-info mt-1" onclick="showSchema('${tableName}', ${JSON.stringify(info.columns).replace(/"/g, '&quot;')})">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

function displayQdrantCollections(collections) {
    const container = document.getElementById('qdrant-collections');
    
    if (collections.error) {
        container.innerHTML = `<div class="alert alert-warning">${collections.error}</div>`;
        return;
    }
    
    if (Object.keys(collections).length === 0) {
        container.innerHTML = '<p class="text-muted">无可用集合</p>';
        return;
    }
    
    let html = '';
    for (const [collectionName, info] of Object.entries(collections)) {
        const iconMap = {
            'instructions': 'fas fa-terminal',
            'tool_results': 'fas fa-cogs',
            'error_patterns': 'fas fa-exclamation-triangle',
            'knowledge_base': 'fas fa-brain',
            'improvements': 'fas fa-arrow-up'
        };
        
        const icon = iconMap[collectionName] || 'fas fa-vector-square';
        const description = getCollectionDescription(collectionName);
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <i class="${icon}"></i>
                    <strong class="ms-2">${collectionName}</strong>
                    <br>
                    <small class="text-muted ms-3">${description}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-success">${info.points_count}</span>
                    <br>
                    <small class="text-muted">${info.vector_dimension}维</small>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

function populateSelectors(tables, collections) {
    const tableSelect = document.getElementById('table-select');
    const collectionSelect = document.getElementById('collection-select');
    
    // 填充表选择器
    tableSelect.innerHTML = '<option value="">请选择表...</option>';
    for (const tableName of Object.keys(tables)) {
        const option = document.createElement('option');
        option.value = tableName;
        option.textContent = tableName + ` (${tables[tableName].count}条)`;
        tableSelect.appendChild(option);
    }
    
    // 填充集合选择器
    collectionSelect.innerHTML = '<option value="">请选择集合...</option>';
    if (!collections.error) {
        for (const [collectionName, info] of Object.entries(collections)) {
            const option = document.createElement('option');
            option.value = collectionName;
            option.textContent = collectionName + ` (${info.points_count}个向量)`;
            collectionSelect.appendChild(option);
        }
    }
}

function queryTable() {
    const tableName = document.getElementById('table-select').value;
    const limit = document.getElementById('limit-select').value;
    
    if (!tableName) {
        alert('请选择要查询的表');
        return;
    }
    
    currentTable = tableName;
    currentLimit = parseInt(limit);
    currentOffset = 0;
    
    performQuery();
}

function performQuery() {
    const resultsContainer = document.getElementById('results-container');
    resultsContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">查询中...</span>
            </div>
            <p class="mt-2">正在查询数据...</p>
        </div>
    `;
    
    fetch('/api/database/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            table: currentTable,
            limit: currentLimit,
            offset: currentOffset
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayQueryResults(data);
            updatePagination(data);
        } else {
            showError('查询失败: ' + data.error);
        }
    })
    .catch(error => {
        showError('请求失败: ' + error.message);
    });
}

function queryCollection() {
    const collectionName = document.getElementById('collection-select').value;
    const limit = document.getElementById('vector-limit-select').value;
    
    if (!collectionName) {
        alert('请选择要查询的集合');
        return;
    }
    
    const resultsContainer = document.getElementById('results-container');
    resultsContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">查询中...</span>
            </div>
            <p class="mt-2">正在查询向量数据...</p>
        </div>
    `;
    
    fetch(`/api/database/qdrant/${collectionName}?limit=${limit}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayVectorResults(data);
            } else {
                showError('查询失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('请求失败: ' + error.message);
        });
}

function displayQueryResults(data) {
    const container = document.getElementById('results-container');
    
    if (data.data.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-2x mb-3"></i>
                <p>该表暂无数据</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="mb-3">
            <h6>表: ${data.table} (共 ${data.total} 条记录)</h6>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
    `;
    
    // 表头
    data.columns.forEach(column => {
        html += `<th>${column}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 表数据
    data.data.forEach(row => {
        html += '<tr>';
        data.columns.forEach(column => {
            const value = row[column];
            let displayValue = '';
            
            if (value === null || value === undefined) {
                displayValue = '<span class="text-muted">NULL</span>';
            } else if (typeof value === 'object') {
                displayValue = `<span class="data-cell expandable" onclick="showJSON('${column}', ${JSON.stringify(value).replace(/"/g, '&quot;')})">
                    <i class="fas fa-file-code"></i> JSON对象
                </span>`;
            } else if (typeof value === 'string' && value.length > 50) {
                displayValue = `<span class="data-cell" title="${escapeHtml(value)}">${escapeHtml(value.substring(0, 50))}...</span>`;
            } else {
                displayValue = `<span class="data-cell">${escapeHtml(String(value))}</span>`;
            }
            
            html += `<td>${displayValue}</td>`;
        });
        html += '</tr>';
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

function displayVectorResults(data) {
    const container = document.getElementById('results-container');
    
    if (data.points.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-2x mb-3"></i>
                <p>该集合暂无向量数据</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="mb-3">
            <h6>集合: ${data.collection} (显示 ${data.count} 个向量点)</h6>
        </div>
    `;
    
    data.points.forEach((point, index) => {
        html += `
            <div class="vector-payload">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">
                        <i class="fas fa-dot-circle"></i> 向量点 #${index + 1}
                    </h6>
                    <code>${point.id}</code>
                </div>
                <div class="json-viewer">
                    <pre>${JSON.stringify(point.payload, null, 2)}</pre>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function updatePagination(data) {
    currentTotal = data.total;
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    
    prevBtn.disabled = currentOffset === 0;
    nextBtn.disabled = currentOffset + currentLimit >= currentTotal;
}

function prevPage() {
    if (currentOffset > 0) {
        currentOffset = Math.max(0, currentOffset - currentLimit);
        performQuery();
    }
}

function nextPage() {
    if (currentOffset + currentLimit < currentTotal) {
        currentOffset += currentLimit;
        performQuery();
    }
}

function showSchema(tableName, columns) {
    const modal = new bootstrap.Modal(document.getElementById('schemaModal'));
    const modalTitle = document.querySelector('#schemaModal .modal-title');
    const schemaContent = document.getElementById('schema-content');
    
    modalTitle.textContent = `表结构: ${tableName}`;
    
    let html = `
        <div class="table-responsive">
            <table class="table table-schema">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>允许NULL</th>
                        <th>主键</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    columns.forEach(column => {
        html += `
            <tr>
                <td><strong>${column.name}</strong></td>
                <td><code>${column.type}</code></td>
                <td>${column.notnull ? '<span class="text-danger">否</span>' : '<span class="text-success">是</span>'}</td>
                <td>${column.pk ? '<i class="fas fa-key text-warning"></i>' : ''}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    html += `<div class="mt-3"><h6>表说明:</h6><p>${getTableDescription(tableName)}</p></div>`;
    
    schemaContent.innerHTML = html;
    modal.show();
}

function showJSON(fieldName, jsonData) {
    const modal = new bootstrap.Modal(document.getElementById('schemaModal'));
    const modalTitle = document.querySelector('#schemaModal .modal-title');
    const schemaContent = document.getElementById('schema-content');
    
    modalTitle.textContent = `JSON字段: ${fieldName}`;
    schemaContent.innerHTML = `
        <div class="json-viewer">
            <pre>${JSON.stringify(jsonData, null, 2)}</pre>
        </div>
    `;
    
    modal.show();
}

function clearResults() {
    document.getElementById('results-container').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-info-circle fa-2x mb-3"></i>
            <p>请选择表或集合进行查看</p>
        </div>
    `;
}

function showError(message) {
    document.getElementById('results-container').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>错误:</strong> ${message}
        </div>
    `;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function getTableDescription(tableName) {
    const descriptions = {
        'task_executions': '任务执行主记录，存储每次测试任务的基本信息、执行状态和统计数据',
        'task_steps': '任务步骤详细记录，存储每个工具调用的参数、结果和执行状态',
        'plan_improvements': '计划改进记录，存储对执行计划的优化建议和评估结果',
        'knowledge_entries': '知识库条目，存储从历史执行中提取的最佳实践和解决方案'
    };
    return descriptions[tableName] || '数据库表';
}

function getCollectionDescription(collectionName) {
    const descriptions = {
        'instructions': '用户指令向量，存储原始自然语言指令的语义向量',
        'tool_results': '工具执行结果向量，存储工具调用结果的语义向量',
        'error_patterns': '错误模式向量，存储错误信息的语义向量用于相似错误匹配',
        'knowledge_base': '知识库向量，存储知识条目的语义向量',
        'improvements': '改进建议向量，存储优化建议的语义向量'
    };
    return descriptions[collectionName] || '向量集合';
}
</script>
{% endblock %}