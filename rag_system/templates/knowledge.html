{% extends "base.html" %}

{% block title %}知识库 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-book text-primary"></i> 知识库
            <small class="text-muted">结构化知识条目管理</small>
        </h1>
        <p class="lead">浏览和管理从历史执行中提取的结构化知识条目。</p>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database"></i> 知识条目
                </h5>
            </div>
            <div class="card-body">
                {% if knowledge_entries %}
                <div class="row">
                    {% for entry in knowledge_entries %}
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">{{ entry.title }}</h6>
                                <span class="badge bg-primary">{{ entry.knowledge_type }}</span>
                            </div>
                            <div class="card-body">
                                <p class="card-text">{{ entry.description }}</p>
                                <div class="row text-center mt-3">
                                    <div class="col-6">
                                        <h6 class="text-primary">{{ entry.usage_count }}</h6>
                                        <small class="text-muted">使用次数</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-success">{{ "%.1f"|format(entry.success_rate * 100) }}%</h6>
                                        <small class="text-muted">成功率</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        {% if entry.platform %}
                                        <i class="{{ entry.platform | platform_icon }}"></i>
                                        {{ entry.platform.upper() }}
                                        {% else %}
                                        通用
                                        {% endif %}
                                    </small>
                                    <small class="text-muted">{{ entry.created_at | datetime }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-book-open fa-3x mb-3"></i>
                    <p>暂无知识条目</p>
                    <p class="small">知识条目将从执行记录中自动提取生成</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}