{% extends "base.html" %}

{% block title %}Judge报告 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-gavel text-primary"></i> Judge报告
            <small class="text-muted">AI评价报告管理</small>
        </h1>
        <p class="lead">查看和管理AI对任务执行结果的评价报告，支持人工修正和批量解析。</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
                <button class="btn btn-success" onclick="showBatchParseModal()">
                    <i class="fas fa-sync"></i> 批量解析
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>总报告数</h6>
                        <h4>{{ pagination.total }}</h4>
                    </div>
                    <i class="fas fa-file-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>AI判断成功</h6>
                        <h4>{{ (reports|selectattr('overall_success')|list)|length }}</h4>
                    </div>
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>人工修改</h6>
                        <h4>{{ (reports|selectattr('human_modified')|list)|length }}</h4>
                    </div>
                    <i class="fas fa-user-edit fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>平均评分</h6>
                        <h4>{{ "%.2f"|format((reports|sum(attribute='success_score')/reports|length) if reports else 0) }}</h4>
                    </div>
                    <i class="fas fa-star fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 报告列表 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> Judge报告列表
                </h5>
                <span class="badge bg-primary">共 {{ pagination.total }} 条记录</span>
            </div>
            <div class="card-body">
                {% if reports %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>轮次ID</th>
                                <th>评价时间</th>
                                <th>AI判断</th>
                                <th>成功评分</th>
                                <th>置信度</th>
                                <th>人工修改</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                            <tr>
                                <td>
                                    <code>{{ report.round_id }}</code>
                                </td>
                                <td>{{ report.judge_timestamp|datetime }}</td>
                                <td>
                                    {% if report.overall_success %}
                                        <span class="badge bg-success">成功</span>
                                    {% else %}
                                        <span class="badge bg-danger">失败</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ "%.2f"|format(report.success_score) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ "%.2f"|format(report.confidence_score) }}
                                    </span>
                                </td>
                                <td>
                                    {% if report.human_modified %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-user-edit"></i> 已修改
                                        </span>
                                        {% if report.human_override_success is not none %}
                                            {% if report.human_override_success %}
                                                <span class="badge bg-success">人工: 成功</span>
                                            {% else %}
                                                <span class="badge bg-danger">人工: 失败</span>
                                            {% endif %}
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('judge_report_detail', report_id=report.report_id) }}" 
                                           class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('judge_report_edit', report_id=report.report_id) }}" 
                                           class="btn btn-outline-warning btn-sm" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteReport('{{ report.report_id }}', '{{ report.round_id }}')" 
                                                title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="d-flex justify-content-center mt-4">
                    {{ pagination.links }}
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-gavel fa-3x mb-3"></i>
                    <p>暂无Judge报告</p>
                    <button class="btn btn-primary" onclick="showBatchParseModal()">
                        <i class="fas fa-sync"></i> 批量解析报告
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

    <!-- 批量解析模态框 -->
    <div class="modal fade" id="batchParseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量解析Judge报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchParseForm">
                        <div class="mb-3">
                            <label for="logDir" class="form-label">日志目录</label>
                            <input type="text" class="form-control" id="logDir" value="log" required>
                            <div class="form-text">包含round_xxx文件夹的日志根目录</div>
                        </div>
                        <div class="mb-3">
                            <label for="parseLimit" class="form-label">解析数量限制</label>
                            <input type="number" class="form-control" id="parseLimit" value="10" min="1" max="100">
                            <div class="form-text">最多解析多少个最新的轮次</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeBatchParse()">
                        <i class="fas fa-sync"></i> 开始解析
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功/错误提示模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_css %}
<style>
    .success-badge { background-color: #28a745; }
    .failure-badge { background-color: #dc3545; }
    .modified-badge { background-color: #ffc107; color: #000; }
    .score-badge { font-weight: bold; }
</style>
{% endblock %}

{% block extra_js %}
<script>
        function showBatchParseModal() {
            new bootstrap.Modal(document.getElementById('batchParseModal')).show();
        }

        function showMessage(title, message, isError = false) {
            document.getElementById('messageModalTitle').textContent = title;
            document.getElementById('messageModalBody').innerHTML = message;
            const modal = document.getElementById('messageModal');
            modal.className = 'modal fade';
            if (isError) {
                modal.className += ' text-danger';
            }
            new bootstrap.Modal(modal).show();
        }

        function executeBatchParse() {
            const logDir = document.getElementById('logDir').value;
            const limit = parseInt(document.getElementById('parseLimit').value);
            
            if (!logDir.trim()) {
                showMessage('错误', '请输入日志目录路径', true);
                return;
            }
            
            // 隐藏模态框
            bootstrap.Modal.getInstance(document.getElementById('batchParseModal')).hide();
            
            // 显示加载状态
            showMessage('处理中', '<div class="text-center"><div class="spinner-border" role="status"></div><br/>正在批量解析Judge报告...</div>');
            
            fetch('/api/batch_parse_judge_reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    log_dir: logDir,
                    limit: limit
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const result = data.result;
                    const message = `
                        <h6>解析完成!</h6>
                        <ul class="list-unstyled">
                            <li><strong>总处理数:</strong> ${result.total_processed}</li>
                            <li><strong>成功:</strong> ${result.successful}</li>
                            <li><strong>失败:</strong> ${result.failed}</li>
                            <li><strong>跳过:</strong> ${result.skipped}</li>
                            <li><strong>成功率:</strong> ${(result.success_rate * 100).toFixed(1)}%</li>
                        </ul>
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">刷新页面</button>
                    `;
                    showMessage('批量解析结果', message);
                } else {
                    showMessage('错误', data.error || '批量解析失败', true);
                }
            })
            .catch(error => {
                showMessage('错误', '网络请求失败: ' + error.message, true);
            });
        }

        function deleteReport(reportId, roundId) {
            if (!confirm(`确定要删除轮次 ${roundId} 的Judge报告吗？\n\n此操作不可撤销！`)) {
                return;
            }
            
            fetch(`/api/judge_report/${reportId}/delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('成功', data.message);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('错误', data.error || '删除失败', true);
                }
            })
            .catch(error => {
                showMessage('错误', '网络请求失败: ' + error.message, true);
            });
        }
</script>
{% endblock %}