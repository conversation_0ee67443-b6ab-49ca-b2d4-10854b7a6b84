{% extends "base.html" %}

{% block title %}指令映射 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-exchange-alt text-primary"></i> 指令映射
            <small class="text-muted">原始指令到结构化指令的转换映射</small>
        </h1>
        <p class="lead">管理和浏览系统自动生成的指令映射，支持搜索和质量评估。</p>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>总映射数</h6>
                        <h4>{{ pagination.total }}</h4>
                    </div>
                    <i class="fas fa-exchange-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>iOS平台</h6>
                        <h4>{{ (mappings|selectattr('platform', 'equalto', 'ios')|list)|length }}</h4>
                    </div>
                    <i class="fab fa-apple fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>Android平台</h6>
                        <h4>{{ (mappings|selectattr('platform', 'equalto', 'android')|list)|length }}</h4>
                    </div>
                    <i class="fab fa-android fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white card-stats">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>平均质量评分</h6>
                        <h4>{{ "%.2f"|format((mappings|sum(attribute='conversion_quality_score')/mappings|length) if mappings else 0) }}</h4>
                    </div>
                    <i class="fas fa-star fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索区域 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search"></i> 指令映射搜索
                </h5>
            </div>
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-6">
                        <label for="searchQuery" class="form-label">搜索内容</label>
                        <input type="text" class="form-control" id="searchQuery" 
                               placeholder="输入原始指令或结构化指令内容...">
                    </div>
                    <div class="col-md-3">
                        <label for="platformFilter" class="form-label">平台筛选</label>
                        <select class="form-select" id="platformFilter">
                            <option value="">所有平台</option>
                            <option value="ios">iOS</option>
                            <option value="android">Android</option>
                            <option value="unknown">未知</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="topK" class="form-label">返回数量</label>
                        <select class="form-select" id="topK">
                            <option value="5">5个</option>
                            <option value="10" selected>10个</option>
                            <option value="20">20个</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索映射
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearSearch()">
                            <i class="fas fa-times"></i> 清空
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 搜索结果区域 -->
<div class="row" id="searchResults" style="display: none;">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search-plus"></i> 搜索结果
                </h5>
            </div>
            <div class="card-body" id="searchResultsContent">
                <!-- 搜索结果将在这里显示 -->
            </div>
        </div>
    </div>
</div>

<!-- 指令映射列表 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 指令映射列表
                </h5>
                <span class="badge bg-primary">共 {{ pagination.total }} 条记录</span>
            </div>
            <div class="card-body">
                {% if mappings %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 25%;">原始指令</th>
                                <th style="width: 25%;">结构化计划</th>
                                <th>转换质量</th>
                                <th>使用次数</th>
                                <th>平台</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mapping in mappings %}
                            <tr>
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;" title="{{ mapping.original_instruction }}">
                                        {{ mapping.original_instruction }}
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;" title="{{ mapping.structured_instruction }}">
                                        <code>{{ mapping.structured_instruction }}</code>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if mapping.conversion_quality_score > 0.8 else 'warning' if mapping.conversion_quality_score > 0.5 else 'danger' }}">
                                        {{ "%.2f"|format(mapping.conversion_quality_score) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ mapping.usage_count }}</span>
                                </td>
                                <td>
                                    <i class="{{ mapping.platform | platform_icon }} me-1"></i>
                                    {{ mapping.platform.upper() }}
                                </td>
                                <td>
                                    <span title="{{ mapping.created_at }}">
                                        {{ mapping.created_at|datetime }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="showMappingDetail('{{ mapping.mapping_id }}', {{ mapping|tojson_unicode|e }})"
                                                title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteMapping('{{ mapping.mapping_id }}')"
                                                title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="d-flex justify-content-center mt-4">
                    {{ pagination.links }}
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                    <p>暂无指令映射</p>
                    <p class="small">指令映射将从执行记录中自动生成</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

    <!-- 映射详情模态框 -->
    <div class="modal fade" id="mappingDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">指令映射详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="mappingDetailContent">
                    <!-- 详情内容将在这里显示 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功/错误提示模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_css %}
<style>
    .score-badge { font-weight: bold; }
    .instruction-preview {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .platform-badge {
        text-transform: uppercase;
        font-size: 0.75em;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
        function showMessage(title, message, isError = false) {
            document.getElementById('messageModalTitle').textContent = title;
            document.getElementById('messageModalBody').innerHTML = message;
            const modal = document.getElementById('messageModal');
            modal.className = 'modal fade';
            if (isError) {
                modal.className += ' text-danger';
            }
            new bootstrap.Modal(modal).show();
        }

        function showMappingDetail(mappingId, mappingData) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><th>映射ID</th><td><code>${mappingData.mapping_id}</code></td></tr>
                            <tr><th>转换质量</th><td><span class="badge bg-primary">${mappingData.conversion_quality_score.toFixed(2)}</span></td></tr>
                            <tr><th>使用次数</th><td><span class="badge bg-info">${mappingData.usage_count}</span></td></tr>
                            <tr><th>平台</th><td><span class="badge bg-secondary">${mappingData.platform}</span></td></tr>
                            <tr><th>创建时间</th><td>${new Date(mappingData.created_at).toLocaleString()}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>原始指令</h6>
                    <div class="bg-light p-3 rounded border">
                        ${mappingData.original_instruction}
                    </div>
                </div>
                <div class="mt-3">
                    <h6>结构化指令</h6>
                    <div class="bg-dark text-light p-3 rounded">
                        <pre style="color: white; margin: 0;">${mappingData.structured_instruction}</pre>
                    </div>
                </div>
            `;
            document.getElementById('mappingDetailContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('mappingDetailModal')).show();
        }

        function deleteMapping(mappingId) {
            if (!confirm('确定要删除此指令映射吗？\n\n此操作不可撤销！')) {
                return;
            }
            
            fetch(`/api/instruction_mapping/${mappingId}/delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('成功', data.message);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('错误', data.error || '删除失败', true);
                }
            })
            .catch(error => {
                showMessage('错误', '网络请求失败: ' + error.message, true);
            });
        }

        function clearSearch() {
            document.getElementById('searchQuery').value = '';
            document.getElementById('platformFilter').value = '';
            document.getElementById('topK').value = '10';
            document.getElementById('searchResults').style.display = 'none';
        }

        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const query = document.getElementById('searchQuery').value.trim();
            const platform = document.getElementById('platformFilter').value;
            const topK = document.getElementById('topK').value;
            
            if (!query) {
                showMessage('错误', '请输入搜索内容', true);
                return;
            }
            
            fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    type: 'instruction_mappings',
                    platform: platform,
                    top_k: parseInt(topK)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data.results);
                } else {
                    showMessage('错误', data.error || '搜索失败', true);
                }
            })
            .catch(error => {
                showMessage('错误', '网络请求失败: ' + error.message, true);
            });
        });

        function displaySearchResults(results) {
            const container = document.getElementById('searchResultsContent');
            
            if (results.length === 0) {
                container.innerHTML = '<p class="text-muted">未找到相关的指令映射</p>';
            } else {
                let html = '<div class="table-responsive"><table class="table table-sm table-striped">';
                html += '<thead><tr><th>相似度</th><th>原始指令</th><th>结构化指令</th><th>质量评分</th><th>平台</th></tr></thead>';
                html += '<tbody>';
                
                results.forEach(result => {
                    html += `<tr>
                        <td><span class="badge bg-primary">${result.score.toFixed(3)}</span></td>
                        <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${result.payload.original_instruction}</td>
                        <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;"><code>${result.payload.structured_instruction}</code></td>
                        <td><span class="badge bg-success">${result.payload.conversion_quality_score.toFixed(2)}</span></td>
                        <td><span class="badge bg-secondary">${result.payload.platform}</span></td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
            }
            
            document.getElementById('searchResults').style.display = 'block';
            document.getElementById('searchResults').scrollIntoView({ behavior: 'smooth' });
        }
</script>
{% endblock %}