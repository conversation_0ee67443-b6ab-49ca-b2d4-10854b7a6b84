{% extends "base.html" %}

{% block title %}执行详情 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">
                    <i class="fas fa-file-alt text-primary"></i> 执行详情
                    <span class="badge bg-{{ execution.execution_status.value | status_badge }} ms-2">
                        {{ execution.execution_status.value | title }}
                    </span>
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('executions') }}">执行记录</a></li>
                        <li class="breadcrumb-item active">{{ execution.round_id }}</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="{{ url_for('executions') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 基本信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 基本信息
                </h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">执行ID:</dt>
                    <dd class="col-sm-9">
                        <code>{{ execution.execution_id }}</code>
                        <button class="btn btn-sm btn-outline-secondary ms-2" 
                                onclick="copyToClipboard('{{ execution.execution_id }}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </dd>
                    
                    <dt class="col-sm-3">轮次信息:</dt>
                    <dd class="col-sm-9">
                        <span class="badge bg-secondary">#{{ execution.round_number }}</span>
                        <span class="text-muted ms-2">{{ execution.round_id }}</span>
                    </dd>
                    
                    <dt class="col-sm-3">任务类型:</dt>
                    <dd class="col-sm-9">
                        <span class="badge bg-info">{{ execution.task_type.value | title }}</span>
                    </dd>
                    
                    <dt class="col-sm-3">目标平台:</dt>
                    <dd class="col-sm-9">
                        <i class="{{ execution.platform.value | platform_icon }} me-1"></i>
                        {{ execution.platform.value.upper() }}
                    </dd>
                    
                    <dt class="col-sm-3">设备信息:</dt>
                    <dd class="col-sm-9">
                        {% if execution.device_name %}
                            <strong>{{ execution.device_name }}</strong><br>
                            <small class="text-muted">UDID: {{ execution.device_udid or '未知' }}</small>
                        {% else %}
                            <span class="text-muted">无设备信息</span>
                        {% endif %}
                    </dd>
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> 执行统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary">{{ execution.total_steps }}</h4>
                        <small class="text-muted">总步骤</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-success">{{ execution.successful_steps }}</h4>
                        <small class="text-muted">成功</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-danger">{{ execution.failed_steps }}</h4>
                        <small class="text-muted">失败</small>
                    </div>
                </div>
                
                {% if execution.total_duration %}
                <hr>
                <div class="text-center">
                    <h5 class="text-info">{{ "%.1f"|format(execution.total_duration) }}s</h5>
                    <small class="text-muted">总耗时</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 原始指令 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal"></i> 原始指令
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded">{{ execution.original_instruction }}</pre>
            </div>
        </div>
    </div>
</div>

<!-- 结构化计划 -->
{% if execution.structured_plan %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sitemap"></i> 结构化计划
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">{{ execution.structured_plan | tojson_unicode }}</pre>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 执行步骤 -->
{% if steps %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-ol"></i> 执行步骤 ({{ steps | length }} 步)
                </h5>
            </div>
            <div class="card-body">
                {% for step in steps %}
                <div class="border rounded p-3 mb-3 {% if step.is_successful %}border-success{% else %}border-danger{% endif %}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">
                            <span class="badge bg-primary me-2">步骤 {{ step.step_number }}</span>
                            {{ step.tool_name }}
                        </h6>
                        <div>
                            <span class="badge bg-{{ 'success' if step.is_successful else 'danger' }}">
                                {{ step.status }}
                            </span>
                            {% if step.duration %}
                            <span class="badge bg-info ms-1">{{ "%.2f"|format(step.duration) }}s</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if step.tool_args %}
                    <details class="mb-2">
                        <summary class="text-muted">工具参数</summary>
                        <pre class="bg-light p-2 mt-2 small">{{ step.tool_args | tojson_unicode }}</pre>
                    </details>
                    {% endif %}
                    
                    {% if step.text_result %}
                    <div class="mb-2">
                        <strong>结果:</strong>
                        <p class="text-muted mb-0">{{ step.text_result }}</p>
                    </div>
                    {% endif %}
                    
                    {% if step.error_message %}
                    <div class="alert alert-danger mb-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>错误:</strong> {{ step.error_message }}
                    </div>
                    {% endif %}
                    
                    {% if step.image_url %}
                    <div class="mb-2">
                        <strong>相关图片:</strong>
                        <a href="{{ step.image_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-image"></i> 查看图片
                        </a>
                    </div>
                    {% endif %}
                    
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> {{ step.executed_at | datetime }}
                    </small>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 最终结果 -->
{% if execution.final_result %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-flag-checkered"></i> 最终结果
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded">{{ execution.final_result }}</pre>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 1050;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check"></i> 已复制到剪贴板
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }).catch(function(err) {
        alert('复制失败: ' + err);
    });
}
</script>
{% endblock %}