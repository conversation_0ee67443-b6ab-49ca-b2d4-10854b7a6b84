{% extends "base.html" %}

{% block title %}系统概览 - RAG知识检索系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3">
            <i class="fas fa-brain text-primary"></i> RAG知识检索系统
            <small class="text-muted">智能知识管理与检索平台</small>
        </h1>
        <p class="lead">基于向量相似度的智能知识检索系统，帮助您从历史执行记录中快速找到相关经验和解决方案。</p>
    </div>
</div>

<!-- 系统统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-stats bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_executions or 0 }}</h4>
                        <p class="card-text">总执行数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_steps or 0 }}</h4>
                        <p class="card-text">总步骤数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-step-forward fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_vectors or 0 }}</h4>
                        <p class="card-text">向量总数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-vector-square fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_knowledge_entries or 0 }}</h4>
                        <p class="card-text">知识条目</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-lightbulb fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细统计 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-mobile-alt"></i> 平台分布
                </h5>
            </div>
            <div class="card-body">
                {% if stats.executions_by_platform %}
                    {% for platform, count in stats.executions_by_platform.items() %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <i class="{{ platform | platform_icon }}"></i>
                            <span class="ms-2">{{ platform.upper() }}</span>
                        </div>
                        <span class="badge bg-secondary">{{ count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> 执行状态分布
                </h5>
            </div>
            <div class="card-body">
                {% if stats.executions_by_status %}
                    {% for status, count in stats.executions_by_status.items() %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="badge bg-{{ status | status_badge }} me-2">
                                {{ status | title }}
                            </span>
                        </div>
                        <span class="badge bg-secondary">{{ count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database"></i> 向量数据库统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if stats.vector_collections %}
                        {% for collection, count in stats.vector_collections.items() %}
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ count }}</h4>
                                <small class="text-muted">{{ collection | replace('_', ' ') | title }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="col-md-12">
                            <p class="text-muted">暂无向量数据</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-rocket"></i> 快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('search') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-search"></i><br>
                            <small>智能搜索</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('executions') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-list"></i><br>
                            <small>浏览记录</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('extract') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-download"></i><br>
                            <small>数据提取</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button id="refresh-stats" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-sync-alt"></i><br>
                            <small>刷新统计</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.getElementById('refresh-stats').addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i><br><small>刷新中...</small>';
    this.disabled = true;
    
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // 刷新页面以显示最新数据
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('刷新失败: ' + error.message);
        })
        .finally(() => {
            this.innerHTML = '<i class="fas fa-sync-alt"></i><br><small>刷新统计</small>';
            this.disabled = false;
        });
});
</script>
{% endblock %}