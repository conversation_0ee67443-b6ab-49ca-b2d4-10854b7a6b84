#!/usr/bin/env python3
"""
RAG系统Web管理界面
提供数据库管理、向量搜索、知识库浏览等功能
"""

import sys
import os
import json
import socket
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
from flask_paginate import Pagination, get_page_parameter
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from rag_system.data_models import DatabaseConfig, ExecutionStatus, PlatformType, TaskType, InstructionMapping, JudgeReport
from rag_system.database_manager import DatabaseManager
from rag_system.knowledge_extractor import KnowledgeExtractor
from rag_system.judge_parser import create_judge_parser

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'rag_system_secret_key_2024'

# 全局变量
config = DatabaseConfig()
db_manager = None
knowledge_extractor = None

def find_free_port(host='0.0.0.0', start_port=5001, max_port=5100):
    """查找可用端口"""
    for port in range(start_port, max_port + 1):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind((host, port))
                return port
        except OSError:
            continue
    
    # 如果指定范围内没有可用端口，让系统自动分配
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind((host, 0))
        return s.getsockname()[1]

def init_rag_system():
    """初始化RAG系统"""
    global db_manager, knowledge_extractor
    
    try:
        logger.info("初始化RAG系统...")
        db_manager = DatabaseManager(config)
        knowledge_extractor = KnowledgeExtractor(db_manager)
        logger.info("RAG系统初始化成功")
        return True
    except Exception as e:
        logger.error(f"RAG系统初始化失败: {e}")
        return False

# =================== 路由定义 ===================

@app.route('/')
def index():
    """首页 - 系统概览"""
    try:
        stats = db_manager.get_database_stats()
        return render_template('index.html', stats=stats)
    except Exception as e:
        logger.error(f"获取首页数据失败: {e}")
        return render_template('error.html', error=str(e))

@app.route('/executions')
def executions():
    """任务执行列表"""
    try:
        page = request.args.get(get_page_parameter(), type=int, default=1)
        per_page = 20
        
        # 获取所有执行记录
        with db_manager.db_path.open() as _:  # 简单的连接检查
            pass
            
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取总数
            cursor.execute("SELECT COUNT(*) FROM task_executions")
            total = cursor.fetchone()[0]
            
            # 分页查询
            offset = (page - 1) * per_page
            cursor.execute("""
                SELECT execution_id, round_id, round_number, original_instruction, 
                       execution_status, platform, created_at, total_steps, 
                       successful_steps, failed_steps
                FROM task_executions 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            """, (per_page, offset))
            
            executions_data = []
            for row in cursor.fetchall():
                executions_data.append({
                    'execution_id': row[0],
                    'round_id': row[1],
                    'round_number': row[2],
                    'original_instruction': row[3][:100] + '...' if len(row[3]) > 100 else row[3],
                    'execution_status': row[4],
                    'platform': row[5],
                    'created_at': row[6],
                    'total_steps': row[7],
                    'successful_steps': row[8],
                    'failed_steps': row[9]
                })
        
        pagination = Pagination(page=page, total=total, per_page=per_page,
                              css_framework='bootstrap4')
        
        return render_template('executions.html', 
                             executions=executions_data, 
                             pagination=pagination)
    except Exception as e:
        logger.error(f"获取执行列表失败: {e}")
        return render_template('error.html', error=str(e))

@app.route('/execution/<execution_id>')
def execution_detail(execution_id):
    """任务执行详情"""
    try:
        execution = db_manager.get_task_execution(execution_id)
        if not execution:
            flash('执行记录不存在', 'error')
            return redirect(url_for('executions'))
        
        steps = db_manager.get_steps_by_execution(execution_id)
        
        return render_template('execution_detail.html', 
                             execution=execution, 
                             steps=steps)
    except Exception as e:
        logger.error(f"获取执行详情失败: {e}")
        return render_template('error.html', error=str(e))

@app.route('/search')
def search():
    """向量搜索页面"""
    return render_template('search.html')

@app.route('/api/search', methods=['POST'])
def api_search():
    """向量搜索API"""
    try:
        data = request.get_json()
        query_text = data.get('query', '').strip()
        search_type = data.get('type', 'instructions')
        top_k = int(data.get('top_k', 5))
        platform_filter = data.get('platform')
        
        if not query_text:
            return jsonify({'error': '搜索内容不能为空'}), 400
        
        # 根据搜索类型执行不同的搜索
        if search_type == 'instructions':
            platform = PlatformType(platform_filter) if platform_filter else None
            results = db_manager.search_similar_instructions(
                query_text=query_text,
                top_k=top_k,
                platform=platform
            )
        elif search_type == 'instruction_mappings':
            platform = PlatformType(platform_filter) if platform_filter else None
            results = db_manager.search_similar_instruction_mappings(
                query_text=query_text,
                top_k=top_k,
                platform=platform
            )
        elif search_type == 'judge_insights':
            results = db_manager.search_judge_insights(
                query_text=query_text,
                top_k=top_k
            )
        elif search_type == 'tool_results':
            tool_name = data.get('tool_name')
            results = db_manager.search_similar_tool_results(
                query_text=query_text,
                tool_name=tool_name,
                top_k=top_k
            )
        elif search_type == 'error_patterns':
            tool_name = data.get('tool_name')
            results = db_manager.search_error_patterns(
                error_text=query_text,
                tool_name=tool_name,
                top_k=top_k
            )
        else:
            return jsonify({'error': '不支持的搜索类型'}), 400
        
        return jsonify({
            'success': True,
            'results': results,
            'total': len(results)
        })
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/knowledge')
def knowledge():
    """知识库浏览页面"""
    try:
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT knowledge_id, title, description, knowledge_type, 
                       platform, usage_count, success_rate, created_at
                FROM knowledge_entries 
                ORDER BY created_at DESC 
                LIMIT 50
            """)
            
            knowledge_data = []
            for row in cursor.fetchall():
                knowledge_data.append({
                    'knowledge_id': row[0],
                    'title': row[1],
                    'description': row[2][:150] + '...' if len(row[2]) > 150 else row[2],
                    'knowledge_type': row[3],
                    'platform': row[4],
                    'usage_count': row[5],
                    'success_rate': row[6],
                    'created_at': row[7]
                })
        
        return render_template('knowledge.html', knowledge_entries=knowledge_data)
    except Exception as e:
        logger.error(f"获取知识库失败: {e}")
        return render_template('error.html', error=str(e))

@app.route('/extract')
def extract():
    """数据提取页面"""
    return render_template('extract.html')

@app.route('/api/extract', methods=['POST'])
def api_extract():
    """批量提取数据API"""
    try:
        data = request.get_json()
        limit = int(data.get('limit', 10))
        
        # 执行批量提取
        result = knowledge_extractor.batch_process_logs(limit=limit)
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        logger.error(f"数据提取失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def api_stats():
    """获取系统统计信息API"""
    try:
        stats = db_manager.get_database_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/test_embedding', methods=['POST'])
def api_test_embedding():
    """测试embedding服务API"""
    try:
        data = request.get_json()
        text = data.get('text', '测试文本')
        
        embedding = db_manager.embedding_service.get_embedding(text)
        
        if embedding:
            return jsonify({
                'success': True,
                'dimension': len(embedding),
                'sample': embedding[:5]  # 返回前5个值作为示例
            })
        else:
            return jsonify({'success': False, 'error': 'embedding服务不可用'})
            
    except Exception as e:
        logger.error(f"测试embedding失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/comprehensive_search', methods=['POST'])
def api_comprehensive_search():
    """综合搜索API - 支持多维度搜索"""
    try:
        data = request.get_json()
        query_text = data.get('query', '').strip()
        search_levels = data.get('levels', ['execution', 'step'])  # 搜索级别
        top_k = int(data.get('top_k', 5))
        platform_filter = data.get('platform')
        
        if not query_text:
            return jsonify({'error': '搜索内容不能为空'}), 400
        
        platform = PlatformType(platform_filter) if platform_filter else None
        
        # 执行综合搜索
        results = db_manager.comprehensive_search(
            query_text=query_text,
            search_levels=search_levels,
            top_k=top_k,
            platform=platform
        )
        
        return jsonify({
            'success': True,
            'results': results,
            'total': sum(len(level_results) for level_results in results.values())
        })
        
    except Exception as e:
        logger.error(f"综合搜索失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/database')
def database_viewer():
    """数据库原始数据查看页面"""
    return render_template('database.html')

@app.route('/api/database/tables')
def api_database_tables():
    """获取数据库表信息API"""
    try:
        import sqlite3
        tables_info = {}
        
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            table_names = [row[0] for row in cursor.fetchall()]
            
            for table_name in table_names:
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                # 获取记录数量
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                
                tables_info[table_name] = {
                    'columns': [{'name': col[1], 'type': col[2], 'notnull': col[3], 'pk': col[5]} for col in columns],
                    'count': count
                }
        
        # 获取Qdrant集合信息
        qdrant_info = {}
        try:
            collections = db_manager.qdrant_client.get_collections().collections
            for collection in collections:
                collection_info = db_manager.qdrant_client.get_collection(collection.name)
                qdrant_info[collection.name] = {
                    'points_count': collection_info.points_count,
                    'vector_dimension': collection_info.config.params.vectors.size
                }
        except Exception as e:
            logger.error(f"获取Qdrant信息失败: {e}")
            qdrant_info = {'error': str(e)}
        
        return jsonify({
            'success': True,
            'sqlite_tables': tables_info,
            'qdrant_collections': qdrant_info
        })
        
    except Exception as e:
        logger.error(f"获取数据库表信息失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/database/query', methods=['POST'])
def api_database_query():
    """执行数据库查询API"""
    try:
        data = request.get_json()
        table_name = data.get('table')
        limit = int(data.get('limit', 10))
        offset = int(data.get('offset', 0))
        
        if not table_name:
            return jsonify({'error': '表名不能为空'}), 400
        
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]
            
            # 查询数据
            cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT ? OFFSET ?", 
                          (limit, offset))
            rows = cursor.fetchall()
            
            # 获取总数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total = cursor.fetchone()[0]
            
            # 格式化数据
            data_rows = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    column_name = columns[i]
                    # 处理JSON字段
                    if column_name in ['structured_plan', 'metadata', 'tool_args', 'tool_result'] and value:
                        try:
                            row_dict[column_name] = json.loads(value)
                        except:
                            row_dict[column_name] = value
                    else:
                        row_dict[column_name] = value
                data_rows.append(row_dict)
            
            return jsonify({
                'success': True,
                'table': table_name,
                'columns': columns,
                'data': data_rows,
                'total': total,
                'limit': limit,
                'offset': offset
            })
            
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/database/qdrant/<collection_name>')
def api_qdrant_collection(collection_name):
    """获取Qdrant集合数据API"""
    try:
        limit = int(request.args.get('limit', 5))
        
        # 获取集合中的向量点
        points, next_page_offset = db_manager.qdrant_client.scroll(
            collection_name=collection_name,
            limit=limit,
            with_payload=True,
            with_vectors=False  # 不返回向量数据，只返回payload
        )
        
        # 格式化数据
        data_points = []
        for point in points:
            data_points.append({
                'id': point.id,
                'payload': point.payload
            })
        
        return jsonify({
            'success': True,
            'collection': collection_name,
            'points': data_points,
            'count': len(data_points),
            'has_more': next_page_offset is not None
        })
        
    except Exception as e:
        logger.error(f"获取Qdrant集合数据失败: {e}")
        return jsonify({'error': str(e)}), 500

# =================== Judge报告管理 ===================

@app.route('/judge_reports')
def judge_reports():
    """Judge报告列表页面"""
    try:
        page = request.args.get(get_page_parameter(), type=int, default=1)
        per_page = 20
        
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取总数
            cursor.execute("SELECT COUNT(*) FROM judge_reports")
            total = cursor.fetchone()[0]
            
            # 分页查询
            offset = (page - 1) * per_page
            cursor.execute("""
                SELECT report_id, execution_id, round_id, judge_timestamp,
                       overall_success, success_score, confidence_score,
                       human_modified, human_override_success, human_modifier,
                       created_at
                FROM judge_reports 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            """, (per_page, offset))
            
            reports_data = []
            for row in cursor.fetchall():
                reports_data.append({
                    'report_id': row[0],
                    'execution_id': row[1],
                    'round_id': row[2],
                    'judge_timestamp': row[3],
                    'overall_success': row[4],
                    'success_score': row[5],
                    'confidence_score': row[6],
                    'human_modified': row[7],
                    'human_override_success': row[8],
                    'human_modifier': row[9],
                    'created_at': row[10]
                })
        
        pagination = Pagination(page=page, total=total, per_page=per_page,
                              css_framework='bootstrap4')
        
        logger.info(f"返回Judge报告数据: {len(reports_data)} 条记录")
        return render_template('judge_reports.html', 
                             reports=reports_data, 
                             pagination=pagination)
    except Exception as e:
        import traceback
        logger.error(f"获取Judge报告列表失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return render_template('error.html', error=str(e))

@app.route('/judge_report/<report_id>')
def judge_report_detail(report_id):
    """Judge报告详情页面"""
    try:
        report = db_manager.get_judge_report_by_id(report_id)
        if not report:
            flash('Judge报告不存在', 'error')
            return redirect(url_for('judge_reports'))
        
        return render_template('judge_report_detail.html', report=report)
    except Exception as e:
        logger.error(f"获取Judge报告详情失败: {e}")
        return render_template('error.html', error=str(e))

@app.route('/judge_report/<report_id>/edit')
def judge_report_edit(report_id):
    """编辑Judge报告页面"""
    try:
        report = db_manager.get_judge_report_by_id(report_id)
        if not report:
            flash('Judge报告不存在', 'error')
            return redirect(url_for('judge_reports'))
        
        return render_template('judge_report_edit.html', report=report)
    except Exception as e:
        logger.error(f"获取Judge报告编辑页面失败: {e}")
        return render_template('error.html', error=str(e))

@app.route('/api/judge_report/<report_id>/update', methods=['POST'])
def api_update_judge_report(report_id):
    """更新Judge报告API"""
    try:
        data = request.get_json()
        
        # 获取修改数据
        human_override_success = data.get('human_override_success')
        human_override_score = data.get('human_override_score')
        human_notes = data.get('human_notes', '')
        human_modifier = data.get('human_modifier', 'web_user')
        
        # 验证数据
        if human_override_success not in [True, False, None]:
            return jsonify({'error': '成功状态修正值无效'}), 400
        
        if human_override_score is not None:
            try:
                human_override_score = float(human_override_score)
                if not (0.0 <= human_override_score <= 1.0):
                    return jsonify({'error': '评分修正值必须在0.0-1.0之间'}), 400
            except (ValueError, TypeError):
                return jsonify({'error': '评分修正值格式无效'}), 400
        
        # 执行更新
        success = db_manager.update_judge_report_human_override(
            report_id=report_id,
            human_override_success=human_override_success,
            human_override_score=human_override_score,
            human_notes=human_notes,
            human_modifier=human_modifier
        )
        
        if success:
            return jsonify({'success': True, 'message': 'Judge报告更新成功'})
        else:
            return jsonify({'error': '更新失败，报告不存在或数据库错误'}), 400
            
    except Exception as e:
        logger.error(f"更新Judge报告失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/judge_report/<report_id>/delete', methods=['DELETE'])
def api_delete_judge_report(report_id):
    """删除Judge报告API"""
    try:
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM judge_reports WHERE report_id = ?", (report_id,))
            
            if cursor.rowcount > 0:
                conn.commit()
                return jsonify({'success': True, 'message': 'Judge报告删除成功'})
            else:
                return jsonify({'error': '报告不存在或已被删除'}), 404
                
    except Exception as e:
        logger.error(f"删除Judge报告失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/batch_parse_judge_reports', methods=['POST'])
def api_batch_parse_judge_reports():
    """批量解析Judge报告API"""
    try:
        data = request.get_json()
        limit = data.get('limit', 10)
        log_dir = Path(data.get('log_dir', 'log'))
        
        if not log_dir.exists():
            return jsonify({'error': f'日志目录不存在: {log_dir}'}), 400
        
        # 创建解析器并执行批量处理
        parser = create_judge_parser(db_manager)
        result = parser.process_round_folders_batch(log_dir, limit=limit)
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        logger.error(f"批量解析Judge报告失败: {e}")
        return jsonify({'error': str(e)}), 500

# =================== 指令映射管理 ===================

@app.route('/instruction_mappings')
def instruction_mappings():
    """指令映射列表页面"""
    try:
        page = request.args.get(get_page_parameter(), type=int, default=1)
        per_page = 20
        
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取总数
            cursor.execute("SELECT COUNT(*) FROM instruction_mappings")
            total = cursor.fetchone()[0]
            
            # 分页查询
            offset = (page - 1) * per_page
            cursor.execute("""
                SELECT mapping_id, original_instruction, structured_plan,
                       conversion_quality_score, reuse_count, platform, created_at
                FROM instruction_mappings 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            """, (per_page, offset))
            
            mappings_data = []
            for row in cursor.fetchall():
                mappings_data.append({
                    'mapping_id': row[0],
                    'original_instruction': row[1][:100] + '...' if len(row[1]) > 100 else row[1],
                    'structured_instruction': str(row[2])[:100] + '...' if len(str(row[2])) > 100 else str(row[2]),
                    'conversion_quality_score': row[3],
                    'usage_count': row[4],
                    'platform': row[5],
                    'created_at': row[6]
                })
        
        pagination = Pagination(page=page, total=total, per_page=per_page,
                              css_framework='bootstrap4')
        
        logger.info(f"返回指令映射数据: {len(mappings_data)} 条记录")
        return render_template('instruction_mappings.html', 
                             mappings=mappings_data, 
                             pagination=pagination)
    except Exception as e:
        import traceback
        logger.error(f"获取指令映射列表失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return render_template('error.html', error=str(e))

@app.route('/api/instruction_mapping/<mapping_id>/delete', methods=['DELETE'])
def api_delete_instruction_mapping(mapping_id):
    """删除指令映射API"""
    try:
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM instruction_mappings WHERE mapping_id = ?", (mapping_id,))
            
            if cursor.rowcount > 0:
                conn.commit()
                return jsonify({'success': True, 'message': '指令映射删除成功'})
            else:
                return jsonify({'error': '映射不存在或已被删除'}), 404
                
    except Exception as e:
        logger.error(f"删除指令映射失败: {e}")
        return jsonify({'error': str(e)}), 500

# =================== 错误处理 ===================

@app.route('/favicon.ico')
def favicon():
    """处理favicon请求"""
    return '', 204

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error='页面不存在'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error='服务器内部错误'), 500

# =================== 模板过滤器 ===================

@app.template_filter('datetime')
def datetime_filter(value):
    """格式化日期时间"""
    if isinstance(value, str):
        try:
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return value
    return value

@app.template_filter('status_badge')
def status_badge_filter(status):
    """状态徽章样式"""
    status_map = {
        'completed': 'success',
        'failed': 'danger', 
        'timeout': 'warning',
        'running': 'info',
        'pending': 'secondary'
    }
    return status_map.get(status, 'secondary')

@app.template_filter('platform_icon')
def platform_icon_filter(platform):
    """平台图标"""
    icon_map = {
        'ios': 'fab fa-apple',
        'android': 'fab fa-android',
        'unknown': 'fas fa-question'
    }
    return icon_map.get(platform, 'fas fa-question')

@app.template_filter('tojson_unicode')
def tojson_unicode_filter(obj):
    """转换为JSON格式，不转义Unicode字符"""
    return json.dumps(obj, indent=2, ensure_ascii=False)

if __name__ == '__main__':
    # 初始化系统
    if not init_rag_system():
        print("❌ RAG系统初始化失败，请检查配置")
        sys.exit(1)
    
    # 设置主机地址
    host = '0.0.0.0'
    local_ip = '************'
    
    # 查找可用端口
    port = find_free_port(host)
    
    print("🚀 启动RAG系统Web界面...")
    print(f"📱 本地访问地址: http://localhost:{port}")
    print(f"🌐 局域网访问地址: http://{local_ip}:{port}")
    
    # 启动Flask应用
    app.run(
        host=host,
        port=port,
        debug=True,
        threaded=True
    )