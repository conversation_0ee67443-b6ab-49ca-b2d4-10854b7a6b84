#!/usr/bin/env python3
"""
知识提取器 - 从执行日志中提取结构化信息
基于现有的日志分析工具，提取任务执行数据并存储到RAG系统
"""

import re
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

from .data_models import (
    TaskExecution, TaskStep, ExecutionStatus, PlatformType, TaskType
)
from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class KnowledgeExtractor:
    """知识提取器 - 基于现有日志分析逻辑"""
    
    def __init__(self, db_manager: DatabaseManager, log_dir: str = "log"):
        self.db_manager = db_manager
        self.log_dir = Path(log_dir)
        
    def extract_round_info(self, folder_name: str) -> Optional[Dict[str, Any]]:
        """提取轮次信息 - 复用现有逻辑"""
        pattern = r"round_(\d+)_(\d{8})_(\d{6})"
        match = re.match(pattern, folder_name)
        if match:
            round_num = int(match.group(1))
            date_str = match.group(2)
            time_str = match.group(3)
            datetime_str = f"{date_str}_{time_str}"
            try:
                folder_datetime = datetime.strptime(datetime_str, "%Y%m%d_%H%M%S")
                return {
                    "round_num": round_num,
                    "folder_datetime": folder_datetime,
                    "folder_name": folder_name
                }
            except ValueError:
                pass
        return None
    
    def extract_device_info(self, content: str) -> Optional[Dict[str, str]]:
        """提取设备信息 - 复用现有逻辑"""
        # 查找 find_available_device 的执行结果
        pattern = r'"tool_name": "find_available_device"[^}]*?"result": \{[^}]*?"device_name": "([^"]+)"[^}]*?"device_udid": "([^"]+)"'
        match = re.search(pattern, content)
        if match:
            return {
                "device_name": match.group(1),
                "device_udid": match.group(2)
            }
        
        # 备用方案：查找设备名称和UDID
        device_name_pattern = r'"device_name": "([^"]+)"'
        udid_pattern = r'"(?:udid|device_udid)": "([^"]+)"'
        
        device_name_match = re.search(device_name_pattern, content)
        udid_match = re.search(udid_pattern, content)
        
        if device_name_match and udid_match:
            return {
                "device_name": device_name_match.group(1),
                "device_udid": udid_match.group(1)
            }
        
        return None
    
    def extract_task_completion_info(self, content: str) -> Optional[Dict[str, Any]]:
        """提取任务完成信息 - 复用现有逻辑"""
        try:
            # 查找任务完成部分，包含时间戳
            completion_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?============================================================\n任务完成.*?\n============================================================\n(.*?)============================================================'
            match = re.search(completion_pattern, content, re.DOTALL)
            
            if match:
                timestamp_str = match.group(1)
                completion_content = match.group(2)
                info = {'timestamp': timestamp_str}
                
                # 提取结束时间
                end_time_pattern = r'🕐 结束时间: (.+)'
                end_time_match = re.search(end_time_pattern, completion_content)
                if end_time_match:
                    info['end_time'] = end_time_match.group(1).strip()
                
                # 提取总耗时
                duration_pattern = r'⏱️  总耗时: (.+)'
                duration_match = re.search(duration_pattern, completion_content)
                if duration_match:
                    info['total_duration'] = duration_match.group(1).strip()
                
                # 提取总轮数
                rounds_pattern = r'📊 总轮数: (.+)'
                rounds_match = re.search(rounds_pattern, completion_content)
                if rounds_match:
                    info['total_rounds'] = rounds_match.group(1).strip()
                
                # 提取执行状态
                status_pattern = r'✅ 执行状态: (.+)'
                status_match = re.search(status_pattern, completion_content)
                if status_match:
                    info['execution_status'] = status_match.group(1).strip()
                
                return info if len(info) > 1 else None
            
            return None
        except Exception as e:
            logger.error(f"提取任务完成信息失败: {e}")
            return None
    
    def extract_original_instruction(self, content: str) -> Optional[str]:
        """从日志中提取原始用户指令"""
        try:
            # 查找用户输入部分
            user_input_pattern = r'用户输入: (.*?)(?=\n\d{4}-\d{2}-\d{2}|$)'
            match = re.search(user_input_pattern, content, re.DOTALL)
            
            if match:
                instruction = match.group(1).strip()
                # 清理格式化标记
                instruction = re.sub(r'={40,}', '', instruction)
                instruction = re.sub(r'🎯.*?\n', '', instruction)
                instruction = re.sub(r'📝.*?\n', '', instruction)
                instruction = re.sub(r'📱.*?\n', '', instruction)
                instruction = re.sub(r'🔢.*?\n', '', instruction)
                instruction = re.sub(r'📋.*?\n', '', instruction)
                instruction = instruction.strip()
                
                return instruction if instruction else None
            
            return None
            
        except Exception as e:
            logger.error(f"提取原始指令失败: {e}")
            return None
    
    def extract_structured_plan(self, content: str) -> Optional[Dict[str, Any]]:
        """从日志中提取结构化计划"""
        try:
            # 查找JSON计划部分
            plan_pattern = r'步骤 (\d+): (.+?)\n.*?🔧 工具调用: (\w+)\n.*?📝 参数: (\{.*?\})\n.*?✅ 预期结果: (.+?)\n'
            matches = re.findall(plan_pattern, content, re.DOTALL)
            
            if matches:
                steps = []
                for match in matches:
                    step_id, description, tool_name, params_str, expected_result = match
                    try:
                        parameters = json.loads(params_str)
                    except:
                        parameters = {"raw": params_str}
                    
                    steps.append({
                        "step_id": int(step_id),
                        "action": tool_name,
                        "description": description.strip(),
                        "parameters": parameters,
                        "expected_result": expected_result.strip()
                    })
                
                if steps:
                    return {
                        "plan_id": f"extracted_plan_{int(time.time())}",
                        "total_steps": len(steps),
                        "steps": steps
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"提取结构化计划失败: {e}")
            return None
    
    def extract_final_reply(self, content: str) -> Optional[Dict[str, str]]:
        """提取模型最终回复内容和时间戳 - 复用现有逻辑"""
        try:
            # 查找最终回复部分，使用更精确的匹配
            reply_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?💬 最终回复开始:\s*\n(.*?)💬 最终回复结束'
            match = re.search(reply_pattern, content, re.DOTALL)
            
            if match:
                timestamp_str = match.group(1)
                reply_content = match.group(2).strip()
                
                # 按行处理，只保留实际的回复内容
                lines = reply_content.split('\n')
                actual_reply_lines = []
                
                for line in lines:
                    # 移除时间戳和日志前缀，获取纯内容
                    clean_line = re.sub(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} - \w+ - \[Task_[^\]]+\] -\s*', '', line)
                    
                    # 跳过空行
                    if not clean_line.strip():
                        continue
                    
                    # 跳过仅包含时间戳的行
                    if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} - \w+ - \[Task_[^\]]+\] -\s*$', line):
                        continue
                    
                    actual_reply_lines.append(clean_line.strip())
                
                # 合并有效的回复内容，保持换行结构
                if actual_reply_lines:
                    final_content = '\n'.join(actual_reply_lines)
                    
                    # 最终清理多余空格，但保持换行
                    final_content = re.sub(r'[ \t]+', ' ', final_content)
                    final_content = re.sub(r'\n\s*\n', '\n', final_content)
                    
                    return {
                        'content': final_content.strip(),
                        'timestamp': timestamp_str
                    } if final_content.strip() else None
            
            return None
        except Exception as e:
            logger.error(f"提取最终回复失败: {e}")
            return None
    
    def parse_tool_executions(self, content: str) -> List[Dict[str, Any]]:
        """解析工具执行记录 - 基于现有逻辑"""
        tool_executions = []
        
        # 查找所有工具执行记录，包含时间戳
        tool_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?🔧 执行工具: (\w+).*?📊 执行结果: (\{.*?\}).*?💬 结果摘要: ([^\n]+)'
        matches = re.findall(tool_pattern, content, re.DOTALL)
        
        for i, (timestamp_str, tool_name, result_json, summary) in enumerate(matches):
            try:
                # 解析结果JSON
                result_data = json.loads(result_json)
                
                # 提取关键信息
                status = "success" if result_data.get("status") in ["success", "completed"] else "failed"
                error_message = result_data.get("error") or result_data.get("message", "") if status == "failed" else None
                
                # 提取文本结果
                text_result = ""
                if "text_result" in result_data:
                    text_result = str(result_data["text_result"])
                elif "message" in result_data:
                    text_result = str(result_data["message"])
                elif "content" in result_data:
                    text_result = str(result_data["content"])
                
                # 提取图片URL
                image_url = result_data.get("image_url", "")
                if not image_url and isinstance(result_data.get("text_result"), dict):
                    image_url = result_data["text_result"].get("image_url", "")
                
                tool_execution = {
                    "step_number": i + 1,
                    "tool_name": tool_name,
                    "tool_args": result_data,  # 将结果数据作为参数记录
                    "tool_result": result_data,
                    "status": status,
                    "error_message": error_message,
                    "duration": result_data.get("duration", 0.0),
                    "executed_at": timestamp_str,
                    "is_successful": status == "success",
                    "image_url": image_url or None,
                    "text_result": text_result,
                    "summary": summary
                }
                
                tool_executions.append(tool_execution)
                
            except Exception as e:
                logger.error(f"解析工具执行记录失败 {tool_name}: {e}")
                continue
        
        return tool_executions
    
    def determine_platform(self, content: str, folder_name: str) -> PlatformType:
        """确定平台类型"""
        content_lower = content.lower()
        folder_lower = folder_name.lower()
        
        # 检查iOS关键词
        ios_keywords = ['ios', 'iphone', 'ipad', '苹果', 'apple']
        android_keywords = ['android', '安卓', 'google']
        
        ios_count = sum(1 for keyword in ios_keywords if keyword in content_lower or keyword in folder_lower)
        android_count = sum(1 for keyword in android_keywords if keyword in content_lower or keyword in folder_lower)
        
        if ios_count > android_count:
            return PlatformType.IOS
        elif android_count > ios_count:
            return PlatformType.ANDROID
        else:
            return PlatformType.UNKNOWN
    
    def determine_task_type(self, content: str) -> TaskType:
        """确定任务类型"""
        # 检查是否包含结构化计划标记
        if "🎯 测试计划摘要" in content and "📋 详细执行步骤" in content:
            return TaskType.SMART
        else:
            return TaskType.NORMAL
    
    def extract_execution_from_log(self, round_folder: Path) -> Optional[TaskExecution]:
        """从日志文件夹提取完整的执行信息"""
        try:
            # 读取主要日志文件
            task_log_file = round_folder / "task_structured.log"
            if not task_log_file.exists():
                logger.warning(f"日志文件不存在: {task_log_file}")
                return None
            
            with open(task_log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取轮次信息
            round_info = self.extract_round_info(round_folder.name)
            if not round_info:
                logger.error(f"无法提取轮次信息: {round_folder.name}")
                return None
            
            # 提取设备信息
            device_info = self.extract_device_info(content)
            if not device_info:
                logger.warning(f"无法提取设备信息: {round_folder.name}")
                device_info = {"device_name": "unknown", "device_udid": "unknown"}
            
            # 提取原始指令
            original_instruction = self.extract_original_instruction(content)
            if not original_instruction:
                logger.warning(f"无法提取原始指令: {round_folder.name}")
                original_instruction = "未知指令"
            
            # 提取结构化计划
            structured_plan = self.extract_structured_plan(content)
            
            # 提取最终回复
            final_reply = self.extract_final_reply(content)
            final_result = final_reply["content"] if final_reply else None
            
            # 提取完成信息
            completion_info = self.extract_task_completion_info(content)
            
            # 确定状态
            execution_status = ExecutionStatus.COMPLETED
            if "error" in content.lower() or "failed" in content.lower():
                execution_status = ExecutionStatus.FAILED
            elif completion_info and completion_info.get("execution_status"):
                if "超时" in completion_info["execution_status"] or "timeout" in completion_info["execution_status"].lower():
                    execution_status = ExecutionStatus.TIMEOUT
            
            # 计算时间
            created_at = round_info["folder_datetime"]
            started_at = created_at  # 假设立即开始
            ended_at = None
            total_duration = None
            
            if completion_info and completion_info.get("total_duration"):
                try:
                    duration_str = completion_info["total_duration"]
                    # 解析持续时间，如 "2分30秒" 或 "150秒"
                    if "分" in duration_str and "秒" in duration_str:
                        parts = duration_str.replace("分", ":").replace("秒", "").split(":")
                        total_duration = int(parts[0]) * 60 + int(parts[1])
                    elif "秒" in duration_str:
                        total_duration = float(duration_str.replace("秒", ""))
                    
                    if total_duration:
                        ended_at = started_at.replace(second=started_at.second + int(total_duration))
                except:
                    pass
            
            # 解析工具执行记录
            tool_executions = self.parse_tool_executions(content)
            
            # 统计步骤
            total_steps = len(tool_executions)
            successful_steps = len([t for t in tool_executions if t["is_successful"]])
            failed_steps = total_steps - successful_steps
            
            # 确定平台和任务类型
            platform = self.determine_platform(content, round_folder.name)
            task_type = self.determine_task_type(content)
            
            # 从状态管理器获取MIS ID（如果可用）
            mis_id = "unknown"
            try:
                from tools._concurrent_task_manager import get_concurrent_task_manager
                task_manager = get_concurrent_task_manager()
                if hasattr(task_manager, 'round_tasks'):
                    possible_round_ids = [
                        f"round_{round_info['round_num']:04d}",
                        f"round_{round_info['round_num']:06d}",
                        str(round_info['round_num'])
                    ]
                    for round_id in possible_round_ids:
                        if round_id in task_manager.round_tasks:
                            task_info = task_manager.round_tasks[round_id]
                            mis_id = task_info.get("mis_id", "unknown")
                            break
            except:
                pass
            
            # 创建TaskExecution对象
            execution = TaskExecution(
                execution_id="",  # 将在__post_init__中生成
                round_id=round_folder.name,
                round_number=round_info["round_num"],
                original_instruction=original_instruction,
                structured_plan=structured_plan,
                task_type=task_type,
                platform=platform,
                mis_id=mis_id,
                device_udid=device_info["device_udid"],
                device_name=device_info["device_name"],
                execution_status=execution_status,
                final_result=final_result,
                execution_error=None,
                created_at=created_at,
                started_at=started_at,
                ended_at=ended_at,
                total_duration=total_duration,
                total_steps=total_steps,
                successful_steps=successful_steps,
                failed_steps=failed_steps,
                log_folder_path=str(round_folder),
                metadata={
                    "completion_info": completion_info,
                    "has_structured_plan": structured_plan is not None,
                    "has_final_reply": final_reply is not None
                }
            )
            
            logger.info(f"成功提取执行信息: {round_folder.name}")
            return execution
            
        except Exception as e:
            logger.error(f"从日志提取执行信息失败 {round_folder.name}: {e}")
            return None
    
    def extract_steps_from_log(self, round_folder: Path, execution_id: str) -> List[TaskStep]:
        """从日志文件夹提取步骤信息"""
        try:
            task_log_file = round_folder / "task_structured.log"
            if not task_log_file.exists():
                return []
            
            with open(task_log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tool_executions = self.parse_tool_executions(content)
            
            steps = []
            for tool_exec in tool_executions:
                try:
                    # 解析执行时间
                    executed_at = datetime.strptime(tool_exec["executed_at"], "%Y-%m-%d %H:%M:%S")
                except:
                    executed_at = datetime.now()
                
                step = TaskStep(
                    step_id="",  # 将在__post_init__中生成
                    execution_id=execution_id,
                    step_number=tool_exec["step_number"],
                    tool_name=tool_exec["tool_name"],
                    tool_args=tool_exec["tool_args"],
                    tool_result=tool_exec["tool_result"],
                    status=tool_exec["status"],
                    error_message=tool_exec["error_message"],
                    duration=tool_exec["duration"],
                    executed_at=executed_at,
                    is_successful=tool_exec["is_successful"],
                    image_url=tool_exec["image_url"],
                    text_result=tool_exec["text_result"],
                    metadata={"summary": tool_exec["summary"]}
                )
                
                steps.append(step)
                
            logger.info(f"成功提取 {len(steps)} 个步骤: {round_folder.name}")
            return steps
            
        except Exception as e:
            logger.error(f"从日志提取步骤信息失败 {round_folder.name}: {e}")
            return []
    
    def process_round_folder(self, round_folder: Path) -> bool:
        """处理单个轮次文件夹"""
        try:
            logger.info(f"开始处理轮次文件夹: {round_folder.name}")
            
            # 提取执行信息
            execution = self.extract_execution_from_log(round_folder)
            if not execution:
                logger.error(f"无法提取执行信息: {round_folder.name}")
                return False
            
            # 保存执行信息
            if not self.db_manager.save_task_execution(execution):
                logger.error(f"保存执行信息失败: {round_folder.name}")
                return False
            
            # 提取并保存步骤信息
            steps = self.extract_steps_from_log(round_folder, execution.execution_id)
            for step in steps:
                if not self.db_manager.save_task_step(step):
                    logger.warning(f"保存步骤失败: {step.step_id}")
            
            logger.info(f"成功处理轮次文件夹: {round_folder.name}, 执行ID: {execution.execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"处理轮次文件夹失败 {round_folder.name}: {e}")
            return False
    
    def batch_process_logs(self, limit: Optional[int] = None) -> Dict[str, Any]:
        """批量处理日志文件夹"""
        try:
            # 获取所有轮次文件夹
            round_folders = [f for f in self.log_dir.iterdir() 
                           if f.is_dir() and f.name.startswith("round_")]
            
            # 按文件夹名称排序
            round_folders.sort(key=lambda x: x.name)
            
            if limit:
                round_folders = round_folders[-limit:]  # 处理最新的N个
            
            logger.info(f"开始批量处理 {len(round_folders)} 个轮次文件夹")
            
            # 处理统计
            processed_count = 0
            successful_count = 0
            failed_count = 0
            
            for folder in round_folders:
                processed_count += 1
                
                try:
                    # 检查是否已经处理过
                    existing_execution = None
                    round_info = self.extract_round_info(folder.name)
                    if round_info:
                        existing_executions = self.db_manager.get_executions_by_round(round_info["round_num"])
                        if existing_executions:
                            logger.info(f"轮次已存在，跳过: {folder.name}")
                            continue
                    
                    # 处理文件夹
                    if self.process_round_folder(folder):
                        successful_count += 1
                        logger.info(f"处理成功 ({successful_count}/{processed_count}): {folder.name}")
                    else:
                        failed_count += 1
                        logger.error(f"处理失败 ({failed_count}/{processed_count}): {folder.name}")
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"处理异常 ({failed_count}/{processed_count}) {folder.name}: {e}")
                
                # 避免处理过快
                time.sleep(0.1)
            
            result = {
                "total_processed": processed_count,
                "successful": successful_count,
                "failed": failed_count,
                "success_rate": successful_count / processed_count if processed_count > 0 else 0
            }
            
            logger.info(f"批量处理完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {"error": str(e)}