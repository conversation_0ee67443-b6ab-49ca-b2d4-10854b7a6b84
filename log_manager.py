import logging
import os
import uuid
import time
import glob
import shutil
from datetime import datetime
from typing import Optional, Dict, Any


class LogManager:
    """统一的日志管理器，支持分角色记录日志"""
    
    def __init__(self, log_dir: str = "log", enable_default_logs: bool = True):
        self.log_dir = log_dir
        self.base_log_dir = log_dir  # 保存基础日志目录
        self.is_round_mode = False  # 是否在轮次模式
        self.enable_default_logs = enable_default_logs  # 是否启用默认日志文件
        self._ensure_log_dir()
        self._setup_loggers()
        
        # 任务跟踪相关属性
        self.current_task_id = None
        self.current_task_start_time = None
        self.current_round = 0
        self.task_rounds = []
        
        # 日志清理配置
        self.max_log_rounds = 100  # 保留最近100个轮次的日志
    
    def _ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def _setup_loggers(self):
        """设置不同角色的logger"""
        # 文件日志格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台日志格式（简化，不显示logger名称）
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        if self.enable_default_logs:
            # 创建不同角色的logger
            self.agent_logger = self._create_logger('Agent', 'agent.log', file_formatter)
            self.tools_logger = self._create_logger('Tools', 'tools.log', file_formatter)
            
            # 创建详细LLM日志（记录完整输入输出）
            self.llm_detail_logger = self._create_logger('LLM-Detail', 'llm_detail.log', file_formatter)
            
            # 创建统一的日志文件（包含所有日志）
            self.unified_logger = self._create_logger('Unified', 'unified.log', file_formatter)
            
            # 创建任务结构化日志
            self.task_logger = self._create_logger('Task', 'task_structured.log', file_formatter)
        else:
            # 创建最小化的logger（只创建控制台logger）
            self.agent_logger = None
            self.tools_logger = None
            self.llm_detail_logger = None
            self.unified_logger = None
            self.task_logger = None
        
        # 控制台输出logger（始终创建）
        self.console_logger = self._create_console_logger('Console', console_formatter)
    
    def _create_logger(self, name: str, filename: str, formatter: logging.Formatter) -> logging.Logger:
        """创建文件logger"""
        # 为每个LogManager实例创建唯一的logger名称，确保日志隔离
        unique_name = f"{name}_{id(self)}"
        logger = logging.getLogger(unique_name)
        logger.setLevel(logging.INFO)
        
        # 清理已有的handlers，避免重复添加
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
            handler.close()
        
        # 文件handler
        file_handler = logging.FileHandler(
            os.path.join(self.log_dir, filename),
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 防止日志传播到根logger
        logger.propagate = False
        
        return logger
    
    def _create_console_logger(self, name: str, formatter: logging.Formatter) -> logging.Logger:
        """创建控制台logger"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        
        # 清理已有的handlers，避免重复添加
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
            handler.close()
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        logger.propagate = False
        
        return logger
    
    def _log_to_unified(self, level: str, role: str, message: str):
        """记录到统一日志文件"""
        # 如果unified_logger为None，跳过文件日志记录
        if self.unified_logger is not None:
            # 始终记录到统一日志，不管是否在轮次模式
            log_method = getattr(self.unified_logger, level.lower())
            log_method(f"[{role}] - {message}")
    
    def log_agent(self, level: str, message: str, console: bool = True):
        """记录Agent相关日志"""
        # 如果agent_logger为None，跳过文件日志记录
        if self.agent_logger is not None:
            log_method = getattr(self.agent_logger, level.lower())
            log_method(message)
            
            # 记录到统一日志
            self._log_to_unified(level, 'Agent', message)
        
        # 控制台输出
        if console:
            console_method = getattr(self.console_logger, level.lower())
            console_method(f"[Agent] - {message}")
    
    def log_tools(self, level: str, message: str, tool_name: str = "", console: bool = False):
        """记录Tools相关日志"""
        if tool_name:
            full_message = f"[{tool_name}] - {message}"
        else:
            full_message = message
            
        # 如果tools_logger为None，跳过文件日志记录
        if self.tools_logger is not None:
            log_method = getattr(self.tools_logger, level.lower())
            log_method(full_message)
            
            # 记录到统一日志
            self._log_to_unified(level, 'Tools', full_message)
        
        # 控制台输出（默认不输出，避免太多噪音）
        if console:
            console_method = getattr(self.console_logger, level.lower())
            console_method(f"[Tools] - {full_message}")
    
    
    def info_agent(self, message: str, console: bool = True):
        """Agent信息日志"""
        self.log_agent('info', message, console)
    
    def error_agent(self, message: str, console: bool = True):
        """Agent错误日志"""
        self.log_agent('error', message, console)
    
    def warning_agent(self, message: str, console: bool = True):
        """Agent警告日志"""
        self.log_agent('warning', message, console)
    
    def debug_agent(self, message: str, console: bool = False):
        """Agent调试日志"""
        self.log_agent('debug', message, console)
    
    def info_tools(self, message: str, tool_name: str = "", console: bool = False):
        """Tools信息日志"""
        self.log_tools('info', message, tool_name, console)
    
    def error_tools(self, message: str, tool_name: str = "", console: bool = True):
        """Tools错误日志"""
        self.log_tools('error', message, tool_name, console)
    
    def warning_tools(self, message: str, tool_name: str = "", console: bool = True):
        """Tools警告日志"""
        self.log_tools('warning', message, tool_name, console)
    
    
    def log_llm_input(self, messages: list, model_name: str = ""):
        """记录LLM的详细输入信息"""
        try:
            input_content = "=== LLM 输入 ==="
            for i, msg in enumerate(messages):
                msg_type = type(msg).__name__
                content = getattr(msg, 'content', str(msg))
                input_content += f"\n[消息 {i+1}] {msg_type}: {content}"
            
            if model_name:
                full_message = f"[{model_name}] - {input_content}"
            else:
                full_message = input_content
            
            # 如果llm_detail_logger为None，跳过文件日志记录
            if self.llm_detail_logger is not None:
                self.llm_detail_logger.info(full_message)
                self._log_to_unified('info', 'LLM-Detail', full_message)
            
        except Exception as e:
            self.error_llm(f"记录LLM输入时出错: {e}", model_name)
    
    def log_llm_output(self, response, model_name: str = ""):
        """记录LLM的详细输出信息"""
        try:
            output_content = "=== LLM 输出 ==="
            
            # 记录基本响应内容
            if hasattr(response, 'content'):
                output_content += f"\n内容: {response.content}"
            
            # 记录工具调用信息
            if hasattr(response, 'tool_calls') and response.tool_calls:
                output_content += f"\n工具调用数量: {len(response.tool_calls)}"
                for i, tool_call in enumerate(response.tool_calls):
                    output_content += f"\n[工具调用 {i+1}]"
                    output_content += f"\n  - 工具名: {tool_call.get('name', 'unknown')}"
                    output_content += f"\n  - 参数: {tool_call.get('args', {})}"
                    output_content += f"\n  - ID: {tool_call.get('id', 'unknown')}"
            
            # 记录其他属性
            if hasattr(response, 'response_metadata'):
                output_content += f"\n元数据: {response.response_metadata}"
            
            if model_name:
                full_message = f"[{model_name}] - {output_content}"
            else:
                full_message = output_content
            
            # 如果llm_detail_logger为None，跳过文件日志记录
            if self.llm_detail_logger is not None:
                self.llm_detail_logger.info(full_message)
                self._log_to_unified('info', 'LLM-Detail', full_message)
            
        except Exception as e:
            self.error_llm(f"记录LLM输出时出错: {e}", model_name)
    
    def log_llm_tool_result(self, tool_name: str, tool_result: str, model_name: str = ""):
        """记录工具执行结果"""
        try:
            result_content = f"=== 工具执行结果 ===\n工具名: {tool_name}\n结果: {tool_result}"
            
            if model_name:
                full_message = f"[{model_name}] - {result_content}"
            else:
                full_message = result_content
            
            # 如果llm_detail_logger为None，跳过文件日志记录
            if self.llm_detail_logger is not None:
                self.llm_detail_logger.info(full_message)
                self._log_to_unified('info', 'LLM-Detail', full_message)
            
        except Exception as e:
            self.error_llm(f"记录工具结果时出错: {e}", model_name)
    
    # ============ 任务结构化日志方法 ============
    
    def start_task(self, user_input: str, task_type: str = "多轮工具调用") -> str:
        """开始一个新任务"""
        # 生成任务ID
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        task_id = f"TASK-{timestamp}-{str(uuid.uuid4())[:8].upper()}"
        
        self.current_task_id = task_id
        self.current_task_start_time = time.time()
        self.current_round = 0
        self.task_rounds = []
        
        # 记录任务开始
        start_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        task_header = f"""
{'='*60}
任务开始 [{task_id}]
{'='*60}
用户输入: {user_input}
任务类型: {task_type}  
开始时间: {start_time_str}
"""
        
        # 如果task_logger为None，跳过文件日志记录
        if self.task_logger is not None:
            self.task_logger.info(task_header)
        self.info_agent(f"🎯 开始新任务: {task_id}")
        
        return task_id
    
    def start_round(self) -> int:
        """开始新的执行轮次"""
        self.current_round += 1
        round_start_time = time.time()
        
        # 初始化轮次数据
        round_data = {
            "round": self.current_round,
            "start_time": round_start_time,
            "model_decision": None,
            "tool_name": None,
            "tool_args": None,
            "tool_result": None,
            "tool_summary": None,
            "duration": None,
            "status": "running"
        }
        
        self.task_rounds.append(round_data)
        
        round_header = f"\n    ─── 第{self.current_round}轮执行 ───"
        # 如果task_logger为None，跳过文件日志记录
        if self.task_logger is not None:
            self.task_logger.info(round_header)
        
        return self.current_round
    
    def log_model_decision(self, tool_calls: list = None, direct_reply: bool = False):
        """记录模型决策"""
        if not self.task_rounds:
            return
            
        current_round_data = self.task_rounds[-1]
        
        if direct_reply:
            decision = "直接回复用户"
            # 如果task_logger为None，跳过文件日志记录
            if self.task_logger is not None:
                self.task_logger.info(f"    📋 询问模型: 下一步操作")
                self.task_logger.info(f"    ✅ 模型决定: {decision}")
            current_round_data["model_decision"] = decision
        else:
            tool_names = [tc.get('name', 'unknown') for tc in tool_calls] if tool_calls else []
            decision = f"调用工具 {tool_names}"
            # 如果task_logger为None，跳过文件日志记录
            if self.task_logger is not None:
                self.task_logger.info(f"    📋 询问模型: 下一步操作")
                self.task_logger.info(f"    ✅ 模型决定: {decision}")
            current_round_data["model_decision"] = decision
    
    def log_tool_execution(self, tool_name: str, tool_args: dict, tool_result: str, duration: float):
        """记录工具执行"""
        if not self.task_rounds:
            return
            
        current_round_data = self.task_rounds[-1]
        
        # 解析工具结果获取摘要
        try:
            import json
            result_data = json.loads(tool_result)
            if 'message' in result_data:
                summary = result_data['message']
            elif 'status' in result_data and result_data['status'] == 'success':
                summary = "执行成功"
            else:
                summary = tool_result[:50] + "..." if len(tool_result) > 50 else tool_result
        except:
            summary = tool_result[:50] + "..." if len(tool_result) > 50 else tool_result
        
        # 记录到结构化日志
        # 如果task_logger为None，跳过文件日志记录
        if self.task_logger is not None:
            self.task_logger.info(f"    🔧 执行工具: {tool_name}")
            self.task_logger.info(f"    📝 工具参数: {tool_args}")
            self.task_logger.info(f"    ⏱️  执行耗时: {duration:.1f}秒")
            self.task_logger.info(f"    📊 执行结果: {tool_result}")
            self.task_logger.info(f"    💬 结果摘要: {summary}")
        
        # 更新轮次数据
        current_round_data.update({
            "tool_name": tool_name,
            "tool_args": tool_args,
            "tool_result": tool_result,
            "tool_summary": summary,
            "duration": duration,
            "status": "completed"
        })
    
    def log_final_reply(self, reply_content: str):
        """记录最终回复"""
        if not self.task_rounds:
            return
            
        current_round_data = self.task_rounds[-1]
        
        # 如果task_logger为None，跳过文件日志记录
        if self.task_logger is not None:
            self.task_logger.info(f"    📋 询问模型: 下一步操作")
            self.task_logger.info(f"    ✅ 模型决定: 直接回复用户")
            
            # 记录完整的最终回复内容（不截断）
            self.task_logger.info(f"    💬 最终回复开始:")
            
            # 将长回复内容按行分割，保持格式
            for line in reply_content.split('\n'):
                self.task_logger.info(f"        {line}")
            
            self.task_logger.info(f"    💬 最终回复结束")
        
        current_round_data.update({
            "model_decision": "直接回复用户",
            "final_reply": reply_content,
            "status": "completed"
        })
    
    def end_task(self, status: str = "成功") -> Dict[str, Any]:
        """结束当前任务"""
        if not self.current_task_id:
            return {}
            
        end_time = time.time()
        total_duration = end_time - self.current_task_start_time
        end_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 记录任务完成
        task_footer = f"""
{'='*60}
任务完成 [{self.current_task_id}]
{'='*60}
📊 总轮数: {self.current_round}轮
⏱️  总耗时: {total_duration:.1f}秒
✅ 执行状态: {status}
🕐 结束时间: {end_time_str}
{'='*60}
"""
        
        # 如果task_logger为None，跳过文件日志记录
        if self.task_logger is not None:
            self.task_logger.info(task_footer)
        self.info_agent(f"🏁 任务完成: {self.current_task_id} (总耗时: {total_duration:.1f}秒)")
        
        # 构建任务摘要
        task_summary = {
            "task_id": self.current_task_id,
            "total_rounds": self.current_round,
            "total_duration": total_duration,
            "status": status,
            "rounds": self.task_rounds.copy()
        }
        
        # 重置任务状态
        self.current_task_id = None
        self.current_task_start_time = None
        self.current_round = 0
        self.task_rounds = []
        
        return task_summary
    
    def switch_log_directory(self, new_log_dir: str) -> Dict[str, str]:
        """
        切换日志目录到新的轮次目录
        
        Args:
            new_log_dir: 新的日志目录路径
            
        Returns:
            包含切换信息的字典
        """
        old_log_dir = self.log_dir
        
        try:
            # 更新日志目录
            self.log_dir = new_log_dir
            self.is_round_mode = True  # 标记为轮次模式
            
            # 确保新目录存在
            self._ensure_log_dir()
            
            # 清理现有的所有logger handlers
            self._cleanup_loggers()
            
            # 重新设置所有logger到新目录
            self._setup_loggers()
            
            # 执行日志清理
            self._cleanup_old_logs()
            
            switch_info = {
                "old_directory": old_log_dir,
                "new_directory": new_log_dir,
                "status": "success",
                "message": f"日志目录已切换：{old_log_dir} -> {new_log_dir}"
            }
            
            # 记录切换信息
            self.info_agent(f"日志目录已切换到: {new_log_dir}")
            
            return switch_info
            
        except Exception as e:
            # 如果切换失败，恢复原来的目录
            self.log_dir = old_log_dir
            self.is_round_mode = False
            self._cleanup_loggers()
            self._setup_loggers()
            
            switch_info = {
                "old_directory": old_log_dir,
                "new_directory": new_log_dir,
                "status": "error",
                "error": str(e),
                "message": f"切换日志目录失败: {str(e)}"
            }
            
            self.error_agent(f"切换日志目录失败: {str(e)}")
            return switch_info
    
    def _cleanup_loggers(self):
        """清理现有的logger handlers"""
        loggers_to_cleanup = [
            'Agent', 'Tools', 'LLM', 'LLM-Detail', 'Unified', 'Task', 'Console'
        ]
        
        for logger_name in loggers_to_cleanup:
            logger = logging.getLogger(logger_name)
            # 移除所有现有的handlers
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
    
    def get_current_log_directory(self) -> str:
        """
        获取当前日志目录
        
        Returns:
            当前日志目录路径
        """
        return self.log_dir
    
    def _cleanup_old_logs(self):
        """
        清理旧的日志文件夹，只保留最近的N个轮次
        """
        try:
            # 获取基础日志目录下的所有轮次文件夹
            round_pattern = os.path.join(self.base_log_dir, "round_*")
            round_folders = glob.glob(round_pattern)
            
            # 过滤出有效的轮次文件夹并按时间排序
            valid_folders = []
            for folder in round_folders:
                folder_name = os.path.basename(folder)
                # 检查是否符合轮次文件夹命名规则
                if folder_name.startswith("round_") and os.path.isdir(folder):
                    # 获取文件夹的创建时间
                    folder_time = os.path.getctime(folder)
                    valid_folders.append((folder, folder_time))
            
            # 如果轮次文件夹数量超过限制，删除最旧的
            if len(valid_folders) > self.max_log_rounds:
                # 按时间排序，最新的在前
                valid_folders.sort(key=lambda x: x[1], reverse=True)
                
                # 删除超出限制的旧文件夹
                folders_to_delete = valid_folders[self.max_log_rounds:]
                for folder_path, _ in folders_to_delete:
                    try:
                        shutil.rmtree(folder_path)
                        self.info_agent(f"已删除旧日志文件夹: {os.path.basename(folder_path)}")
                    except Exception as e:
                        self.error_agent(f"删除日志文件夹失败: {folder_path}, 错误: {str(e)}")
                        
                self.info_agent(f"日志清理完成，保留最近 {self.max_log_rounds} 个轮次的日志")
            
        except Exception as e:
            self.error_agent(f"清理旧日志时出错: {str(e)}")
    
    def disable_base_logging(self):
        """
        禁用基础日志记录，避免在轮次模式下重复记录
        """
        try:
            # 临时禁用基础目录的日志记录
            if self.is_round_mode:
                self.info_agent("已禁用基础日志记录，所有日志将写入轮次文件夹")
                
        except Exception as e:
            self.error_agent(f"禁用基础日志记录时出错: {str(e)}")
    
    def set_max_log_rounds(self, max_rounds: int):
        """
        设置最大保留的日志轮次数
        
        Args:
            max_rounds: 最大保留的轮次数
        """
        self.max_log_rounds = max_rounds
        self.info_agent(f"日志保留数量已设置为: {max_rounds} 个轮次")
    
    def cleanup_device_wda_logs(self, device_udid: str = None, device_name: str = None,
                               max_logs: int = 10, hours: int = None) -> bool:
        """
        清理设备WDA日志文件，保留最近的N个日志文件

        Args:
            device_udid: 设备UDID，用于筛选特定设备的日志
            device_name: 设备名称，用于筛选特定设备的日志
            max_logs: 最大保留的日志文件数量，默认10个
            hours: 保留最近N小时的日志，如果指定则忽略max_logs参数

        Returns:
            bool: 清理是否成功
        """
        try:
            wda_logs_dir = os.path.join(self.base_log_dir, "wda_logs")
            if not os.path.exists(wda_logs_dir):
                self.info_agent("WDA日志目录不存在，无需清理")
                return True

            # 获取所有WDA日志文件
            wda_log_files = []
            for filename in os.listdir(wda_logs_dir):
                if filename.startswith("wda_") and filename.endswith(".log"):
                    file_path = os.path.join(wda_logs_dir, filename)
                    if os.path.isfile(file_path):
                        # 如果指定了设备筛选条件，则进行筛选
                        if device_udid and device_udid not in filename:
                            continue
                        if device_name and device_name not in filename:
                            continue

                        # 获取文件修改时间
                        mtime = os.path.getmtime(file_path)
                        wda_log_files.append({
                            'path': file_path,
                            'filename': filename,
                            'mtime': mtime
                        })

            if not wda_log_files:
                self.info_agent("没有找到需要清理的WDA日志文件")
                return True

            # 按修改时间排序，最新的在前
            wda_log_files.sort(key=lambda x: x['mtime'], reverse=True)

            files_to_delete = []

            if hours is not None:
                # 按时间清理：删除超过指定小时数的文件
                current_time = time.time()
                cutoff_time = current_time - (hours * 3600)

                for file_info in wda_log_files:
                    if file_info['mtime'] < cutoff_time:
                        files_to_delete.append(file_info)

                self.info_agent(f"按时间清理WDA日志：删除{hours}小时前的文件，共{len(files_to_delete)}个")
            else:
                # 按数量清理：保留最新的max_logs个文件
                if len(wda_log_files) > max_logs:
                    files_to_delete = wda_log_files[max_logs:]
                    self.info_agent(f"按数量清理WDA日志：保留最新{max_logs}个文件，删除{len(files_to_delete)}个旧文件")
                else:
                    self.info_agent(f"WDA日志文件数量({len(wda_log_files)})未超过限制({max_logs})，无需清理")
                    return True

            # 执行删除操作
            deleted_count = 0
            for file_info in files_to_delete:
                try:
                    os.remove(file_info['path'])
                    self.info_agent(f"已删除WDA日志文件: {file_info['filename']}")
                    deleted_count += 1
                except Exception as e:
                    self.error_agent(f"删除WDA日志文件失败 {file_info['filename']}: {e}")

            self.info_agent(f"WDA日志清理完成，成功删除{deleted_count}个文件")
            return True

        except Exception as e:
            self.error_agent(f"清理WDA日志时出错: {str(e)}")
            return False

    def get_log_statistics(self) -> Dict[str, Any]:
        """
        获取日志统计信息

        Returns:
            包含日志统计信息的字典
        """
        try:
            # 获取基础日志目录下的所有轮次文件夹
            round_pattern = os.path.join(self.base_log_dir, "round_*")
            round_folders = glob.glob(round_pattern)

            # 计算总大小
            total_size = 0
            folder_count = 0

            for folder in round_folders:
                if os.path.isdir(folder):
                    folder_count += 1
                    # 计算文件夹大小
                    for root, _, files in os.walk(folder):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                total_size += os.path.getsize(file_path)
                            except OSError:
                                continue

            # 转换为人类可读的大小
            def format_size(size_bytes):
                if size_bytes < 1024:
                    return f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    return f"{size_bytes / 1024:.1f} KB"
                elif size_bytes < 1024 * 1024 * 1024:
                    return f"{size_bytes / (1024 * 1024):.1f} MB"
                else:
                    return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

            stats = {
                "total_rounds": folder_count,
                "total_size_bytes": total_size,
                "total_size_formatted": format_size(total_size),
                "max_rounds_limit": self.max_log_rounds,
                "current_log_dir": self.log_dir,
                "is_round_mode": self.is_round_mode,
                "base_log_dir": self.base_log_dir
            }

            return stats

        except Exception as e:
            self.error_agent(f"获取日志统计信息时出错: {str(e)}")
            return {"error": str(e)}


# 全局日志管理器实例
log_manager = LogManager() 