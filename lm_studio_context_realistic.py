#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LM Studio 双角色测试器 - Planner + Actor 架构
实现单模型双角色的智能测试执行系统
"""

import json
import time
import random
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from dataclasses import dataclass, asdict
from langchain_openai import ChatOpenAI


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'dual_role_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class StepResult:
    """步骤执行结果"""
    step_id: int
    status: str  # success, failed, retry, skipped
    result_data: Dict[str, Any]
    execution_time: float
    retry_count: int
    error_message: Optional[str] = None


@dataclass
class DAGState:
    """DAG状态管理"""
    completed_steps: List[int]
    failed_steps: List[int]
    current_step: int
    variables: Dict[str, Any]  # 存储步骤间传递的变量
    step_results: Dict[int, StepResult]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "completed_steps": self.completed_steps,
            "failed_steps": self.failed_steps,
            "current_step": self.current_step,
            "variables": self.variables,
            "step_results": {k: asdict(v) for k, v in self.step_results.items()}
        }


class DeviceState:
    """模拟设备状态管理"""
    def __init__(self):
        self.available_devices = [
            {"udid": "ios_device_001", "platform": "ios", "status": "available", "model": "iPhone 14"},
            {"udid": "ios_device_002", "platform": "ios", "status": "available", "model": "iPhone 15 Pro"},
        ]
        self.active_sessions = {}
        self.current_page = "home"
        self.elements_on_page = {
            "home": [{"element": "左上角地址", "x": 100, "y": 50}],
            "address_selection": [{"element": "搜索框", "x": 200, "y": 100}],
            "address_search": [{"element": "输入框", "x": 200, "y": 120}],
            "search_results": [{"element": "北京市", "x": 150, "y": 200}]
        }
        self.current_text = ""
        
    def get_page_text(self):
        """根据当前页面返回OCR文本"""
        page_texts = {
            "home": "首页内容 左上角地址显示",
            "address_selection": "地址选择页 搜索城市/区县/地点",
            "address_search": "地址搜索页 搜索城市/区县/地点",
            "search_results": "搜索结果 北京市 上海市 广州市",
            "home_beijing": "首页内容 北京"
        }
        return page_texts.get(self.current_page, "页面内容")


# 全局设备状态
device_state = DeviceState()


# 模拟工具函数（保持原有的实现）
def mock_find_available_device(platform: str) -> Dict[str, Any]:
    """查找可用设备"""
    time.sleep(0.5)
    available = [d for d in device_state.available_devices if d["platform"] == platform and d["status"] == "available"]
    
    if available:
        device = available[0]
        logger.info(f"找到可用{platform}设备: {device['udid']} ({device['model']})")
        return {
            "success": True,
            "device": device,
            "udid": device['udid'],  # 提供给变量传递
            "message": f"成功找到{platform}设备"
        }
    else:
        return {
            "success": False,
            "error": f"没有找到可用的{platform}设备"
        }


def mock_start_device_test(udid: str) -> Dict[str, Any]:
    """启动设备测试会话"""
    time.sleep(1.0)
    
    if udid not in [d["udid"] for d in device_state.available_devices]:
        return {"success": False, "error": "设备不存在"}
    
    device_state.active_sessions[udid] = {"start_time": datetime.now(), "status": "active"}
    logger.info(f"启动设备 {udid} 的测试会话")
    
    return {
        "success": True,
        "session_id": f"session_{udid}_{int(time.time())}",
        "message": "成功启动测试会话"
    }


def mock_find_element_on_page(udid: str, element: str) -> Dict[str, Any]:
    """在页面上查找元素"""
    time.sleep(0.8)
    
    if udid not in device_state.active_sessions:
        return {"success": False, "error": "设备会话未启动"}
    
    elements = device_state.elements_on_page.get(device_state.current_page, [])
    found_element = next((e for e in elements if e["element"] == element), None)
    
    if found_element:
        logger.info(f"在页面 {device_state.current_page} 找到元素: {element}")
        return {
            "success": True,
            "element": found_element,
            "x": found_element["x"],
            "y": found_element["y"],
            "message": f"成功找到元素: {element}"
        }
    else:
        mock_coords = {"x": random.randint(50, 300), "y": random.randint(50, 500)}
        logger.info(f"模拟找到元素: {element} 在坐标 ({mock_coords['x']}, {mock_coords['y']})")
        return {
            "success": True,
            "element": {"element": element, **mock_coords},
            "x": mock_coords["x"],
            "y": mock_coords["y"],
            "message": f"成功找到元素: {element}"
        }


def mock_tap_device(udid: str, x: int, y: int) -> Dict[str, Any]:
    """点击设备屏幕"""
    time.sleep(0.3)
    
    if udid not in device_state.active_sessions:
        return {"success": False, "error": "设备会话未启动"}
    
    # 根据点击位置模拟页面跳转
    if device_state.current_page == "home" and 50 <= x <= 150 and 25 <= y <= 75:
        device_state.current_page = "address_selection"
        logger.info("点击左上角地址，跳转到地址选择页")
    elif device_state.current_page == "address_selection" and 150 <= x <= 250 and 75 <= y <= 125:
        device_state.current_page = "address_search"
        logger.info("点击搜索框，跳转到地址搜索页")
    elif device_state.current_page == "search_results" and 100 <= x <= 200 and 175 <= y <= 225:
        device_state.current_page = "home_beijing"
        logger.info("点击北京市，返回首页并显示北京")
    
    logger.info(f"在设备 {udid} 的坐标 ({x}, {y}) 执行点击")
    return {
        "success": True,
        "message": f"成功点击坐标 ({x}, {y})"
    }


def mock_wait_seconds(seconds: int) -> Dict[str, Any]:
    """等待指定秒数"""
    logger.info(f"等待 {seconds} 秒...")
    time.sleep(min(seconds, 1))
    return {
        "success": True,
        "message": f"等待 {seconds} 秒完成"
    }


def mock_ocr_text_only(udid: str) -> Dict[str, Any]:
    """OCR识别页面文本"""
    time.sleep(1.2)
    
    if udid not in device_state.active_sessions:
        return {"success": False, "error": "设备会话未启动"}
    
    text = device_state.get_page_text()
    logger.info(f"OCR识别结果: {text}")
    
    return {
        "success": True,
        "text": text,
        "message": "OCR识别完成"
    }


def mock_input_text_smart(udid: str, text: str) -> Dict[str, Any]:
    """智能输入文本"""
    time.sleep(0.6)
    
    if udid not in device_state.active_sessions:
        return {"success": False, "error": "设备会话未启动"}
    
    device_state.current_text = text
    if text == "北京":
        device_state.current_page = "search_results"
    
    logger.info(f"在设备 {udid} 输入文本: {text}")
    return {
        "success": True,
        "message": f"成功输入文本: {text}"
    }


def mock_end_device_test(udid: str) -> Dict[str, Any]:
    """结束设备测试会话"""
    time.sleep(0.5)
    
    if udid not in device_state.active_sessions:
        return {"success": False, "error": "设备会话不存在"}
    
    session_info = device_state.active_sessions.pop(udid)
    duration = datetime.now() - session_info["start_time"]
    
    logger.info(f"结束设备 {udid} 的测试会话，持续时间: {duration}")
    return {
        "success": True,
        "duration": str(duration),
        "message": "成功结束测试会话"
    }


# 工具注册表
TOOL_REGISTRY = {
    "find_available_device": mock_find_available_device,
    "start_device_test": mock_start_device_test,
    "find_element_on_page": mock_find_element_on_page,
    "tap_device": mock_tap_device,
    "wait_seconds": mock_wait_seconds,
    "ocr_text_only": mock_ocr_text_only,
    "input_text_smart": mock_input_text_smart,
    "end_device_test": mock_end_device_test
}


class TestPlanner:
    """测试策略规划器 - 负责DAG管理和决策"""
    
    def __init__(self, llm: ChatOpenAI):
        self.llm = llm
        
    def get_next_action(self, plan: Dict[str, Any], dag_state: DAGState) -> Dict[str, Any]:
        """决定下一步执行策略"""
        
        # 构建当前状态摘要
        remaining_steps = [s for s in plan["steps"] if s["step_id"] not in dag_state.completed_steps]
        recent_results = list(dag_state.step_results.values())[-3:] if dag_state.step_results else []
        
        state_snippet = {
            "total_steps": len(plan["steps"]),
            "completed_count": len(dag_state.completed_steps),
            "failed_count": len(dag_state.failed_steps),
            "current_step_id": dag_state.current_step,
            "remaining_steps": len(remaining_steps),
            "recent_results": [{"step_id": r.step_id, "status": r.status} for r in recent_results]
        }
        
        # 检查依赖关系，找出可执行的下一步
        executable_steps = []
        for step in remaining_steps:
            deps_satisfied = all(dep in dag_state.completed_steps for dep in step["dependencies"])
            if deps_satisfied and step["step_id"] not in dag_state.failed_steps:
                executable_steps.append(step["step_id"])
        
        # 构建Planner prompt
        planner_prompt = f"""你是一名测试策略师，只能输出 JSON 格式的决策。

全局目标：{plan["test_plan"]["title"]}
总步骤数：{plan["test_plan"]["total_steps"]}
已完成节点：{dag_state.completed_steps}
失败节点：{dag_state.failed_steps}
当前进度：{len(dag_state.completed_steps)}/{len(plan["steps"])}

当前状态：
{json.dumps(state_snippet, ensure_ascii=False, indent=2)}

可执行步骤：{executable_steps}

可选策略：
- next: 下一步要执行的 step_id（int）
- action: continue | retry(step_id) | skip(step_id) | wait(seconds) | complete

决策规则：
1. 如果有可执行步骤，选择其中step_id最小的执行
2. 如果某步骤失败且retry_count < max_retry，可以选择retry
3. 如果所有关键步骤完成，可以选择complete
4. 对于非关键步骤，可以选择skip

请输出JSON格式的决策：
{{"next": <step_id>, "action": "<action_type>", "reason": "<决策理由>"}}"""

        try:
            response = self.llm.invoke([{"role": "user", "content": planner_prompt}])
            decision_text = response.content.strip()
            
            # 尝试解析JSON
            if decision_text.startswith("```json"):
                decision_text = decision_text.split("```json")[1].split("```")[0].strip()
            elif decision_text.startswith("```"):
                decision_text = decision_text.split("```")[1].split("```")[0].strip()
            
            decision = json.loads(decision_text)
            logger.info(f"🧠 Planner决策: {decision}")
            return decision
            
        except Exception as e:
            logger.error(f"Planner决策解析失败: {e}")
            # 默认策略：执行下一个可行步骤
            if executable_steps:
                return {
                    "next": min(executable_steps),
                    "action": "continue",
                    "reason": "默认策略：执行下一个可行步骤"
                }
            else:
                return {
                    "next": -1,
                    "action": "complete", 
                    "reason": "没有可执行步骤，任务完成"
                }


class TestActor:
    """测试执行器 - 负责单步工具调用"""
    
    def __init__(self, llm: ChatOpenAI):
        self.llm = llm
        
    def execute_step(self, step: Dict[str, Any], variables: Dict[str, Any]) -> StepResult:
        """执行单个测试步骤"""
        start_time = time.time()
        step_id = step["step_id"]
        
        try:
            # 解析参数中的变量引用
            resolved_params = self._resolve_parameters(step["parameters"], variables)
            
            # Actor直接执行工具，不需要LLM交互
            # 这里简化了Actor的逻辑，专注于工具执行

            logger.info(f"🤖 Actor执行步骤 {step_id}: {step['name']}")
            
            # 调用工具
            tool_name = step["tool"]
            if tool_name in TOOL_REGISTRY:
                tool_func = TOOL_REGISTRY[tool_name]
                result = tool_func(**resolved_params)
                
                execution_time = time.time() - start_time
                
                # 判断执行状态
                if isinstance(result, dict):
                    if result.get("success", False):
                        status = "success"
                        error_message = None
                    else:
                        status = "failed"
                        error_message = result.get("error", "未知错误")
                else:
                    status = "success"
                    error_message = None
                
                return StepResult(
                    step_id=step_id,
                    status=status,
                    result_data=result if isinstance(result, dict) else {"result": result},
                    execution_time=execution_time,
                    retry_count=step.get("retry_count", 0),
                    error_message=error_message
                )
            else:
                return StepResult(
                    step_id=step_id,
                    status="failed",
                    result_data={"error": f"工具 {tool_name} 未找到"},
                    execution_time=time.time() - start_time,
                    retry_count=step.get("retry_count", 0),
                    error_message=f"工具 {tool_name} 未找到"
                )
                
        except Exception as e:
            logger.error(f"Actor执行步骤 {step_id} 异常: {e}")
            return StepResult(
                step_id=step_id,
                status="failed",
                result_data={"error": str(e)},
                execution_time=time.time() - start_time,
                retry_count=step.get("retry_count", 0),
                error_message=str(e)
            )
    
    def _resolve_parameters(self, params: Dict[str, Any], variables: Dict[str, Any]) -> Dict[str, Any]:
        """解析参数中的变量引用"""
        resolved = {}
        
        for key, value in params.items():
            if isinstance(value, str) and value.startswith("${"):
                # 解析变量引用，如 ${step1.udid} 或 ${global.platform}
                var_path = value[2:-1]  # 去掉 ${} 
                
                if var_path.startswith("global."):
                    # 全局变量
                    global_key = var_path.split(".", 1)[1]
                    resolved[key] = variables.get("global", {}).get(global_key, value)
                elif var_path.startswith("step"):
                    # 步骤变量，如 step1.udid
                    step_ref, field = var_path.split(".", 1)
                    step_num = int(step_ref[4:])  # 提取步骤号
                    step_data = variables.get(f"step{step_num}", {})
                    resolved[key] = step_data.get(field, value)
                else:
                    resolved[key] = variables.get(var_path, value)
            else:
                resolved[key] = value
                
        return resolved


class DualRoleOrchestrator:
    """双角色协调器 - 协调Planner和Actor"""
    
    def __init__(self, base_url: str = "http://localhost:1234/v1", model_name: str = "local-model"):
        self.llm = ChatOpenAI(
            base_url=base_url,
            api_key="lm-studio",
            model=model_name,
            temperature=0.1,
            timeout=60
        )
        
        self.planner = TestPlanner(self.llm)
        self.actor = TestActor(self.llm)
        
        # 加载测试计划
        self.plan = self._load_plan()
        
        # 初始化DAG状态
        self.dag_state = DAGState(
            completed_steps=[],
            failed_steps=[],
            current_step=1,
            variables={"global": self.plan["test_plan"]["global_variables"]},
            step_results={}
        )
        
        # 状态持久化文件
        self.state_file = f"dag_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
    def _load_plan(self) -> Dict[str, Any]:
        """加载测试计划"""
        try:
            with open("human_plan.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载测试计划失败: {e}")
            raise
    
    def _save_state(self):
        """保存DAG状态"""
        try:
            with open(self.state_file, "w", encoding="utf-8") as f:
                json.dump(self.dag_state.to_dict(), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存状态失败: {e}")
    
    def _extract_step_outputs(self, step: Dict[str, Any], result: StepResult):
        """从步骤结果中提取输出变量"""
        if result.status != "success":
            return
            
        step_id = step["step_id"]
        expected_outputs = step.get("expected_output", [])
        
        step_vars = {}
        for output_key in expected_outputs:
            if output_key in result.result_data:
                step_vars[output_key] = result.result_data[output_key]
        
        # 保存步骤变量
        self.dag_state.variables[f"step{step_id}"] = step_vars
        logger.info(f"📊 步骤 {step_id} 输出变量: {step_vars}")
    
    def run_test(self) -> Dict[str, Any]:
        """运行完整测试"""
        logger.info("🚀 启动双角色测试执行器")
        start_time = time.time()
        
        max_iterations = 50
        iteration = 0
        
        try:
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"\n━━━━━━━━━━━━━ 第 {iteration} 轮执行 ━━━━━━━━━━━━━")
                
                # Planner决策下一步
                decision = self.planner.get_next_action(self.plan, self.dag_state)
                
                if decision["action"] == "complete":
                    logger.info("🎉 Planner决定任务完成")
                    break
                
                if decision["action"] == "wait":
                    wait_seconds = decision.get("seconds", 3)
                    logger.info(f"⏱️ Planner要求等待 {wait_seconds} 秒")
                    time.sleep(wait_seconds)
                    continue
                
                # 获取要执行的步骤
                next_step_id = decision["next"]
                if next_step_id <= 0:
                    logger.warning("⚠️ 没有可执行的步骤")
                    break
                    
                step = next((s for s in self.plan["steps"] if s["step_id"] == next_step_id), None)
                if not step:
                    logger.error(f"❌ 步骤 {next_step_id} 不存在")
                    break
                
                # Actor执行步骤
                logger.info(f"🎯 执行步骤 {next_step_id}: {step['name']}")
                result = self.actor.execute_step(step, self.dag_state.variables)
                
                # 记录结果
                self.dag_state.step_results[next_step_id] = result
                self.dag_state.current_step = next_step_id
                
                if result.status == "success":
                    self.dag_state.completed_steps.append(next_step_id)
                    self._extract_step_outputs(step, result)
                    logger.info(f"✅ 步骤 {next_step_id} 执行成功")
                else:
                    # 更新重试次数
                    step["retry_count"] = step.get("retry_count", 0) + 1
                    
                    if step["retry_count"] >= step["max_retry"]:
                        self.dag_state.failed_steps.append(next_step_id)
                        logger.error(f"❌ 步骤 {next_step_id} 失败且达到最大重试次数")
                        
                        if step["is_critical"]:
                            logger.error("💥 关键步骤失败，终止测试")
                            break
                    else:
                        logger.warning(f"⚠️ 步骤 {next_step_id} 失败，将重试 ({step['retry_count']}/{step['max_retry']})")
                
                # 保存状态
                self._save_state()
                
                # 检查完成条件
                if len(self.dag_state.completed_steps) >= len(self.plan["steps"]):
                    logger.info("🎊 所有步骤已完成")
                    break
            
            execution_time = time.time() - start_time
            
            # 生成总结报告
            return self._generate_summary_report(execution_time, iteration)
            
        except Exception as e:
            logger.error(f"💥 测试执行异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time
            }
    
    def _generate_summary_report(self, execution_time: float, iterations: int) -> Dict[str, Any]:
        """生成测试总结报告"""
        total_steps = len(self.plan["steps"])
        completed_count = len(self.dag_state.completed_steps)
        failed_count = len(self.dag_state.failed_steps)
        success_rate = (completed_count / total_steps) * 100 if total_steps > 0 else 0
        
        return {
            "success": completed_count == total_steps,
            "execution_time": execution_time,
            "total_iterations": iterations,
            "statistics": {
                "total_steps": total_steps,
                "completed_steps": completed_count,
                "failed_steps": failed_count,
                "success_rate": round(success_rate, 2)
            },
            "completed_steps": self.dag_state.completed_steps,
            "failed_steps": self.dag_state.failed_steps,
            "final_variables": self.dag_state.variables,
            "device_state": {
                "current_page": device_state.current_page,
                "active_sessions": list(device_state.active_sessions.keys()),
                "final_text": device_state.current_text
            }
        }
    
    def print_summary(self, result: Dict[str, Any]):
        """打印测试总结"""
        print("\n" + "="*80)
        print("🎭 LM Studio 双角色测试总结 (Planner + Actor)")
        print("="*80)
        
        stats = result["statistics"]
        
        if result["success"]:
            print(f"✅ 测试成功完成")
            print(f"⏱️  总执行时间: {result['execution_time']:.2f} 秒")
            print(f"🔄 总执行轮数: {result['total_iterations']} 轮")
            print(f"📊 成功率: {stats['success_rate']}% ({stats['completed_steps']}/{stats['total_steps']})")
            print(f"📱 最终设备状态: {result['device_state']['current_page']}")
        else:
            print(f"❌ 测试未完全成功")
            print(f"⏱️  执行时间: {result['execution_time']:.2f} 秒")
            print(f"📊 完成度: {stats['success_rate']}% ({stats['completed_steps']}/{stats['total_steps']})")
            print(f"💥 失败步骤: {result['failed_steps']}")
        
        print(f"\n📈 执行统计:")
        print(f"  ✅ 成功步骤: {stats['completed_steps']} 个")
        print(f"  ❌ 失败步骤: {stats['failed_steps']} 个")
        print(f"  📋 总步骤数: {stats['total_steps']} 个")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    print("🎭 启动 LM Studio 双角色测试器...")
    print("📡 连接地址: http://localhost:1234")
    print("🎯 测试架构: Planner (策略) + Actor (执行)")
    print("-" * 60)
    
    # 创建双角色协调器
    orchestrator = DualRoleOrchestrator()
    
    # 执行测试
    result = orchestrator.run_test()
    
    # 打印结果
    orchestrator.print_summary(result)


if __name__ == "__main__":
    main()