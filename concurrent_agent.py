#!/usr/bin/env python3
"""
支持并发的Agent类
每个Agent实例拥有独立的日志管理器，实现真正的任务隔离
"""

import json
import time
import re
import uuid
import os
from typing import List, Dict, Any, Optional
from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field

# 导入并发日志管理器
from tools._concurrent_log_manager import LogManager, get_global_log_manager, set_current_task_log_manager

# 导入工具模块
from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios, get_available_device
from tools.wait_tools import wait_for_seconds
from tools.screenshot_tools import get_screenshot
from tools.tap_tools import tap
from tools.slide_tools import slide
from tools.api_ocr_tools import show_page_ocr_result, locate_element_from_ocr
from tools.app_operate_tools import restart_app, background_switch
from tools.smart_input_tools import smart_input_text
from tools.llm_base_tools import start_test, end_test, record_summary, record_issue
from tools._device_status_manage_tools import (
    update_device_status, 
    create_device_status, 
    get_device_status,
)
from tools.check_page_detail_tools import check_ui_bugs, locate_element_from_layout, analysis_now_page

# 导入评价器
from process_judge import TestProcessJudge



class ConcurrentAgent:
    """支持并发的Agent类 - 每个实例拥有独立的日志管理器"""
    
    def __init__(self, 
                 model_name: str = "hf.co/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Qwen3-30B-A3B-Instruct-2507-UD-Q8_K_XL.gguf",
                 base_url: str = "http://localhost:11435",
                 temperature: float = 0.1,
                 task_id: Optional[str] = None,
                 log_manager: Optional[LogManager] = None,
                 is_smart_task: bool = False):
        self.model_name = model_name
        self.base_url = base_url
        self.temperature = temperature
        self.chat_history = []
        
        # 任务和日志管理
        self.task_id = task_id
        self.log_manager = log_manager if log_manager else get_global_log_manager()
        self.is_smart_task = is_smart_task  # 标识是否为智能任务（来自submit_smart_task）
        
        # 任务上下文
        self.task_context = None
        self.current_device_udid = None
        
        # 初始化LLM
        self.llm = ChatOllama(
            model=model_name,
            base_url=base_url,
            temperature=temperature,
            client_kwargs={"trust_env": False}
        )
        
        # 定义工具
        self.tools = self._create_tools()
        
        # 绑定工具到LLM
        try:
            self.llm_with_tools = self.llm.bind_tools(self.tools)
            self.supports_tools = True
            self.log_manager.info_agent(f"模型 {model_name} 支持工具调用")
        except Exception as e:
            self.log_manager.error_agent(f"绑定工具失败: {e}")
            self.llm_with_tools = self.llm
            self.supports_tools = False
        
        # 初始化评价器
        try:
            self.judge = TestProcessJudge()
            self.log_manager.info_agent("测试过程评价器初始化成功")
        except Exception as e:
            self.log_manager.error_agent(f"测试过程评价器初始化失败: {e}")
            self.judge = None
        
        self.log_manager.info_agent(f"ConcurrentAgent初始化完成，模型: {model_name}, 任务ID: {task_id}")
        self.log_manager.info_agent(f"可用工具: {[tool.name for tool in self.tools]}")
    
    def set_task_context(self, task_id: str, round_id: str, port: int):
        """设置任务上下文"""
        try:
            self.task_context = {
                'task_id': task_id,
                'round_id': round_id,
                'port': port,
                'agent_prefix': f"[Agent-{port}]"
            }
            self.log_manager.info_agent(f"Agent-{port} 开始处理任务 {task_id} (轮次: {round_id})")
        except Exception as e:
            self.log_manager.error_agent(f"设置任务上下文失败: {e}")
    
    def reset_device_connections(self):
        """重置设备连接状态"""
        try:
            self.current_device_udid = None
            self.log_manager.info_agent("已重置设备连接状态")
        except Exception as e:
            self.log_manager.error_agent(f"重置设备连接失败: {e}")
    
    def _estimate_token_count(self, text: str) -> int:
        """估算文本的token数量"""
        char_count = len(text)
        estimated_tokens = int(char_count / 3)
        return estimated_tokens
    
    def _calculate_context_usage(self, messages: List) -> Dict[str, Any]:
        """计算当前上下文的使用情况"""
        total_chars = 0
        message_breakdown = []
        
        full_content_list = []
        for i, msg in enumerate(messages):
            content = ""
            if hasattr(msg, 'content'):
                content = str(msg.content)
            else:
                content = str(msg)
            
            chars = len(content)
            tokens = self._estimate_token_count(content)
            total_chars += chars
            full_content_list.append(content)
            
            msg_type = type(msg).__name__
            message_breakdown.append({
                "index": i,
                "type": msg_type,
                "chars": chars,
                "estimated_tokens": tokens,
                "content_preview": content[:100] + "..." if len(content) > 100 else content
            })
        
        total_tokens = self._estimate_token_count("".join(full_content_list))
        context_limit = 40960
        usage_percentage = (total_tokens / context_limit) * 100
        
        return {
            "total_messages": len(messages),
            "total_characters": total_chars,
            "estimated_total_tokens": total_tokens,
            "context_limit": context_limit,
            "usage_percentage": round(usage_percentage, 2),
            "remaining_tokens": context_limit - total_tokens,
            "is_approaching_limit": usage_percentage > 80,
            "is_critical": usage_percentage > 95,
            "message_breakdown": message_breakdown
        }
    
    def _mark_device_testing(self, udid: str, device_name: str = "", platform: str = "") -> bool:
        """标记设备为测试中状态"""
        try:
            current_status = get_device_status(udid)
            
            if not current_status:
                if not device_name or not platform:
                    self.log_manager.error_agent(f"设备 {udid} 不存在，需要提供 device_name 和 platform 参数")
                    return False
                
                create_result = create_device_status(udid, device_name, platform)
                if not create_result:
                    self.log_manager.error_agent(f"创建设备状态失败: {udid}")
                    return False
                
                self.log_manager.info_agent(f"创建新设备状态: {udid} ({device_name}, {platform})")
            
            update_device_status(udid, {
                "status": "testing",
                "start_time": time.time()
            })
            
            self.log_manager.info_agent(f"设备状态更新: {udid} -> testing")
            return True
            
        except Exception as e:
            self.log_manager.error_agent(f"标记设备测试状态失败: {e}")
            return False
    
    def _mark_device_ready(self, udid: str) -> bool:
        """标记设备为就绪状态"""
        try:
            current_status = get_device_status(udid)
            if not current_status:
                self.log_manager.error_agent(f"设备 {udid} 不存在，无法更新状态")
                return False
            
            start_time = current_status.get("start_time", 0)
            test_duration = time.time() - start_time if start_time > 0 else 0
            
            update_device_status(udid, {
                "status": "ready",
                "test_duration": test_duration,
                "start_time": 0.0
            })
            
            self.log_manager.info_agent(f"设备状态更新: {udid} -> ready (测试时长: {test_duration:.1f}秒)")
            return True
            
        except Exception as e:
            self.log_manager.error_agent(f"标记设备就绪状态失败: {e}")
            return False
    
    def _cleanup_device_status(self):
        """清理设备状态"""
        if self.current_device_udid:
            self.log_manager.info_agent(f"🔄 开始清理设备状态: {self.current_device_udid}")
            success = self._mark_device_ready(self.current_device_udid)
            if success:
                self.log_manager.info_agent(f"✅ 设备状态已恢复: {self.current_device_udid}")
            else:
                self.log_manager.error_agent(f"❌ 设备状态恢复失败: {self.current_device_udid}")
            self.current_device_udid = None
        else:
            self.log_manager.info_agent("📝 没有需要清理的设备状态")
    
    def _run_test_evaluation(self):
        """运行测试过程评价"""
        if not self.judge:
            self.log_manager.warning_agent("⚠️ 评价器未初始化，跳过测试评价")
            return
        
        try:
            # 获取当前轮次的日志目录
            current_log_dir = self.log_manager.get_current_log_directory()
            
            if not current_log_dir or not os.path.exists(current_log_dir):
                self.log_manager.warning_agent(f"⚠️ 日志目录不存在，跳过测试评价: {current_log_dir}")
                return
            
            # 检查是否存在 task_structured.log 文件
            task_log_file = os.path.join(current_log_dir, "task_structured.log")
            if not os.path.exists(task_log_file):
                self.log_manager.warning_agent(f"⚠️ 任务日志文件不存在，跳过测试评价: {task_log_file}")
                return
            
            self.log_manager.info_agent(f"🔍 开始对测试过程进行智能评价...")
            self.log_manager.info_agent(f"📁 评价目标: {current_log_dir}")
            
            # 调用评价器，保存到轮次文件夹
            evaluation_result = self.judge.judge_test_execution(
                round_folder_path=current_log_dir,
                save_to_round_folder=True
            )
            
            if evaluation_result and not evaluation_result.startswith("❌"):
                self.log_manager.info_agent("✅ 测试过程评价完成，报告已保存到 judge_report.log")
            else:
                self.log_manager.error_agent(f"❌ 测试过程评价失败: {evaluation_result}")
                
        except Exception as e:
            self.log_manager.error_agent(f"❌ 运行测试评价时出错: {e}")
            import traceback
            self.log_manager.error_agent(f"详细错误信息: {traceback.format_exc()}")
    
    def _create_tools(self) -> List:
        """创建工具列表"""
        
        @tool
        def find_available_device(platform: str) -> str:
            """查找指定平台的可用设备"""
            try:
                udid = get_available_device(platform)
                if udid:
                    device_info = get_device_status(udid)
                    device_name = device_info.get("device_name", "Unknown") if device_info else "Unknown"
                    
                    if self._mark_device_testing(udid, device_name, platform):
                        self.current_device_udid = udid
                        self.log_manager.info_agent(f"📱 自动标记设备为测试中: {udid}")
                        
                        result = {
                            "status": "success",
                            "platform": platform,
                            "udid": udid,
                            "device_name": device_name,
                            "message": f"找到可用的{platform}设备: {udid}，已自动标记为测试中"
                        }
                    else:
                        result = {
                            "status": "error",
                            "platform": platform,
                            "udid": udid,
                            "message": f"找到设备{udid}但标记为测试中状态失败"
                        }
                else:
                    result = {
                        "status": "not_found",
                        "platform": platform,
                        "message": f"没有找到可用的{platform}设备（已连接且状态为ready）"
                    }
                return json.dumps(result, ensure_ascii=False)
            except Exception as e:
                result = {
                    "status": "error",
                    "platform": platform,
                    "error": str(e),
                    "message": f"查找{platform}设备时出错: {str(e)}"
                }
                return json.dumps(result, ensure_ascii=False)
        
        @tool
        def wait_seconds(seconds: int = 5) -> str:
            """等待指定的秒数"""
            return wait_for_seconds(seconds)
        
        @tool
        def take_screenshot(udid: str, description: str = "") -> str:
            """对指定设备进行截图操作"""
            try:
                self.log_manager.info_agent(f"开始为设备 {udid} 进行截图")
                if description:
                    self.log_manager.info_agent(f"截图描述: {description}")
                
                screenshot_result = get_screenshot(udid)
                
                result = {
                    "status": "success",
                    "udid": udid,
                    "local_path": screenshot_result.get("local_path", ""),
                    "image_url": screenshot_result.get("image_url", ""),
                    "description": description,
                    "message": f"成功完成设备 {udid} 的截图操作"
                }
                
                if screenshot_result.get("image_url"):
                    self.log_manager.info_agent(f"截图已上传，URL: {screenshot_result['image_url']}")
                else:
                    self.log_manager.warning_agent("截图上传失败，但本地文件已保存")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"截图操作失败: {str(e)}"
                }
                self.log_manager.error_agent(f"截图操作失败: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def start_device_test(udid: str) -> str:
            """开始设备测试"""
            return start_test(udid)
        
        @tool
        def end_device_test(udid: str) -> str:
            """结束设备测试"""
            return end_test(udid)
        
        @tool
        def tap_device(udid: str, x: int, y: int, description: str = "") -> str:
            """在指定设备上执行点击操作"""
            try:
                self.log_manager.info_agent(f"开始对设备 {udid} 执行点击操作，坐标: ({x}, {y})")
                if description:
                    self.log_manager.info_agent(f"点击描述: {description}")
                
                success = tap(udid, x, y)
                
                if success:
                    result = {
                        "status": "success",
                        "udid": udid,
                        "x": x,
                        "y": y,
                        "description": description,
                        "message": f"成功在设备 {udid} 的坐标 ({x}, {y}) 执行点击操作"
                    }
                    self.log_manager.info_agent(f"点击操作成功完成")
                else:
                    result = {
                        "status": "failed",
                        "udid": udid,
                        "x": x,
                        "y": y,
                        "description": description,
                        "message": f"在设备 {udid} 的坐标 ({x}, {y}) 点击操作失败"
                    }
                    self.log_manager.warning_agent(f"点击操作失败")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "x": x,
                    "y": y,
                    "error": str(e),
                    "message": f"点击操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"点击操作异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def slide_device(udid: str, from_x: float, from_y: float, to_x: float, to_y: float, duration: float = 0.5, description: str = "") -> str:
            """在指定设备上执行滑动操作
重要说明：坐标必须使用屏幕比例值(0-1之间)，而非像素坐标
- from_x, from_y: 滑动起始位置的屏幕比例坐标 (0-1之间)
- to_x, to_y: 滑动结束位置的屏幕比例坐标 (0-1之间)
- 例如：从屏幕中央向下滑动到底部 -> from_x=0.5, from_y=0.5, to_x=0.5, to_y=0.9
            """
            try:
                self.log_manager.info_agent(f"开始对设备 {udid} 执行滑动操作，从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})，持续 {duration}秒")
                if description:
                    self.log_manager.info_agent(f"滑动描述: {description}")
                
                success = slide(udid, from_x, from_y, to_x, to_y, duration)
                
                if success:
                    result = {
                        "status": "success",
                        "udid": udid,
                        "from_x": from_x,
                        "from_y": from_y,
                        "to_x": to_x,
                        "to_y": to_y,
                        "duration": duration,
                        "description": description,
                        "message": f"成功在设备 {udid} 上执行滑动操作：从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})"
                    }
                    self.log_manager.info_agent(f"滑动操作成功完成")
                else:
                    result = {
                        "status": "failed",
                        "udid": udid,
                        "from_x": from_x,
                        "from_y": from_y,
                        "to_x": to_x,
                        "to_y": to_y,
                        "duration": duration,
                        "description": description,
                        "message": f"在设备 {udid} 上滑动操作失败：从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})"
                    }
                    self.log_manager.warning_agent(f"滑动操作失败")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "from_x": from_x,
                    "from_y": from_y,
                    "to_x": to_x,
                    "to_y": to_y,
                    "duration": duration,
                    "error": str(e),
                    "message": f"滑动操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"滑动操作异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def ocr_text_only(udid: str) -> str:
            """对指定设备进行OCR文字识别"""
            try:
                self.log_manager.info_agent(f"开始为设备 {udid} 进行OCR文本识别")
                
                ocr_result = show_page_ocr_result(udid)
                text_result = ocr_result.get("final_text", "")
                image_url = ocr_result.get("image_url", "")
                
                result = {
                    "status": "success",
                    "udid": udid,
                    "text_result": text_result,
                    "image_url": image_url
                }
                
                self.log_manager.info_agent(f"设备 {udid} OCR文本识别完成")
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"OCR文本识别失败: {str(e)}"
                }
                self.log_manager.error_agent(f"OCR文本识别异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        @tool
        def ocr_text_validation(udid: str, target_text: str) -> str:
            """使用OCR校验页面是否包含目标文本（子串匹配，大小写不敏感）"""
            try:
                self.log_manager.info_agent(f"开始OCR文本校验 - 设备: {udid}, 目标文本: '{target_text}'")

                validate_result = locate_element_from_ocr(udid, target_text)

                # 统一返回结构
                result = {
                    "status": validate_result.get("status", "success"),
                    "udid": udid,
                    "target_text": target_text,
                    "found": validate_result.get("found", False),
                    "matches": validate_result.get("matches", []),
                    "image_url": validate_result.get("image_url", ""),
                    "local_path": validate_result.get("local_path", ""),
                    "final_reply": validate_result.get("final_reply", "")
                }

                self.log_manager.info_agent(f"OCR文本校验完成，found={result['found']}，匹配数={len(result['matches'])}")
                return json.dumps(result, ensure_ascii=False)

            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "target_text": target_text,
                    "found": False,
                    "matches": [],
                    "image_url": "",
                    "local_path": "",
                    "final_reply": "没有找到对应的文本",
                    "message": f"OCR文本校验失败: {str(e)}"
                }
                self.log_manager.error_agent(f"OCR文本校验异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def check_page_display(udid: str, scene_desc: str = "页面检查") -> str:
            """使用MLX-VLM模型检查页面展示是否正常"""
            try:
                self.log_manager.info_agent(f"开始检查设备 {udid} 的页面展示")
                if scene_desc:
                    self.log_manager.info_agent(f"场景描述: {scene_desc}")
                
                check_result = check_ui_bugs(udid, scene_desc)
                
                if check_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": check_result["text_result"],
                        "image_url": check_result.get("image_url", ""),
                        "message": f"页面检查失败: {check_result['text_result']}"
                    }
                    self.log_manager.error_agent(f"页面检查失败: {check_result['text_result']}")
                else:
                    text_result = check_result["text_result"]
                    is_normal = "页面展示正常" in text_result
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": check_result.get("image_url", ""),
                        "is_normal": is_normal,
                        "message": f"页面检查完成: {'正常' if is_normal else '异常'}"
                    }
                    self.log_manager.info_agent(f"页面检查完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"页面检查出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"页面检查异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def find_element_on_page(udid: str, element: str, scene_desc: str = "元素查找") -> str:
            """使用MLX-VLM模型在页面上查找指定元素"""
            try:
                self.log_manager.info_agent(f"开始在设备 {udid} 上查找元素: {element}")
                if scene_desc:
                    self.log_manager.info_agent(f"场景描述: {scene_desc}")
                
                find_result = locate_element_from_layout(udid, element, scene_desc)
                
                if find_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": find_result["text_result"],
                        "image_url": find_result.get("image_url", ""),
                        "message": f"元素查找失败: {find_result['text_result']}"
                    }
                    self.log_manager.error_agent(f"元素查找失败: {find_result['text_result']}")
                else:
                    text_result = find_result["text_result"]
                    found = not text_result.startswith("未找到")
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": find_result.get("image_url", ""),
                        "found": found,
                        "element": element,
                        "message": f"元素查找完成: {'找到' if found else '未找到'}"
                    }
                    self.log_manager.info_agent(f"元素查找完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"元素查找出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"元素查找异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def restart_application(udid: str) -> str:
            """重启指定设备上的应用"""
            try:
                self.log_manager.info_agent(f"开始重启设备 {udid} 上的应用")
                
                result = restart_app(udid)
                
                if result == "success":
                    response = {
                        "status": "success",
                        "udid": udid,
                        "message": f"成功重启设备 {udid} 上的应用"
                    }
                    self.log_manager.info_agent(f"应用重启成功完成")
                elif result == "unknown_device":
                    response = {
                        "status": "error",
                        "udid": udid,
                        "error": "unknown_device",
                        "message": f"设备 {udid} 不存在或未配置"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 不存在")
                elif result == "device_offline":
                    response = {
                        "status": "error", 
                        "udid": udid,
                        "error": "device_offline",
                        "message": f"设备 {udid} 已掉线"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 掉线")
                else:
                    response = {
                        "status": "failed",
                        "udid": udid,
                        "error": result,
                        "message": f"重启设备 {udid} 应用失败: {result}"
                    }
                    self.log_manager.warning_agent(f"应用重启失败: {result}")
                
                return json.dumps(response, ensure_ascii=False)
                
            except Exception as e:
                error_response = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"重启应用操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"重启应用操作异常: {e}")
                return json.dumps(error_response, ensure_ascii=False)
        
        @tool
        def app_background_switch(udid: str) -> str:
            """让美团应用退到后台，等待3秒后再切回前台"""
            try:
                self.log_manager.info_agent(f"开始执行应用后台切换操作 - 设备: {udid}")
                
                result = background_switch(udid)
                
                if result == "success":
                    response = {
                        "status": "success",
                        "udid": udid,
                        "message": f"成功完成设备 {udid} 的应用后台切换操作"
                    }
                    self.log_manager.info_agent(f"应用后台切换操作成功完成")
                elif result == "unknown_device":
                    response = {
                        "status": "error",
                        "udid": udid,
                        "error": "unknown_device",
                        "message": f"设备 {udid} 不存在或未配置"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 不存在")
                elif result == "device_offline":
                    response = {
                        "status": "error", 
                        "udid": udid,
                        "error": "device_offline",
                        "message": f"设备 {udid} 已掉线"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 掉线")
                else:
                    response = {
                        "status": "failed",
                        "udid": udid,
                        "error": result,
                        "message": f"设备 {udid} 后台切换操作失败: {result}"
                    }
                    self.log_manager.warning_agent(f"应用后台切换操作失败: {result}")
                
                return json.dumps(response, ensure_ascii=False)
                
            except Exception as e:
                error_response = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"后台切换操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"后台切换操作异常: {e}")
                return json.dumps(error_response, ensure_ascii=False)
        
        @tool
        def record_agent_summary(summary_text: str, summary_type: str = "步骤总结") -> str:
            """记录Agent的实时流程总结和想法"""
            try:
                self.log_manager.info_agent(f"开始记录Agent流程总结: {summary_type}")
                
                result = record_summary(summary_text, summary_type)
                
                self.log_manager.info_agent(f"Agent流程总结记录完成")
                return result
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "summary_text": summary_text,
                    "summary_type": summary_type,
                    "error": str(e),
                    "message": f"记录Agent流程总结失败: {str(e)}"
                }
                self.log_manager.error_agent(f"记录Agent流程总结异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def record_test_issue(issue_text: str, issue_type: str = "测试问题", severity: str = "中等") -> str:
            """记录测试过程中发现的问题"""
            try:
                self.log_manager.warning_agent(f"开始记录测试问题: {issue_type}, 严重程度: {severity}")
                
                result = record_issue(issue_text, issue_type, severity)
                
                self.log_manager.warning_agent(f"测试问题记录完成")
                return result
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "issue_text": issue_text,
                    "issue_type": issue_type,
                    "severity": severity,
                    "error": str(e),
                    "message": f"记录测试问题失败: {str(e)}"
                }
                self.log_manager.error_agent(f"记录测试问题异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        @tool
        def input_text_smart(udid: str, text: str, element_index: int = None) -> str:
            """智能文本输入工具"""
            try:
                self.log_manager.info_agent(f"开始智能文本输入操作 - 设备: {udid}, 文本: '{text}', 元素索引: {element_index}")

                result = smart_input_text(udid, text, element_index)

                self.log_manager.info_agent(f"设备 {udid} 智能文本输入操作完成")
                return result

            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text": text,
                    "element_index": element_index,
                    "error": str(e),
                    "message": f"智能文本输入失败: {str(e)}"
                }
                self.log_manager.error_agent(f"智能文本输入异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        @tool
        def analyze_meituan_page(udid: str, action_description: str = "", model: str = "qwen2.5vl:3b") -> str:
            """分析美团app界面截图，识别当前界面特征和界面类型。
            
            参数说明：
            - udid: 设备UDID
            - action_description: 进入当前页面的动作描述，建议格式如："点击搜索框后"、"滑动页面后"、"重启应用后"等
            - model: 使用的模型名称
            
            使用示例：
            - analyze_meituan_page(udid, "点击外卖按钮后")
            - analyze_meituan_page(udid, "输入搜索文字后")
            """
            try:
                self.log_manager.info_agent(f"开始分析设备 {udid} 的美团app界面，模型: {model}")
                if action_description:
                    self.log_manager.info_agent(f"进入动作描述: {action_description}")
                
                analysis_result = analysis_now_page(udid, action_description, model)
                
                if analysis_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": analysis_result["text_result"],
                        "message": f"美团界面分析失败: {analysis_result['text_result']}"
                    }
                    self.log_manager.error_agent(f"美团界面分析失败: {analysis_result['text_result']}")
                else:
                    text_result = analysis_result["text_result"]
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": analysis_result.get("image_url", ""),
                        "local_path": analysis_result.get("local_path", ""),
                        "action_description": action_description,
                        "model": model,
                        "message": "美团界面分析完成"
                    }
                    self.log_manager.info_agent(f"美团界面分析完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "message": f"美团界面分析出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"美团界面分析异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        return [find_available_device, start_device_test, end_device_test, wait_seconds, take_screenshot, tap_device, slide_device, ocr_text_only, ocr_text_validation, check_page_display, find_element_on_page, restart_application, app_background_switch, record_agent_summary, record_test_issue, input_text_smart, analyze_meituan_page]
    
    def _create_system_message(self) -> str:
        """创建系统消息"""
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description}")
        
        tools_text = "\n".join(tool_descriptions)
        
        if self.is_smart_task:
            # 智能任务：简化版系统提示词，避免重复规划和规则
            return f"""你是一个专业的移动设备测试执行助手。你的任务是严格按照提供的结构化测试计划执行操作。

可用工具：
{tools_text}

**核心执行原则：**
- 严格按照用户提供的步骤顺序执行，不要跳过或修改任何步骤
- 一次只调用一个工具，等待上一步执行完成后，根据执行结果再进行下一步

**步骤关联性要求：**
- **设备一致性**: 确保所有操作都在同一设备上执行，udid保持一致
- **结果传递**: 上一步的执行结果可能会影响下一步的参数，请仔细阅读工具返回结果

**特别注意：**
- 用户提供的是已经经过规划的结构化测试指令，请直接执行，无需额外规划
- 如果工具调用失败，尝试1-2次后继续下一步
- 完成所有步骤后调用record_agent_summary记录总结

请用中文回答，专注执行而非规划，特别注意步骤间的关联性。"""
        else:
            # 普通任务：完整版系统提示词，包含完整的规划和规则
            return f"""你是一个专业的移动设备测试助手。你可以使用以下工具来帮助用户：

工具说明：
{tools_text}

**重要的测试工作流程：**
1. **查找设备**: 使用 find_available_device 工具查找可用设备，每次会返回对应设备的 udid
2. **开始测试**: 获取到设备的 udid 后必须要使用 start_device_test 工具为选定的设备启动测试环境（不管是 ios 还是 Android），这一步是必须要做的，否则后续的工具调用就会出现问题
3. **执行操作**: 使用 take_screenshot、wait_seconds 等工具执行具体测试操作，具体使用什么工具，请根据用户的要求来决定
4. **记录问题**: 使用 record_test_issue 工具记录测试过程中发现的问题
5. **结束测试**: 当用户明确表示"完成测试"、"结束测试"或类似指示时，使用 end_device_test 工具结束测试
6. **记录结论**: 完成一轮测试后，要使用 record_agent_summary 工具记录之前步骤的操作流程，并做出一个测试总结，涵盖测试流程以及总体的结论

**关键执行规则：**
- **必须逐步执行**: 用户会给出包含多个步骤的操作指令，必须按顺序逐一执行每个步骤，不能跳过或提前结束
- **单一工具调用**: 一般来说我给你的指令都会比较复杂，需要多次调用工具才能完成，但是我希望你一次只调用一个工具，然后我会将其执行的结果告诉你，你再根据结果来决定下一步做什么，尽量不要一次调用多个工具
- **结束条件**: 当用户的步骤中明确包含"结束测试"、"完成测试"、"测试结束"等明确结束指示时，你要先调用 end_device_test 工具结束测试，然后调用 record_agent_summary 工具记录总结结论，
- **持续执行**: 在没有明确结束指示的情况下，按照用户最开始指令中的所有步骤，依次进行
- **错误情况**: 如果工具调用失败，尝试重复 1-2 次，仍然失败则结束测试

**工具使用规则：**
- 在使用 take_screenshot 之前，必须先调用 start_device_test 启动测试环境
- 只有在用户明确表示测试完成时，才能调用 end_device_test 结束测试
- 每个设备在一次会话中只需要调用一次 start_device_test 和 end_device_test
- 工具返回JSON格式的结构化数据，请解析并用友好的语言回答

**任务执行策略：**
1. 你要先完整理解用户的所有需求和步骤，然后有一个大致的任务执行规划
2. 一般来说所有的任务一开始都需要先查找可用设备，然后为选定设备启动测试环境
3. 一次只能执行一个工具，然后我会将其执行的结果告诉你，你再根据结果来决定下一步做什么
4. 当且仅当用户的步骤中明确表示"结束测试"或者类似语句时，才结束本次测试的流程，你要尽可能的按照用户指定的操作路径来完成一步步的操作（工具调用）
5. 一般来说用户的指令会是"1.做A; 2.做B; 3.做C; 4.结束测试"，你必须依次执行A、B、C，然后才能结束

**注意事项：**
- 测试会话的启动和结束将由系统自动管理
- 设备状态管理由系统自动处理
- 重要：每次开始新的测试会话时，系统会自动重启美团应用，确保测试从美团App首页开始
- 重要：每次调用工具前都重新查看用户要求，思考下一步工具调用是什么
- 阶段性总结不代表测试结束: 完成OCR识别、截图等中间操作后，可以进行阶段性总结，调用 record_agent_summary 来记录截断性的结论，但是你依然要思考下一个步骤是什么，来接着完成用户最开始的指令
- 遇到不确定的问题不要来询问用户: 你要时刻想着按照用户原初的指令来执行对应流程，遇到不确定的问题，请先思考，然后调用工具来解决，不要来询问用户下一步怎么做，直至你完成了全部流程

请用中文回答用户问题，并严格按照上述工作流程执行。"""
    
    def chat(self, user_input: str) -> str:
        """处理用户输入并生成响应"""
        try:
            # 设置当前任务的日志管理器，确保工具调用使用同一个日志管理器
            set_current_task_log_manager(self.log_manager)
            
            # 记录用户输入
            self.log_manager.info_agent(f"收到用户输入: {user_input}")
            
            # 添加用户消息到历史
            self.chat_history.append(HumanMessage(content=user_input))
            
            # 执行对话逻辑
            if self.supports_tools:
                response = self._chat_with_tools(user_input)
            else:
                response = self._chat_without_tools(user_input)
            
            # 清理设备状态
            self._cleanup_device_status()
            
            # 运行测试过程评价
            self._run_test_evaluation()
            
            return response
                
        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            self.log_manager.error_agent(error_msg)
            
            self._cleanup_device_status()
            
            # 即使出现异常，也尝试运行测试过程评价
            try:
                self._run_test_evaluation()
            except Exception as eval_error:
                self.log_manager.error_agent(f"异常情况下运行评价失败: {eval_error}")
            
            return error_msg
    
    def _chat_with_tools(self, user_input: str) -> str:
        """使用工具调用模式"""
        # 开始任务结构化日志记录
        task_id = self.log_manager.start_task(user_input)
        
        # 构建完整的消息列表
        messages = [
            SystemMessage(content=self._create_system_message()),
            *self.chat_history
        ]
        
        # 计算上下文使用情况
        context_usage = self._calculate_context_usage(messages)
        
        # 记录上下文使用统计
        self.log_manager.info_agent(f"📊 上下文使用统计:")
        self.log_manager.info_agent(f"  总消息数: {context_usage['total_messages']}")
        self.log_manager.info_agent(f"  总字符数: {context_usage['total_characters']:,}")
        self.log_manager.info_agent(f"  估算token数: {context_usage['estimated_total_tokens']:,}")
        self.log_manager.info_agent(f"  使用率: {context_usage['usage_percentage']:.1f}% ({context_usage['estimated_total_tokens']}/{context_usage['context_limit']})")
        
        if context_usage['is_critical']:
            self.log_manager.error_agent("🚨 上下文使用率超过95%，可能导致模型遗忘早期信息!")
        elif context_usage['is_approaching_limit']:
            self.log_manager.warning_agent("⚠️ 上下文使用率超过80%，建议注意context长度")
        
        self.log_manager.log_llm_input(messages, self.model_name)
        
        # 支持多轮工具调用的循环
        max_rounds = 30
        round_count = 0
        consecutive_failures = 0
        
        while round_count < max_rounds:
            round_count += 1
            
            # 开始新轮次的结构化日志记录
            current_round = self.log_manager.start_round()
            self.log_manager.info_agent(f"━━━ 开始第 {current_round} 轮执行 ━━━")
            
            # 调用LLM
            self.log_manager.info_agent(f"正在询问模型下一步操作...")
            response = self.llm_with_tools.invoke(messages)
            
            # 记录LLM详细输出
            self.log_manager.log_llm_output(response, self.model_name)
            
            # 检查是否有工具调用
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行第一个工具调用
                tool_call = response.tool_calls[0]
                tool_name = tool_call.get('name', 'unknown')
                
                if len(response.tool_calls) > 1:
                    self.log_manager.info_agent(f"⚠️  模型请求了 {len(response.tool_calls)} 个工具，但本轮只执行第一个: '{tool_name}'")
                else:
                    self.log_manager.info_agent(f"🔧 准备执行工具: '{tool_name}'")
                
                self.log_manager.log_model_decision(tool_calls=response.tool_calls, direct_reply=False)
                
                # 添加AI响应到历史
                self.chat_history.append(response)
                
                # 执行工具调用
                self.log_manager.info_agent(f"⏳ 正在执行工具 '{tool_name}'...")
                
                tool_start_time = time.time()
                tool_result = self._execute_tool_call(tool_call)
                tool_duration = time.time() - tool_start_time
                
                # 检查工具执行结果
                tool_success = False
                try:
                    result_data = json.loads(tool_result)
                    if result_data.get('status') in ['success', 'completed']:
                        tool_success = True
                        consecutive_failures = 0
                    elif result_data.get('status') in ['error', 'failed']:
                        consecutive_failures += 1
                        self.log_manager.warning_agent(f"⚠️ 工具执行失败，连续失败次数: {consecutive_failures}")
                    else:
                        tool_success = True
                        consecutive_failures = 0
                except:
                    if 'error' in tool_result.lower() or 'failed' in tool_result.lower():
                        consecutive_failures += 1
                        self.log_manager.warning_agent(f"⚠️ 工具执行可能失败，连续失败次数: {consecutive_failures}")
                    else:
                        tool_success = True
                        consecutive_failures = 0
                
                # 记录结构化工具执行日志
                self.log_manager.log_tool_execution(
                    tool_name=tool_name,
                    tool_args=tool_call.get('args', {}),
                    tool_result=tool_result,
                    duration=tool_duration
                )
                
                if tool_success:
                    self.log_manager.info_agent(f"✅ 工具 '{tool_name}' 执行成功")
                else:
                    self.log_manager.warning_agent(f"⚠️ 工具 '{tool_name}' 执行可能失败")
                
                # 记录工具执行结果
                self.log_manager.log_llm_tool_result(tool_name, tool_result, self.model_name)
                self.chat_history.append(
                    ToolMessage(
                        content=tool_result,
                        tool_call_id=tool_call.get('id', 'unknown')
                    )
                )
                
                # # 🆕 新增：添加简单的进度提醒
                # progress_reminder = self._create_simple_progress_reminder(
                #     user_input=user_input,
                #     current_round=round_count
                # )
                # self.chat_history.append(SystemMessage(content=progress_reminder))
                # self.log_manager.info_agent(f"📋 已添加进度提醒 (第{round_count}轮工具调用)")
                
                # 检查是否需要提前终止
                if consecutive_failures >= 3:
                    self.log_manager.error_agent(f"❌ 连续失败次数过多 ({consecutive_failures})，可能存在系统性问题")
                    self.log_manager.error_agent("🛑 为避免浪费资源，提前终止执行")
                    break
                
                # 更新消息列表，准备下一轮
                messages = [
                    SystemMessage(content=self._create_system_message()),
                    *self.chat_history
                ]
                
                # 继续下一轮循环
                continue
            else:
                # 没有工具调用，检查任务完成情况
                if self.is_smart_task:
                    # 智能任务：直接认为任务完成，无需硬编码的"结束测试"判断
                    self.chat_history.append(response)
                    
                    # 记录最终回复到结构化日志
                    self.log_manager.log_final_reply(response.content)
                    self.log_manager.log_model_decision(direct_reply=True)
                    
                    self.log_manager.info_agent(f"🎉 智能任务执行完成！")
                    self.log_manager.info_agent(f"📊 总执行轮数: {round_count} 轮")
                    
                    # 结束任务结构化日志记录
                    self.log_manager.end_task(status="成功")
                    
                    return response.content
                else:
                    # 普通任务：保持原有的"结束测试"关键词判断逻辑
                    end_keywords = ["结束测试", "完成测试", "测试结束", "最后结束测试"]
                    user_wants_end = any(keyword in user_input for keyword in end_keywords)
                    
                    if user_wants_end:
                        # 用户明确要求结束测试
                        self.chat_history.append(response)
                        
                        # 记录最终回复到结构化日志
                        self.log_manager.log_final_reply(response.content)
                        self.log_manager.log_model_decision(direct_reply=True)
                        
                        self.log_manager.info_agent(f"🎉 用户明确要求结束测试，任务完成！")
                        self.log_manager.info_agent(f"📊 总执行轮数: {round_count} 轮")
                        
                        # 结束任务结构化日志记录
                        self.log_manager.end_task(status="成功")
                        
                        return response.content
                    else:
                        # 用户没有明确要求结束，但模型停止了工具调用
                        self.log_manager.warning_agent(f"⚠️ 模型在第 {round_count} 轮停止工具调用，但用户未明确要求结束测试")
                        
                        # 添加提示信息，要求模型继续执行
                        reminder_msg = SystemMessage(content="""
警告：你似乎提前结束了任务执行。请检查用户的原始指令，确保所有步骤都已完成。
只有当用户明确说出"结束测试"、"完成测试"等结束指示时，才能停止工具调用。
如果还有未完成的步骤，请继续执行相应的工具。
""")
                        
                        # 将模型回复和提醒添加到历史
                        self.chat_history.append(response)
                        self.chat_history.append(reminder_msg)
                        
                        # 更新消息列表，强制模型重新思考
                        messages = [
                            SystemMessage(content=self._create_system_message()),
                            *self.chat_history
                        ]
                        
                        self.log_manager.info_agent(f"🔄 发送提醒信息，要求模型继续执行...")
                        # 继续下一轮
                        continue
        
        # 达到最大轮数限制
        if round_count >= max_rounds:
            self.log_manager.warning_agent(f"达到最大工具调用轮数限制 ({max_rounds})，强制结束")
            termination_reason = "超时"
        elif consecutive_failures >= 3:
            termination_reason = "连续失败"
        else:
            termination_reason = "其他原因"
        
        # 结束任务结构化日志记录
        self.log_manager.end_task(status=termination_reason)
        
        # 返回最后的响应
        if hasattr(response, 'content'):
            self.chat_history.append(response)
            return response.content
        else:
            return f"任务执行终止 ({termination_reason})，请重新开始"
    
    def _chat_without_tools(self, user_input: str) -> str:
        """不使用工具的对话模式"""
        messages = [
            SystemMessage(content="你是一个友好的AI助手，请用中文回答问题。"),
            *self.chat_history
        ]
        
        response = self.llm.invoke(messages)
        self.chat_history.append(response)
        
        return response.content
    
    def _create_simple_progress_reminder(self, user_input: str, current_round: int) -> str:
        """创建简单的进度提醒"""
        
        reminder = f"""
════════════════════════════════════════════════════════════════
📋 **进度检查提醒** (第{current_round}轮工具调用)
════════════════════════════════════════════════════════════════

🎯 **用户原始指令**：
{user_input}

💭 **请你现在思考一下**：
1. 我刚刚完成了什么工具调用？
2. 根据用户的指令，我现在进行到了哪一步？
3. 按照用户指令，我下一步应该做什么？

⚠️ **重要提醒**：
- 请严格按照用户指令的步骤顺序执行
- 不要跳过任何步骤，也不要提前结束
- 每完成一个工具调用后，都要思考下一步是什么
- 只有用户明确说"结束测试"或所有步骤都完成时才能停止

请基于以上思考，继续执行下一步操作。
════════════════════════════════════════════════════════════════
"""
        
        return reminder
    
    def _execute_tool_call(self, tool_call: Dict[str, Any]) -> str:
        """执行工具调用"""
        try:
            tool_name = tool_call['name']
            tool_args = tool_call.get('args', {})
            
            self.log_manager.info_agent(f"开始执行工具: {tool_name}, 参数: {tool_args}")
            
            # 查找并执行工具
            for tool in self.tools:
                if tool.name == tool_name:
                    result = tool.func(**tool_args)
                    self.log_manager.info_agent(f"工具 {tool_name} 执行成功")
                    return result
            
            error_msg = f"未找到工具: {tool_name}"
            self.log_manager.error_agent(error_msg)
            return json.dumps({
                "status": "error",
                "message": error_msg
            }, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"执行工具 {tool_call.get('name', 'unknown')} 时出错: {str(e)}"
            self.log_manager.error_agent(error_msg)
            return json.dumps({
                "status": "error",
                "error": str(e),
                "message": error_msg
            }, ensure_ascii=False)
    
    def clear_history(self):
        """清除聊天历史"""
        self.chat_history.clear()
        self.log_manager.info_agent("聊天历史已清除")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools]
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "supports_tools": self.supports_tools,
            "task_id": self.task_id
        }


if __name__ == "__main__":
    # 测试并发Agent
    print("🧪 测试ConcurrentAgent")
    
    agent = ConcurrentAgent(task_id="test_001")
    
    model_info = agent.get_model_info()
    print(f"✅ Agent初始化成功")
    print(f"🤖 模型: {model_info['model_name']}")
    print(f"🔧 工具支持: {'是' if model_info['supports_tools'] else '否'}")
    print(f"📋 任务ID: {model_info['task_id']}")
    
    print("✅ 测试完成")