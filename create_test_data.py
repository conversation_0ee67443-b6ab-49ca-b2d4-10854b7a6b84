#!/usr/bin/env python3
"""
创建测试数据用于验证RAG系统功能
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from rag_system.database_manager import create_database_manager
from rag_system.data_models import DatabaseConfig, InstructionMapping, JudgeReport, PlatformType

def create_test_instruction_mappings(db_manager):
    """创建测试指令映射数据"""
    try:
        print("正在创建测试指令映射...")
        
        test_mappings = [
            {
                "original_instruction": "点击地址选择器并选择北京市朝阳区",
                "structured_plan": {
                    "summary": "地址选择器操作",
                    "platform": "ios",
                    "steps": [
                        {"action": "find_element", "selector": "地址选择器", "tool": "element_finder"},
                        {"action": "tap", "element": "地址选择器", "tool": "ui_operator"},
                        {"action": "select_option", "option": "北京市朝阳区", "tool": "ui_operator"}
                    ]
                },
                "platform": "ios",
                "conversion_quality_score": 0.9,
                "reuse_count": 3
            },
            {
                "original_instruction": "在设置页面中打开通知权限",
                "structured_plan": {
                    "summary": "通知权限设置",
                    "platform": "android", 
                    "steps": [
                        {"action": "navigate", "target": "设置", "tool": "navigator"},
                        {"action": "find_element", "selector": "通知", "tool": "element_finder"},
                        {"action": "tap", "element": "通知权限开关", "tool": "ui_operator"}
                    ]
                },
                "platform": "android",
                "conversion_quality_score": 0.85,
                "reuse_count": 2
            },
            {
                "original_instruction": "截屏并保存到相册",
                "structured_plan": {
                    "summary": "截屏保存操作",
                    "platform": "ios",
                    "steps": [
                        {"action": "screenshot", "save_path": "/tmp/screenshot.png", "tool": "screenshot_tools"},
                        {"action": "save_to_album", "file_path": "/tmp/screenshot.png", "tool": "file_manager"}
                    ]
                },
                "platform": "ios",
                "conversion_quality_score": 0.95,
                "reuse_count": 5
            }
        ]
        
        for i, mapping_data in enumerate(test_mappings):
            mapping = InstructionMapping(
                mapping_id=f"test_mapping_{i+1:03d}",
                original_instruction=mapping_data["original_instruction"],
                structured_plan=mapping_data["structured_plan"],
                conversion_timestamp=datetime.now(),
                planner_model="gpt-4",
                conversion_context={"test_data": True},
                conversion_quality_score=mapping_data["conversion_quality_score"],
                plan_complexity_score=0.7,
                reuse_count=mapping_data["reuse_count"],
                success_reuse_count=mapping_data["reuse_count"] - 1,
                effectiveness_rating=0.8,
                source_execution_ids=[f"exec_{i+1:03d}"],
                platform=PlatformType(mapping_data["platform"]),
                human_reviewed=False,
                human_quality_score=None,
                human_notes=None,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                tags=["test", "demo"],
                metadata={"created_by": "test_script"}
            )
            
            success = db_manager.save_instruction_mapping(mapping)
            if success:
                print(f"✅ 创建指令映射 {i+1}: {mapping_data['original_instruction'][:30]}...")
            else:
                print(f"❌ 创建指令映射 {i+1} 失败")
                
    except Exception as e:
        print(f"❌ 创建测试指令映射失败: {e}")
        import traceback
        traceback.print_exc()

def create_test_judge_reports(db_manager):
    """创建测试Judge报告数据"""
    try:
        print("正在创建测试Judge报告...")
        
        test_reports = [
            {
                "round_id": "round_000001_20250806_160000",
                "overall_success": True,
                "success_score": 0.9,
                "confidence_score": 0.85,
                "detailed_analysis": "测试执行成功，所有步骤都按预期完成。地址选择器操作准确，用户界面响应及时。",
                "human_modified": False
            },
            {
                "round_id": "round_000002_20250806_160100", 
                "overall_success": False,
                "success_score": 0.3,
                "confidence_score": 0.8,
                "detailed_analysis": "测试执行失败，通知权限设置页面无法找到预期元素。可能由于系统版本差异导致UI变化。",
                "human_modified": True,
                "human_override_success": False,
                "human_override_score": 0.2,
                "human_notes": "确认失败，UI确实发生了变化，需要更新元素选择器"
            },
            {
                "round_id": "round_000003_20250806_160200",
                "overall_success": True,
                "success_score": 0.95,
                "confidence_score": 0.9,
                "detailed_analysis": "截屏功能执行完美，图片质量高，保存路径正确。整个流程无任何问题。",
                "human_modified": True,
                "human_override_success": True,
                "human_override_score": 1.0,
                "human_notes": "优秀的执行结果，可以作为最佳实践案例"
            }
        ]
        
        for i, report_data in enumerate(test_reports):
            report = JudgeReport(
                report_id=f"judge_report_{i+1:03d}",
                execution_id=f"exec_{i+1:03d}",
                round_id=report_data["round_id"],
                judge_model="qwen-30b",
                judge_timestamp=datetime.now(),
                judge_version="1.0",
                
                overall_success=report_data["overall_success"],
                success_score=report_data["success_score"],
                confidence_score=report_data["confidence_score"],
                
                test_quality_score=0.8,
                plan_quality_score=0.85,
                execution_quality_score=0.9,
                target_achievement_score=report_data["success_score"],
                
                detailed_analysis=report_data["detailed_analysis"],
                key_issues=["元素查找困难"] if not report_data["overall_success"] else [],
                improvement_suggestions=["更新元素选择器", "添加重试机制"] if not report_data["overall_success"] else ["保持当前实现"],
                best_practices=["清晰的步骤分解", "准确的元素定位"],
                
                failure_points=[],
                error_categories=[],
                root_cause_analysis="UI变化导致元素选择器失效" if not report_data["overall_success"] else None,
                
                human_modified=report_data["human_modified"],
                human_override_success=report_data.get("human_override_success"),
                human_override_score=report_data.get("human_override_score"),
                human_notes=report_data.get("human_notes"),
                human_modifier="test_user" if report_data["human_modified"] else None,
                human_modified_at=datetime.now() if report_data["human_modified"] else None,
                
                raw_judge_output=f"原始AI输出: {report_data['detailed_analysis']}",
                judge_thinking_process="分析步骤: 1. 检查执行状态 2. 评估结果质量 3. 给出建议",
                
                created_at=datetime.now(),
                updated_at=datetime.now(),
                
                metadata={"created_by": "test_script", "test_data": True}
            )
            
            success = db_manager.save_judge_report(report)
            if success:
                print(f"✅ 创建Judge报告 {i+1}: {report_data['round_id']}")
            else:
                print(f"❌ 创建Judge报告 {i+1} 失败")
                
    except Exception as e:
        print(f"❌ 创建测试Judge报告失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🧪 开始创建测试数据...")
    print("=" * 50)
    
    # 创建数据库管理器
    config = DatabaseConfig()
    db_manager = create_database_manager(config)
    
    # 创建测试数据
    create_test_instruction_mappings(db_manager)
    print()
    create_test_judge_reports(db_manager)
    
    print("=" * 50)
    print("🎉 测试数据创建完成!")
    print("\n现在您可以:")
    print("1. 启动Web界面: python rag_system/web_interface.py")
    print("2. 访问 /judge_reports 查看Judge报告")
    print("3. 访问 /instruction_mappings 查看指令映射")
    print("4. 测试搜索、编辑、删除等功能")

if __name__ == "__main__":
    main()