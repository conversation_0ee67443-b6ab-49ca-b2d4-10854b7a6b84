#!/usr/bin/env python3
"""
简化的并发模式启动脚本
启动两个 Ollama 实例并验证状态
"""

import os
import time
import requests
from ollama_simple_manager import ollama_manager, DEFAULT_MODEL, OLLAMA_PORTS, OLLAMA_CONFIGS

def check_ollama_command():
    """检查 ollama 命令是否可用"""
    return os.system("which ollama > /dev/null 2>&1") == 0

def verify_instances():
    """验证实例是否正常运行"""
    print("🔍 验证实例状态...")
    
    status = ollama_manager.get_status()
    running_count = 0
    
    for port, info in status.items():
        # 启动阶段只需要检查服务器是否运行，模型会在预加载阶段加载
        if info['server_running']:
            if info['model_loaded']:
                print(f"   ✅ 端口 {port}: {info['model']} 运行正常")
            else:
                print(f"   ✅ 端口 {port}: 服务器运行正常，等待模型加载")
            running_count += 1
        else:
            print(f"   ❌ 端口 {port}: 服务器未运行")
    
    return running_count

def main():
    print("🚀 启动简化并发模式...")
    print("📋 配置信息:")
    for config in OLLAMA_CONFIGS:
        print(f"   端口 {config['port']}: {config['model']}")
    print(f"📋 目标端口: {OLLAMA_PORTS}")
    
    # 检查 ollama 命令
    if not check_ollama_command():
        print("❌ 错误: 找不到 ollama 命令，请确保已安装 Ollama")
        return False
    
    print("✅ Ollama 命令可用")
    
    # 确保日志目录存在
    os.makedirs("log/ollama", exist_ok=True)
    
    # 启动所有实例
    print("\n📦 启动 Ollama 实例...")
    if not ollama_manager.start_all_instances():
        print("❌ 实例启动失败")
        return False
    
    # 验证实例
    print("\n🔍 验证实例...")
    running_count = verify_instances()
    
    if running_count == 0:
        print("❌ 没有实例正常运行")
        return False
    elif running_count == 1:
        print("⚠️  只有一个实例运行，将以单实例模式工作")
    else:
        print(f"✅ {running_count} 个实例正常运行")
    
    # 预加载模型
    print("\n📦 预加载模型...")
    ollama_manager.preload_all_models()
    
    # 验证预加载结果
    print("\n🔍 验证模型加载状态...")
    ready_ports = ollama_manager.get_ready_ports()
    final_status = ollama_manager.get_status()
    
    print("=== 最终状态检查 ===")
    for port, info in final_status.items():
        server_status = "✅运行中" if info['server_running'] else "❌未运行"
        model_status = f"✅{info['model']}" if info['model_loaded'] else "❌未加载"
        print(f"   端口 {port}: 服务器={server_status}, 模型={model_status}")
    
    if len(ready_ports) >= 2:
        print(f"\n🎉 成功！{len(ready_ports)} 个实例模型已加载，支持并发模式")
        print("🌐 现在可以启动 API 服务器:")
        print("   python api_server.py")
    elif len(ready_ports) == 1:
        print(f"\n⚠️  警告：只有 {len(ready_ports)} 个实例模型已加载，将以单实例模式工作")
        print("🌐 可以启动 API 服务器，但无法并发:")
        print("   python api_server.py")
    else:
        print("\n❌ 错误：没有实例成功加载模型")
        print("请检查:")
        print("1. 模型目录是否存在且包含模型文件")
        print("2. 端口是否被其他进程占用")
        print("3. Ollama 实例日志: log/ollama/")
        return False
    
    print("\n📊 验证命令:")
    for port in OLLAMA_PORTS:
        print(f"   curl http://localhost:{port}/api/ps")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n💡 提示: Ollama 实例将持续在后台运行")
            print("   使用 'ps aux | grep ollama' 查看进程")
            print("   日志文件位于: log/ollama/")
        else:
            exit(1)
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
        exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        exit(1)