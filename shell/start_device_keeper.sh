#!/bin/bash
# 设备状态保持器启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== 设备状态保持器启动脚本 ==="
echo "当前目录: $SCRIPT_DIR"
echo "时间: $(date)"
echo

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 python3 命令"
    exit 1
fi

# 检查必要的工具
echo "检查依赖工具..."
tools_missing=false

if ! command -v adb &> /dev/null; then
    echo "警告: 未找到 adb 命令，Android设备管理可能不可用"
    tools_missing=true
fi

if ! command -v idevice_id &> /dev/null; then
    echo "警告: 未找到 idevice_id 命令，iOS设备发现可能不可用"
    tools_missing=true
fi

if ! command -v appium &> /dev/null; then
    echo "警告: 未找到 appium 命令，移动应用自动化可能不可用"
    tools_missing=true
fi

if $tools_missing; then
    echo
    echo "注意: 某些工具缺失，可能影响部分功能"
    echo "是否继续启动？(y/n)"
    read -r response
    if [[ "$response" != "y" && "$response" != "Y" ]]; then
        echo "启动已取消"
        exit 1
    fi
fi

# 创建日志目录
mkdir -p log

# 检查是否已有运行的实例
if pgrep -f "_devices_status_keep_tools.py" > /dev/null; then
    echo "警告: 检测到已有运行的设备状态保持器实例"
    echo "是否终止现有实例并启动新的？(y/n)"
    read -r response
    if [[ "$response" == "y" || "$response" == "Y" ]]; then
        echo "终止现有实例..."
        pkill -f "_devices_status_keep_tools.py"
        sleep 2
    else
        echo "启动已取消"
        exit 1
    fi
fi

# 启动设备状态保持器
echo "启动设备状态保持器..."
echo "日志文件: log/device_status_keep.log"
echo "按 Ctrl+C 停止服务"
echo

# 启动主程序
python3 tools/_devices_status_keep_tools.py