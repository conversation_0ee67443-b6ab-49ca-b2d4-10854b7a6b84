#!/usr/bin/env python3
"""
测试过程智能评价器
用于评价已完成的测试任务是否满足初始要求，并给出改进建议
"""

import json
import re
import requests
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestProcessJudge:
    """测试过程智能评价器"""
    
    def __init__(self, ollama_host: str = "127.0.0.1:1234", 
                 model_name: str = "qwen3-30b-a3b-thinking-2507"):
        """
        初始化评价器
        
        Args:
            ollama_host: Ollama服务地址
            model_name: 使用的模型名称
        """
        self.ollama_host = ollama_host
        self.model_name = model_name
        self.base_url = f"http://{ollama_host}"
        
        # 检查服务可用性
        self._check_service_availability()
    
    def _check_service_availability(self) -> bool:
        """检查LM Studio服务是否可用"""
        try:
            response = requests.get(
                f"{self.base_url}/v1/models", 
                timeout=5,
                proxies={'http': None, 'https': None}  # 禁用代理避免VPN干扰
            )
            if response.status_code == 200:
                logger.info(f"✅ LM Studio服务连接成功: {self.base_url}")
                return True
            else:
                logger.error(f"❌ LM Studio服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 无法连接到LM Studio服务 {self.base_url}: {e}")
            return False
    
    def _call_llm(self, prompt: str, system_prompt: str = None) -> Dict[str, str]:
        """调用LLM进行分析"""
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": 0.7,  # 较高的温度以获得更灵活的分析
                "top_p": 0.9,
                "max_tokens": 8000,
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=120,  # 2分钟超时
                proxies={'http': None, 'https': None}  # 禁用代理避免VPN干扰
            )
            
            if response.status_code == 200:
                result = response.json()
                raw_content = result.get('choices', [{}])[0].get('message', {}).get('content', '').strip()
                
                # 分离思考过程和最终输出
                thinking_content = ""
                final_content = raw_content
                
                # 提取 <think> 标签内容
                think_pattern = r'<think>(.*?)</think>'
                think_match = re.search(think_pattern, raw_content, re.DOTALL)
                if think_match:
                    thinking_content = think_match.group(1).strip()
                    # 移除原文中的 <think> 标签
                    final_content = re.sub(think_pattern, '', raw_content, flags=re.DOTALL).strip()
                
                return {
                    "thinking": thinking_content,
                    "analysis": final_content
                }
            else:
                logger.error(f"LLM调用失败: {response.status_code} - {response.text}")
                return {
                    "thinking": "",
                    "analysis": f"❌ LLM调用失败: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"LLM调用异常: {e}")
            return {
                "thinking": "",
                "analysis": f"❌ LLM调用异常: {str(e)}"
            }
    
    def parse_test_log(self, log_file_path: str) -> Dict[str, Any]:
        """解析测试日志文件"""
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 先从日志内容中提取原始指令
            original_instruction = self._extract_original_instruction(content)
            
            # 如果没找到，或者找到的是格式化后的指令，尝试从转换日志中获取
            if original_instruction == "未找到原始指令" or "🎯 测试计划摘要" in original_instruction:
                folder_path = str(Path(log_file_path).parent)
                round_number = self._extract_round_number_from_folder(folder_path)
                if round_number:
                    conversion_instruction = self._find_original_instruction_from_conversion_log(round_number)
                    if conversion_instruction:
                        original_instruction = conversion_instruction
                        logger.info(f"✅ 使用从转换日志获取的原始指令")
            
            # 提取基本信息
            log_data = {
                "log_path": log_file_path,
                "parse_time": datetime.now().isoformat(),
                "original_instruction": original_instruction,
                "round_number": self._extract_round_number_from_folder(str(Path(log_file_path).parent)),
                "test_plan": self._extract_test_plan(content),
                "execution_rounds": self._extract_execution_rounds(content),
                "completion_info": self._extract_completion_info(content),
                "raw_content": content
            }
            
            return log_data
            
        except Exception as e:
            logger.error(f"解析日志文件失败: {e}")
            return {"error": str(e), "log_path": log_file_path}
    
    def _extract_original_instruction(self, content: str) -> str:
        """提取原始用户指令"""
        # 查找原始用户输入
        pattern = r'用户输入:.*?(?=\n=|$)'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            instruction = match.group(0).replace('用户输入:', '').strip()
            # 清理格式化标记
            instruction = re.sub(r'={40,}', '', instruction)
            instruction = re.sub(r'🎯.*?\n', '', instruction)
            instruction = re.sub(r'📝.*?\n', '', instruction)
            instruction = re.sub(r'📱.*?\n', '', instruction)
            instruction = re.sub(r'🔢.*?\n', '', instruction)
            instruction = re.sub(r'📋.*?\n', '', instruction)
            return instruction.strip()
        
        return "未找到原始指令"
    
    def _extract_round_number_from_folder(self, folder_path: str) -> Optional[int]:
        """从文件夹名称提取轮次编号"""
        folder_name = Path(folder_path).name
        # 匹配格式: round_000371_20250801_152136
        pattern = r'round_(\d+)_'
        match = re.search(pattern, folder_name)
        if match:
            return int(match.group(1))
        return None
    
    def _find_original_instruction_from_conversion_log(self, round_number: int) -> Optional[str]:
        """从转换日志中查找原始自然语言指令"""
        try:
            conversion_log_dir = Path("log/smart_task_conversion")
            if not conversion_log_dir.exists():
                logger.warning("转换日志目录不存在: log/smart_task_conversion")
                return None
            
            # 遍历所有转换日志文件
            for log_file in conversion_log_dir.glob("conversion_*.log"):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否包含对应的轮次信息
                    round_patterns = [
                        f"轮次: {round_number}",
                        f"轮次：{round_number}"
                    ]
                    
                    if any(pattern in content for pattern in round_patterns):
                        # 提取原始自然语言
                        instruction_pattern = r'📝 原始自然语言: (.+?)(?=\n|\[)'
                        match = re.search(instruction_pattern, content)
                        if match:
                            original_instruction = match.group(1).strip()
                            logger.info(f"✅ 从转换日志找到轮次 {round_number} 的原始指令")
                            return original_instruction
                            
                except Exception as e:
                    logger.warning(f"读取转换日志失败 {log_file}: {e}")
                    continue
            
            logger.warning(f"未在转换日志中找到轮次 {round_number} 的记录")
            return None
            
        except Exception as e:
            logger.error(f"查找转换日志失败: {e}")
            return None
    
    def _extract_test_plan(self, content: str) -> Dict[str, Any]:
        """提取测试计划信息"""
        plan_info = {
            "summary": "",
            "platform": "",
            "total_planned_steps": 0,
            "steps": []
        }
        
        # 提取计划摘要
        summary_pattern = r'📝 计划描述: (.+)'
        summary_match = re.search(summary_pattern, content)
        if summary_match:
            plan_info["summary"] = summary_match.group(1).strip()
        
        # 提取平台信息
        platform_pattern = r'📱 目标平台: (\w+)'
        platform_match = re.search(platform_pattern, content)
        if platform_match:
            plan_info["platform"] = platform_match.group(1).strip()
        
        # 提取总步骤数
        steps_pattern = r'🔢 总步骤数: (\d+)'
        steps_match = re.search(steps_pattern, content)
        if steps_match:
            plan_info["total_planned_steps"] = int(steps_match.group(1))
        
        # 提取详细步骤
        step_pattern = r'步骤 (\d+): (.+?)\n.*?🔧 工具调用: (\w+)\n.*?📝 参数: (\{.*?\})\n.*?✅ 预期结果: (.+?)(?=\n\n|步骤|\n===|$)'
        steps = re.findall(step_pattern, content, re.DOTALL)
        
        for step_id, description, tool, params_str, expected in steps:
            try:
                params = json.loads(params_str)
            except:
                params = {"raw": params_str}
            
            plan_info["steps"].append({
                "step_id": int(step_id),
                "description": description.strip(),
                "tool": tool,
                "parameters": params,
                "expected_result": expected.strip()
            })
        
        return plan_info
    
    def _extract_execution_rounds(self, content: str) -> List[Dict[str, Any]]:
        """提取执行轮次信息"""
        rounds = []
        
        # 使用更精确的方法：先分割每一轮，再逐个解析
        round_sections = re.split(r'─── 第\d+轮执行 ───', content)[1:]  # 跳过第一个空部分
        round_numbers = re.findall(r'─── 第(\d+)轮执行 ───', content)
        
        for i, section in enumerate(round_sections):
            if i >= len(round_numbers):
                break
                
            round_num = round_numbers[i]
            try:
                # 提取工具名称
                tool_match = re.search(r'🔧 执行工具: (\w+)', section)
                tool = tool_match.group(1) if tool_match else "unknown"
                
                # 提取执行耗时
                duration_match = re.search(r'⏱️  执行耗时: ([\d.]+)秒', section)
                duration = float(duration_match.group(1)) if duration_match else 0.0
                
                # 提取结果摘要
                summary_match = re.search(r'💬 结果摘要: ([^\n]+)', section)
                summary = summary_match.group(1).strip() if summary_match else "N/A"
                
                # 提取工具参数 - 使用更健壮的方法
                params_match = re.search(r'📝 工具参数: ({.*?})\s*(?=\n|\s*⏱️)', section, re.DOTALL)
                if params_match:
                    params_str = params_match.group(1).strip()
                    params = self._safe_json_parse(params_str, f"第{round_num}轮参数")
                else:
                    params = {"error": "未找到参数"}
                
                # 提取执行结果 - 使用更复杂的匹配策略
                result_match = re.search(r'📊 执行结果: ({.*?})\s*(?=\n.*?💬|$)', section, re.DOTALL)
                if result_match:
                    result_str = result_match.group(1).strip()
                    result = self._safe_json_parse(result_str, f"第{round_num}轮结果")
                else:
                    result = {"error": "未找到结果"}
                
                # 确定状态
                status = "unknown"
                success = False
                if isinstance(result, dict):
                    status = result.get("status", "unknown")
                    success = status in ["success", "completed"]
                
                rounds.append({
                    "round": int(round_num),
                    "tool": tool,
                    "parameters": params,
                    "duration": duration,
                    "result": result,
                    "summary": summary,
                    "status": status,
                    "success": success
                })
                
            except Exception as e:
                logger.warning(f"解析第{round_num}轮执行失败: {e}")
                # 保存基本信息
                rounds.append({
                    "round": int(round_num),
                    "tool": "unknown",
                    "parameters": {"error": "解析失败"},
                    "duration": 0.0,
                    "result": {"error": "解析失败"},
                    "summary": "解析失败",
                    "status": "parse_error",
                    "success": False
                })
        
        return rounds
    
    def _safe_json_parse(self, json_str: str, context: str) -> Dict[str, Any]:
        """安全的JSON解析，处理常见的格式问题"""
        try:
            # 清理字符串
            cleaned = json_str.strip()
            
            # 尝试直接解析
            return json.loads(cleaned)
            
        except json.JSONDecodeError as e:
            try:
                # 尝试修复单引号问题
                fixed = cleaned.replace("'", '"')
                return json.loads(fixed)
                
            except json.JSONDecodeError:
                try:
                    # 尝试处理Python字典格式
                    import ast
                    return ast.literal_eval(cleaned)
                    
                except (ValueError, SyntaxError):
                    # 如果所有解析都失败，返回原始字符串
                    logger.warning(f"{context} JSON解析失败: {str(e)[:100]}...")
                    return {"raw": cleaned, "parse_error": str(e)}
    
    def _extract_completion_info(self, content: str) -> Dict[str, Any]:
        """提取任务完成信息"""
        completion_info = {}
        
        # 查找任务完成部分
        completion_pattern = r'任务完成.*?\n============================================================\n(.*?)============================================================'
        match = re.search(completion_pattern, content, re.DOTALL)
        
        if match:
            completion_content = match.group(1)
            
            # 提取各种信息
            patterns = {
                "total_rounds": r'📊 总轮数: (.+)',
                "total_duration": r'⏱️  总耗时: (.+)',
                "execution_status": r'✅ 执行状态: (.+)',
                "end_time": r'🕐 结束时间: (.+)'
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, completion_content)
                if match:
                    completion_info[key] = match.group(1).strip()
        
        return completion_info
    
    
    def judge_test_execution(self, round_folder_path: str, save_to_round_folder: bool = False) -> str:
        """评价测试执行
        
        Args:
            round_folder_path: 轮次文件夹路径
            save_to_round_folder: 是否保存到轮次文件夹中的judge_report.log
        
        Returns:
            评价报告内容
        """
        try:
            folder_path = Path(round_folder_path)
            if not folder_path.exists():
                return f"❌ 文件夹不存在: {round_folder_path}"
            
            log_file = folder_path / "task_structured.log"
            if not log_file.exists():
                return f"❌ 日志文件不存在: {log_file}"
            
            logger.info(f"🔍 开始分析测试轮次: {folder_path.name}")
            
            # 解析日志
            log_data = self.parse_test_log(str(log_file))
            if "error" in log_data:
                return f"❌ 日志解析失败: {log_data['error']}"
            
            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(log_data)
            
            # 调用LLM进行分析
            system_prompt = self._get_system_prompt()
            analysis_result = self._call_llm(analysis_prompt, system_prompt)
            
            # 格式化最终报告
            final_report = self._format_final_report(log_data, analysis_result)
            
            # 如果需要保存到轮次文件夹
            if save_to_round_folder:
                judge_report_file = folder_path / "judge_report.log"
                try:
                    with open(judge_report_file, 'w', encoding='utf-8') as f:
                        f.write(final_report)
                    logger.info(f"📄 评价报告已保存到: {judge_report_file}")
                except Exception as e:
                    logger.error(f"保存评价报告失败: {e}")
            
            logger.info(f"✅ 分析完成: {folder_path.name}")
            return final_report
            
        except Exception as e:
            logger.error(f"评价过程异常: {e}")
            return f"❌ 评价过程异常: {str(e)}"
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的自动化测试评价专家，专注于理解测试业务逻辑和执行质量。

你的核心任务是基于原始信息进行深度业务分析，而不是机械化的步骤对比。

**输出格式要求：**
请严格按照以下格式输出分析结果，以便后续系统解析：

## 总体评价结果
**最终成功状态**: [成功/失败]  
**成功评分**: [0.0-1.0的数值]  
**置信度**: [0.0-1.0的数值]

## 分维度评分  
**测试计划质量评分**: [0.0-1.0的数值]  
**执行符合度评分**: [0.0-1.0的数值]  
**执行质量评分**: [0.0-1.0的数值]  
**目标达成度评分**: [0.0-1.0的数值]

## 1. 测试计划质量分析
[分析内容]
- **关键问题**: [列出主要问题]
- **改进建议**: [具体建议]

## 2. 执行符合度分析  
[分析内容]
- **关键问题**: [列出主要问题]  
- **改进建议**: [具体建议]

## 3. 执行路径追踪分析
[分析内容]
- **关键问题**: [列出主要问题]
- **改进建议**: [具体建议]

## 4. 综合评价与建议
[整体评价内容]
- **根本问题**: [识别的根本问题]
- **最佳实践**: [提取的最佳实践]
- **改进方向**: [具体的改进方向]

请严格遵循此格式，确保每个section都有清晰的标题和结构化内容。评分请基于专业判断给出0.0-1.0之间的数值。"""

    def _build_analysis_prompt(self, log_data: Dict[str, Any]) -> str:
        """构建分析提示词"""
        
        execution_rounds = log_data.get("execution_rounds", [])
        completion_info = log_data.get("completion_info", {})
        test_plan = log_data.get("test_plan", {})
        plan_steps = test_plan.get('steps', [])
        
        prompt = f"""请基于以下三部分原始信息进行专业的测试质量分析：

    # 第一部分：原始测试指令
    {log_data.get('original_instruction', '未找到原始指令')}

    # 第二部分：结构化测试计划
    **计划摘要**: {test_plan.get('summary', '无')}
    **目标平台**: {test_plan.get('platform', '未知')}
    **总步骤数**: {test_plan.get('total_planned_steps', 0)}

    **详细测试步骤**:"""

        # 添加计划步骤，保持简洁
        if plan_steps:
            for i, step in enumerate(plan_steps, 1):
                prompt += f"""
步骤{i}: {step.get('description', 'N/A')}
  工具: {step.get('tool', 'unknown')}
  参数: {json.dumps(step.get('parameters', {}), ensure_ascii=False)}
  预期: {step.get('expected_result', 'N/A')}"""
        else:
            prompt += "\n（从日志中未解析出详细步骤信息）"

        prompt += f"""

# 第三部分：实际执行日志
**执行统计**: 共{len(execution_rounds)}轮，成功{len([r for r in execution_rounds if r.get('success', False)])}轮，总耗时{completion_info.get('total_duration', '未知')}

**执行详情**:"""

        # 添加执行详情，保持简洁
        for i, round_data in enumerate(execution_rounds, 1):
            status = "✅" if round_data.get("success") else "❌"
            prompt += f"""
第{i}轮 {status} {round_data.get('tool', 'unknown')}
  参数: {json.dumps(round_data.get('parameters', {}), ensure_ascii=False)}
  结果: {round_data.get('summary', 'N/A')}"""
            
            # 显示错误信息（如果有）
            result = round_data.get('result', {})
            if isinstance(result, dict) and 'message' in result and not round_data.get("success"):
                prompt += f"""
  错误: {result['message']}"""

        prompt += """

# 分析要求
请深入理解测试业务逻辑，进行三个维度的专业分析：

1. **测试计划质量分析** - 结构化测试计划对原始指令的拆解是否准确合理
2. **执行符合度分析** - 实际执行是否忠实按照测试计划进行
3. **综合评价与建议** - 测试目标达成情况和改进建议

请基于你的专业判断，重点关注业务逻辑的正确性，而非简单的步骤计数对比。"""

        return prompt

    def _format_final_report(self, log_data: Dict[str, Any], analysis_result: Dict[str, str]) -> str:
        """格式化最终报告"""
        
        folder_name = Path(log_data.get("log_path", "")).parent.name
        round_number = log_data.get("round_number")
        test_plan = log_data.get("test_plan", {})
        execution_rounds = log_data.get("execution_rounds", [])
        completion_info = log_data.get("completion_info", {})
        
        # 获取原始指令
        original_instruction = log_data.get('original_instruction', '未找到原始指令')
        
        # 分离思考过程和最终分析
        thinking_content = analysis_result.get('thinking', '')
        final_analysis = analysis_result.get('analysis', '')
        
        report = f"""
{'='*80}
自动化测试质量评价报告
{'='*80}

📁 测试轮次: {folder_name}
🔢 轮次编号: {round_number if round_number else '未知'}
⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 分析模型: {self.model_name}

{'='*80}
测试信息概览
{'='*80}

📋 原始指令:
{original_instruction}

📊 执行统计:
• 计划步骤: {test_plan.get('total_planned_steps', 0)} 步
• 实际执行: {len(execution_rounds)} 轮 (成功 {len([r for r in execution_rounds if r.get('success', False)])} 轮)
• 总耗时: {completion_info.get('total_duration', '未知')}
• 最终状态: {completion_info.get('execution_status', '未知')}"""
        
        # 如果有思考过程，先显示思考过程
        if thinking_content:
            report += f"""

{'='*80}
分析思考过程
{'='*80}

{thinking_content}"""
        
        # 然后显示专业分析评价
        report += f"""

{'='*80}
专业分析评价
{'='*80}

{final_analysis}"""
        
        # 只显示失败的执行步骤
        failed_rounds = [r for r in execution_rounds if not r.get("success")]
        if failed_rounds:
            report += f"""

{'='*80}
执行失败详情
{'='*80}"""
            
            for round_data in failed_rounds:
                round_num = execution_rounds.index(round_data) + 1
                report += f"""
第{round_num}轮执行失败:
  工具: {round_data.get('tool', 'unknown')}
  摘要: {round_data.get('summary', 'N/A')}"""
                
                result = round_data.get("result", {})
                if "message" in result:
                    report += f"""
  错误: {result['message']}"""

        report += f"""

{'='*80}
报告生成完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}
"""
        
        return report


def main():
    """主函数 - 用于测试"""
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python process_judge.py <round_folder_path>")
        print("示例: python process_judge.py log/round_000371_20250801_152136")
        sys.exit(1)
    
    round_folder = sys.argv[1]
    
    # 创建评价器
    judge = TestProcessJudge()
    
    # 执行评价
    result = judge.judge_test_execution(round_folder)
    
    # 打印结果
    print(result)
    
    # 创建输出目录
    output_dir = Path("log/judge_report")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 提取轮次编号
    folder_name = Path(round_folder).name
    round_match = re.search(r'round_(\d+)_', folder_name)
    round_number = round_match.group(1) if round_match else "unknown"
    
    # 生成文件名：轮次+时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = output_dir / f"round_{round_number}_{timestamp}.txt"
    
    # 保存结果到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(result)
    
    print(f"\n📄 报告已保存到: {output_file}")


if __name__ == "__main__":
    main()