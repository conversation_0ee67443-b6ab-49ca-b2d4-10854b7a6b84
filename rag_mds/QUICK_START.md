# 🚀 RAG系统快速入门指南

## 5分钟快速上手

### 第一步：检查服务状态 ✅

打开终端，检查必要服务是否运行：

```bash
# 检查Qdrant服务
curl -s http://127.0.0.1:6333/collections

# 检查Ollama Embedding服务
curl -s http://127.0.0.1:11437/api/version
```

如果服务未运行，请参考[完整安装指南](RAG_SYSTEM_GUIDE.md#安装部署)。

### 第二步：启动Web界面 🌐

```bash
cd rag_system
python web_interface.py
```

看到以下信息表示启动成功：
```
🚀 启动RAG系统Web界面...
📱 访问地址: http://localhost:5001
```

### 第三步：访问系统 🖥️

打开浏览器访问：**http://localhost:5001**

你将看到系统概览页面，显示当前的数据统计。

### 第四步：提取历史数据 📥

1. 点击导航栏的 **"数据管理"**
2. 在"批量数据提取"区域：
   - 选择提取数量（建议先选择10个）
   - 点击 **"开始提取"**
3. 等待提取完成，查看结果统计

### 第五步：尝试智能搜索 🔍

1. 点击导航栏的 **"智能搜索"**
2. 在搜索框输入自然语言查询，例如：
   - `"点击地址选择器"`
   - `"iOS设备截图失败"`
   - `"find_available_device工具调用"`
3. 选择搜索类型（推荐先用"指令搜索"）
4. 点击 **"开始搜索"**
5. 查看搜索结果和相似度评分

## 🎯 核心功能演示

### 智能搜索示例

**场景1：查找相似指令**
```
输入："点击页面上的地址"
搜索类型：指令搜索
预期结果：找到包含"点击"、"地址"等相关操作的历史指令
```

**场景2：排查问题**
```
输入："截图操作失败"
搜索类型：错误模式
预期结果：找到历史上相似的截图失败案例和解决方案
```

**场景3：工具使用**
```
输入："find_available_device"
搜索类型：工具结果
预期结果：查看该工具的历史执行结果和使用情况
```

### 执行记录浏览

1. 访问 **"执行记录"** 页面
2. 浏览所有历史任务执行
3. 点击任意记录查看详细信息：
   - 原始指令
   - 执行步骤
   - 工具调用结果
   - 成功/失败状态

### 系统监控

1. 在首页查看实时统计：
   - 总执行数
   - 总步骤数
   - 向量总数
   - 平台分布
2. 点击 **"刷新统计"** 获取最新数据

## 🛠️ 常用操作

### 测试系统连接

在任意页面点击相关的"测试连接"按钮：
- ✅ 测试Embedding服务
- ✅ 测试Qdrant向量数据库
- ✅ 测试整体系统状态

### 批量数据管理

推荐的数据提取策略：
1. **首次使用**：提取最新10-20个日志
2. **日常维护**：每周提取新增的5-10个日志
3. **大量历史数据**：分批处理，每次50-100个

### 搜索技巧

**🎯 提高搜索准确性的技巧：**

1. **使用具体词汇**：
   - ❌ "操作失败" （太泛泛）
   - ✅ "截图操作失败" （更具体）

2. **选择合适的搜索类型**：
   - 查找相似任务 → 指令搜索
   - 分析工具行为 → 工具结果
   - 排查问题 → 错误模式

3. **善用过滤条件**：
   - 按平台筛选（iOS/Android）
   - 按工具名称筛选
   - 调整返回数量

## 📈 进阶使用

### 了解相似度评分

- **90%以上**：几乎完全相同的操作
- **80-90%**：非常相似，可直接参考
- **70-80%**：有一定相似性，需要分析差异
- **70%以下**：相似度较低，仅供参考

### 利用元数据信息

搜索结果中的元数据标签帮助你理解内容：
- **执行ID**：唯一标识，可用于查看完整记录
- **平台**：iOS/Android
- **工具**：使用的具体工具名称
- **时间**：执行时间戳

### 结合执行详情分析

1. 从搜索结果点击 **"查看详情"**
2. 分析完整的执行流程：
   - 输入指令
   - 执行步骤
   - 中间结果
   - 最终输出
3. 理解成功/失败的关键因素

## 🔧 故障快速诊断

### 常见问题1：搜索无结果

**可能原因：**
- 数据库中数据不足
- 查询词过于具体或过于泛泛

**解决方案：**
1. 先到"数据管理"提取更多历史数据
2. 尝试使用更通用的关键词
3. 检查是否选择了过于严格的过滤条件

### 常见问题2：页面加载慢

**可能原因：**
- 向量搜索计算量大
- 服务器资源不足

**解决方案：**
1. 减少返回结果数量（top_k）
2. 检查系统资源使用情况
3. 重启相关服务

### 常见问题3：Embedding服务异常

**症状：**
- 搜索功能不可用
- 提示"502 Bad Gateway"

**解决方案：**
```bash
# 重启Ollama服务
killall ollama
OLLAMA_HOST=127.0.0.1:11437 OLLAMA_MODELS=~/.ollama3 OLLAMA_CONTEXT_LENGTH=40960 ollama serve
```

## 📞 获取帮助

1. **查阅完整文档**：[RAG_SYSTEM_GUIDE.md](RAG_SYSTEM_GUIDE.md)
2. **运行系统测试**：`python rag_system/test_data_pipeline.py`
3. **查看实时日志**：观察终端输出的详细信息
4. **使用调试功能**：Web界面中的"测试连接"功能

---

**🎉 恭喜！您已经掌握了RAG系统的基本使用方法！**

继续探索系统的高级功能，让AI助力您的知识管理工作！