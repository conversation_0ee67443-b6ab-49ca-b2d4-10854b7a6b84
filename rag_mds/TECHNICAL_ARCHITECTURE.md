# 🏗️ RAG系统技术架构详解

## 📋 架构概览

### 系统层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                     用户界面层 (UI Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  Flask Web框架 + Bootstrap前端 + JavaScript交互             │
│  - HTML模板渲染                                              │
│  - RESTful API接口                                          │
│  - 响应式用户界面                                            │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                │
├─────────────────────────────────────────────────────────────┤
│  核心组件协调与业务逻辑处理                                   │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ 知识提取器        │  │ 数据库管理器     │                  │
│  │ KnowledgeExtractor│  │ DatabaseManager │                  │
│  │ - 日志解析        │  │ - 混合数据库     │                  │
│  │ - 结构化提取      │  │ - 向量操作       │                  │
│  │ - 批量处理        │  │ - CRUD接口       │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                   数据访问层 (Data Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────────────────┐│
│  │    SQLite数据库      │  │      Qdrant向量数据库         ││
│  │  - 关系型元数据      │  │    - 高维向量存储             ││
│  │  - 事务支持          │  │    - 相似度搜索               ││
│  │  - 结构化查询        │  │    - 集合管理                 ││
│  └─────────────────────┘  └─────────────────────────────────┘│
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                   外部服务层 (External Services)             │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Ollama Embedding服务                      │ │
│  │  - Qwen3-Embedding-4B模型                             │ │
│  │  - 2560维向量生成                                       │ │
│  │  - HTTP API接口                                        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件详解

### 1. Web界面层 (Flask + Bootstrap)

#### 技术栈
- **后端框架**：Flask 3.1.0
- **前端框架**：Bootstrap 5.1.3 + Font Awesome 6.0.0
- **模板引擎**：Jinja2
- **分页组件**：Flask-Paginate

#### 架构特点
```python
# Flask应用结构
app/
├── web_interface.py       # 主应用文件
├── templates/            # 模板文件
│   ├── base.html        # 基础模板
│   ├── index.html       # 首页
│   ├── search.html      # 搜索页面
│   └── ...              # 其他页面
└── static/              # 静态资源（如果需要）
```

#### 路由设计
```python
@app.route('/')                    # 系统概览
@app.route('/search')              # 智能搜索
@app.route('/executions')          # 执行记录
@app.route('/execution/<id>')      # 执行详情
@app.route('/knowledge')           # 知识库
@app.route('/extract')             # 数据管理
@app.route('/api/<endpoint>')      # API接口
```

### 2. 知识提取器 (KnowledgeExtractor)

#### 核心功能
```python
class KnowledgeExtractor:
    """知识提取器 - 核心数据处理组件"""
    
    def __init__(self, db_manager, log_dir="log"):
        self.db_manager = db_manager
        self.log_dir = Path(log_dir)
    
    # 核心方法
    def extract_round_info()          # 轮次信息提取
    def extract_device_info()         # 设备信息提取
    def extract_original_instruction() # 原始指令提取
    def extract_structured_plan()     # 结构化计划提取
    def parse_tool_executions()       # 工具执行解析
    def batch_process_logs()          # 批量处理
```

#### 数据流处理
```
日志文件 → 正则表达式解析 → 结构化对象 → 数据库存储
    ↓           ↓              ↓           ↓
round_xxx/  extract_*()   TaskExecution  SQLite+Qdrant
task.log    methods       TaskStep       混合存储
```

#### 解析策略
1. **文件系统扫描**：遍历log目录下的round_*文件夹
2. **正则表达式匹配**：使用复杂正则提取关键信息
3. **JSON数据解析**：处理工具调用的JSON格式数据
4. **错误处理机制**：容错设计，跳过损坏的日志文件

### 3. 数据库管理器 (DatabaseManager)

#### 双重存储架构
```python
class DatabaseManager:
    """混合数据库管理器"""
    
    def __init__(self, config):
        # SQLite：结构化元数据
        self.sqlite_db = sqlite3.connect(db_path)
        
        # Qdrant：向量数据
        self.qdrant_client = QdrantClient(host, port)
        
        # Embedding服务
        self.embedding_service = EmbeddingService(config)
```

#### 数据同步机制
```python
def save_task_execution(self, execution):
    """保存执行记录 - 同时写入两个数据库"""
    
    # 1. 保存到SQLite（元数据）
    sqlite_success = self._save_to_sqlite(execution)
    
    # 2. 生成向量并保存到Qdrant
    vector_success = self._create_execution_vectors(execution)
    
    return sqlite_success and vector_success
```

### 4. 向量化服务 (EmbeddingService)

#### 模型配置
```python
class EmbeddingService:
    """Embedding服务封装"""
    
    def __init__(self, config):
        self.model_name = "Qwen3-Embedding-4B-Q4_K_M.gguf"
        self.base_url = "http://127.0.0.1:11437"
        self.vector_dimension = 2560
```

#### 向量生成流程
```python
def get_embedding(self, text):
    """文本向量化"""
    
    # 1. 构建请求
    payload = {
        "model": self.model_name,
        "prompt": text
    }
    
    # 2. 发送HTTP请求（绕过代理）
    response = requests.post(
        f"{self.base_url}/api/embeddings",
        json=payload,
        proxies={'http': None, 'https': None}
    )
    
    # 3. 解析响应
    return response.json()["embedding"]
```

## 🗄️ 数据模型设计

### 实体关系图 (ERD)
```
TaskExecution (任务执行)
├── execution_id (PK)
├── round_id (唯一)
├── original_instruction
├── platform
├── execution_status
└── created_at

TaskStep (任务步骤)
├── step_id (PK)
├── execution_id (FK)
├── tool_name
├── tool_result
├── is_successful
└── executed_at

VectorRecord (向量记录)
├── vector_id (PK)
├── source_type
├── text_content
├── vector [2560维浮点数组]
└── metadata
```

### 向量集合设计
```python
# Qdrant集合结构
Collections = {
    "instructions": {        # 指令向量
        "vector_size": 2560,
        "distance": "Cosine",
        "payload": {
            "execution_id": str,
            "platform": str,
            "task_type": str
        }
    },
    "tool_results": {        # 工具结果向量
        "vector_size": 2560,
        "distance": "Cosine", 
        "payload": {
            "tool_name": str,
            "status": str,
            "execution_id": str
        }
    },
    "error_patterns": {      # 错误模式向量
        "vector_size": 2560,
        "distance": "Cosine",
        "payload": {
            "tool_name": str,
            "error_type": str
        }
    }
}
```

## 🔍 搜索算法实现

### 向量相似度搜索
```python
def vector_search(query_text, collection_name, top_k=5):
    """向量搜索算法"""
    
    # 1. 查询向量化
    query_vector = embedding_service.get_embedding(query_text)
    
    # 2. Qdrant搜索
    results = qdrant_client.search(
        collection_name=collection_name,
        query_vector=query_vector,
        limit=top_k,
        score_threshold=0.7  # 相似度阈值
    )
    
    # 3. 结果处理
    return [
        {
            "id": result.id,
            "score": result.score,
            "payload": result.payload
        }
        for result in results
    ]
```

### 混合检索策略
```python
def hybrid_search(query, filters=None):
    """混合检索：向量搜索 + 元数据过滤"""
    
    # 1. 向量搜索
    vector_results = vector_search(query)
    
    # 2. 元数据过滤
    if filters:
        filtered_results = apply_metadata_filters(vector_results, filters)
    else:
        filtered_results = vector_results
    
    # 3. 结果重排序
    final_results = rerank_results(filtered_results, query)
    
    return final_results
```

## ⚡ 性能优化策略

### 1. 数据库优化

#### SQLite优化
```sql
-- 创建索引
CREATE INDEX idx_executions_round ON task_executions (round_number);
CREATE INDEX idx_executions_status ON task_executions (execution_status);
CREATE INDEX idx_executions_platform ON task_executions (platform);
CREATE INDEX idx_steps_execution ON task_steps (execution_id);
CREATE INDEX idx_steps_tool ON task_steps (tool_name);

-- 查询优化
EXPLAIN QUERY PLAN SELECT * FROM task_executions WHERE platform = 'ios';
```

#### Qdrant优化
```python
# 向量数据库配置优化
vector_config = VectorParams(
    size=2560,
    distance=Distance.COSINE,
    on_disk=True  # 大数据集使用磁盘存储
)

# 批量插入优化
def batch_upsert_vectors(vectors, batch_size=100):
    """批量插入向量"""
    for i in range(0, len(vectors), batch_size):
        batch = vectors[i:i + batch_size]
        qdrant_client.upsert(collection_name, points=batch)
```

### 2. 缓存机制
```python
from functools import lru_cache

class CachedEmbeddingService:
    """带缓存的Embedding服务"""
    
    @lru_cache(maxsize=1000)
    def get_embedding(self, text):
        """缓存向量化结果"""
        return self._compute_embedding(text)
    
    def _compute_embedding(self, text):
        """实际计算向量（不缓存）"""
        # 调用Ollama API
        pass
```

### 3. 并发处理
```python
import asyncio
import aiohttp

class AsyncEmbeddingService:
    """异步Embedding服务"""
    
    async def get_embeddings_batch(self, texts):
        """批量异步处理"""
        
        async with aiohttp.ClientSession() as session:
            tasks = [
                self._get_single_embedding(session, text)
                for text in texts
            ]
            results = await asyncio.gather(*tasks)
        
        return results
```

## 🔒 安全性考虑

### 1. 数据隐私
```python
# 敏感信息过滤
def sanitize_log_content(content):
    """清理敏感信息"""
    
    # 移除密码、密钥等敏感信息
    patterns = [
        r'password["\s]*[:=]["\s]*([^"\s,}]+)',
        r'api_key["\s]*[:=]["\s]*([^"\s,}]+)',
        r'secret["\s]*[:=]["\s]*([^"\s,}]+)'
    ]
    
    for pattern in patterns:
        content = re.sub(pattern, r'\1: [REDACTED]', content)
    
    return content
```

### 2. 输入验证
```python
def validate_search_input(query, search_type, top_k):
    """验证搜索输入"""
    
    # 查询长度限制
    if len(query) > 1000:
        raise ValueError("查询文本过长")
    
    # 搜索类型验证
    valid_types = ["instructions", "tool_results", "error_patterns"]
    if search_type not in valid_types:
        raise ValueError("无效的搜索类型")
    
    # 返回数量限制
    if top_k > 100:
        raise ValueError("返回数量过大")
```

### 3. API限流
```python
from flask_limiter import Limiter

limiter = Limiter(
    app,
    key_func=lambda: request.remote_addr,
    default_limits=["100 per hour"]
)

@app.route('/api/search', methods=['POST'])
@limiter.limit("10 per minute")
def api_search():
    """限流的搜索API"""
    pass
```

## 📊 监控与日志

### 1. 性能监控
```python
import time
import logging
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            success = True
        except Exception as e:
            result = None
            success = False
            logging.error(f"{func.__name__} 执行失败: {e}")
        
        duration = time.time() - start_time
        
        # 记录性能指标
        logging.info(f"{func.__name__} 执行时间: {duration:.4f}s, 成功: {success}")
        
        if not success:
            raise
        
        return result
    
    return wrapper
```

### 2. 错误追踪
```python
import traceback

class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self):
        self.error_counts = {}
    
    def log_error(self, error, context=None):
        """记录错误"""
        
        error_key = f"{type(error).__name__}: {str(error)}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        logging.error(f"错误发生: {error_key}")
        logging.error(f"上下文: {context}")
        logging.error(f"堆栈追踪:\n{traceback.format_exc()}")
```

## 🔧 扩展性设计

### 1. 插件架构
```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.plugins = {}
    
    def register_plugin(self, name, plugin):
        """注册插件"""
        self.plugins[name] = plugin
    
    def execute_hook(self, hook_name, *args, **kwargs):
        """执行钩子"""
        results = []
        for plugin in self.plugins.values():
            if hasattr(plugin, hook_name):
                result = getattr(plugin, hook_name)(*args, **kwargs)
                results.append(result)
        return results
```

### 2. 配置管理
```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class SystemConfig:
    """系统配置"""
    
    # 数据库配置
    qdrant_host: str = "127.0.0.1"
    qdrant_port: int = 6333
    sqlite_path: str = "rag_data/rag_metadata.db"
    
    # Embedding配置
    embedding_model: str = "Qwen3-Embedding-4B-Q4_K_M.gguf"
    ollama_host: str = "127.0.0.1:11437"
    vector_dimension: int = 2560
    
    # 搜索配置
    default_top_k: int = 5
    similarity_threshold: float = 0.7
    
    # 系统配置
    log_level: str = "INFO"
    max_batch_size: int = 100
    
    @classmethod
    def from_file(cls, config_file: str):
        """从配置文件加载"""
        import json
        with open(config_file) as f:
            config_data = json.load(f)
        return cls(**config_data)
```

## 🚀 部署架构

### 开发环境
```
单机部署：
┌─────────────────────────────────────┐
│            开发机器                  │
│  ┌─────────┐ ┌─────────┐            │
│  │ Flask   │ │ Qdrant  │            │
│  │ :5001   │ │ :6333   │            │
│  └─────────┘ └─────────┘            │
│  ┌─────────────────────┐            │
│  │     Ollama          │            │
│  │     :11437          │            │
│  └─────────────────────┘            │
└─────────────────────────────────────┘
```

### 生产环境
```
分布式部署：
┌─────────────────┐  ┌─────────────────┐
│   Web服务器      │  │   向量数据库     │
│  ┌─────────────┐ │  │  ┌─────────────┐│
│  │   Flask     │ │  │  │   Qdrant    ││
│  │   :5001     │◄──┤  │   :6333     ││
│  └─────────────┘ │  │  └─────────────┘│
└─────────────────┘  └─────────────────┘
        │                      
        ▼                      
┌─────────────────┐  ┌─────────────────┐
│  Embedding服务   │  │   数据存储       │
│  ┌─────────────┐ │  │  ┌─────────────┐│
│  │   Ollama    │ │  │  │   SQLite    ││
│  │   :11437    │ │  │  │   数据库     ││
│  └─────────────┘ │  │  └─────────────┘│
└─────────────────┘  └─────────────────┘
```

---

**本技术架构文档详细描述了RAG系统的完整技术实现，为系统维护、扩展和优化提供技术指导。**