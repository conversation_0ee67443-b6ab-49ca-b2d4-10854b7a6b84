#!/usr/bin/env python3
"""
Ollama Multi-Port Manager
A utility script to manage multiple Ollama instances running on different ports (11434-11439)
"""

import os
import subprocess
import sys
from typing import List, Dict


class OllamaManager:
    def __init__(self):
        self.ports = list(range(11434, 11440))  # 11434-11439
        self.host = "127.0.0.1"
    
    def run_ollama_command(self, port: int, command: str) -> tuple[bool, str]:
        """Run an ollama command on a specific port"""
        env = os.environ.copy()
        env['OLLAMA_HOST'] = f"{self.host}:{port}"
        
        try:
            result = subprocess.run(
                f"ollama {command}",
                shell=True,
                capture_output=True,
                text=True,
                env=env,
                timeout=30
            )
            return result.returncode == 0, result.stdout + result.stderr
        except subprocess.TimeoutExpired:
            return False, "Command timed out"
        except Exception as e:
            return False, f"Error: {str(e)}"
    
    def get_all_models(self) -> Dict[int, List[Dict]]:
        """Get list of models with details from all ports"""
        models_by_port = {}
        
        for port in self.ports:
            success, output = self.run_ollama_command(port, "list")
            if success:
                lines = output.strip().split('\n')
                if len(lines) > 1:  # Has header and data
                    models = []
                    for line in lines[1:]:  # Skip header
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 3:  # NAME, ID, SIZE, MODIFIED
                                model_info = {
                                    'name': parts[0],
                                    'id': parts[1],
                                    'size': parts[2],
                                    'modified': ' '.join(parts[3:]) if len(parts) > 3 else 'N/A'
                                }
                                models.append(model_info)
                    models_by_port[port] = models
                else:
                    models_by_port[port] = []
            else:
                models_by_port[port] = [{"error": "Service unavailable"}]
        
        return models_by_port
    
    def show_overview(self):
        """Show all models across all ports with memory usage"""
        print("\n" + "="*100)
        print("OLLAMA SERVICES OVERVIEW")
        print("="*100)
        
        models_by_port = self.get_all_models()
        total_models = 0
        
        # Find the longest model name to set proper column width
        max_name_length = 0
        for models in models_by_port.values():
            if isinstance(models, list) and models:
                for model in models:
                    if isinstance(model, dict) and 'name' in model:
                        max_name_length = max(max_name_length, len(model['name']))
        
        # Ensure minimum width and add some padding
        name_width = max(max_name_length + 2, 30)
        
        for port in self.ports:
            print(f"\n📍 Port {port}:")
            models = models_by_port.get(port, [])
            
            if not models:
                print("  No models found")
            elif isinstance(models[0], dict) and "error" in models[0]:
                print(f"  ❌ {models[0]['error']}")
            else:
                print(f"  {'Model Name':<{name_width}} {'Size':<10} {'Modified'}")
                print(f"  {'-'*name_width} {'-'*10} {'-'*20}")
                for model in models:
                    name = model['name']
                    size = model['size']
                    modified = model['modified'][:20]  # Keep modified truncated for readability
                    print(f"  {name:<{name_width}} {size:<10} {modified}")
                    total_models += 1
                
                # Show total size for this port
                if models:
                    print(f"  📊 Total models on port {port}: {len(models)}")
        
        print(f"\n🎯 Summary: {total_models} models across {len(self.ports)} ports")
        print("="*100)
    
    def interactive_port_selection(self) -> int:
        """Interactive port selection menu"""
        print("\n" + "-"*40)
        print("SELECT OLLAMA PORT")
        print("-"*40)
        
        for i, port in enumerate(self.ports, 1):
            print(f"{i}. Port {port}")
        
        while True:
            try:
                choice = input(f"\nSelect port (1-{len(self.ports)}): ").strip()
                if choice.isdigit():
                    index = int(choice) - 1
                    if 0 <= index < len(self.ports):
                        return self.ports[index]
                print("Invalid choice. Please try again.")
            except KeyboardInterrupt:
                print("\nOperation cancelled.")
                return None
    
    def interactive_command(self, port: int):
        """Interactive command execution for a specific port"""
        print(f"\n" + "-"*40)
        print(f"OLLAMA COMMANDS - Port {port}")
        print("-"*40)
        print("Available commands:")
        print("  list          - List all models")
        print("  ps            - Show running models")
        print("  rm <model>    - Remove a model")
        print("  pull <model>  - Pull a model")
        print("  run <model>   - Run a model")
        print("  show <model>  - Show model info")
        print("  stop <model>  - Stop a running model")
        print("  back          - Go back to main menu")
        print("  quit          - Exit program")
        print("-"*40)
        
        while True:
            try:
                command = input(f"\nollama@{port}> ").strip()
                
                if not command:
                    continue
                
                if command.lower() in ['back', 'b']:
                    break
                elif command.lower() in ['quit', 'q', 'exit']:
                    return False
                
                print(f"\nExecuting: OLLAMA_HOST={self.host}:{port} ollama {command}")
                success, output = self.run_ollama_command(port, command)
                
                if output.strip():
                    print(output)
                if not success:
                    print("Command failed or service unavailable.")
                
            except KeyboardInterrupt:
                print("\nOperation cancelled. Type 'back' to return to main menu.")
        
        return True
    
    def main_menu(self):
        """Main interactive menu"""
        while True:
            print("\n" + "="*50)
            print("OLLAMA MULTI-PORT MANAGER")
            print("="*50)
            print("1. Overview (All ports)")
            print("2. Select port for commands")
            print("3. Quit")
            print("="*50)
            
            try:
                choice = input("Select option (1-3): ").strip()
                
                if choice == '1':
                    self.show_overview()
                elif choice == '2':
                    port = self.interactive_port_selection()
                    if port:
                        if not self.interactive_command(port):
                            break
                elif choice == '3':
                    print("Goodbye!")
                    break
                else:
                    print("Invalid choice. Please try again.")
            
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except EOFError:
                print("\nGoodbye!")
                break


def main():
    """Entry point"""
    manager = OllamaManager()
    manager.main_menu()


if __name__ == "__main__":
    main()