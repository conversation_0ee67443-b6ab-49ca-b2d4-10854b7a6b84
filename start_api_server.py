#!/usr/bin/env python3
"""
启动API服务器的脚本
"""
import os
import sys
import uvicorn
from api_server import app
from log_manager import log_manager

def main():
    """启动API服务器"""
    print("🚀 启动Local Agent API服务器...")
    print("📡 服务地址: http://localhost:5630")
    print("📚 API文档: http://localhost:5630/docs")
    print("🔧 RedDoc文档: http://localhost:5630/redoc")
    print("💡 按Ctrl+C停止服务器")
    print("-" * 50)
    
    try:
        log_manager.info_agent("API服务器启动脚本: 开始启动服务器")
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=5630,
            log_level="info"
        )
    except KeyboardInterrupt:
        log_manager.info_agent("API服务器启动脚本: 用户中断服务器")
        print("\n👋 服务器已停止")
    except Exception as e:
        error_msg = f"启动服务器失败: {str(e)}"
        log_manager.error_agent(f"API服务器启动脚本: {error_msg}")
        print(f"❌ {error_msg}")
        sys.exit(1)

if __name__ == "__main__":
    main()