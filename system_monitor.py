#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统资源监控器
在模型测试过程中监控 CPU、内存、GPU 使用情况
"""

import psutil
import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

try:
    # 尝试导入 GPUtil 用于 GPU 监控
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False


@dataclass
class SystemSnapshot:
    """系统资源快照"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_available_gb: float
    memory_total_gb: float
    disk_usage_percent: float
    gpu_info: Optional[Dict[str, Any]] = None
    process_info: Optional[Dict[str, Any]] = None


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, monitor_interval: float = 2.0, monitor_processes: List[str] = None):
        """
        初始化监控器
        
        Args:
            monitor_interval: 监控间隔（秒）
            monitor_processes: 要特别监控的进程名列表
        """
        self.monitor_interval = monitor_interval
        self.monitor_processes = monitor_processes or ['ollama', 'LM Studio', 'python']
        self.snapshots: List[SystemSnapshot] = []
        self.monitoring = False
        self.monitor_thread = None
    
    def get_system_snapshot(self) -> SystemSnapshot:
        """获取当前系统资源快照"""
        # CPU 使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_used_gb = memory.used / (1024**3)
        memory_available_gb = memory.available / (1024**3)
        memory_total_gb = memory.total / (1024**3)
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage_percent = disk.used / disk.total * 100
        
        # GPU 信息（如果可用）
        gpu_info = None
        if GPU_AVAILABLE:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]  # 使用第一个 GPU
                    gpu_info = {
                        "name": gpu.name,
                        "load": gpu.load * 100,
                        "memory_used": gpu.memoryUsed,
                        "memory_total": gpu.memoryTotal,
                        "memory_percent": gpu.memoryUtil * 100,
                        "temperature": gpu.temperature
                    }
            except Exception as e:
                gpu_info = {"error": str(e)}
        
        # 进程信息
        process_info = self.get_process_info()
        
        return SystemSnapshot(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=memory_used_gb,
            memory_available_gb=memory_available_gb,
            memory_total_gb=memory_total_gb,
            disk_usage_percent=disk_usage_percent,
            gpu_info=gpu_info,
            process_info=process_info
        )
    
    def get_process_info(self) -> Dict[str, Any]:
        """获取特定进程的资源使用信息"""
        process_info = {}
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'memory_percent']):
            try:
                pinfo = proc.info
                process_name = pinfo['name']
                
                # 检查是否是我们关心的进程
                for monitor_name in self.monitor_processes:
                    if monitor_name.lower() in process_name.lower():
                        if monitor_name not in process_info:
                            process_info[monitor_name] = []
                        
                        memory_mb = pinfo['memory_info'].rss / (1024**2)
                        process_info[monitor_name].append({
                            'pid': pinfo['pid'],
                            'name': process_name,
                            'cpu_percent': pinfo['cpu_percent'],
                            'memory_mb': memory_mb,
                            'memory_percent': pinfo['memory_percent']
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        return process_info
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            print("⚠️ 监控器已在运行")
            return
        
        self.monitoring = True
        self.snapshots = []
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print(f"🔍 系统监控器已启动，监控间隔: {self.monitor_interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print(f"🛑 系统监控器已停止，共收集 {len(self.snapshots)} 个快照")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                snapshot = self.get_system_snapshot()
                self.snapshots.append(snapshot)
                
                # 实时显示关键指标
                print(f"\r💻 CPU: {snapshot.cpu_percent:5.1f}% | "
                      f"内存: {snapshot.memory_percent:5.1f}% "
                      f"({snapshot.memory_used_gb:.1f}/{snapshot.memory_total_gb:.1f}GB)", 
                      end="", flush=True)
                
                time.sleep(self.monitor_interval)
            except Exception as e:
                print(f"\n⚠️ 监控异常: {e}")
                time.sleep(self.monitor_interval)
    
    def get_monitoring_report(self) -> Dict[str, Any]:
        """生成监控报告"""
        if not self.snapshots:
            return {"error": "没有监控数据"}
        
        # 计算统计数据
        cpu_values = [s.cpu_percent for s in self.snapshots]
        memory_values = [s.memory_percent for s in self.snapshots]
        memory_used_values = [s.memory_used_gb for s in self.snapshots]
        
        report = {
            "monitoring_duration": len(self.snapshots) * self.monitor_interval,
            "total_snapshots": len(self.snapshots),
            "cpu_stats": {
                "avg": sum(cpu_values) / len(cpu_values),
                "min": min(cpu_values),
                "max": max(cpu_values)
            },
            "memory_stats": {
                "avg_percent": sum(memory_values) / len(memory_values),
                "min_percent": min(memory_values),
                "max_percent": max(memory_values),
                "avg_used_gb": sum(memory_used_values) / len(memory_used_values),
                "min_used_gb": min(memory_used_values),
                "max_used_gb": max(memory_used_values),
                "total_gb": self.snapshots[0].memory_total_gb
            }
        }
        
        # GPU 统计（如果可用）
        gpu_load_values = []
        gpu_memory_values = []
        for snapshot in self.snapshots:
            if snapshot.gpu_info and "load" in snapshot.gpu_info:
                gpu_load_values.append(snapshot.gpu_info["load"])
                gpu_memory_values.append(snapshot.gpu_info["memory_percent"])
        
        if gpu_load_values:
            report["gpu_stats"] = {
                "avg_load": sum(gpu_load_values) / len(gpu_load_values),
                "max_load": max(gpu_load_values),
                "avg_memory": sum(gpu_memory_values) / len(gpu_memory_values),
                "max_memory": max(gpu_memory_values)
            }
        
        # 进程统计
        process_stats = {}
        for snapshot in self.snapshots:
            if snapshot.process_info:
                for proc_name, proc_list in snapshot.process_info.items():
                    if proc_name not in process_stats:
                        process_stats[proc_name] = {
                            "max_memory_mb": 0,
                            "max_cpu_percent": 0,
                            "count": 0
                        }
                    
                    total_memory = sum(p["memory_mb"] for p in proc_list)
                    total_cpu = sum(p["cpu_percent"] or 0 for p in proc_list)
                    
                    process_stats[proc_name]["max_memory_mb"] = max(
                        process_stats[proc_name]["max_memory_mb"], 
                        total_memory
                    )
                    process_stats[proc_name]["max_cpu_percent"] = max(
                        process_stats[proc_name]["max_cpu_percent"], 
                        total_cpu
                    )
                    process_stats[proc_name]["count"] = max(
                        process_stats[proc_name]["count"], 
                        len(proc_list)
                    )
        
        report["process_stats"] = process_stats
        
        return report
    
    def save_monitoring_data(self, filename: Optional[str] = None):
        """保存监控数据到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"system_monitoring_{timestamp}.json"
        
        try:
            data = {
                "report": self.get_monitoring_report(),
                "snapshots": [asdict(s) for s in self.snapshots]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"\n📁 监控数据已保存: {filename}")
            return filename
        except Exception as e:
            print(f"\n⚠️ 保存监控数据失败: {e}")
            return None
    
    def print_report(self):
        """打印监控报告"""
        report = self.get_monitoring_report()
        
        if "error" in report:
            print(f"❌ {report['error']}")
            return
        
        print("\n" + "="*60)
        print("💻 系统资源监控报告")
        print("="*60)
        
        print(f"⏱️  监控时长: {report['monitoring_duration']:.1f} 秒")
        print(f"📊 数据点数: {report['total_snapshots']} 个")
        
        # CPU 统计
        cpu_stats = report["cpu_stats"]
        print(f"\n🧮 CPU 使用率:")
        print(f"   平均: {cpu_stats['avg']:.1f}%")
        print(f"   范围: {cpu_stats['min']:.1f}% - {cpu_stats['max']:.1f}%")
        
        # 内存统计
        memory_stats = report["memory_stats"]
        print(f"\n💾 内存使用:")
        print(f"   平均: {memory_stats['avg_percent']:.1f}% ({memory_stats['avg_used_gb']:.1f}GB)")
        print(f"   峰值: {memory_stats['max_percent']:.1f}% ({memory_stats['max_used_gb']:.1f}GB)")
        print(f"   总量: {memory_stats['total_gb']:.1f}GB")
        
        # GPU 统计（如果可用）
        if "gpu_stats" in report:
            gpu_stats = report["gpu_stats"]
            print(f"\n🎮 GPU 使用:")
            print(f"   平均负载: {gpu_stats['avg_load']:.1f}%")
            print(f"   峰值负载: {gpu_stats['max_load']:.1f}%")
            print(f"   平均显存: {gpu_stats['avg_memory']:.1f}%")
            print(f"   峰值显存: {gpu_stats['max_memory']:.1f}%")
        
        # 进程统计
        if "process_stats" in report and report["process_stats"]:
            print(f"\n🔍 关键进程资源使用:")
            for proc_name, stats in report["process_stats"].items():
                print(f"   {proc_name}:")
                print(f"     最大内存: {stats['max_memory_mb']:.1f}MB")
                print(f"     最大CPU: {stats['max_cpu_percent']:.1f}%")
                print(f"     进程数: {stats['count']}")
        
        print("="*60)


def main():
    """测试监控器"""
    print("🔍 系统监控器测试")
    
    monitor = SystemMonitor(monitor_interval=1.0)
    
    try:
        # 获取单次快照
        print("📸 获取系统快照...")
        snapshot = monitor.get_system_snapshot()
        print(f"CPU: {snapshot.cpu_percent:.1f}%")
        print(f"内存: {snapshot.memory_percent:.1f}% ({snapshot.memory_used_gb:.1f}/{snapshot.memory_total_gb:.1f}GB)")
        
        if snapshot.gpu_info and "load" in snapshot.gpu_info:
            print(f"GPU: {snapshot.gpu_info['load']:.1f}% 负载, {snapshot.gpu_info['memory_percent']:.1f}% 显存")
        
        print("\n🎯 开始 10 秒监控测试...")
        monitor.start_monitoring()
        time.sleep(10)
        monitor.stop_monitoring()
        
        # 打印报告
        monitor.print_report()
        
        # 保存数据
        monitor.save_monitoring_data()
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被中断")
        monitor.stop_monitoring()


if __name__ == "__main__":
    main()