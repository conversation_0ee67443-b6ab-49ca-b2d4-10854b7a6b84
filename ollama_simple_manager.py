#!/usr/bin/env python3
"""
简化的 Ollama 实例管理器
启动两个独立的 Ollama 实例用于并发处理
"""

import subprocess
import time
import os
import requests
import json
from typing import List, Dict, Optional
from log_manager import log_manager

# 配置常量 - 每个端口的配置
OLLAMA_CONFIGS = [
    {"port": 11434, "model": "qwen3:14b", "model_dir": "~/.ollama"},
    {"port": 11435, "model": "hf.co/unsloth/Magistral-Small-2507-GGUF:Magistral-Small-2507-Q4_K_M.gguf", "model_dir": "~/.ollama1"},
    {"port": 11436, "model": "hf.co/unsloth/GLM-4-32B-0414-GGUF:GLM-4-32B-0414-Q4_K_M.gguf", "model_dir": "~/.ollama2"},
    {"port": 11437, "model": "hf.co/Qwen/Qwen3-Embedding-4B-GGUF:Qwen3-Embedding-4B-Q4_K_M.gguf", "model_dir": "~/.ollama3"},
    {"port": 11438, "model": "hf.co/unsloth/Qwen3-30B-A3B-Thinking-2507-GGUF:Qwen3-30B-A3B-Thinking-2507-Q4_K_M.gguf", "model_dir": "~/.ollama4"},
    {"port": 11439, "model": "qwen2.5vl:3b", "model_dir": "~/.ollama5"}
]

# 向后兼容
DEFAULT_MODEL = "qwen3-30b"
OLLAMA_PORTS = [config["port"] for config in OLLAMA_CONFIGS]
OLLAMA_DIRS = [config["model_dir"] for config in OLLAMA_CONFIGS]

class SimpleOllamaManager:
    """简化的 Ollama 实例管理器"""
    
    def __init__(self):
        self.configs = OLLAMA_CONFIGS
        self.model_name = DEFAULT_MODEL
        self.ports = OLLAMA_PORTS
        self.model_dirs = OLLAMA_DIRS
        self.log_dir = "log/ollama"
        self._ensure_log_dir()
        
    def _ensure_log_dir(self):
        """确保日志目录存在"""
        os.makedirs(self.log_dir, exist_ok=True)
    
    def _check_server_running(self, port: int) -> bool:
        """检查服务器是否正在运行"""
        try:
            # 禁用代理以避免干扰
            proxies = {"http": None, "https": None}
            response = requests.get(f"http://localhost:{port}/api/version", timeout=5, proxies=proxies)
            return response.status_code == 200
        except Exception as e:
            log_manager.error_agent(f"检查端口 {port} 服务器时出错: {e}")
            return False
    
    def _check_model_loaded(self, port: int) -> bool:
        """检查模型是否已加载"""
        try:
            # 找到对应端口的模型配置
            config_map = {config["port"]: config for config in self.configs}
            config = config_map.get(port, {"model": self.model_name})
            expected_model = config["model"]
            
            # 禁用代理以避免干扰
            proxies = {"http": None, "https": None}
            response = requests.get(f"http://localhost:{port}/api/ps", timeout=5, proxies=proxies)
            if response.status_code == 200:
                data = response.json()
                # 检查是否有模型在运行
                models = data.get('models', [])
                for model in models:
                    model_name = model.get('name', '')
                    # 支持带标签的模型名称匹配
                    if (model_name == expected_model or 
                        model_name == f"{expected_model}:latest" or
                        expected_model in model_name):
                        return True
                # 如果没有目标模型运行，返回 False
                return False
            return False
        except Exception as e:
            log_manager.error_agent(f"检查端口 {port} 模型时出错: {e}")
            return False
    
    def _check_instance_running(self, port: int) -> bool:
        """检查实例是否正在运行（通过检查模型是否加载）"""
        return self._check_model_loaded(port)
    
    def _start_ollama_instance(self, port: int, model_dir: str) -> bool:
        """启动单个 Ollama 实例"""
        log_file = os.path.join(self.log_dir, f"ollama_{port}.log")
        expanded_dir = os.path.expanduser(model_dir)
        
        # 构建启动命令
        cmd = [
            'nohup', 'env',
            f'OLLAMA_HOST=127.0.0.1:{port}',
            f'OLLAMA_MODELS={expanded_dir}',
            'OLLAMA_CONTEXT_LENGTH=40960',
            'ollama', 'serve'
        ]
        
        try:
            log_manager.info_agent(f"启动 Ollama 实例: 端口={port}, 模型目录={model_dir}")
            
            with open(log_file, 'w') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    preexec_fn=os.setsid  # 创建新的进程组
                )
            
            # 等待实例启动
            log_manager.info_agent(f"等待端口 {port} 实例启动...")
            time.sleep(8)
            
            # 检查实例是否响应
            for i in range(10):  # 最多等待50秒
                try:
                    response = requests.get(f"http://localhost:{port}/api/version", timeout=3)
                    if response.status_code == 200:
                        log_manager.info_agent(f"端口 {port} 实例启动成功")
                        return True
                except:
                    time.sleep(5)
                    continue
            
            log_manager.error_agent(f"端口 {port} 实例启动超时")
            return False
            
        except Exception as e:
            log_manager.error_agent(f"启动端口 {port} 实例失败: {e}")
            return False
    
    def start_all_instances(self) -> bool:
        """启动所有 Ollama 实例"""
        success_count = 0
        
        for config in self.configs:
            port = config["port"]
            model_dir = config["model_dir"]
            model = config["model"]
            
            # 检查服务器是否已经在运行
            if self._check_server_running(port):
                log_manager.info_agent(f"端口 {port} 服务器已在运行，跳过启动")
                success_count += 1
                continue
            
            # 启动新实例
            if self._start_ollama_instance(port, model_dir):
                success_count += 1
            else:
                log_manager.error_agent(f"端口 {port} 实例启动失败")
        
        log_manager.info_agent(f"Ollama 实例启动完成: {success_count}/{len(self.ports)} 个成功")
        return success_count >= 1  # 至少一个实例成功
    
    def get_available_ports(self) -> List[int]:
        """获取可用的端口列表（服务器运行中的端口）"""
        available = []
        for port in self.ports:
            if self._check_server_running(port):
                available.append(port)
        return available
    
    def get_ready_ports(self) -> List[int]:
        """获取已加载模型的端口列表"""
        ready = []
        for port in self.ports:
            if self._check_model_loaded(port):
                ready.append(port)
        return ready
    
    def get_status(self) -> Dict:
        """获取所有实例的状态"""
        status = {}
        config_map = {config["port"]: config for config in self.configs}
        
        for port in self.ports:
            server_running = self._check_server_running(port)
            model_loaded = self._check_model_loaded(port)
            config = config_map.get(port, {"model": self.model_name})
            
            status[port] = {
                'server_running': server_running,
                'model_loaded': model_loaded,
                'running': model_loaded,  # 向后兼容
                'model': config["model"] if model_loaded else None,
                'url': f"http://localhost:{port}"
            }
        return status
    
    def _is_embedding_model(self, model_name: str) -> bool:
        """判断是否为embedding模型"""
        embedding_keywords = [
            "embedding", "embed", "Embedding", "Embed",
            "Qwen3-Embedding", "text-embedding"
        ]
        return any(keyword in model_name for keyword in embedding_keywords)
    
    def preload_model(self, port: int) -> bool:
        """预加载模型到指定端口"""
        try:
            # 找到对应端口的模型配置
            config_map = {config["port"]: config for config in self.configs}
            config = config_map.get(port, {"model": self.model_name})
            model_name = config["model"]
            
            log_manager.info_agent(f"预加载模型 {model_name} 到端口 {port}")
            
            # 禁用代理以避免干扰
            proxies = {"http": None, "https": None}
            
            # 判断模型类型并使用相应的API
            if self._is_embedding_model(model_name):
                # Embedding模型使用 /api/embeddings 接口
                response = requests.post(
                    f"http://localhost:{port}/api/embeddings",
                    json={
                        "model": model_name,
                        "prompt": "测试文本"
                    },
                    timeout=60,
                    proxies=proxies
                )
            else:
                # 文本生成模型使用 /api/generate 接口
                response = requests.post(
                    f"http://localhost:{port}/api/generate",
                    json={
                        "model": model_name,
                        "prompt": "Hello",
                        "stream": False,
                        "options": {"num_predict": 1}
                    },
                    timeout=60,
                    proxies=proxies
                )
            
            if response.status_code == 200:
                log_manager.info_agent(f"模型 {model_name} 已预加载到端口 {port}")
                return True
            else:
                log_manager.warning_agent(f"模型预加载失败，状态码: {response.status_code}, 响应: {response.text}")
                return False
                
        except Exception as e:
            log_manager.error_agent(f"预加载模型失败: {e}")
            return False
    
    def preload_all_models(self):
        """预加载模型到所有可用实例"""
        # 获取所有运行的服务器端口，而不是只获取已加载模型的端口
        running_ports = []
        for port in self.ports:
            if self._check_server_running(port):
                running_ports.append(port)
        
        log_manager.info_agent(f"开始预加载模型到 {len(running_ports)} 个实例")
        
        for port in running_ports:
            self.preload_model(port)

# 全局实例
ollama_manager = SimpleOllamaManager()

if __name__ == "__main__":
    print("🚀 启动 Ollama 并发实例...")
    print(f"📋 使用模型: {DEFAULT_MODEL}")
    print(f"📋 端口列表: {OLLAMA_PORTS}")
    
    if ollama_manager.start_all_instances():
        print("✅ Ollama 实例启动成功")
        
        # 显示状态
        status = ollama_manager.get_status()
        for port, info in status.items():
            status_icon = "✅" if info['running'] else "❌"
            print(f"   端口 {port}: {status_icon} {info.get('model', 'N/A')}")
        
        # 预加载模型
        print("📦 预加载模型...")
        ollama_manager.preload_all_models()
        
        print("🎉 所有实例准备完毕！")
    else:
        print("❌ Ollama 实例启动失败")