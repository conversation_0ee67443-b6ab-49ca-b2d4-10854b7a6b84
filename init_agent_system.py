#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent系统初始化脚本
用于启动Agent系统所需的三个核心服务：
1. 设备状态维护服务
2. API服务器
3. 日志分析服务
"""

import os
import sys
import time
import signal
import subprocess
import json
import argparse
from pathlib import Path
from typing import Dict, List, Optional
import psutil

class AgentSystemManager:
    """Agent系统管理器"""
    
    def __init__(self, env: str = "local", log_interval: int = 30):
        self.env = env
        self.log_interval = log_interval
        self.project_root = Path(__file__).parent
        self.pid_file = self.project_root / "status" / "agent_services.json"
        self.services = {}
        
        # 确保状态目录存在
        self.pid_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 服务配置
        self.service_configs = {
            "device_keeper": {
                "name": "设备状态维护服务",
                "command": [sys.executable, "tools/_device_status_keep_tools.py"],
                "cwd": self.project_root,
                "required": True
            },
            "api_server": {
                "name": "API服务器",
                "command": [sys.executable, "start_api_server.py"],
                "cwd": self.project_root,
                "required": True
            },
            "log_analyzer": {
                "name": "日志分析服务",
                "command": [
                    sys.executable, "tools/_log_analysis_tools.py", 
                    "--monitor", 
                    "--log-dir", "log", 
                    "--env", self.env, 
                    "--interval", str(self.log_interval)
                ],
                "cwd": self.project_root,
                "required": True
            }
        }
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到信号 {signum}，正在停止所有服务...")
        self.stop_all_services()
        sys.exit(0)
    
    def _load_service_pids(self) -> Dict:
        """加载服务PID信息"""
        if self.pid_file.exists():
            try:
                with open(self.pid_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载PID文件失败: {e}")
        return {}
    
    def _save_service_pids(self):
        """保存服务PID信息"""
        try:
            with open(self.pid_file, 'w', encoding='utf-8') as f:
                json.dump(self.services, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ 保存PID文件失败: {e}")
    
    def _is_process_running(self, pid: int) -> bool:
        """检查进程是否运行"""
        try:
            return psutil.pid_exists(pid) and psutil.Process(pid).is_running()
        except:
            return False
    
    def _start_service(self, service_name: str, config: Dict) -> bool:
        """启动单个服务"""
        print(f"🚀 启动 {config['name']}...")
        
        try:
            # 启动进程
            process = subprocess.Popen(
                config['command'],
                cwd=config['cwd'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                # 在Windows上需要设置创建新进程组
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0,
                # 在Unix系统上设置新会话
                preexec_fn=None if os.name == 'nt' else os.setsid
            )
            
            # 等待一下确保进程启动
            time.sleep(2)
            
            # 检查进程是否仍在运行
            if process.poll() is None:
                # 进程仍在运行
                self.services[service_name] = {
                    "name": config['name'],
                    "pid": process.pid,
                    "command": " ".join(config['command']),
                    "start_time": time.time(),
                    "status": "running"
                }
                print(f"✅ {config['name']} 启动成功 (PID: {process.pid})")
                return True
            else:
                # 进程已退出
                stdout, stderr = process.communicate()
                print(f"❌ {config['name']} 启动失败")
                if stderr:
                    print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
                return False
                
        except Exception as e:
            print(f"❌ 启动 {config['name']} 时出错: {e}")
            return False
    
    def _stop_service(self, service_name: str, service_info: Dict) -> bool:
        """停止单个服务"""
        pid = service_info.get('pid')
        if not pid:
            return True
        
        print(f"🛑 停止 {service_info['name']} (PID: {pid})...")
        
        try:
            if not self._is_process_running(pid):
                print(f"   进程 {pid} 已经不存在")
                return True
            
            process = psutil.Process(pid)
            
            # 首先尝试优雅关闭
            if os.name == 'nt':
                # Windows
                process.terminate()
            else:
                # Unix/Linux
                process.send_signal(signal.SIGTERM)
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
                print(f"✅ {service_info['name']} 已优雅停止")
                return True
            except psutil.TimeoutExpired:
                # 强制杀死
                print(f"⚠️ {service_info['name']} 未响应优雅停止，强制终止...")
                process.kill()
                process.wait(timeout=5)
                print(f"✅ {service_info['name']} 已强制停止")
                return True
                
        except psutil.NoSuchProcess:
            print(f"   进程 {pid} 已经不存在")
            return True
        except Exception as e:
            print(f"❌ 停止 {service_info['name']} 时出错: {e}")
            return False
    
    def check_services_status(self) -> Dict:
        """检查所有服务状态"""
        print("📊 检查服务状态...")
        
        # 加载已保存的服务信息
        saved_services = self._load_service_pids()
        status = {}
        
        # 过滤掉非服务字段
        service_fields = {'device_keeper', 'api_server', 'log_analyzer'}
        
        for service_name, service_info in saved_services.items():
            # 跳过非服务字段
            if service_name not in service_fields or not isinstance(service_info, dict):
                continue
                
            pid = service_info.get('pid')
            if pid and self._is_process_running(pid):
                status[service_name] = {
                    "status": "running",
                    "pid": pid,
                    "name": service_info.get('name', service_name)
                }
                print(f"✅ {service_info.get('name', service_name)} 正在运行 (PID: {pid})")
            else:
                status[service_name] = {
                    "status": "stopped",
                    "pid": pid,
                    "name": service_info.get('name', service_name)
                }
                print(f"❌ {service_info.get('name', service_name)} 未运行")
        
        return status
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        print("🚀 启动Agent系统服务...")
        print(f"📁 项目目录: {self.project_root}")
        print(f"🌍 环境: {self.env}")
        print(f"⏱️ 日志分析间隔: {self.log_interval}秒")
        print("-" * 50)
        
        # 检查现有服务状态
        existing_services = self._load_service_pids()
        running_services = []
        
        for service_name, service_info in existing_services.items():
            pid = service_info.get('pid')
            if pid and self._is_process_running(pid):
                running_services.append(service_name)
                print(f"⚠️ {service_info.get('name', service_name)} 已在运行 (PID: {pid})")
        
        if running_services:
            print(f"\n发现 {len(running_services)} 个服务已在运行")
            response = input("是否停止现有服务并重新启动? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print("停止现有服务...")
                for service_name in running_services:
                    self._stop_service(service_name, existing_services[service_name])
                time.sleep(2)
            else:
                print("保持现有服务运行")
                return False
        
        print("\n开始启动服务...")
        success_count = 0
        
        # 按顺序启动服务
        for service_name, config in self.service_configs.items():
            if self._start_service(service_name, config):
                success_count += 1
            else:
                if config.get('required', True):
                    print(f"❌ 必要服务 {config['name']} 启动失败，停止启动流程")
                    # 清理已启动的服务
                    self.stop_all_services()
                    return False
        
        # 保存服务信息
        self._save_service_pids()
        
        print(f"\n🎉 成功启动 {success_count}/{len(self.service_configs)} 个服务")
        
        if success_count == len(self.service_configs):
            print("\n📋 服务信息:")
            print("   • 设备状态维护: 持续监控设备状态")
            print("   • API服务器: http://localhost:5630")
            print("   • 日志分析: 自动分析和上传日志")
            print("\n💡 使用 'python init_agent_system.py --stop' 停止所有服务")
            print("💡 使用 'python init_agent_system.py --status' 查看服务状态")
            return True
        
        return False
    
    def stop_all_services(self) -> bool:
        """停止所有服务"""
        print("🛑 停止Agent系统服务...")
        
        # 加载服务信息
        services = self._load_service_pids()
        if not services:
            print("📝 没有发现运行中的服务")
            return True
        
        # 过滤掉非服务字段
        service_fields = {'device_keeper', 'api_server', 'log_analyzer'}
        actual_services = {k: v for k, v in services.items() 
                          if k in service_fields and isinstance(v, dict)}
        
        if not actual_services:
            print("📝 没有发现运行中的服务")
            return True
        
        stopped_count = 0
        for service_name, service_info in actual_services.items():
            if self._stop_service(service_name, service_info):
                stopped_count += 1
        
        # 清理PID文件
        if self.pid_file.exists():
            self.pid_file.unlink()
            print("🗑️ 已清理服务状态文件")
        
        print(f"\n🎉 成功停止 {stopped_count}/{len(actual_services)} 个服务")
        return stopped_count == len(actual_services)
    
    def restart_all_services(self) -> bool:
        """重启所有服务"""
        print("🔄 重启Agent系统服务...")
        
        # 先停止所有服务
        if not self.stop_all_services():
            print("❌ 停止服务失败，无法重启")
            return False
        
        # 等待一下
        print("⏳ 等待3秒后重新启动...")
        time.sleep(3)
        
        # 重新启动所有服务
        return self.start_all_services()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Agent系统管理工具")
    parser.add_argument("--env", choices=["local", "online"], default="local", 
                       help="日志分析环境 (默认: local)")
    parser.add_argument("--interval", type=int, default=30, 
                       help="日志分析间隔秒数 (默认: 30)")
    parser.add_argument("--start", action="store_true", 
                       help="启动所有服务 (默认行为)")
    parser.add_argument("--stop", action="store_true", 
                       help="停止所有服务")
    parser.add_argument("--restart", action="store_true", 
                       help="重启所有服务")
    parser.add_argument("--status", action="store_true", 
                       help="查看服务状态")
    
    args = parser.parse_args()
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查psutil依赖
    try:
        import psutil
    except ImportError:
        print("❌ 缺少psutil依赖，请运行: pip install psutil")
        sys.exit(1)
    
    manager = AgentSystemManager(env=args.env, log_interval=args.interval)
    
    try:
        if args.stop:
            success = manager.stop_all_services()
            sys.exit(0 if success else 1)
        elif args.restart:
            success = manager.restart_all_services()
            sys.exit(0 if success else 1)
        elif args.status:
            manager.check_services_status()
            sys.exit(0)
        else:
            # 默认启动服务
            success = manager.start_all_services()
            if success:
                print("\n🎯 所有服务已启动，Agent系统准备就绪！")
                sys.exit(0)
            else:
                print("\n❌ 服务启动失败")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 运行时错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 